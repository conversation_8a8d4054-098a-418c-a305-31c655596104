{"name": "management-client", "version": "1.0.0", "description": "", "type": "module", "main": "index.js", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@tauri-apps/api": "^2.7.0", "@tauri-apps/plugin-shell": "^2.3.0", "clsx": "^2.1.1", "echarts": "^6.0.0", "echarts-for-react": "^3.0.2", "lucide-react": "^0.536.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-router-dom": "^7.7.1", "tailwind-merge": "^3.3.1", "zustand": "^5.0.7"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@types/echarts": "^5.0.0", "@types/node": "^24.1.0", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@types/react-router-dom": "^5.3.3", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "@vitejs/plugin-react": "^4.7.0", "autoprefixer": "^10.4.21", "eslint": "^9.32.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "postcss": "^8.5.6", "prettier": "^3.6.2", "tailwindcss": "^4.1.11", "typescript": "^5.9.2", "vite": "^7.0.6"}}