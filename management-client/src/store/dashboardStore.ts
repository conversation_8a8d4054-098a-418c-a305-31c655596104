import { create } from 'zustand';
import { Statistics, TrendAnalysis } from '../../../shared/types';

// 仪表板状态管理
interface DashboardStore {
  // State
  statistics: Statistics | null;
  trends: TrendAnalysis[];
  isLoading: boolean;
  lastUpdated: string | null;
  
  // Actions
  setStatistics: (stats: Statistics) => void;
  setTrends: (trends: TrendAnalysis[]) => void;
  setIsLoading: (loading: boolean) => void;
  updateLastUpdated: () => void;
  refreshData: () => Promise<void>;
}

export const useDashboardStore = create<DashboardStore>((set) => ({
  // Initial state
  statistics: null,
  trends: [],
  isLoading: false,
  lastUpdated: null,
  
  // Actions
  setStatistics: (statistics) => {
    set({ statistics });
  },
  
  setTrends: (trends) => {
    set({ trends });
  },
  
  setIsLoading: (isLoading) => {
    set({ isLoading });
  },
  
  updateLastUpdated: () => {
    set({ lastUpdated: new Date().toISOString() });
  },
  
  refreshData: async () => {
    set({ isLoading: true });
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 模拟统计数据
      const mockStats: Statistics = {
        total_users: 12580,
        total_posts: 45230,
        total_comments: 89450,
        active_tasks: 3,
        completed_tasks: 127,
        failed_tasks: 5,
        last_updated: new Date().toISOString(),
      };
      
      // 模拟趋势数据
      const mockTrends: TrendAnalysis[] = [
        {
          keyword: '人工智能',
          date: new Date().toISOString().split('T')[0],
          mention_count: 1250,
          sentiment_score: 0.75,
          influence_score: 0.85,
        },
        {
          keyword: '新能源汽车',
          date: new Date().toISOString().split('T')[0],
          mention_count: 980,
          sentiment_score: 0.65,
          influence_score: 0.72,
        },
      ];
      
      set({ 
        statistics: mockStats, 
        trends: mockTrends,
        lastUpdated: new Date().toISOString(),
        isLoading: false 
      });
    } catch (error) {
      console.error('Failed to refresh data:', error);
      set({ isLoading: false });
    }
  },
}));
