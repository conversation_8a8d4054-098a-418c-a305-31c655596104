import React, { useEffect } from 'react';
import { BarChart3, Users, MessageSquare, TrendingUp, RefreshCw } from 'lucide-react';
import { useDashboardStore } from '../store/dashboardStore';
import { <PERSON>ton, Card, CardHeader, CardContent, Badge } from '../components/ui';
import { formatNumber } from '../../../shared/utils';

const DashboardPage: React.FC = () => {
  const { 
    statistics, 
    trends, 
    isLoading, 
    lastUpdated,
    refreshData 
  } = useDashboardStore();

  useEffect(() => {
    // 初始化时加载数据
    if (!statistics) {
      refreshData();
    }
  }, [statistics, refreshData]);

  const handleRefresh = () => {
    refreshData();
  };

  const getStatCards = () => {
    if (!statistics) return [];
    
    return [
      {
        title: '总用户数',
        value: formatNumber(statistics.total_users),
        icon: Users,
        color: 'text-blue-600',
        bgColor: 'bg-blue-100',
      },
      {
        title: '总微博数',
        value: formatNumber(statistics.total_posts),
        icon: MessageSquare,
        color: 'text-green-600',
        bgColor: 'bg-green-100',
      },
      {
        title: '总评论数',
        value: formatNumber(statistics.total_comments),
        icon: MessageSquare,
        color: 'text-purple-600',
        bgColor: 'bg-purple-100',
      },
      {
        title: '活跃任务',
        value: statistics.active_tasks.toString(),
        icon: TrendingUp,
        color: 'text-orange-600',
        bgColor: 'bg-orange-100',
      },
    ];
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <BarChart3 className="h-8 w-8 text-primary-600" />
              <h1 className="ml-3 text-xl font-semibold text-gray-900 dark:text-white">
                微博管理客户端
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              {lastUpdated && (
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  更新时间: {new Date(lastUpdated).toLocaleTimeString()}
                </span>
              )}
              <Button 
                variant="outline" 
                size="sm"
                onClick={handleRefresh}
                loading={isLoading}
                icon={<RefreshCw className="w-4 h-4" />}
              >
                刷新
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            {getStatCards().map((stat, index) => {
              const IconComponent = stat.icon;
              return (
                <Card key={index} padding="sm">
                  <div className="flex items-center">
                    <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                      <IconComponent className={`w-6 h-6 ${stat.color}`} />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                        {stat.title}
                      </p>
                      <p className="text-2xl font-bold text-gray-900 dark:text-white">
                        {stat.value}
                      </p>
                    </div>
                  </div>
                </Card>
              );
            })}
          </div>

          {/* Charts and Analytics */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            {/* Trend Analysis */}
            <Card>
              <CardHeader 
                title="热门话题趋势"
                subtitle="实时话题热度分析"
              />
              <CardContent>
                {trends.length > 0 ? (
                  <div className="space-y-4">
                    {trends.map((trend, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900 dark:text-white">
                            {trend.keyword}
                          </h4>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            提及次数: {formatNumber(trend.mention_count)}
                          </p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge 
                            variant={trend.sentiment_score > 0.6 ? 'success' : trend.sentiment_score > 0.4 ? 'warning' : 'danger'}
                            size="sm"
                          >
                            情感: {(trend.sentiment_score * 100).toFixed(0)}%
                          </Badge>
                          <Badge variant="info" size="sm">
                            影响: {(trend.influence_score * 100).toFixed(0)}%
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <TrendingUp className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                    <p className="text-gray-500 dark:text-gray-400">
                      暂无趋势数据
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Task Status */}
            <Card>
              <CardHeader 
                title="任务执行状态"
                subtitle="爬虫任务执行情况"
              />
              <CardContent>
                {statistics ? (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">活跃任务:</span>
                      <Badge variant="info">{statistics.active_tasks}</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">已完成:</span>
                      <Badge variant="success">{statistics.completed_tasks}</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">失败任务:</span>
                      <Badge variant="danger">{statistics.failed_tasks}</Badge>
                    </div>
                    
                    {/* Success Rate */}
                    <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm text-gray-500">成功率:</span>
                        <span className="text-sm font-medium">
                          {((statistics.completed_tasks / (statistics.completed_tasks + statistics.failed_tasks)) * 100).toFixed(1)}%
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-green-600 h-2 rounded-full"
                          style={{ 
                            width: `${(statistics.completed_tasks / (statistics.completed_tasks + statistics.failed_tasks)) * 100}%` 
                          }}
                        />
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                    <p className="text-gray-500 dark:text-gray-400">
                      加载中...
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <Card>
            <CardHeader 
              title="快速操作"
              subtitle="常用管理功能"
            />
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button className="justify-start" variant="outline">
                  <Users className="w-4 h-4 mr-2" />
                  用户管理
                </Button>
                <Button className="justify-start" variant="outline">
                  <MessageSquare className="w-4 h-4 mr-2" />
                  内容管理
                </Button>
                <Button className="justify-start" variant="outline">
                  <TrendingUp className="w-4 h-4 mr-2" />
                  分析报告
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
};

export default DashboardPage;
