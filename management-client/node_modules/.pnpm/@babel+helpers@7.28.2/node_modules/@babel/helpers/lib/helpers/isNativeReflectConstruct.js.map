{"version": 3, "names": ["_isNativeReflectConstruct", "result", "Boolean", "prototype", "valueOf", "call", "Reflect", "construct", "_", "exports", "default"], "sources": ["../../src/helpers/isNativeReflectConstruct.ts"], "sourcesContent": ["/* @minVersion 7.9.0 */\n\nexport default function _isNativeReflectConstruct() {\n  // Since Reflect.construct can't be properly polyfilled, some\n  // implementations (e.g. core-js@2) don't set the correct internal slots.\n  // Those polyfills don't allow us to subclass built-ins, so we need to\n  // use our fallback implementation.\n  try {\n    // If the internal slots aren't set, this throws an error similar to\n    //   TypeError: this is not a Boolean object.\n    var result = !Boolean.prototype.valueOf.call(\n      Reflect.construct(<PERSON>olean, [], function () {}),\n    );\n  } catch (_) {}\n  // @ts-expect-error assign to function\n  return (_isNativeReflectConstruct = function () {\n    return !!result;\n  })();\n}\n"], "mappings": ";;;;;;AAEe,SAASA,yBAAyBA,CAAA,EAAG;EAKlD,IAAI;IAGF,IAAIC,MAAM,GAAG,CAACC,OAAO,CAACC,SAAS,CAACC,OAAO,CAACC,IAAI,CAC1CC,OAAO,CAACC,SAAS,CAACL,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAC/C,CAAC;EACH,CAAC,CAAC,OAAOM,CAAC,EAAE,CAAC;EAEb,OAAO,CAAAC,OAAA,CAAAC,OAAA,GAACV,yBAAyB,GAAG,SAAAA,CAAA,EAAY;IAC9C,OAAO,CAAC,CAACC,MAAM;EACjB,CAAC,EAAE,CAAC;AACN", "ignoreList": []}