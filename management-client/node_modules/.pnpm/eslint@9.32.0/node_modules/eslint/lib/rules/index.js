/**
 * @fileoverview Collects the built-in rules into a map structure so that they can be imported all at once and without
 * using the file-system directly.
 * <AUTHOR> (<PERSON>) Metz
 */

"use strict";

/* eslint sort-keys: ["error", "asc"] -- More readable for long list */

const { LazyLoadingRuleMap } = require("./utils/lazy-loading-rule-map");

/** @type {Map<string, import("../types").Rule.RuleModule>} */
module.exports = new LazyLoadingRuleMap(
	Object.entries({
		"accessor-pairs": () => require("./accessor-pairs"),
		"array-bracket-newline": () => require("./array-bracket-newline"),
		"array-bracket-spacing": () => require("./array-bracket-spacing"),
		"array-callback-return": () => require("./array-callback-return"),
		"array-element-newline": () => require("./array-element-newline"),
		"arrow-body-style": () => require("./arrow-body-style"),
		"arrow-parens": () => require("./arrow-parens"),
		"arrow-spacing": () => require("./arrow-spacing"),
		"block-scoped-var": () => require("./block-scoped-var"),
		"block-spacing": () => require("./block-spacing"),
		"brace-style": () => require("./brace-style"),
		"callback-return": () => require("./callback-return"),
		camelcase: () => require("./camelcase"),
		"capitalized-comments": () => require("./capitalized-comments"),
		"class-methods-use-this": () => require("./class-methods-use-this"),
		"comma-dangle": () => require("./comma-dangle"),
		"comma-spacing": () => require("./comma-spacing"),
		"comma-style": () => require("./comma-style"),
		complexity: () => require("./complexity"),
		"computed-property-spacing": () =>
			require("./computed-property-spacing"),
		"consistent-return": () => require("./consistent-return"),
		"consistent-this": () => require("./consistent-this"),
		"constructor-super": () => require("./constructor-super"),
		curly: () => require("./curly"),
		"default-case": () => require("./default-case"),
		"default-case-last": () => require("./default-case-last"),
		"default-param-last": () => require("./default-param-last"),
		"dot-location": () => require("./dot-location"),
		"dot-notation": () => require("./dot-notation"),
		"eol-last": () => require("./eol-last"),
		eqeqeq: () => require("./eqeqeq"),
		"for-direction": () => require("./for-direction"),
		"func-call-spacing": () => require("./func-call-spacing"),
		"func-name-matching": () => require("./func-name-matching"),
		"func-names": () => require("./func-names"),
		"func-style": () => require("./func-style"),
		"function-call-argument-newline": () =>
			require("./function-call-argument-newline"),
		"function-paren-newline": () => require("./function-paren-newline"),
		"generator-star-spacing": () => require("./generator-star-spacing"),
		"getter-return": () => require("./getter-return"),
		"global-require": () => require("./global-require"),
		"grouped-accessor-pairs": () => require("./grouped-accessor-pairs"),
		"guard-for-in": () => require("./guard-for-in"),
		"handle-callback-err": () => require("./handle-callback-err"),
		"id-blacklist": () => require("./id-blacklist"),
		"id-denylist": () => require("./id-denylist"),
		"id-length": () => require("./id-length"),
		"id-match": () => require("./id-match"),
		"implicit-arrow-linebreak": () => require("./implicit-arrow-linebreak"),
		indent: () => require("./indent"),
		"indent-legacy": () => require("./indent-legacy"),
		"init-declarations": () => require("./init-declarations"),
		"jsx-quotes": () => require("./jsx-quotes"),
		"key-spacing": () => require("./key-spacing"),
		"keyword-spacing": () => require("./keyword-spacing"),
		"line-comment-position": () => require("./line-comment-position"),
		"linebreak-style": () => require("./linebreak-style"),
		"lines-around-comment": () => require("./lines-around-comment"),
		"lines-around-directive": () => require("./lines-around-directive"),
		"lines-between-class-members": () =>
			require("./lines-between-class-members"),
		"logical-assignment-operators": () =>
			require("./logical-assignment-operators"),
		"max-classes-per-file": () => require("./max-classes-per-file"),
		"max-depth": () => require("./max-depth"),
		"max-len": () => require("./max-len"),
		"max-lines": () => require("./max-lines"),
		"max-lines-per-function": () => require("./max-lines-per-function"),
		"max-nested-callbacks": () => require("./max-nested-callbacks"),
		"max-params": () => require("./max-params"),
		"max-statements": () => require("./max-statements"),
		"max-statements-per-line": () => require("./max-statements-per-line"),
		"multiline-comment-style": () => require("./multiline-comment-style"),
		"multiline-ternary": () => require("./multiline-ternary"),
		"new-cap": () => require("./new-cap"),
		"new-parens": () => require("./new-parens"),
		"newline-after-var": () => require("./newline-after-var"),
		"newline-before-return": () => require("./newline-before-return"),
		"newline-per-chained-call": () => require("./newline-per-chained-call"),
		"no-alert": () => require("./no-alert"),
		"no-array-constructor": () => require("./no-array-constructor"),
		"no-async-promise-executor": () =>
			require("./no-async-promise-executor"),
		"no-await-in-loop": () => require("./no-await-in-loop"),
		"no-bitwise": () => require("./no-bitwise"),
		"no-buffer-constructor": () => require("./no-buffer-constructor"),
		"no-caller": () => require("./no-caller"),
		"no-case-declarations": () => require("./no-case-declarations"),
		"no-catch-shadow": () => require("./no-catch-shadow"),
		"no-class-assign": () => require("./no-class-assign"),
		"no-compare-neg-zero": () => require("./no-compare-neg-zero"),
		"no-cond-assign": () => require("./no-cond-assign"),
		"no-confusing-arrow": () => require("./no-confusing-arrow"),
		"no-console": () => require("./no-console"),
		"no-const-assign": () => require("./no-const-assign"),
		"no-constant-binary-expression": () =>
			require("./no-constant-binary-expression"),
		"no-constant-condition": () => require("./no-constant-condition"),
		"no-constructor-return": () => require("./no-constructor-return"),
		"no-continue": () => require("./no-continue"),
		"no-control-regex": () => require("./no-control-regex"),
		"no-debugger": () => require("./no-debugger"),
		"no-delete-var": () => require("./no-delete-var"),
		"no-div-regex": () => require("./no-div-regex"),
		"no-dupe-args": () => require("./no-dupe-args"),
		"no-dupe-class-members": () => require("./no-dupe-class-members"),
		"no-dupe-else-if": () => require("./no-dupe-else-if"),
		"no-dupe-keys": () => require("./no-dupe-keys"),
		"no-duplicate-case": () => require("./no-duplicate-case"),
		"no-duplicate-imports": () => require("./no-duplicate-imports"),
		"no-else-return": () => require("./no-else-return"),
		"no-empty": () => require("./no-empty"),
		"no-empty-character-class": () => require("./no-empty-character-class"),
		"no-empty-function": () => require("./no-empty-function"),
		"no-empty-pattern": () => require("./no-empty-pattern"),
		"no-empty-static-block": () => require("./no-empty-static-block"),
		"no-eq-null": () => require("./no-eq-null"),
		"no-eval": () => require("./no-eval"),
		"no-ex-assign": () => require("./no-ex-assign"),
		"no-extend-native": () => require("./no-extend-native"),
		"no-extra-bind": () => require("./no-extra-bind"),
		"no-extra-boolean-cast": () => require("./no-extra-boolean-cast"),
		"no-extra-label": () => require("./no-extra-label"),
		"no-extra-parens": () => require("./no-extra-parens"),
		"no-extra-semi": () => require("./no-extra-semi"),
		"no-fallthrough": () => require("./no-fallthrough"),
		"no-floating-decimal": () => require("./no-floating-decimal"),
		"no-func-assign": () => require("./no-func-assign"),
		"no-global-assign": () => require("./no-global-assign"),
		"no-implicit-coercion": () => require("./no-implicit-coercion"),
		"no-implicit-globals": () => require("./no-implicit-globals"),
		"no-implied-eval": () => require("./no-implied-eval"),
		"no-import-assign": () => require("./no-import-assign"),
		"no-inline-comments": () => require("./no-inline-comments"),
		"no-inner-declarations": () => require("./no-inner-declarations"),
		"no-invalid-regexp": () => require("./no-invalid-regexp"),
		"no-invalid-this": () => require("./no-invalid-this"),
		"no-irregular-whitespace": () => require("./no-irregular-whitespace"),
		"no-iterator": () => require("./no-iterator"),
		"no-label-var": () => require("./no-label-var"),
		"no-labels": () => require("./no-labels"),
		"no-lone-blocks": () => require("./no-lone-blocks"),
		"no-lonely-if": () => require("./no-lonely-if"),
		"no-loop-func": () => require("./no-loop-func"),
		"no-loss-of-precision": () => require("./no-loss-of-precision"),
		"no-magic-numbers": () => require("./no-magic-numbers"),
		"no-misleading-character-class": () =>
			require("./no-misleading-character-class"),
		"no-mixed-operators": () => require("./no-mixed-operators"),
		"no-mixed-requires": () => require("./no-mixed-requires"),
		"no-mixed-spaces-and-tabs": () => require("./no-mixed-spaces-and-tabs"),
		"no-multi-assign": () => require("./no-multi-assign"),
		"no-multi-spaces": () => require("./no-multi-spaces"),
		"no-multi-str": () => require("./no-multi-str"),
		"no-multiple-empty-lines": () => require("./no-multiple-empty-lines"),
		"no-native-reassign": () => require("./no-native-reassign"),
		"no-negated-condition": () => require("./no-negated-condition"),
		"no-negated-in-lhs": () => require("./no-negated-in-lhs"),
		"no-nested-ternary": () => require("./no-nested-ternary"),
		"no-new": () => require("./no-new"),
		"no-new-func": () => require("./no-new-func"),
		"no-new-native-nonconstructor": () =>
			require("./no-new-native-nonconstructor"),
		"no-new-object": () => require("./no-new-object"),
		"no-new-require": () => require("./no-new-require"),
		"no-new-symbol": () => require("./no-new-symbol"),
		"no-new-wrappers": () => require("./no-new-wrappers"),
		"no-nonoctal-decimal-escape": () =>
			require("./no-nonoctal-decimal-escape"),
		"no-obj-calls": () => require("./no-obj-calls"),
		"no-object-constructor": () => require("./no-object-constructor"),
		"no-octal": () => require("./no-octal"),
		"no-octal-escape": () => require("./no-octal-escape"),
		"no-param-reassign": () => require("./no-param-reassign"),
		"no-path-concat": () => require("./no-path-concat"),
		"no-plusplus": () => require("./no-plusplus"),
		"no-process-env": () => require("./no-process-env"),
		"no-process-exit": () => require("./no-process-exit"),
		"no-promise-executor-return": () =>
			require("./no-promise-executor-return"),
		"no-proto": () => require("./no-proto"),
		"no-prototype-builtins": () => require("./no-prototype-builtins"),
		"no-redeclare": () => require("./no-redeclare"),
		"no-regex-spaces": () => require("./no-regex-spaces"),
		"no-restricted-exports": () => require("./no-restricted-exports"),
		"no-restricted-globals": () => require("./no-restricted-globals"),
		"no-restricted-imports": () => require("./no-restricted-imports"),
		"no-restricted-modules": () => require("./no-restricted-modules"),
		"no-restricted-properties": () => require("./no-restricted-properties"),
		"no-restricted-syntax": () => require("./no-restricted-syntax"),
		"no-return-assign": () => require("./no-return-assign"),
		"no-return-await": () => require("./no-return-await"),
		"no-script-url": () => require("./no-script-url"),
		"no-self-assign": () => require("./no-self-assign"),
		"no-self-compare": () => require("./no-self-compare"),
		"no-sequences": () => require("./no-sequences"),
		"no-setter-return": () => require("./no-setter-return"),
		"no-shadow": () => require("./no-shadow"),
		"no-shadow-restricted-names": () =>
			require("./no-shadow-restricted-names"),
		"no-spaced-func": () => require("./no-spaced-func"),
		"no-sparse-arrays": () => require("./no-sparse-arrays"),
		"no-sync": () => require("./no-sync"),
		"no-tabs": () => require("./no-tabs"),
		"no-template-curly-in-string": () =>
			require("./no-template-curly-in-string"),
		"no-ternary": () => require("./no-ternary"),
		"no-this-before-super": () => require("./no-this-before-super"),
		"no-throw-literal": () => require("./no-throw-literal"),
		"no-trailing-spaces": () => require("./no-trailing-spaces"),
		"no-unassigned-vars": () => require("./no-unassigned-vars"),
		"no-undef": () => require("./no-undef"),
		"no-undef-init": () => require("./no-undef-init"),
		"no-undefined": () => require("./no-undefined"),
		"no-underscore-dangle": () => require("./no-underscore-dangle"),
		"no-unexpected-multiline": () => require("./no-unexpected-multiline"),
		"no-unmodified-loop-condition": () =>
			require("./no-unmodified-loop-condition"),
		"no-unneeded-ternary": () => require("./no-unneeded-ternary"),
		"no-unreachable": () => require("./no-unreachable"),
		"no-unreachable-loop": () => require("./no-unreachable-loop"),
		"no-unsafe-finally": () => require("./no-unsafe-finally"),
		"no-unsafe-negation": () => require("./no-unsafe-negation"),
		"no-unsafe-optional-chaining": () =>
			require("./no-unsafe-optional-chaining"),
		"no-unused-expressions": () => require("./no-unused-expressions"),
		"no-unused-labels": () => require("./no-unused-labels"),
		"no-unused-private-class-members": () =>
			require("./no-unused-private-class-members"),
		"no-unused-vars": () => require("./no-unused-vars"),
		"no-use-before-define": () => require("./no-use-before-define"),
		"no-useless-assignment": () => require("./no-useless-assignment"),
		"no-useless-backreference": () => require("./no-useless-backreference"),
		"no-useless-call": () => require("./no-useless-call"),
		"no-useless-catch": () => require("./no-useless-catch"),
		"no-useless-computed-key": () => require("./no-useless-computed-key"),
		"no-useless-concat": () => require("./no-useless-concat"),
		"no-useless-constructor": () => require("./no-useless-constructor"),
		"no-useless-escape": () => require("./no-useless-escape"),
		"no-useless-rename": () => require("./no-useless-rename"),
		"no-useless-return": () => require("./no-useless-return"),
		"no-var": () => require("./no-var"),
		"no-void": () => require("./no-void"),
		"no-warning-comments": () => require("./no-warning-comments"),
		"no-whitespace-before-property": () =>
			require("./no-whitespace-before-property"),
		"no-with": () => require("./no-with"),
		"nonblock-statement-body-position": () =>
			require("./nonblock-statement-body-position"),
		"object-curly-newline": () => require("./object-curly-newline"),
		"object-curly-spacing": () => require("./object-curly-spacing"),
		"object-property-newline": () => require("./object-property-newline"),
		"object-shorthand": () => require("./object-shorthand"),
		"one-var": () => require("./one-var"),
		"one-var-declaration-per-line": () =>
			require("./one-var-declaration-per-line"),
		"operator-assignment": () => require("./operator-assignment"),
		"operator-linebreak": () => require("./operator-linebreak"),
		"padded-blocks": () => require("./padded-blocks"),
		"padding-line-between-statements": () =>
			require("./padding-line-between-statements"),
		"prefer-arrow-callback": () => require("./prefer-arrow-callback"),
		"prefer-const": () => require("./prefer-const"),
		"prefer-destructuring": () => require("./prefer-destructuring"),
		"prefer-exponentiation-operator": () =>
			require("./prefer-exponentiation-operator"),
		"prefer-named-capture-group": () =>
			require("./prefer-named-capture-group"),
		"prefer-numeric-literals": () => require("./prefer-numeric-literals"),
		"prefer-object-has-own": () => require("./prefer-object-has-own"),
		"prefer-object-spread": () => require("./prefer-object-spread"),
		"prefer-promise-reject-errors": () =>
			require("./prefer-promise-reject-errors"),
		"prefer-reflect": () => require("./prefer-reflect"),
		"prefer-regex-literals": () => require("./prefer-regex-literals"),
		"prefer-rest-params": () => require("./prefer-rest-params"),
		"prefer-spread": () => require("./prefer-spread"),
		"prefer-template": () => require("./prefer-template"),
		"quote-props": () => require("./quote-props"),
		quotes: () => require("./quotes"),
		radix: () => require("./radix"),
		"require-atomic-updates": () => require("./require-atomic-updates"),
		"require-await": () => require("./require-await"),
		"require-unicode-regexp": () => require("./require-unicode-regexp"),
		"require-yield": () => require("./require-yield"),
		"rest-spread-spacing": () => require("./rest-spread-spacing"),
		semi: () => require("./semi"),
		"semi-spacing": () => require("./semi-spacing"),
		"semi-style": () => require("./semi-style"),
		"sort-imports": () => require("./sort-imports"),
		"sort-keys": () => require("./sort-keys"),
		"sort-vars": () => require("./sort-vars"),
		"space-before-blocks": () => require("./space-before-blocks"),
		"space-before-function-paren": () =>
			require("./space-before-function-paren"),
		"space-in-parens": () => require("./space-in-parens"),
		"space-infix-ops": () => require("./space-infix-ops"),
		"space-unary-ops": () => require("./space-unary-ops"),
		"spaced-comment": () => require("./spaced-comment"),
		strict: () => require("./strict"),
		"switch-colon-spacing": () => require("./switch-colon-spacing"),
		"symbol-description": () => require("./symbol-description"),
		"template-curly-spacing": () => require("./template-curly-spacing"),
		"template-tag-spacing": () => require("./template-tag-spacing"),
		"unicode-bom": () => require("./unicode-bom"),
		"use-isnan": () => require("./use-isnan"),
		"valid-typeof": () => require("./valid-typeof"),
		"vars-on-top": () => require("./vars-on-top"),
		"wrap-iife": () => require("./wrap-iife"),
		"wrap-regex": () => require("./wrap-regex"),
		"yield-star-spacing": () => require("./yield-star-spacing"),
		yoda: () => require("./yoda"),
	}),
);
