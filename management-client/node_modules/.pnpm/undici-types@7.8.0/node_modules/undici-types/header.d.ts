import { Autocomplete } from './utility'

/**
 * The header type declaration of `undici`.
 */
export type IncomingHttpHeaders = Record<string, string | string[] | undefined>

type HeaderNames = Autocomplete<
  | 'Accept'
  | 'Accept-CH'
  | 'Accept-Charset'
  | 'Accept-Encoding'
  | 'Accept-Language'
  | 'Accept-Patch'
  | 'Accept-Post'
  | 'Accept-Ranges'
  | 'Access-Control-Allow-Credentials'
  | 'Access-Control-Allow-Headers'
  | 'Access-Control-Allow-Methods'
  | 'Access-Control-Allow-Origin'
  | 'Access-Control-Expose-Headers'
  | 'Access-Control-Max-Age'
  | 'Access-Control-Request-Headers'
  | 'Access-Control-Request-Method'
  | 'Age'
  | 'Allow'
  | 'Alt-Svc'
  | 'Alt-Used'
  | 'Authorization'
  | 'Cache-Control'
  | 'Clear-Site-Data'
  | 'Connection'
  | 'Content-Disposition'
  | 'Content-Encoding'
  | 'Content-Language'
  | 'Content-Length'
  | 'Content-Location'
  | 'Content-Range'
  | 'Content-Security-Policy'
  | 'Content-Security-Policy-Report-Only'
  | 'Content-Type'
  | 'Cookie'
  | 'Cross-Origin-Embedder-Policy'
  | 'Cross-Origin-Opener-Policy'
  | 'Cross-Origin-Resource-Policy'
  | 'Date'
  | 'Device-Memory'
  | 'ETag'
  | 'Expect'
  | 'Expect-CT'
  | 'Expires'
  | 'Forwarded'
  | 'From'
  | 'Host'
  | 'If-Match'
  | 'If-Modified-Since'
  | 'If-None-Match'
  | 'If-Range'
  | 'If-Unmodified-Since'
  | 'Keep-Alive'
  | 'Last-Modified'
  | 'Link'
  | 'Location'
  | 'Max-Forwards'
  | 'Origin'
  | 'Permissions-Policy'
  | 'Priority'
  | 'Proxy-Authenticate'
  | 'Proxy-Authorization'
  | 'Range'
  | 'Referer'
  | 'Referrer-Policy'
  | 'Retry-After'
  | 'Sec-Fetch-Dest'
  | 'Sec-Fetch-Mode'
  | 'Sec-Fetch-Site'
  | 'Sec-Fetch-User'
  | 'Sec-Purpose'
  | 'Sec-WebSocket-Accept'
  | 'Server'
  | 'Server-Timing'
  | 'Service-Worker-Navigation-Preload'
  | 'Set-Cookie'
  | 'SourceMap'
  | 'Strict-Transport-Security'
  | 'TE'
  | 'Timing-Allow-Origin'
  | 'Trailer'
  | 'Transfer-Encoding'
  | 'Upgrade'
  | 'Upgrade-Insecure-Requests'
  | 'User-Agent'
  | 'Vary'
  | 'Via'
  | 'WWW-Authenticate'
  | 'X-Content-Type-Options'
  | 'X-Frame-Options'
>

type IANARegisteredMimeType = Autocomplete<
  | 'audio/aac'
  | 'video/x-msvideo'
  | 'image/avif'
  | 'video/av1'
  | 'application/octet-stream'
  | 'image/bmp'
  | 'text/css'
  | 'text/csv'
  | 'application/vnd.ms-fontobject'
  | 'application/epub+zip'
  | 'image/gif'
  | 'application/gzip'
  | 'text/html'
  | 'image/x-icon'
  | 'text/calendar'
  | 'image/jpeg'
  | 'text/javascript'
  | 'application/json'
  | 'application/ld+json'
  | 'audio/x-midi'
  | 'audio/mpeg'
  | 'video/mp4'
  | 'video/mpeg'
  | 'audio/ogg'
  | 'video/ogg'
  | 'application/ogg'
  | 'audio/opus'
  | 'font/otf'
  | 'application/pdf'
  | 'image/png'
  | 'application/rtf'
  | 'image/svg+xml'
  | 'image/tiff'
  | 'video/mp2t'
  | 'font/ttf'
  | 'text/plain'
  | 'application/wasm'
  | 'video/webm'
  | 'audio/webm'
  | 'image/webp'
  | 'font/woff'
  | 'font/woff2'
  | 'application/xhtml+xml'
  | 'application/xml'
  | 'application/zip'
  | 'video/3gpp'
  | 'video/3gpp2'
  | 'model/gltf+json'
  | 'model/gltf-binary'
>

type KnownHeaderValues = {
  'content-type': IANARegisteredMimeType
}

export type HeaderRecord = {
  [K in HeaderNames | Lowercase<HeaderNames>]?: Lowercase<K> extends keyof KnownHeaderValues
    ? KnownHeaderValues[Lowercase<K>]
    : string
}
