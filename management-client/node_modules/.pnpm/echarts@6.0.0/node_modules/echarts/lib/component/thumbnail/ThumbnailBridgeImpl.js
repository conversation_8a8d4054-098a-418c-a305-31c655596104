
/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/


/**
 * AUTO-GENERATED FILE. DO NOT MODIFY.
 */

/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/
import { calcZ2Range } from '../../util/graphic.js';
/**
 * [CAVEAT]: the call order of `ThumbnailView['render']` and other
 *  `ChartView['render']/ComponentView['render']` is not guaranteed.
 */
var ThumbnailBridgeImpl = /** @class */function () {
  function ThumbnailBridgeImpl(thumbnailModel) {
    this._thumbnailModel = thumbnailModel;
  }
  ThumbnailBridgeImpl.prototype.reset = function (api) {
    this._renderVersion = api.getMainProcessVersion();
  };
  ThumbnailBridgeImpl.prototype.renderContent = function (opt) {
    var thumbnailView = opt.api.getViewOfComponentModel(this._thumbnailModel);
    if (!thumbnailView) {
      return;
    }
    opt.group.silent = true;
    thumbnailView.renderContent({
      group: opt.group,
      targetTrans: opt.targetTrans,
      z2Range: calcZ2Range(opt.group),
      roamType: opt.roamType,
      viewportRect: opt.viewportRect,
      renderVersion: this._renderVersion
    });
  };
  ThumbnailBridgeImpl.prototype.updateWindow = function (targetTrans, api) {
    var thumbnailView = api.getViewOfComponentModel(this._thumbnailModel);
    if (!thumbnailView) {
      return;
    }
    thumbnailView.updateWindow({
      targetTrans: targetTrans,
      renderVersion: this._renderVersion
    });
  };
  return ThumbnailBridgeImpl;
}();
export { ThumbnailBridgeImpl };