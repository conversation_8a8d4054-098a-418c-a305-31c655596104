
/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).echarts={})}(this,(function(t){"use strict";
/*! *****************************************************************************
    Copyright (c) Microsoft Corporation.

    Permission to use, copy, modify, and/or distribute this software for any
    purpose with or without fee is hereby granted.

    THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
    REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
    AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
    INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
    LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
    OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
    PERFORMANCE OF THIS SOFTWARE.
    ***************************************************************************** */var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},e(t,n)};function n(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}var i=function(){this.firefox=!1,this.ie=!1,this.edge=!1,this.newEdge=!1,this.weChat=!1},r=new function(){this.browser=new i,this.node=!1,this.wxa=!1,this.worker=!1,this.svgSupported=!1,this.touchEventsSupported=!1,this.pointerEventsSupported=!1,this.domSupported=!1,this.transformSupported=!1,this.transform3dSupported=!1,this.hasGlobalWindow="undefined"!=typeof window};"object"==typeof wx&&"function"==typeof wx.getSystemInfoSync?(r.wxa=!0,r.touchEventsSupported=!0):"undefined"==typeof document&&"undefined"!=typeof self?r.worker=!0:!r.hasGlobalWindow||"Deno"in window||"undefined"!=typeof navigator&&"string"==typeof navigator.userAgent&&navigator.userAgent.indexOf("Node.js")>-1?(r.node=!0,r.svgSupported=!0):function(t,e){var n=e.browser,i=t.match(/Firefox\/([\d.]+)/),r=t.match(/MSIE\s([\d.]+)/)||t.match(/Trident\/.+?rv:(([\d.]+))/),o=t.match(/Edge?\/([\d.]+)/),a=/micromessenger/i.test(t);i&&(n.firefox=!0,n.version=i[1]);r&&(n.ie=!0,n.version=r[1]);o&&(n.edge=!0,n.version=o[1],n.newEdge=+o[1].split(".")[0]>18);a&&(n.weChat=!0);e.svgSupported="undefined"!=typeof SVGRect,e.touchEventsSupported="ontouchstart"in window&&!n.ie&&!n.edge,e.pointerEventsSupported="onpointerdown"in window&&(n.edge||n.ie&&+n.version>=11);var s=e.domSupported="undefined"!=typeof document;if(s){var l=document.documentElement.style;e.transform3dSupported=(n.ie&&"transition"in l||n.edge||"WebKitCSSMatrix"in window&&"m11"in new WebKitCSSMatrix||"MozPerspective"in l)&&!("OTransition"in l),e.transformSupported=e.transform3dSupported||n.ie&&+n.version>=9}}(navigator.userAgent,r);var o="12px sans-serif";var a,s,l=function(t){var e={};if("undefined"==typeof JSON)return e;for(var n=0;n<t.length;n++){var i=String.fromCharCode(n+32),r=(t.charCodeAt(n)-20)/100;e[i]=r}return e}("007LLmW'55;N0500LLLLLLLLLL00NNNLzWW\\\\WQb\\0FWLg\\bWb\\WQ\\WrWWQ000CL5LLFLL0LL**F*gLLLL5F0LF\\FFF5.5N"),u={createCanvas:function(){return"undefined"!=typeof document&&document.createElement("canvas")},measureText:function(t,e){if(!a){var n=u.createCanvas();a=n&&n.getContext("2d")}if(a)return s!==e&&(s=a.font=e||o),a.measureText(t);t=t||"";var i=/((?:\d+)?\.?\d*)px/.exec(e=e||o),r=i&&+i[1]||12,h=0;if(e.indexOf("mono")>=0)h=r*t.length;else for(var c=0;c<t.length;c++){var p=l[t[c]];h+=null==p?r:p*r}return{width:h}},loadImage:function(t,e,n){var i=new Image;return i.onload=e,i.onerror=n,i.src=t,i}};function h(t){for(var e in u)t[e]&&(u[e]=t[e])}var c=B(["Function","RegExp","Date","Error","CanvasGradient","CanvasPattern","Image","Canvas"],(function(t,e){return t["[object "+e+"]"]=!0,t}),{}),p=B(["Int8","Uint8","Uint8Clamped","Int16","Uint16","Int32","Uint32","Float32","Float64"],(function(t,e){return t["[object "+e+"Array]"]=!0,t}),{}),f=Object.prototype.toString,d=Array.prototype,g=d.forEach,v=d.filter,y=d.slice,m=d.map,_=function(){}.constructor,x=_?_.prototype:null,b="__proto__",w=2311;function S(){return w++}function M(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];"undefined"!=typeof console&&console.error.apply(console,t)}function T(t){if(null==t||"object"!=typeof t)return t;var e=t,n=f.call(t);if("[object Array]"===n){if(!ht(t)){e=[];for(var i=0,r=t.length;i<r;i++)e[i]=T(t[i])}}else if(p[n]){if(!ht(t)){var o=t.constructor;if(o.from)e=o.from(t);else{e=new o(t.length);for(i=0,r=t.length;i<r;i++)e[i]=t[i]}}}else if(!c[n]&&!ht(t)&&!K(t))for(var a in e={},t)t.hasOwnProperty(a)&&a!==b&&(e[a]=T(t[a]));return e}function k(t,e,n){if(!q(e)||!q(t))return n?T(e):t;for(var i in e)if(e.hasOwnProperty(i)&&i!==b){var r=t[i],o=e[i];!q(o)||!q(r)||H(o)||H(r)||K(o)||K(r)||Z(o)||Z(r)||ht(o)||ht(r)?!n&&i in t||(t[i]=T(e[i])):k(r,o,n)}return t}function C(t,e){if(Object.assign)Object.assign(t,e);else for(var n in e)e.hasOwnProperty(n)&&n!==b&&(t[n]=e[n]);return t}function D(t,e,n){for(var i=F(e),r=0,o=i.length;r<o;r++){var a=i[r];(n?null!=e[a]:null==t[a])&&(t[a]=e[a])}return t}var I=u.createCanvas;function A(t,e){if(t){if(t.indexOf)return t.indexOf(e);for(var n=0,i=t.length;n<i;n++)if(t[n]===e)return n}return-1}function L(t,e){var n=t.prototype;function i(){}for(var r in i.prototype=e.prototype,t.prototype=new i,n)n.hasOwnProperty(r)&&(t.prototype[r]=n[r]);t.prototype.constructor=t,t.superClass=e}function P(t,e,n){if(t="prototype"in t?t.prototype:t,e="prototype"in e?e.prototype:e,Object.getOwnPropertyNames)for(var i=Object.getOwnPropertyNames(e),r=0;r<i.length;r++){var o=i[r];"constructor"!==o&&(n?null!=e[o]:null==t[o])&&(t[o]=e[o])}else D(t,e,n)}function O(t){return!!t&&("string"!=typeof t&&"number"==typeof t.length)}function R(t,e,n){if(t&&e)if(t.forEach&&t.forEach===g)t.forEach(e,n);else if(t.length===+t.length)for(var i=0,r=t.length;i<r;i++)e.call(n,t[i],i,t);else for(var o in t)t.hasOwnProperty(o)&&e.call(n,t[o],o,t)}function N(t,e,n){if(!t)return[];if(!e)return rt(t);if(t.map&&t.map===m)return t.map(e,n);for(var i=[],r=0,o=t.length;r<o;r++)i.push(e.call(n,t[r],r,t));return i}function B(t,e,n,i){if(t&&e){for(var r=0,o=t.length;r<o;r++)n=e.call(i,n,t[r],r,t);return n}}function E(t,e,n){if(!t)return[];if(!e)return rt(t);if(t.filter&&t.filter===v)return t.filter(e,n);for(var i=[],r=0,o=t.length;r<o;r++)e.call(n,t[r],r,t)&&i.push(t[r]);return i}function z(t,e,n){if(t&&e)for(var i=0,r=t.length;i<r;i++)if(e.call(n,t[i],i,t))return t[i]}function F(t){if(!t)return[];if(Object.keys)return Object.keys(t);var e=[];for(var n in t)t.hasOwnProperty(n)&&e.push(n);return e}var V=x&&G(x.bind)?x.call.bind(x.bind):function(t,e){for(var n=[],i=2;i<arguments.length;i++)n[i-2]=arguments[i];return function(){return t.apply(e,n.concat(y.call(arguments)))}};function W(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];return function(){return t.apply(this,e.concat(y.call(arguments)))}}function H(t){return Array.isArray?Array.isArray(t):"[object Array]"===f.call(t)}function G(t){return"function"==typeof t}function U(t){return"string"==typeof t}function X(t){return"[object String]"===f.call(t)}function Y(t){return"number"==typeof t}function q(t){var e=typeof t;return"function"===e||!!t&&"object"===e}function Z(t){return!!c[f.call(t)]}function j(t){return!!p[f.call(t)]}function K(t){return"object"==typeof t&&"number"==typeof t.nodeType&&"object"==typeof t.ownerDocument}function $(t){return null!=t.colorStops}function Q(t){return null!=t.image}function J(t){return"[object RegExp]"===f.call(t)}function tt(t){return t!=t}function et(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var n=0,i=t.length;n<i;n++)if(null!=t[n])return t[n]}function nt(t,e){return null!=t?t:e}function it(t,e,n){return null!=t?t:null!=e?e:n}function rt(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];return y.apply(t,e)}function ot(t){if("number"==typeof t)return[t,t,t,t];var e=t.length;return 2===e?[t[0],t[1],t[0],t[1]]:3===e?[t[0],t[1],t[2],t[1]]:t}function at(t,e){if(!t)throw new Error(e)}function st(t){return null==t?null:"function"==typeof t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}var lt="__ec_primitive__";function ut(t){t[lt]=!0}function ht(t){return t[lt]}var ct=function(){function t(){this.data={}}return t.prototype.delete=function(t){var e=this.has(t);return e&&delete this.data[t],e},t.prototype.has=function(t){return this.data.hasOwnProperty(t)},t.prototype.get=function(t){return this.data[t]},t.prototype.set=function(t,e){return this.data[t]=e,this},t.prototype.keys=function(){return F(this.data)},t.prototype.forEach=function(t){var e=this.data;for(var n in e)e.hasOwnProperty(n)&&t(e[n],n)},t}(),pt="function"==typeof Map;var ft=function(){function t(e){var n=H(e);this.data=pt?new Map:new ct;var i=this;function r(t,e){n?i.set(t,e):i.set(e,t)}e instanceof t?e.each(r):e&&R(e,r)}return t.prototype.hasKey=function(t){return this.data.has(t)},t.prototype.get=function(t){return this.data.get(t)},t.prototype.set=function(t,e){return this.data.set(t,e),e},t.prototype.each=function(t,e){this.data.forEach((function(n,i){t.call(e,n,i)}))},t.prototype.keys=function(){var t=this.data.keys();return pt?Array.from(t):t},t.prototype.removeKey=function(t){this.data.delete(t)},t}();function dt(t){return new ft(t)}function gt(t,e){for(var n=new t.constructor(t.length+e.length),i=0;i<t.length;i++)n[i]=t[i];var r=t.length;for(i=0;i<e.length;i++)n[i+r]=e[i];return n}function vt(t,e){var n;if(Object.create)n=Object.create(t);else{var i=function(){};i.prototype=t,n=new i}return e&&C(n,e),n}function yt(t){var e=t.style;e.webkitUserSelect="none",e.userSelect="none",e.webkitTapHighlightColor="rgba(0,0,0,0)",e["-webkit-touch-callout"]="none"}function mt(t,e){return t.hasOwnProperty(e)}function _t(){}var xt=180/Math.PI,bt=Number.EPSILON||Math.pow(2,-52),wt=Object.freeze({__proto__:null,guid:S,logError:M,clone:T,merge:k,mergeAll:function(t,e){for(var n=t[0],i=1,r=t.length;i<r;i++)n=k(n,t[i],e);return n},extend:C,defaults:D,createCanvas:I,indexOf:A,inherits:L,mixin:P,isArrayLike:O,each:R,map:N,reduce:B,filter:E,find:z,keys:F,bind:V,curry:W,isArray:H,isFunction:G,isString:U,isStringSafe:X,isNumber:Y,isObject:q,isBuiltInObject:Z,isTypedArray:j,isDom:K,isGradientObject:$,isImagePatternObject:Q,isRegExp:J,eqNaN:tt,retrieve:et,retrieve2:nt,retrieve3:it,slice:rt,normalizeCssArray:ot,assert:at,trim:st,setAsPrimitive:ut,isPrimitive:ht,HashMap:ft,createHashMap:dt,concatArray:gt,createObject:vt,disableUserSelect:yt,hasOwn:mt,noop:_t,RADIAN_TO_DEGREE:xt,EPSILON:bt});function St(t,e){return null==t&&(t=0),null==e&&(e=0),[t,e]}function Mt(t){return[t[0],t[1]]}function Tt(t,e,n){return t[0]=e[0]+n[0],t[1]=e[1]+n[1],t}function kt(t,e,n){return t[0]=e[0]-n[0],t[1]=e[1]-n[1],t}function Ct(t){return Math.sqrt(It(t))}var Dt=Ct;function It(t){return t[0]*t[0]+t[1]*t[1]}var At=It;function Lt(t,e,n){return t[0]=e[0]*n,t[1]=e[1]*n,t}function Pt(t,e){var n=Ct(e);return 0===n?(t[0]=0,t[1]=0):(t[0]=e[0]/n,t[1]=e[1]/n),t}function Ot(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))}var Rt=Ot;function Nt(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])}var Bt=Nt;function Et(t,e,n,i){return t[0]=e[0]+i*(n[0]-e[0]),t[1]=e[1]+i*(n[1]-e[1]),t}function zt(t,e,n){var i=e[0],r=e[1];return t[0]=n[0]*i+n[2]*r+n[4],t[1]=n[1]*i+n[3]*r+n[5],t}function Ft(t,e,n){return t[0]=Math.min(e[0],n[0]),t[1]=Math.min(e[1],n[1]),t}function Vt(t,e,n){return t[0]=Math.max(e[0],n[0]),t[1]=Math.max(e[1],n[1]),t}var Wt=Object.freeze({__proto__:null,create:St,copy:function(t,e){return t[0]=e[0],t[1]=e[1],t},clone:Mt,set:function(t,e,n){return t[0]=e,t[1]=n,t},add:Tt,scaleAndAdd:function(t,e,n,i){return t[0]=e[0]+n[0]*i,t[1]=e[1]+n[1]*i,t},sub:kt,len:Ct,length:Dt,lenSquare:It,lengthSquare:At,mul:function(t,e,n){return t[0]=e[0]*n[0],t[1]=e[1]*n[1],t},div:function(t,e,n){return t[0]=e[0]/n[0],t[1]=e[1]/n[1],t},dot:function(t,e){return t[0]*e[0]+t[1]*e[1]},scale:Lt,normalize:Pt,distance:Ot,dist:Rt,distanceSquare:Nt,distSquare:Bt,negate:function(t,e){return t[0]=-e[0],t[1]=-e[1],t},lerp:Et,applyTransform:zt,min:Ft,max:Vt}),Ht=function(t,e){this.target=t,this.topTarget=e&&e.topTarget},Gt=function(){function t(t){this.handler=t,t.on("mousedown",this._dragStart,this),t.on("mousemove",this._drag,this),t.on("mouseup",this._dragEnd,this)}return t.prototype._dragStart=function(t){for(var e=t.target;e&&!e.draggable;)e=e.parent||e.__hostTarget;e&&(this._draggingTarget=e,e.dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.handler.dispatchToElement(new Ht(e,t),"dragstart",t.event))},t.prototype._drag=function(t){var e=this._draggingTarget;if(e){var n=t.offsetX,i=t.offsetY,r=n-this._x,o=i-this._y;this._x=n,this._y=i,e.drift(r,o,t),this.handler.dispatchToElement(new Ht(e,t),"drag",t.event);var a=this.handler.findHover(n,i,e).target,s=this._dropTarget;this._dropTarget=a,e!==a&&(s&&a!==s&&this.handler.dispatchToElement(new Ht(s,t),"dragleave",t.event),a&&a!==s&&this.handler.dispatchToElement(new Ht(a,t),"dragenter",t.event))}},t.prototype._dragEnd=function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.handler.dispatchToElement(new Ht(e,t),"dragend",t.event),this._dropTarget&&this.handler.dispatchToElement(new Ht(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null},t}(),Ut=function(){function t(t){t&&(this._$eventProcessor=t)}return t.prototype.on=function(t,e,n,i){this._$handlers||(this._$handlers={});var r=this._$handlers;if("function"==typeof e&&(i=n,n=e,e=null),!n||!t)return this;var o=this._$eventProcessor;null!=e&&o&&o.normalizeQuery&&(e=o.normalizeQuery(e)),r[t]||(r[t]=[]);for(var a=0;a<r[t].length;a++)if(r[t][a].h===n)return this;var s={h:n,query:e,ctx:i||this,callAtLast:n.zrEventfulCallAtLast},l=r[t].length-1,u=r[t][l];return u&&u.callAtLast?r[t].splice(l,0,s):r[t].push(s),this},t.prototype.isSilent=function(t){var e=this._$handlers;return!e||!e[t]||!e[t].length},t.prototype.off=function(t,e){var n=this._$handlers;if(!n)return this;if(!t)return this._$handlers={},this;if(e){if(n[t]){for(var i=[],r=0,o=n[t].length;r<o;r++)n[t][r].h!==e&&i.push(n[t][r]);n[t]=i}n[t]&&0===n[t].length&&delete n[t]}else delete n[t];return this},t.prototype.trigger=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];if(!this._$handlers)return this;var i=this._$handlers[t],r=this._$eventProcessor;if(i)for(var o=e.length,a=i.length,s=0;s<a;s++){var l=i[s];if(!r||!r.filter||null==l.query||r.filter(t,l.query))switch(o){case 0:l.h.call(l.ctx);break;case 1:l.h.call(l.ctx,e[0]);break;case 2:l.h.call(l.ctx,e[0],e[1]);break;default:l.h.apply(l.ctx,e)}}return r&&r.afterTrigger&&r.afterTrigger(t),this},t.prototype.triggerWithContext=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];if(!this._$handlers)return this;var i=this._$handlers[t],r=this._$eventProcessor;if(i)for(var o=e.length,a=e[o-1],s=i.length,l=0;l<s;l++){var u=i[l];if(!r||!r.filter||null==u.query||r.filter(t,u.query))switch(o){case 0:u.h.call(a);break;case 1:u.h.call(a,e[0]);break;case 2:u.h.call(a,e[0],e[1]);break;default:u.h.apply(a,e.slice(1,o-1))}}return r&&r.afterTrigger&&r.afterTrigger(t),this},t}(),Xt=Math.log(2);function Yt(t,e,n,i,r,o){var a=i+"-"+r,s=t.length;if(o.hasOwnProperty(a))return o[a];if(1===e){var l=Math.round(Math.log((1<<s)-1&~r)/Xt);return t[n][l]}for(var u=i|1<<n,h=n+1;i&1<<h;)h++;for(var c=0,p=0,f=0;p<s;p++){var d=1<<p;d&r||(c+=(f%2?-1:1)*t[n][p]*Yt(t,e-1,h,u,r|d,o),f++)}return o[a]=c,c}function qt(t,e){var n=[[t[0],t[1],1,0,0,0,-e[0]*t[0],-e[0]*t[1]],[0,0,0,t[0],t[1],1,-e[1]*t[0],-e[1]*t[1]],[t[2],t[3],1,0,0,0,-e[2]*t[2],-e[2]*t[3]],[0,0,0,t[2],t[3],1,-e[3]*t[2],-e[3]*t[3]],[t[4],t[5],1,0,0,0,-e[4]*t[4],-e[4]*t[5]],[0,0,0,t[4],t[5],1,-e[5]*t[4],-e[5]*t[5]],[t[6],t[7],1,0,0,0,-e[6]*t[6],-e[6]*t[7]],[0,0,0,t[6],t[7],1,-e[7]*t[6],-e[7]*t[7]]],i={},r=Yt(n,8,0,0,0,i);if(0!==r){for(var o=[],a=0;a<8;a++)for(var s=0;s<8;s++)null==o[s]&&(o[s]=0),o[s]+=((a+s)%2?-1:1)*Yt(n,7,0===a?1:0,1<<a,1<<s,i)/r*e[a];return function(t,e,n){var i=e*o[6]+n*o[7]+1;t[0]=(e*o[0]+n*o[1]+o[2])/i,t[1]=(e*o[3]+n*o[4]+o[5])/i}}}var Zt="___zrEVENTSAVED";function jt(t,e,n,i,o){if(e.getBoundingClientRect&&r.domSupported&&!Kt(e)){var a=e[Zt]||(e[Zt]={}),s=function(t,e){var n=e.markers;if(n)return n;n=e.markers=[];for(var i=["left","right"],r=["top","bottom"],o=0;o<4;o++){var a=document.createElement("div"),s=o%2,l=(o>>1)%2;a.style.cssText=["position: absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","user-select: none","width:0","height:0",i[s]+":0",r[l]+":0",i[1-s]+":auto",r[1-l]+":auto",""].join("!important;"),t.appendChild(a),n.push(a)}return e.clearMarkers=function(){R(n,(function(t){t.parentNode&&t.parentNode.removeChild(t)}))},n}(e,a),l=function(t,e,n){for(var i=n?"invTrans":"trans",r=e[i],o=e.srcCoords,a=[],s=[],l=!0,u=0;u<4;u++){var h=t[u].getBoundingClientRect(),c=2*u,p=h.left,f=h.top;a.push(p,f),l=l&&o&&p===o[c]&&f===o[c+1],s.push(t[u].offsetLeft,t[u].offsetTop)}return l&&r?r:(e.srcCoords=a,e[i]=n?qt(s,a):qt(a,s))}(s,a,o);if(l)return l(t,n,i),!0}return!1}function Kt(t){return"CANVAS"===t.nodeName.toUpperCase()}var $t=/([&<>"'])/g,Qt={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function Jt(t){return null==t?"":(t+"").replace($t,(function(t,e){return Qt[e]}))}var te=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,ee=[],ne=r.browser.firefox&&+r.browser.version.split(".")[0]<39;function ie(t,e,n,i){return n=n||{},i?re(t,e,n):ne&&null!=e.layerX&&e.layerX!==e.offsetX?(n.zrX=e.layerX,n.zrY=e.layerY):null!=e.offsetX?(n.zrX=e.offsetX,n.zrY=e.offsetY):re(t,e,n),n}function re(t,e,n){if(r.domSupported&&t.getBoundingClientRect){var i=e.clientX,o=e.clientY;if(Kt(t)){var a=t.getBoundingClientRect();return n.zrX=i-a.left,void(n.zrY=o-a.top)}if(jt(ee,t,i,o))return n.zrX=ee[0],void(n.zrY=ee[1])}n.zrX=n.zrY=0}function oe(t){return t||window.event}function ae(t,e,n){if(null!=(e=oe(e)).zrX)return e;var i=e.type;if(i&&i.indexOf("touch")>=0){var r="touchend"!==i?e.targetTouches[0]:e.changedTouches[0];r&&ie(t,r,e,n)}else{ie(t,e,e,n);var o=function(t){var e=t.wheelDelta;if(e)return e;var n=t.deltaX,i=t.deltaY;if(null==n||null==i)return e;return 3*(0!==i?Math.abs(i):Math.abs(n))*(i>0?-1:i<0?1:n>0?-1:1)}(e);e.zrDelta=o?o/120:-(e.detail||0)/3}var a=e.button;return null==e.which&&void 0!==a&&te.test(e.type)&&(e.which=1&a?1:2&a?3:4&a?2:0),e}var se=function(){function t(){this._track=[]}return t.prototype.recognize=function(t,e,n){return this._doTrack(t,e,n),this._recognize(t)},t.prototype.clear=function(){return this._track.length=0,this},t.prototype._doTrack=function(t,e,n){var i=t.touches;if(i){for(var r={points:[],touches:[],target:e,event:t},o=0,a=i.length;o<a;o++){var s=i[o],l=ie(n,s,{});r.points.push([l.zrX,l.zrY]),r.touches.push(s)}this._track.push(r)}},t.prototype._recognize=function(t){for(var e in ue)if(ue.hasOwnProperty(e)){var n=ue[e](this._track,t);if(n)return n}},t}();function le(t){var e=t[1][0]-t[0][0],n=t[1][1]-t[0][1];return Math.sqrt(e*e+n*n)}var ue={pinch:function(t,e){var n=t.length;if(n){var i,r=(t[n-1]||{}).points,o=(t[n-2]||{}).points||r;if(o&&o.length>1&&r&&r.length>1){var a=le(r)/le(o);!isFinite(a)&&(a=1),e.pinchScale=a;var s=[((i=r)[0][0]+i[1][0])/2,(i[0][1]+i[1][1])/2];return e.pinchX=s[0],e.pinchY=s[1],{type:"pinch",target:t[0].target,event:e}}}}};function he(){return[1,0,0,1,0,0]}function ce(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t}function pe(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t}function fe(t,e,n){var i=e[0]*n[0]+e[2]*n[1],r=e[1]*n[0]+e[3]*n[1],o=e[0]*n[2]+e[2]*n[3],a=e[1]*n[2]+e[3]*n[3],s=e[0]*n[4]+e[2]*n[5]+e[4],l=e[1]*n[4]+e[3]*n[5]+e[5];return t[0]=i,t[1]=r,t[2]=o,t[3]=a,t[4]=s,t[5]=l,t}function de(t,e,n){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+n[0],t[5]=e[5]+n[1],t}function ge(t,e,n,i){void 0===i&&(i=[0,0]);var r=e[0],o=e[2],a=e[4],s=e[1],l=e[3],u=e[5],h=Math.sin(n),c=Math.cos(n);return t[0]=r*c+s*h,t[1]=-r*h+s*c,t[2]=o*c+l*h,t[3]=-o*h+c*l,t[4]=c*(a-i[0])+h*(u-i[1])+i[0],t[5]=c*(u-i[1])-h*(a-i[0])+i[1],t}function ve(t,e,n){var i=n[0],r=n[1];return t[0]=e[0]*i,t[1]=e[1]*r,t[2]=e[2]*i,t[3]=e[3]*r,t[4]=e[4]*i,t[5]=e[5]*r,t}function ye(t,e){var n=e[0],i=e[2],r=e[4],o=e[1],a=e[3],s=e[5],l=n*a-o*i;return l?(l=1/l,t[0]=a*l,t[1]=-o*l,t[2]=-i*l,t[3]=n*l,t[4]=(i*s-a*r)*l,t[5]=(o*r-n*s)*l,t):null}var me=Object.freeze({__proto__:null,create:he,identity:ce,copy:pe,mul:fe,translate:de,rotate:ge,scale:ve,invert:ye,clone:function(t){var e=[1,0,0,1,0,0];return pe(e,t),e}}),_e=function(){function t(t,e){this.x=t||0,this.y=e||0}return t.prototype.copy=function(t){return this.x=t.x,this.y=t.y,this},t.prototype.clone=function(){return new t(this.x,this.y)},t.prototype.set=function(t,e){return this.x=t,this.y=e,this},t.prototype.equal=function(t){return t.x===this.x&&t.y===this.y},t.prototype.add=function(t){return this.x+=t.x,this.y+=t.y,this},t.prototype.scale=function(t){this.x*=t,this.y*=t},t.prototype.scaleAndAdd=function(t,e){this.x+=t.x*e,this.y+=t.y*e},t.prototype.sub=function(t){return this.x-=t.x,this.y-=t.y,this},t.prototype.dot=function(t){return this.x*t.x+this.y*t.y},t.prototype.len=function(){return Math.sqrt(this.x*this.x+this.y*this.y)},t.prototype.lenSquare=function(){return this.x*this.x+this.y*this.y},t.prototype.normalize=function(){var t=this.len();return this.x/=t,this.y/=t,this},t.prototype.distance=function(t){var e=this.x-t.x,n=this.y-t.y;return Math.sqrt(e*e+n*n)},t.prototype.distanceSquare=function(t){var e=this.x-t.x,n=this.y-t.y;return e*e+n*n},t.prototype.negate=function(){return this.x=-this.x,this.y=-this.y,this},t.prototype.transform=function(t){if(t){var e=this.x,n=this.y;return this.x=t[0]*e+t[2]*n+t[4],this.y=t[1]*e+t[3]*n+t[5],this}},t.prototype.toArray=function(t){return t[0]=this.x,t[1]=this.y,t},t.prototype.fromArray=function(t){this.x=t[0],this.y=t[1]},t.set=function(t,e,n){t.x=e,t.y=n},t.copy=function(t,e){t.x=e.x,t.y=e.y},t.len=function(t){return Math.sqrt(t.x*t.x+t.y*t.y)},t.lenSquare=function(t){return t.x*t.x+t.y*t.y},t.dot=function(t,e){return t.x*e.x+t.y*e.y},t.add=function(t,e,n){t.x=e.x+n.x,t.y=e.y+n.y},t.sub=function(t,e,n){t.x=e.x-n.x,t.y=e.y-n.y},t.scale=function(t,e,n){t.x=e.x*n,t.y=e.y*n},t.scaleAndAdd=function(t,e,n,i){t.x=e.x+n.x*i,t.y=e.y+n.y*i},t.lerp=function(t,e,n,i){var r=1-i;t.x=r*e.x+i*n.x,t.y=r*e.y+i*n.y},t}(),xe=Math.min,be=Math.max,we=Math.abs,Se=["x","y"],Me=["width","height"],Te=new _e,ke=new _e,Ce=new _e,De=new _e,Ie=Ee(),Ae=Ie.minTv,Le=Ie.maxTv,Pe=[0,0],Oe=function(){function t(e,n,i,r){t.set(this,e,n,i,r)}return t.set=function(t,e,n,i,r){return i<0&&(e+=i,i=-i),r<0&&(n+=r,r=-r),t.x=e,t.y=n,t.width=i,t.height=r,t},t.prototype.union=function(t){var e=xe(t.x,this.x),n=xe(t.y,this.y);isFinite(this.x)&&isFinite(this.width)?this.width=be(t.x+t.width,this.x+this.width)-e:this.width=t.width,isFinite(this.y)&&isFinite(this.height)?this.height=be(t.y+t.height,this.y+this.height)-n:this.height=t.height,this.x=e,this.y=n},t.prototype.applyTransform=function(e){t.applyTransform(this,this,e)},t.prototype.calculateTransform=function(t){var e=this,n=t.width/e.width,i=t.height/e.height,r=[1,0,0,1,0,0];return de(r,r,[-e.x,-e.y]),ve(r,r,[n,i]),de(r,r,[t.x,t.y]),r},t.prototype.intersect=function(e,n,i){return t.intersect(this,e,n,i)},t.intersect=function(e,n,i,r){i&&_e.set(i,0,0);var o=r&&r.outIntersectRect||null,a=r&&r.clamp;if(o&&(o.x=o.y=o.width=o.height=NaN),!e||!n)return!1;e instanceof t||(e=t.set(Re,e.x,e.y,e.width,e.height)),n instanceof t||(n=t.set(Ne,n.x,n.y,n.width,n.height));var s=!!i;Ie.reset(r,s);var l=Ie.touchThreshold,u=e.x+l,h=e.x+e.width-l,c=e.y+l,p=e.y+e.height-l,f=n.x+l,d=n.x+n.width-l,g=n.y+l,v=n.y+n.height-l;if(u>h||c>p||f>d||g>v)return!1;var y=!(h<f||d<u||p<g||v<c);return(s||o)&&(Pe[0]=1/0,Pe[1]=0,Be(u,h,f,d,0,s,o,a),Be(c,p,g,v,1,s,o,a),s&&_e.copy(i,y?Ie.useDir?Ie.dirMinTv:Ae:Le)),y},t.contain=function(t,e,n){return e>=t.x&&e<=t.x+t.width&&n>=t.y&&n<=t.y+t.height},t.prototype.contain=function(e,n){return t.contain(this,e,n)},t.prototype.clone=function(){return new t(this.x,this.y,this.width,this.height)},t.prototype.copy=function(e){t.copy(this,e)},t.prototype.plain=function(){return{x:this.x,y:this.y,width:this.width,height:this.height}},t.prototype.isFinite=function(){return isFinite(this.x)&&isFinite(this.y)&&isFinite(this.width)&&isFinite(this.height)},t.prototype.isZero=function(){return 0===this.width||0===this.height},t.create=function(e){return new t(e.x,e.y,e.width,e.height)},t.copy=function(t,e){return t.x=e.x,t.y=e.y,t.width=e.width,t.height=e.height,t},t.applyTransform=function(e,n,i){if(i){if(i[1]<1e-5&&i[1]>-1e-5&&i[2]<1e-5&&i[2]>-1e-5){var r=i[0],o=i[3],a=i[4],s=i[5];return e.x=n.x*r+a,e.y=n.y*o+s,e.width=n.width*r,e.height=n.height*o,e.width<0&&(e.x+=e.width,e.width=-e.width),void(e.height<0&&(e.y+=e.height,e.height=-e.height))}Te.x=Ce.x=n.x,Te.y=De.y=n.y,ke.x=De.x=n.x+n.width,ke.y=Ce.y=n.y+n.height,Te.transform(i),De.transform(i),ke.transform(i),Ce.transform(i),e.x=xe(Te.x,ke.x,Ce.x,De.x),e.y=xe(Te.y,ke.y,Ce.y,De.y);var l=be(Te.x,ke.x,Ce.x,De.x),u=be(Te.y,ke.y,Ce.y,De.y);e.width=l-e.x,e.height=u-e.y}else e!==n&&t.copy(e,n)},t}(),Re=new Oe(0,0,0,0),Ne=new Oe(0,0,0,0);function Be(t,e,n,i,r,o,a,s){var l=we(e-n),u=we(i-t),h=xe(l,u),c=Se[r],p=Se[1-r],f=Me[r];e<n||i<t?l<u?(o&&(Le[c]=-l),s&&(a[c]=e,a[f]=0)):(o&&(Le[c]=u),s&&(a[c]=t,a[f]=0)):(a&&(a[c]=be(t,n),a[f]=xe(e,i)-a[c]),o&&(h<Pe[0]||Ie.useDir)&&(Pe[0]=xe(h,Pe[0]),(l<u||!Ie.bidirectional)&&(Ae[c]=l,Ae[p]=0,Ie.useDir&&Ie.calcDirMTV()),(l>=u||!Ie.bidirectional)&&(Ae[c]=-u,Ae[p]=0,Ie.useDir&&Ie.calcDirMTV())))}function Ee(){var t=0,e=new _e,n=new _e,i={minTv:new _e,maxTv:new _e,useDir:!1,dirMinTv:new _e,touchThreshold:0,bidirectional:!0,negativeSize:!1,reset:function(r,o){i.touchThreshold=0,r&&null!=r.touchThreshold&&(i.touchThreshold=be(0,r.touchThreshold)),i.negativeSize=!1,o&&(i.minTv.set(1/0,1/0),i.maxTv.set(0,0),i.useDir=!1,r&&null!=r.direction&&(i.useDir=!0,i.dirMinTv.copy(i.minTv),n.copy(i.minTv),t=r.direction,i.bidirectional=null==r.bidirectional||!!r.bidirectional,i.bidirectional||e.set(Math.cos(t),Math.sin(t))))},calcDirMTV:function(){var o=i.minTv,a=i.dirMinTv,s=o.y*o.y+o.x*o.x,l=Math.sin(t),u=Math.cos(t),h=l*o.y+u*o.x;r(h)?r(o.x)&&r(o.y)&&a.set(0,0):(n.x=s*u/h,n.y=s*l/h,r(n.x)&&r(n.y)?a.set(0,0):(i.bidirectional||e.dot(n)>0)&&n.len()<a.len()&&a.copy(n))}};function r(t){return we(t)<1e-10}return i}var ze="silent";function Fe(){!function(t){t.preventDefault(),t.stopPropagation(),t.cancelBubble=!0}(this.event)}var Ve=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.handler=null,e}return n(e,t),e.prototype.dispose=function(){},e.prototype.setCursor=function(){},e}(Ut),We=function(t,e){this.x=t,this.y=e},He=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],Ge=new Oe(0,0,0,0),Ue=function(t){function e(e,n,i,r,o){var a=t.call(this)||this;return a._hovered=new We(0,0),a.storage=e,a.painter=n,a.painterRoot=r,a._pointerSize=o,i=i||new Ve,a.proxy=null,a.setHandlerProxy(i),a._draggingMgr=new Gt(a),a}return n(e,t),e.prototype.setHandlerProxy=function(t){this.proxy&&this.proxy.dispose(),t&&(R(He,(function(e){t.on&&t.on(e,this[e],this)}),this),t.handler=this),this.proxy=t},e.prototype.mousemove=function(t){var e=t.zrX,n=t.zrY,i=qe(this,e,n),r=this._hovered,o=r.target;o&&!o.__zr&&(o=(r=this.findHover(r.x,r.y)).target);var a=this._hovered=i?new We(e,n):this.findHover(e,n),s=a.target,l=this.proxy;l.setCursor&&l.setCursor(s?s.cursor:"default"),o&&s!==o&&this.dispatchToElement(r,"mouseout",t),this.dispatchToElement(a,"mousemove",t),s&&s!==o&&this.dispatchToElement(a,"mouseover",t)},e.prototype.mouseout=function(t){var e=t.zrEventControl;"only_globalout"!==e&&this.dispatchToElement(this._hovered,"mouseout",t),"no_globalout"!==e&&this.trigger("globalout",{type:"globalout",event:t})},e.prototype.resize=function(){this._hovered=new We(0,0)},e.prototype.dispatch=function(t,e){var n=this[t];n&&n.call(this,e)},e.prototype.dispose=function(){this.proxy.dispose(),this.storage=null,this.proxy=null,this.painter=null},e.prototype.setCursorStyle=function(t){var e=this.proxy;e.setCursor&&e.setCursor(t)},e.prototype.dispatchToElement=function(t,e,n){var i=(t=t||{}).target;if(!i||!i.silent){for(var r="on"+e,o=function(t,e,n){return{type:t,event:n,target:e.target,topTarget:e.topTarget,cancelBubble:!1,offsetX:n.zrX,offsetY:n.zrY,gestureEvent:n.gestureEvent,pinchX:n.pinchX,pinchY:n.pinchY,pinchScale:n.pinchScale,wheelDelta:n.zrDelta,zrByTouch:n.zrByTouch,which:n.which,stop:Fe}}(e,t,n);i&&(i[r]&&(o.cancelBubble=!!i[r].call(i,o)),i.trigger(e,o),i=i.__hostTarget?i.__hostTarget:i.parent,!o.cancelBubble););o.cancelBubble||(this.trigger(e,o),this.painter&&this.painter.eachOtherLayer&&this.painter.eachOtherLayer((function(t){"function"==typeof t[r]&&t[r].call(t,o),t.trigger&&t.trigger(e,o)})))}},e.prototype.findHover=function(t,e,n){var i=this.storage.getDisplayList(),r=new We(t,e);if(Ye(i,r,t,e,n),this._pointerSize&&!r.target){for(var o=[],a=this._pointerSize,s=a/2,l=new Oe(t-s,e-s,a,a),u=i.length-1;u>=0;u--){var h=i[u];h===n||h.ignore||h.ignoreCoarsePointer||h.parent&&h.parent.ignoreCoarsePointer||(Ge.copy(h.getBoundingRect()),h.transform&&Ge.applyTransform(h.transform),Ge.intersect(l)&&o.push(h))}if(o.length)for(var c=Math.PI/12,p=2*Math.PI,f=0;f<s;f+=4)for(var d=0;d<p;d+=c){if(Ye(o,r,t+f*Math.cos(d),e+f*Math.sin(d),n),r.target)return r}}return r},e.prototype.processGesture=function(t,e){this._gestureMgr||(this._gestureMgr=new se);var n=this._gestureMgr;"start"===e&&n.clear();var i=n.recognize(t,this.findHover(t.zrX,t.zrY,null).target,this.proxy.dom);if("end"===e&&n.clear(),i){var r=i.type;t.gestureEvent=r;var o=new We;o.target=i.target,this.dispatchToElement(o,r,i.event)}},e}(Ut);function Xe(t,e,n){if(t[t.rectHover?"rectContain":"contain"](e,n)){for(var i=t,r=void 0,o=!1;i;){if(i.ignoreClip&&(o=!0),!o){var a=i.getClipPath();if(a&&!a.contain(e,n))return!1}i.silent&&(r=!0);var s=i.__hostTarget;i=s?i.ignoreHostSilent?null:s:i.parent}return!r||ze}return!1}function Ye(t,e,n,i,r){for(var o=t.length-1;o>=0;o--){var a=t[o],s=void 0;if(a!==r&&!a.ignore&&(s=Xe(a,n,i))&&(!e.topTarget&&(e.topTarget=a),s!==ze)){e.target=a;break}}}function qe(t,e,n){var i=t.painter;return e<0||e>i.getWidth()||n<0||n>i.getHeight()}R(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],(function(t){Ue.prototype[t]=function(e){var n,i,r=e.zrX,o=e.zrY,a=qe(this,r,o);if("mouseup"===t&&a||(i=(n=this.findHover(r,o)).target),"mousedown"===t)this._downEl=i,this._downPoint=[e.zrX,e.zrY],this._upEl=i;else if("mouseup"===t)this._upEl=i;else if("click"===t){if(this._downEl!==this._upEl||!this._downPoint||Rt(this._downPoint,[e.zrX,e.zrY])>4)return;this._downPoint=null}this.dispatchToElement(n,t,e)}}));function Ze(t,e,n,i){var r=e+1;if(r===n)return 1;if(i(t[r++],t[e])<0){for(;r<n&&i(t[r],t[r-1])<0;)r++;!function(t,e,n){n--;for(;e<n;){var i=t[e];t[e++]=t[n],t[n--]=i}}(t,e,r)}else for(;r<n&&i(t[r],t[r-1])>=0;)r++;return r-e}function je(t,e,n,i,r){for(i===e&&i++;i<n;i++){for(var o,a=t[i],s=e,l=i;s<l;)r(a,t[o=s+l>>>1])<0?l=o:s=o+1;var u=i-s;switch(u){case 3:t[s+3]=t[s+2];case 2:t[s+2]=t[s+1];case 1:t[s+1]=t[s];break;default:for(;u>0;)t[s+u]=t[s+u-1],u--}t[s]=a}}function Ke(t,e,n,i,r,o){var a=0,s=0,l=1;if(o(t,e[n+r])>0){for(s=i-r;l<s&&o(t,e[n+r+l])>0;)a=l,(l=1+(l<<1))<=0&&(l=s);l>s&&(l=s),a+=r,l+=r}else{for(s=r+1;l<s&&o(t,e[n+r-l])<=0;)a=l,(l=1+(l<<1))<=0&&(l=s);l>s&&(l=s);var u=a;a=r-l,l=r-u}for(a++;a<l;){var h=a+(l-a>>>1);o(t,e[n+h])>0?a=h+1:l=h}return l}function $e(t,e,n,i,r,o){var a=0,s=0,l=1;if(o(t,e[n+r])<0){for(s=r+1;l<s&&o(t,e[n+r-l])<0;)a=l,(l=1+(l<<1))<=0&&(l=s);l>s&&(l=s);var u=a;a=r-l,l=r-u}else{for(s=i-r;l<s&&o(t,e[n+r+l])>=0;)a=l,(l=1+(l<<1))<=0&&(l=s);l>s&&(l=s),a+=r,l+=r}for(a++;a<l;){var h=a+(l-a>>>1);o(t,e[n+h])<0?l=h:a=h+1}return l}function Qe(t,e){var n,i,r=7,o=0,a=[];function s(s){var l=n[s],u=i[s],h=n[s+1],c=i[s+1];i[s]=u+c,s===o-3&&(n[s+1]=n[s+2],i[s+1]=i[s+2]),o--;var p=$e(t[h],t,l,u,0,e);l+=p,0!==(u-=p)&&0!==(c=Ke(t[l+u-1],t,h,c,c-1,e))&&(u<=c?function(n,i,o,s){var l=0;for(l=0;l<i;l++)a[l]=t[n+l];var u=0,h=o,c=n;if(t[c++]=t[h++],0==--s){for(l=0;l<i;l++)t[c+l]=a[u+l];return}if(1===i){for(l=0;l<s;l++)t[c+l]=t[h+l];return void(t[c+s]=a[u])}var p,f,d,g=r;for(;;){p=0,f=0,d=!1;do{if(e(t[h],a[u])<0){if(t[c++]=t[h++],f++,p=0,0==--s){d=!0;break}}else if(t[c++]=a[u++],p++,f=0,1==--i){d=!0;break}}while((p|f)<g);if(d)break;do{if(0!==(p=$e(t[h],a,u,i,0,e))){for(l=0;l<p;l++)t[c+l]=a[u+l];if(c+=p,u+=p,(i-=p)<=1){d=!0;break}}if(t[c++]=t[h++],0==--s){d=!0;break}if(0!==(f=Ke(a[u],t,h,s,0,e))){for(l=0;l<f;l++)t[c+l]=t[h+l];if(c+=f,h+=f,0===(s-=f)){d=!0;break}}if(t[c++]=a[u++],1==--i){d=!0;break}g--}while(p>=7||f>=7);if(d)break;g<0&&(g=0),g+=2}if((r=g)<1&&(r=1),1===i){for(l=0;l<s;l++)t[c+l]=t[h+l];t[c+s]=a[u]}else{if(0===i)throw new Error;for(l=0;l<i;l++)t[c+l]=a[u+l]}}(l,u,h,c):function(n,i,o,s){var l=0;for(l=0;l<s;l++)a[l]=t[o+l];var u=n+i-1,h=s-1,c=o+s-1,p=0,f=0;if(t[c--]=t[u--],0==--i){for(p=c-(s-1),l=0;l<s;l++)t[p+l]=a[l];return}if(1===s){for(f=(c-=i)+1,p=(u-=i)+1,l=i-1;l>=0;l--)t[f+l]=t[p+l];return void(t[c]=a[h])}var d=r;for(;;){var g=0,v=0,y=!1;do{if(e(a[h],t[u])<0){if(t[c--]=t[u--],g++,v=0,0==--i){y=!0;break}}else if(t[c--]=a[h--],v++,g=0,1==--s){y=!0;break}}while((g|v)<d);if(y)break;do{if(0!==(g=i-$e(a[h],t,n,i,i-1,e))){for(i-=g,f=(c-=g)+1,p=(u-=g)+1,l=g-1;l>=0;l--)t[f+l]=t[p+l];if(0===i){y=!0;break}}if(t[c--]=a[h--],1==--s){y=!0;break}if(0!==(v=s-Ke(t[u],a,0,s,s-1,e))){for(s-=v,f=(c-=v)+1,p=(h-=v)+1,l=0;l<v;l++)t[f+l]=a[p+l];if(s<=1){y=!0;break}}if(t[c--]=t[u--],0==--i){y=!0;break}d--}while(g>=7||v>=7);if(y)break;d<0&&(d=0),d+=2}(r=d)<1&&(r=1);if(1===s){for(f=(c-=i)+1,p=(u-=i)+1,l=i-1;l>=0;l--)t[f+l]=t[p+l];t[c]=a[h]}else{if(0===s)throw new Error;for(p=c-(s-1),l=0;l<s;l++)t[p+l]=a[l]}}(l,u,h,c))}return n=[],i=[],{mergeRuns:function(){for(;o>1;){var t=o-2;if(t>=1&&i[t-1]<=i[t]+i[t+1]||t>=2&&i[t-2]<=i[t]+i[t-1])i[t-1]<i[t+1]&&t--;else if(i[t]>i[t+1])break;s(t)}},forceMergeRuns:function(){for(;o>1;){var t=o-2;t>0&&i[t-1]<i[t+1]&&t--,s(t)}},pushRun:function(t,e){n[o]=t,i[o]=e,o+=1}}}function Je(t,e,n,i){n||(n=0),i||(i=t.length);var r=i-n;if(!(r<2)){var o=0;if(r<32)je(t,n,i,n+(o=Ze(t,n,i,e)),e);else{var a=Qe(t,e),s=function(t){for(var e=0;t>=32;)e|=1&t,t>>=1;return t+e}(r);do{if((o=Ze(t,n,i,e))<s){var l=r;l>s&&(l=s),je(t,n,n+l,n+o,e),o=l}a.pushRun(n,o),a.mergeRuns(),r-=o,n+=o}while(0!==r);a.forceMergeRuns()}}}var tn=!1;function en(){tn||(tn=!0,console.warn("z / z2 / zlevel of displayable is invalid, which may cause unexpected errors"))}function nn(t,e){return t.zlevel===e.zlevel?t.z===e.z?t.z2-e.z2:t.z-e.z:t.zlevel-e.zlevel}var rn=function(){function t(){this._roots=[],this._displayList=[],this._displayListLen=0,this.displayableSortFunc=nn}return t.prototype.traverse=function(t,e){for(var n=0;n<this._roots.length;n++)this._roots[n].traverse(t,e)},t.prototype.getDisplayList=function(t,e){e=e||!1;var n=this._displayList;return!t&&n.length||this.updateDisplayList(e),n},t.prototype.updateDisplayList=function(t){this._displayListLen=0;for(var e=this._roots,n=this._displayList,i=0,r=e.length;i<r;i++)this._updateAndAddDisplayable(e[i],null,t);n.length=this._displayListLen,Je(n,nn)},t.prototype._updateAndAddDisplayable=function(t,e,n){if(!t.ignore||n){t.beforeUpdate(),t.update(),t.afterUpdate();var i=t.getClipPath(),r=e&&e.length,o=0,a=t.__clipPaths;if(!t.ignoreClip&&(r||i)){if(a||(a=t.__clipPaths=[]),r)for(var s=0;s<e.length;s++)a[o++]=e[s];for(var l=i,u=t;l;)l.parent=u,l.updateTransform(),a[o++]=l,u=l,l=l.getClipPath()}if(a&&(a.length=o),t.childrenRef){for(var h=t.childrenRef(),c=0;c<h.length;c++){var p=h[c];t.__dirty&&(p.__dirty|=1),this._updateAndAddDisplayable(p,a,n)}t.__dirty=0}else{var f=t;isNaN(f.z)&&(en(),f.z=0),isNaN(f.z2)&&(en(),f.z2=0),isNaN(f.zlevel)&&(en(),f.zlevel=0),this._displayList[this._displayListLen++]=f}var d=t.getDecalElement&&t.getDecalElement();d&&this._updateAndAddDisplayable(d,a,n);var g=t.getTextGuideLine();g&&this._updateAndAddDisplayable(g,a,n);var v=t.getTextContent();v&&this._updateAndAddDisplayable(v,a,n)}},t.prototype.addRoot=function(t){t.__zr&&t.__zr.storage===this||this._roots.push(t)},t.prototype.delRoot=function(t){if(t instanceof Array)for(var e=0,n=t.length;e<n;e++)this.delRoot(t[e]);else{var i=A(this._roots,t);i>=0&&this._roots.splice(i,1)}},t.prototype.delAllRoots=function(){this._roots=[],this._displayList=[],this._displayListLen=0},t.prototype.getRoots=function(){return this._roots},t.prototype.dispose=function(){this._displayList=null,this._roots=null},t}(),on=r.hasGlobalWindow&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(t){return setTimeout(t,16)},an={linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return.5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(2-Math.pow(2,-10*(t-1)))},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,n=.1;return 0===t?0:1===t?1:(!n||n<1?(n=1,e=.1):e=.4*Math.asin(1/n)/(2*Math.PI),-n*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/.4))},elasticOut:function(t){var e,n=.1;return 0===t?0:1===t?1:(!n||n<1?(n=1,e=.1):e=.4*Math.asin(1/n)/(2*Math.PI),n*Math.pow(2,-10*t)*Math.sin((t-e)*(2*Math.PI)/.4)+1)},elasticInOut:function(t){var e,n=.1,i=.4;return 0===t?0:1===t?1:(!n||n<1?(n=1,e=.1):e=i*Math.asin(1/n)/(2*Math.PI),(t*=2)<1?n*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/i)*-.5:n*Math.pow(2,-10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/i)*.5+1)},backIn:function(t){var e=1.70158;return t*t*((e+1)*t-e)},backOut:function(t){var e=1.70158;return--t*t*((e+1)*t+e)+1},backInOut:function(t){var e=2.5949095;return(t*=2)<1?t*t*((e+1)*t-e)*.5:.5*((t-=2)*t*((e+1)*t+e)+2)},bounceIn:function(t){return 1-an.bounceOut(1-t)},bounceOut:function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(t){return t<.5?.5*an.bounceIn(2*t):.5*an.bounceOut(2*t-1)+.5}},sn=Math.pow,ln=Math.sqrt,un=1e-8,hn=1e-4,cn=ln(3),pn=1/3,fn=St(),dn=St(),gn=St();function vn(t){return t>-1e-8&&t<un}function yn(t){return t>un||t<-1e-8}function mn(t,e,n,i,r){var o=1-r;return o*o*(o*t+3*r*e)+r*r*(r*i+3*o*n)}function _n(t,e,n,i,r){var o=1-r;return 3*(((e-t)*o+2*(n-e)*r)*o+(i-n)*r*r)}function xn(t,e,n,i,r,o){var a=i+3*(e-n)-t,s=3*(n-2*e+t),l=3*(e-t),u=t-r,h=s*s-3*a*l,c=s*l-9*a*u,p=l*l-3*s*u,f=0;if(vn(h)&&vn(c)){if(vn(s))o[0]=0;else(M=-l/s)>=0&&M<=1&&(o[f++]=M)}else{var d=c*c-4*h*p;if(vn(d)){var g=c/h,v=-g/2;(M=-s/a+g)>=0&&M<=1&&(o[f++]=M),v>=0&&v<=1&&(o[f++]=v)}else if(d>0){var y=ln(d),m=h*s+1.5*a*(-c+y),_=h*s+1.5*a*(-c-y);(M=(-s-((m=m<0?-sn(-m,pn):sn(m,pn))+(_=_<0?-sn(-_,pn):sn(_,pn))))/(3*a))>=0&&M<=1&&(o[f++]=M)}else{var x=(2*h*s-3*a*c)/(2*ln(h*h*h)),b=Math.acos(x)/3,w=ln(h),S=Math.cos(b),M=(-s-2*w*S)/(3*a),T=(v=(-s+w*(S+cn*Math.sin(b)))/(3*a),(-s+w*(S-cn*Math.sin(b)))/(3*a));M>=0&&M<=1&&(o[f++]=M),v>=0&&v<=1&&(o[f++]=v),T>=0&&T<=1&&(o[f++]=T)}}return f}function bn(t,e,n,i,r){var o=6*n-12*e+6*t,a=9*e+3*i-3*t-9*n,s=3*e-3*t,l=0;if(vn(a)){if(yn(o))(h=-s/o)>=0&&h<=1&&(r[l++]=h)}else{var u=o*o-4*a*s;if(vn(u))r[0]=-o/(2*a);else if(u>0){var h,c=ln(u),p=(-o-c)/(2*a);(h=(-o+c)/(2*a))>=0&&h<=1&&(r[l++]=h),p>=0&&p<=1&&(r[l++]=p)}}return l}function wn(t,e,n,i,r,o){var a=(e-t)*r+t,s=(n-e)*r+e,l=(i-n)*r+n,u=(s-a)*r+a,h=(l-s)*r+s,c=(h-u)*r+u;o[0]=t,o[1]=a,o[2]=u,o[3]=c,o[4]=c,o[5]=h,o[6]=l,o[7]=i}function Sn(t,e,n,i,r,o,a,s,l){for(var u=t,h=e,c=0,p=1/l,f=1;f<=l;f++){var d=f*p,g=mn(t,n,r,a,d),v=mn(e,i,o,s,d),y=g-u,m=v-h;c+=Math.sqrt(y*y+m*m),u=g,h=v}return c}function Mn(t,e,n,i){var r=1-i;return r*(r*t+2*i*e)+i*i*n}function Tn(t,e,n,i){return 2*((1-i)*(e-t)+i*(n-e))}function kn(t,e,n){var i=t+n-2*e;return 0===i?.5:(t-e)/i}function Cn(t,e,n,i,r){var o=(e-t)*i+t,a=(n-e)*i+e,s=(a-o)*i+o;r[0]=t,r[1]=o,r[2]=s,r[3]=s,r[4]=a,r[5]=n}function Dn(t,e,n,i,r,o,a){for(var s=t,l=e,u=0,h=1/a,c=1;c<=a;c++){var p=c*h,f=Mn(t,n,r,p),d=Mn(e,i,o,p),g=f-s,v=d-l;u+=Math.sqrt(g*g+v*v),s=f,l=d}return u}var In=/cubic-bezier\(([0-9,\.e ]+)\)/;function An(t){var e=t&&In.exec(t);if(e){var n=e[1].split(","),i=+st(n[0]),r=+st(n[1]),o=+st(n[2]),a=+st(n[3]);if(isNaN(i+r+o+a))return;var s=[];return function(t){return t<=0?0:t>=1?1:xn(0,i,o,1,t,s)&&mn(0,r,a,1,s[0])}}}var Ln=function(){function t(t){this._inited=!1,this._startTime=0,this._pausedTime=0,this._paused=!1,this._life=t.life||1e3,this._delay=t.delay||0,this.loop=t.loop||!1,this.onframe=t.onframe||_t,this.ondestroy=t.ondestroy||_t,this.onrestart=t.onrestart||_t,t.easing&&this.setEasing(t.easing)}return t.prototype.step=function(t,e){if(this._inited||(this._startTime=t+this._delay,this._inited=!0),!this._paused){var n=this._life,i=t-this._startTime-this._pausedTime,r=i/n;r<0&&(r=0),r=Math.min(r,1);var o=this.easingFunc,a=o?o(r):r;if(this.onframe(a),1===r){if(!this.loop)return!0;var s=i%n;this._startTime=t-s,this._pausedTime=0,this.onrestart()}return!1}this._pausedTime+=e},t.prototype.pause=function(){this._paused=!0},t.prototype.resume=function(){this._paused=!1},t.prototype.setEasing=function(t){this.easing=t,this.easingFunc=G(t)?t:an[t]||An(t)},t}(),Pn=function(t){this.value=t},On=function(){function t(){this._len=0}return t.prototype.insert=function(t){var e=new Pn(t);return this.insertEntry(e),e},t.prototype.insertEntry=function(t){this.head?(this.tail.next=t,t.prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},t.prototype.remove=function(t){var e=t.prev,n=t.next;e?e.next=n:this.head=n,n?n.prev=e:this.tail=e,t.next=t.prev=null,this._len--},t.prototype.len=function(){return this._len},t.prototype.clear=function(){this.head=this.tail=null,this._len=0},t}(),Rn=function(){function t(t){this._list=new On,this._maxSize=10,this._map={},this._maxSize=t}return t.prototype.put=function(t,e){var n=this._list,i=this._map,r=null;if(null==i[t]){var o=n.len(),a=this._lastRemovedEntry;if(o>=this._maxSize&&o>0){var s=n.head;n.remove(s),delete i[s.key],r=s.value,this._lastRemovedEntry=s}a?a.value=e:a=new Pn(e),a.key=t,n.insertEntry(a),i[t]=a}return r},t.prototype.get=function(t){var e=this._map[t],n=this._list;if(null!=e)return e!==n.tail&&(n.remove(e),n.insertEntry(e)),e.value},t.prototype.clear=function(){this._list.clear(),this._map={}},t.prototype.len=function(){return this._list.len()},t}(),Nn={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function Bn(t){return(t=Math.round(t))<0?0:t>255?255:t}function En(t){return t<0?0:t>1?1:t}function zn(t){var e=t;return e.length&&"%"===e.charAt(e.length-1)?Bn(parseFloat(e)/100*255):Bn(parseInt(e,10))}function Fn(t){var e=t;return e.length&&"%"===e.charAt(e.length-1)?En(parseFloat(e)/100):En(parseFloat(e))}function Vn(t,e,n){return n<0?n+=1:n>1&&(n-=1),6*n<1?t+(e-t)*n*6:2*n<1?e:3*n<2?t+(e-t)*(2/3-n)*6:t}function Wn(t,e,n){return t+(e-t)*n}function Hn(t,e,n,i,r){return t[0]=e,t[1]=n,t[2]=i,t[3]=r,t}function Gn(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}var Un=new Rn(20),Xn=null;function Yn(t,e){Xn&&Gn(Xn,e),Xn=Un.put(t,Xn||e.slice())}function qn(t,e){if(t){e=e||[];var n=Un.get(t);if(n)return Gn(e,n);var i=(t+="").replace(/ /g,"").toLowerCase();if(i in Nn)return Gn(e,Nn[i]),Yn(t,e),e;var r,o=i.length;if("#"===i.charAt(0))return 4===o||5===o?(r=parseInt(i.slice(1,4),16))>=0&&r<=4095?(Hn(e,(3840&r)>>4|(3840&r)>>8,240&r|(240&r)>>4,15&r|(15&r)<<4,5===o?parseInt(i.slice(4),16)/15:1),Yn(t,e),e):void Hn(e,0,0,0,1):7===o||9===o?(r=parseInt(i.slice(1,7),16))>=0&&r<=16777215?(Hn(e,(16711680&r)>>16,(65280&r)>>8,255&r,9===o?parseInt(i.slice(7),16)/255:1),Yn(t,e),e):void Hn(e,0,0,0,1):void 0;var a=i.indexOf("("),s=i.indexOf(")");if(-1!==a&&s+1===o){var l=i.substr(0,a),u=i.substr(a+1,s-(a+1)).split(","),h=1;switch(l){case"rgba":if(4!==u.length)return 3===u.length?Hn(e,+u[0],+u[1],+u[2],1):Hn(e,0,0,0,1);h=Fn(u.pop());case"rgb":return u.length>=3?(Hn(e,zn(u[0]),zn(u[1]),zn(u[2]),3===u.length?h:Fn(u[3])),Yn(t,e),e):void Hn(e,0,0,0,1);case"hsla":return 4!==u.length?void Hn(e,0,0,0,1):(u[3]=Fn(u[3]),Zn(u,e),Yn(t,e),e);case"hsl":return 3!==u.length?void Hn(e,0,0,0,1):(Zn(u,e),Yn(t,e),e);default:return}}Hn(e,0,0,0,1)}}function Zn(t,e){var n=(parseFloat(t[0])%360+360)%360/360,i=Fn(t[1]),r=Fn(t[2]),o=r<=.5?r*(i+1):r+i-r*i,a=2*r-o;return Hn(e=e||[],Bn(255*Vn(a,o,n+1/3)),Bn(255*Vn(a,o,n)),Bn(255*Vn(a,o,n-1/3)),1),4===t.length&&(e[3]=t[3]),e}function jn(t,e){var n=qn(t);if(n){for(var i=0;i<3;i++)n[i]=e<0?n[i]*(1-e)|0:(255-n[i])*e+n[i]|0,n[i]>255?n[i]=255:n[i]<0&&(n[i]=0);return ei(n,4===n.length?"rgba":"rgb")}}function Kn(t,e,n){if(e&&e.length&&t>=0&&t<=1){n=n||[];var i=t*(e.length-1),r=Math.floor(i),o=Math.ceil(i),a=e[r],s=e[o],l=i-r;return n[0]=Bn(Wn(a[0],s[0],l)),n[1]=Bn(Wn(a[1],s[1],l)),n[2]=Bn(Wn(a[2],s[2],l)),n[3]=En(Wn(a[3],s[3],l)),n}}var $n=Kn;function Qn(t,e,n){if(e&&e.length&&t>=0&&t<=1){var i=t*(e.length-1),r=Math.floor(i),o=Math.ceil(i),a=qn(e[r]),s=qn(e[o]),l=i-r,u=ei([Bn(Wn(a[0],s[0],l)),Bn(Wn(a[1],s[1],l)),Bn(Wn(a[2],s[2],l)),En(Wn(a[3],s[3],l))],"rgba");return n?{color:u,leftIndex:r,rightIndex:o,value:i}:u}}var Jn=Qn;function ti(t,e,n,i){var r,o=qn(t);if(t)return o=function(t){if(t){var e,n,i=t[0]/255,r=t[1]/255,o=t[2]/255,a=Math.min(i,r,o),s=Math.max(i,r,o),l=s-a,u=(s+a)/2;if(0===l)e=0,n=0;else{n=u<.5?l/(s+a):l/(2-s-a);var h=((s-i)/6+l/2)/l,c=((s-r)/6+l/2)/l,p=((s-o)/6+l/2)/l;i===s?e=p-c:r===s?e=1/3+h-p:o===s&&(e=2/3+c-h),e<0&&(e+=1),e>1&&(e-=1)}var f=[360*e,n,u];return null!=t[3]&&f.push(t[3]),f}}(o),null!=e&&(o[0]=(r=G(e)?e(o[0]):e,(r=Math.round(r))<0?0:r>360?360:r)),null!=n&&(o[1]=Fn(G(n)?n(o[1]):n)),null!=i&&(o[2]=Fn(G(i)?i(o[2]):i)),ei(Zn(o),"rgba")}function ei(t,e){if(t&&t.length){var n=t[0]+","+t[1]+","+t[2];return"rgba"!==e&&"hsva"!==e&&"hsla"!==e||(n+=","+t[3]),e+"("+n+")"}}function ni(t,e){var n=qn(t);return n?(.299*n[0]+.587*n[1]+.114*n[2])*n[3]/255+(1-n[3])*e:0}var ii=new Rn(100);function ri(t){if(U(t)){var e=ii.get(t);return e||(e=jn(t,-.1),ii.put(t,e)),e}if($(t)){var n=C({},t);return n.colorStops=N(t.colorStops,(function(t){return{offset:t.offset,color:jn(t.color,-.1)}})),n}return t}var oi=Object.freeze({__proto__:null,parseCssInt:zn,parseCssFloat:Fn,parse:qn,lift:jn,toHex:function(t){var e=qn(t);if(e)return((1<<24)+(e[0]<<16)+(e[1]<<8)+ +e[2]).toString(16).slice(1)},fastLerp:Kn,fastMapToColor:$n,lerp:Qn,mapToColor:Jn,modifyHSL:ti,modifyAlpha:function(t,e){var n=qn(t);if(n&&null!=e)return n[3]=En(e),ei(n,"rgba")},stringify:ei,lum:ni,random:function(){return ei([Math.round(255*Math.random()),Math.round(255*Math.random()),Math.round(255*Math.random())],"rgb")},liftColor:ri});var ai=Array.prototype.slice;function si(t,e,n){return(e-t)*n+t}function li(t,e,n,i){for(var r=e.length,o=0;o<r;o++)t[o]=si(e[o],n[o],i);return t}function ui(t,e,n,i){for(var r=e.length,o=0;o<r;o++)t[o]=e[o]+n[o]*i;return t}function hi(t,e,n,i){for(var r=e.length,o=r&&e[0].length,a=0;a<r;a++){t[a]||(t[a]=[]);for(var s=0;s<o;s++)t[a][s]=e[a][s]+n[a][s]*i}return t}function ci(t,e){for(var n=t.length,i=e.length,r=n>i?e:t,o=Math.min(n,i),a=r[o-1]||{color:[0,0,0,0],offset:0},s=o;s<Math.max(n,i);s++)r.push({offset:a.offset,color:a.color.slice()})}function pi(t,e,n){var i=t,r=e;if(i.push&&r.push){var o=i.length,a=r.length;if(o!==a)if(o>a)i.length=a;else for(var s=o;s<a;s++)i.push(1===n?r[s]:ai.call(r[s]));var l=i[0]&&i[0].length;for(s=0;s<i.length;s++)if(1===n)isNaN(i[s])&&(i[s]=r[s]);else for(var u=0;u<l;u++)isNaN(i[s][u])&&(i[s][u]=r[s][u])}}function fi(t){if(O(t)){var e=t.length;if(O(t[0])){for(var n=[],i=0;i<e;i++)n.push(ai.call(t[i]));return n}return ai.call(t)}return t}function di(t){return t[0]=Math.floor(t[0])||0,t[1]=Math.floor(t[1])||0,t[2]=Math.floor(t[2])||0,t[3]=null==t[3]?1:t[3],"rgba("+t.join(",")+")"}function gi(t){return 4===t||5===t}function vi(t){return 1===t||2===t}var yi=[0,0,0,0],mi=function(){function t(t){this.keyframes=[],this.discrete=!1,this._invalid=!1,this._needsSort=!1,this._lastFr=0,this._lastFrP=0,this.propName=t}return t.prototype.isFinished=function(){return this._finished},t.prototype.setFinished=function(){this._finished=!0,this._additiveTrack&&this._additiveTrack.setFinished()},t.prototype.needsAnimate=function(){return this.keyframes.length>=1},t.prototype.getAdditiveTrack=function(){return this._additiveTrack},t.prototype.addKeyframe=function(t,e,n){this._needsSort=!0;var i=this.keyframes,r=i.length,o=!1,a=6,s=e;if(O(e)){var l=function(t){return O(t&&t[0])?2:1}(e);a=l,(1===l&&!Y(e[0])||2===l&&!Y(e[0][0]))&&(o=!0)}else if(Y(e)&&!tt(e))a=0;else if(U(e))if(isNaN(+e)){var u=qn(e);u&&(s=u,a=3)}else a=0;else if($(e)){var h=C({},s);h.colorStops=N(e.colorStops,(function(t){return{offset:t.offset,color:qn(t.color)}})),"linear"===e.type?a=4:function(t){return"radial"===t.type}(e)&&(a=5),s=h}0===r?this.valType=a:a===this.valType&&6!==a||(o=!0),this.discrete=this.discrete||o;var c={time:t,value:s,rawValue:e,percent:0};return n&&(c.easing=n,c.easingFunc=G(n)?n:an[n]||An(n)),i.push(c),c},t.prototype.prepare=function(t,e){var n=this.keyframes;this._needsSort&&n.sort((function(t,e){return t.time-e.time}));for(var i=this.valType,r=n.length,o=n[r-1],a=this.discrete,s=vi(i),l=gi(i),u=0;u<r;u++){var h=n[u],c=h.value,p=o.value;h.percent=h.time/t,a||(s&&u!==r-1?pi(c,p,i):l&&ci(c.colorStops,p.colorStops))}if(!a&&5!==i&&e&&this.needsAnimate()&&e.needsAnimate()&&i===e.valType&&!e._finished){this._additiveTrack=e;var f=n[0].value;for(u=0;u<r;u++)0===i?n[u].additiveValue=n[u].value-f:3===i?n[u].additiveValue=ui([],n[u].value,f,-1):vi(i)&&(n[u].additiveValue=1===i?ui([],n[u].value,f,-1):hi([],n[u].value,f,-1))}},t.prototype.step=function(t,e){if(!this._finished){this._additiveTrack&&this._additiveTrack._finished&&(this._additiveTrack=null);var n,i,r,o=null!=this._additiveTrack,a=o?"additiveValue":"value",s=this.valType,l=this.keyframes,u=l.length,h=this.propName,c=3===s,p=this._lastFr,f=Math.min;if(1===u)i=r=l[0];else{if(e<0)n=0;else if(e<this._lastFrP){for(n=f(p+1,u-1);n>=0&&!(l[n].percent<=e);n--);n=f(n,u-2)}else{for(n=p;n<u&&!(l[n].percent>e);n++);n=f(n-1,u-2)}r=l[n+1],i=l[n]}if(i&&r){this._lastFr=n,this._lastFrP=e;var d=r.percent-i.percent,g=0===d?1:f((e-i.percent)/d,1);r.easingFunc&&(g=r.easingFunc(g));var v=o?this._additiveValue:c?yi:t[h];if(!vi(s)&&!c||v||(v=this._additiveValue=[]),this.discrete)t[h]=g<1?i.rawValue:r.rawValue;else if(vi(s))1===s?li(v,i[a],r[a],g):function(t,e,n,i){for(var r=e.length,o=r&&e[0].length,a=0;a<r;a++){t[a]||(t[a]=[]);for(var s=0;s<o;s++)t[a][s]=si(e[a][s],n[a][s],i)}}(v,i[a],r[a],g);else if(gi(s)){var y=i[a],m=r[a],_=4===s;t[h]={type:_?"linear":"radial",x:si(y.x,m.x,g),y:si(y.y,m.y,g),colorStops:N(y.colorStops,(function(t,e){var n=m.colorStops[e];return{offset:si(t.offset,n.offset,g),color:di(li([],t.color,n.color,g))}})),global:m.global},_?(t[h].x2=si(y.x2,m.x2,g),t[h].y2=si(y.y2,m.y2,g)):t[h].r=si(y.r,m.r,g)}else if(c)li(v,i[a],r[a],g),o||(t[h]=di(v));else{var x=si(i[a],r[a],g);o?this._additiveValue=x:t[h]=x}o&&this._addToTarget(t)}}},t.prototype._addToTarget=function(t){var e=this.valType,n=this.propName,i=this._additiveValue;0===e?t[n]=t[n]+i:3===e?(qn(t[n],yi),ui(yi,yi,i,1),t[n]=di(yi)):1===e?ui(t[n],t[n],i,1):2===e&&hi(t[n],t[n],i,1)},t}(),_i=function(){function t(t,e,n,i){this._tracks={},this._trackKeys=[],this._maxTime=0,this._started=0,this._clip=null,this._target=t,this._loop=e,e&&i?M("Can' use additive animation on looped animation."):(this._additiveAnimators=i,this._allowDiscrete=n)}return t.prototype.getMaxTime=function(){return this._maxTime},t.prototype.getDelay=function(){return this._delay},t.prototype.getLoop=function(){return this._loop},t.prototype.getTarget=function(){return this._target},t.prototype.changeTarget=function(t){this._target=t},t.prototype.when=function(t,e,n){return this.whenWithKeys(t,e,F(e),n)},t.prototype.whenWithKeys=function(t,e,n,i){for(var r=this._tracks,o=0;o<n.length;o++){var a=n[o],s=r[a];if(!s){s=r[a]=new mi(a);var l=void 0,u=this._getAdditiveTrack(a);if(u){var h=u.keyframes,c=h[h.length-1];l=c&&c.value,3===u.valType&&l&&(l=di(l))}else l=this._target[a];if(null==l)continue;t>0&&s.addKeyframe(0,fi(l),i),this._trackKeys.push(a)}s.addKeyframe(t,fi(e[a]),i)}return this._maxTime=Math.max(this._maxTime,t),this},t.prototype.pause=function(){this._clip.pause(),this._paused=!0},t.prototype.resume=function(){this._clip.resume(),this._paused=!1},t.prototype.isPaused=function(){return!!this._paused},t.prototype.duration=function(t){return this._maxTime=t,this._force=!0,this},t.prototype._doneCallback=function(){this._setTracksFinished(),this._clip=null;var t=this._doneCbs;if(t)for(var e=t.length,n=0;n<e;n++)t[n].call(this)},t.prototype._abortedCallback=function(){this._setTracksFinished();var t=this.animation,e=this._abortedCbs;if(t&&t.removeClip(this._clip),this._clip=null,e)for(var n=0;n<e.length;n++)e[n].call(this)},t.prototype._setTracksFinished=function(){for(var t=this._tracks,e=this._trackKeys,n=0;n<e.length;n++)t[e[n]].setFinished()},t.prototype._getAdditiveTrack=function(t){var e,n=this._additiveAnimators;if(n)for(var i=0;i<n.length;i++){var r=n[i].getTrack(t);r&&(e=r)}return e},t.prototype.start=function(t){if(!(this._started>0)){this._started=1;for(var e=this,n=[],i=this._maxTime||0,r=0;r<this._trackKeys.length;r++){var o=this._trackKeys[r],a=this._tracks[o],s=this._getAdditiveTrack(o),l=a.keyframes,u=l.length;if(a.prepare(i,s),a.needsAnimate())if(!this._allowDiscrete&&a.discrete){var h=l[u-1];h&&(e._target[a.propName]=h.rawValue),a.setFinished()}else n.push(a)}if(n.length||this._force){var c=new Ln({life:i,loop:this._loop,delay:this._delay||0,onframe:function(t){e._started=2;var i=e._additiveAnimators;if(i){for(var r=!1,o=0;o<i.length;o++)if(i[o]._clip){r=!0;break}r||(e._additiveAnimators=null)}for(o=0;o<n.length;o++)n[o].step(e._target,t);var a=e._onframeCbs;if(a)for(o=0;o<a.length;o++)a[o](e._target,t)},ondestroy:function(){e._doneCallback()}});this._clip=c,this.animation&&this.animation.addClip(c),t&&c.setEasing(t)}else this._doneCallback();return this}},t.prototype.stop=function(t){if(this._clip){var e=this._clip;t&&e.onframe(1),this._abortedCallback()}},t.prototype.delay=function(t){return this._delay=t,this},t.prototype.during=function(t){return t&&(this._onframeCbs||(this._onframeCbs=[]),this._onframeCbs.push(t)),this},t.prototype.done=function(t){return t&&(this._doneCbs||(this._doneCbs=[]),this._doneCbs.push(t)),this},t.prototype.aborted=function(t){return t&&(this._abortedCbs||(this._abortedCbs=[]),this._abortedCbs.push(t)),this},t.prototype.getClip=function(){return this._clip},t.prototype.getTrack=function(t){return this._tracks[t]},t.prototype.getTracks=function(){var t=this;return N(this._trackKeys,(function(e){return t._tracks[e]}))},t.prototype.stopTracks=function(t,e){if(!t.length||!this._clip)return!0;for(var n=this._tracks,i=this._trackKeys,r=0;r<t.length;r++){var o=n[t[r]];o&&!o.isFinished()&&(e?o.step(this._target,1):1===this._started&&o.step(this._target,0),o.setFinished())}var a=!0;for(r=0;r<i.length;r++)if(!n[i[r]].isFinished()){a=!1;break}return a&&this._abortedCallback(),a},t.prototype.saveTo=function(t,e,n){if(t){e=e||this._trackKeys;for(var i=0;i<e.length;i++){var r=e[i],o=this._tracks[r];if(o&&!o.isFinished()){var a=o.keyframes,s=a[n?0:a.length-1];s&&(t[r]=fi(s.rawValue))}}}},t.prototype.__changeFinalValue=function(t,e){e=e||F(t);for(var n=0;n<e.length;n++){var i=e[n],r=this._tracks[i];if(r){var o=r.keyframes;if(o.length>1){var a=o.pop();r.addKeyframe(a.time,t[i]),r.prepare(this._maxTime,r.getAdditiveTrack())}}}},t}();function xi(){return(new Date).getTime()}var bi,wi,Si=function(t){function e(e){var n=t.call(this)||this;return n._running=!1,n._time=0,n._pausedTime=0,n._pauseStart=0,n._paused=!1,e=e||{},n.stage=e.stage||{},n}return n(e,t),e.prototype.addClip=function(t){t.animation&&this.removeClip(t),this._head?(this._tail.next=t,t.prev=this._tail,t.next=null,this._tail=t):this._head=this._tail=t,t.animation=this},e.prototype.addAnimator=function(t){t.animation=this;var e=t.getClip();e&&this.addClip(e)},e.prototype.removeClip=function(t){if(t.animation){var e=t.prev,n=t.next;e?e.next=n:this._head=n,n?n.prev=e:this._tail=e,t.next=t.prev=t.animation=null}},e.prototype.removeAnimator=function(t){var e=t.getClip();e&&this.removeClip(e),t.animation=null},e.prototype.update=function(t){for(var e=xi()-this._pausedTime,n=e-this._time,i=this._head;i;){var r=i.next;i.step(e,n)?(i.ondestroy(),this.removeClip(i),i=r):i=r}this._time=e,t||(this.trigger("frame",n),this.stage.update&&this.stage.update())},e.prototype._startLoop=function(){var t=this;this._running=!0,on((function e(){t._running&&(on(e),!t._paused&&t.update())}))},e.prototype.start=function(){this._running||(this._time=xi(),this._pausedTime=0,this._startLoop())},e.prototype.stop=function(){this._running=!1},e.prototype.pause=function(){this._paused||(this._pauseStart=xi(),this._paused=!0)},e.prototype.resume=function(){this._paused&&(this._pausedTime+=xi()-this._pauseStart,this._paused=!1)},e.prototype.clear=function(){for(var t=this._head;t;){var e=t.next;t.prev=t.next=t.animation=null,t=e}this._head=this._tail=null},e.prototype.isFinished=function(){return null==this._head},e.prototype.animate=function(t,e){e=e||{},this.start();var n=new _i(t,e.loop);return this.addAnimator(n),n},e}(Ut),Mi=r.domSupported,Ti=(wi={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},{mouse:bi=["click","dblclick","mousewheel","wheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],touch:["touchstart","touchend","touchmove"],pointer:N(bi,(function(t){var e=t.replace("mouse","pointer");return wi.hasOwnProperty(e)?e:t}))}),ki=["mousemove","mouseup"],Ci=["pointermove","pointerup"],Di=!1;function Ii(t){var e=t.pointerType;return"pen"===e||"touch"===e}function Ai(t){t&&(t.zrByTouch=!0)}function Li(t,e){for(var n=e,i=!1;n&&9!==n.nodeType&&!(i=n.domBelongToZr||n!==e&&n===t.painterRoot);)n=n.parentNode;return i}var Pi=function(t,e){this.stopPropagation=_t,this.stopImmediatePropagation=_t,this.preventDefault=_t,this.type=e.type,this.target=this.currentTarget=t.dom,this.pointerType=e.pointerType,this.clientX=e.clientX,this.clientY=e.clientY},Oi={mousedown:function(t){t=ae(this.dom,t),this.__mayPointerCapture=[t.zrX,t.zrY],this.trigger("mousedown",t)},mousemove:function(t){t=ae(this.dom,t);var e=this.__mayPointerCapture;!e||t.zrX===e[0]&&t.zrY===e[1]||this.__togglePointerCapture(!0),this.trigger("mousemove",t)},mouseup:function(t){t=ae(this.dom,t),this.__togglePointerCapture(!1),this.trigger("mouseup",t)},mouseout:function(t){Li(this,(t=ae(this.dom,t)).toElement||t.relatedTarget)||(this.__pointerCapturing&&(t.zrEventControl="no_globalout"),this.trigger("mouseout",t))},wheel:function(t){Di=!0,t=ae(this.dom,t),this.trigger("mousewheel",t)},mousewheel:function(t){Di||(t=ae(this.dom,t),this.trigger("mousewheel",t))},touchstart:function(t){Ai(t=ae(this.dom,t)),this.__lastTouchMoment=new Date,this.handler.processGesture(t,"start"),Oi.mousemove.call(this,t),Oi.mousedown.call(this,t)},touchmove:function(t){Ai(t=ae(this.dom,t)),this.handler.processGesture(t,"change"),Oi.mousemove.call(this,t)},touchend:function(t){Ai(t=ae(this.dom,t)),this.handler.processGesture(t,"end"),Oi.mouseup.call(this,t),+new Date-+this.__lastTouchMoment<300&&Oi.click.call(this,t)},pointerdown:function(t){Oi.mousedown.call(this,t)},pointermove:function(t){Ii(t)||Oi.mousemove.call(this,t)},pointerup:function(t){Oi.mouseup.call(this,t)},pointerout:function(t){Ii(t)||Oi.mouseout.call(this,t)}};R(["click","dblclick","contextmenu"],(function(t){Oi[t]=function(e){e=ae(this.dom,e),this.trigger(t,e)}}));var Ri={pointermove:function(t){Ii(t)||Ri.mousemove.call(this,t)},pointerup:function(t){Ri.mouseup.call(this,t)},mousemove:function(t){this.trigger("mousemove",t)},mouseup:function(t){var e=this.__pointerCapturing;this.__togglePointerCapture(!1),this.trigger("mouseup",t),e&&(t.zrEventControl="only_globalout",this.trigger("mouseout",t))}};function Ni(t,e){var n=e.domHandlers;r.pointerEventsSupported?R(Ti.pointer,(function(i){Ei(e,i,(function(e){n[i].call(t,e)}))})):(r.touchEventsSupported&&R(Ti.touch,(function(i){Ei(e,i,(function(r){n[i].call(t,r),function(t){t.touching=!0,null!=t.touchTimer&&(clearTimeout(t.touchTimer),t.touchTimer=null),t.touchTimer=setTimeout((function(){t.touching=!1,t.touchTimer=null}),700)}(e)}))})),R(Ti.mouse,(function(i){Ei(e,i,(function(r){r=oe(r),e.touching||n[i].call(t,r)}))})))}function Bi(t,e){function n(n){Ei(e,n,(function(i){i=oe(i),Li(t,i.target)||(i=function(t,e){return ae(t.dom,new Pi(t,e),!0)}(t,i),e.domHandlers[n].call(t,i))}),{capture:!0})}r.pointerEventsSupported?R(Ci,n):r.touchEventsSupported||R(ki,n)}function Ei(t,e,n,i){t.mounted[e]=n,t.listenerOpts[e]=i,function(t,e,n,i){t.addEventListener(e,n,i)}(t.domTarget,e,n,i)}function zi(t){var e,n,i,r,o=t.mounted;for(var a in o)o.hasOwnProperty(a)&&(e=t.domTarget,n=a,i=o[a],r=t.listenerOpts[a],e.removeEventListener(n,i,r));t.mounted={}}var Fi=function(t,e){this.mounted={},this.listenerOpts={},this.touching=!1,this.domTarget=t,this.domHandlers=e},Vi=function(t){function e(e,n){var i=t.call(this)||this;return i.__pointerCapturing=!1,i.dom=e,i.painterRoot=n,i._localHandlerScope=new Fi(e,Oi),Mi&&(i._globalHandlerScope=new Fi(document,Ri)),Ni(i,i._localHandlerScope),i}return n(e,t),e.prototype.dispose=function(){zi(this._localHandlerScope),Mi&&zi(this._globalHandlerScope)},e.prototype.setCursor=function(t){this.dom.style&&(this.dom.style.cursor=t||"default")},e.prototype.__togglePointerCapture=function(t){if(this.__mayPointerCapture=null,Mi&&+this.__pointerCapturing^+t){this.__pointerCapturing=t;var e=this._globalHandlerScope;t?Bi(this,e):zi(e)}},e}(Ut),Wi=1;r.hasGlobalWindow&&(Wi=Math.max(window.devicePixelRatio||window.screen&&window.screen.deviceXDPI/window.screen.logicalXDPI||1,1));var Hi=Wi,Gi="#333",Ui="#ccc",Xi=ce,Yi=5e-5;function qi(t){return t>Yi||t<-5e-5}var Zi,ji=[],Ki=[],$i=[1,0,0,1,0,0],Qi=Math.abs,Ji=function(){function t(){}var e;return t.prototype.getLocalTransform=function(e){return t.getLocalTransform(this,e)},t.prototype.setPosition=function(t){this.x=t[0],this.y=t[1]},t.prototype.setScale=function(t){this.scaleX=t[0],this.scaleY=t[1]},t.prototype.setSkew=function(t){this.skewX=t[0],this.skewY=t[1]},t.prototype.setOrigin=function(t){this.originX=t[0],this.originY=t[1]},t.prototype.needLocalTransform=function(){return qi(this.rotation)||qi(this.x)||qi(this.y)||qi(this.scaleX-1)||qi(this.scaleY-1)||qi(this.skewX)||qi(this.skewY)},t.prototype.updateTransform=function(){var t=this.parent&&this.parent.transform,e=this.needLocalTransform(),n=this.transform;e||t?(n=n||[1,0,0,1,0,0],e?this.getLocalTransform(n):Xi(n),t&&(e?fe(n,t,n):pe(n,t)),this.transform=n,this._resolveGlobalScaleRatio(n)):n&&(Xi(n),this.invTransform=null)},t.prototype._resolveGlobalScaleRatio=function(t){var e=this.globalScaleRatio;if(null!=e&&1!==e){this.getGlobalScale(ji);var n=ji[0]<0?-1:1,i=ji[1]<0?-1:1,r=((ji[0]-n)*e+n)/ji[0]||0,o=((ji[1]-i)*e+i)/ji[1]||0;t[0]*=r,t[1]*=r,t[2]*=o,t[3]*=o}this.invTransform=this.invTransform||[1,0,0,1,0,0],ye(this.invTransform,t)},t.prototype.getComputedTransform=function(){for(var t=this,e=[];t;)e.push(t),t=t.parent;for(;t=e.pop();)t.updateTransform();return this.transform},t.prototype.setLocalTransform=function(t){if(t){var e=t[0]*t[0]+t[1]*t[1],n=t[2]*t[2]+t[3]*t[3],i=Math.atan2(t[1],t[0]),r=Math.PI/2+i-Math.atan2(t[3],t[2]);n=Math.sqrt(n)*Math.cos(r),e=Math.sqrt(e),this.skewX=r,this.skewY=0,this.rotation=-i,this.x=+t[4],this.y=+t[5],this.scaleX=e,this.scaleY=n,this.originX=0,this.originY=0}},t.prototype.decomposeTransform=function(){if(this.transform){var t=this.parent,e=this.transform;t&&t.transform&&(t.invTransform=t.invTransform||[1,0,0,1,0,0],fe(Ki,t.invTransform,e),e=Ki);var n=this.originX,i=this.originY;(n||i)&&($i[4]=n,$i[5]=i,fe(Ki,e,$i),Ki[4]-=n,Ki[5]-=i,e=Ki),this.setLocalTransform(e)}},t.prototype.getGlobalScale=function(t){var e=this.transform;return t=t||[],e?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1]),t):(t[0]=1,t[1]=1,t)},t.prototype.transformCoordToLocal=function(t,e){var n=[t,e],i=this.invTransform;return i&&zt(n,n,i),n},t.prototype.transformCoordToGlobal=function(t,e){var n=[t,e],i=this.transform;return i&&zt(n,n,i),n},t.prototype.getLineScale=function(){var t=this.transform;return t&&Qi(t[0]-1)>1e-10&&Qi(t[3]-1)>1e-10?Math.sqrt(Qi(t[0]*t[3]-t[2]*t[1])):1},t.prototype.copyTransform=function(t){er(this,t)},t.getLocalTransform=function(t,e){e=e||[];var n=t.originX||0,i=t.originY||0,r=t.scaleX,o=t.scaleY,a=t.anchorX,s=t.anchorY,l=t.rotation||0,u=t.x,h=t.y,c=t.skewX?Math.tan(t.skewX):0,p=t.skewY?Math.tan(-t.skewY):0;if(n||i||a||s){var f=n+a,d=i+s;e[4]=-f*r-c*d*o,e[5]=-d*o-p*f*r}else e[4]=e[5]=0;return e[0]=r,e[3]=o,e[1]=p*r,e[2]=c*o,l&&ge(e,e,l),e[4]+=n+u,e[5]+=i+h,e},t.initDefaultProps=((e=t.prototype).scaleX=e.scaleY=e.globalScaleRatio=1,void(e.x=e.y=e.originX=e.originY=e.skewX=e.skewY=e.rotation=e.anchorX=e.anchorY=0)),t}(),tr=["x","y","originX","originY","anchorX","anchorY","rotation","scaleX","scaleY","skewX","skewY"];function er(t,e){for(var n=0;n<tr.length;n++){var i=tr[n];t[i]=e[i]}}function nr(t){Zi||(Zi=new Rn(100)),t=t||o;var e=Zi.get(t);return e||(e={font:t,strWidthCache:new Rn(500),asciiWidthMap:null,asciiWidthMapTried:!1,stWideCharWidth:u.measureText("国",t).width,asciiCharWidth:u.measureText("a",t).width},Zi.put(t,e)),e}var ir=0,rr=5;function or(t,e){return t.asciiWidthMapTried||(t.asciiWidthMap=function(t){if(!(ir>=rr)){t=t||o;for(var e=[],n=+new Date,i=0;i<=127;i++)e[i]=u.measureText(String.fromCharCode(i),t).width;var r=+new Date-n;return r>16?ir=rr:r>2&&ir++,e}}(t.font),t.asciiWidthMapTried=!0),0<=e&&e<=127?null!=t.asciiWidthMap?t.asciiWidthMap[e]:t.asciiCharWidth:t.stWideCharWidth}function ar(t,e){var n=t.strWidthCache,i=n.get(e);return null==i&&(i=u.measureText(e,t.font).width,n.put(e,i)),i}function sr(t,e,n,i){var r=ar(nr(e),t),o=cr(e),a=ur(0,r,n),s=hr(0,o,i);return new Oe(a,s,r,o)}function lr(t,e,n,i){var r=((t||"")+"").split("\n");if(1===r.length)return sr(r[0],e,n,i);for(var o=new Oe(0,0,0,0),a=0;a<r.length;a++){var s=sr(r[a],e,n,i);0===a?o.copy(s):o.union(s)}return o}function ur(t,e,n,i){return"right"===n?i?t+=e:t-=e:"center"===n&&(i?t+=e/2:t-=e/2),t}function hr(t,e,n,i){return"middle"===n?i?t+=e/2:t-=e/2:"bottom"===n&&(i?t+=e:t-=e),t}function cr(t){return nr(t).stWideCharWidth}function pr(t,e){return"string"==typeof t?t.lastIndexOf("%")>=0?parseFloat(t)/100*e:parseFloat(t):t}function fr(t,e,n){var i=e.position||"inside",r=null!=e.distance?e.distance:5,o=n.height,a=n.width,s=o/2,l=n.x,u=n.y,h="left",c="top";if(i instanceof Array)l+=pr(i[0],n.width),u+=pr(i[1],n.height),h=null,c=null;else switch(i){case"left":l-=r,u+=s,h="right",c="middle";break;case"right":l+=r+a,u+=s,c="middle";break;case"top":l+=a/2,u-=r,h="center",c="bottom";break;case"bottom":l+=a/2,u+=o+r,h="center";break;case"inside":l+=a/2,u+=s,h="center",c="middle";break;case"insideLeft":l+=r,u+=s,c="middle";break;case"insideRight":l+=a-r,u+=s,h="right",c="middle";break;case"insideTop":l+=a/2,u+=r,h="center";break;case"insideBottom":l+=a/2,u+=o-r,h="center",c="bottom";break;case"insideTopLeft":l+=r,u+=r;break;case"insideTopRight":l+=a-r,u+=r,h="right";break;case"insideBottomLeft":l+=r,u+=o-r,c="bottom";break;case"insideBottomRight":l+=a-r,u+=o-r,h="right",c="bottom"}return(t=t||{}).x=l,t.y=u,t.align=h,t.verticalAlign=c,t}var dr="__zr_normal__",gr=tr.concat(["ignore"]),vr=B(tr,(function(t,e){return t[e]=!0,t}),{ignore:!1}),yr={},mr=new Oe(0,0,0,0),_r=[],xr=function(){function t(t){this.id=S(),this.animators=[],this.currentStates=[],this.states={},this._init(t)}return t.prototype._init=function(t){this.attr(t)},t.prototype.drift=function(t,e,n){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0}var i=this.transform;i||(i=this.transform=[1,0,0,1,0,0]),i[4]+=t,i[5]+=e,this.decomposeTransform(),this.markRedraw()},t.prototype.beforeUpdate=function(){},t.prototype.afterUpdate=function(){},t.prototype.update=function(){this.updateTransform(),this.__dirty&&this.updateInnerText()},t.prototype.updateInnerText=function(t){var e=this._textContent;if(e&&(!e.ignore||t)){this.textConfig||(this.textConfig={});var n=this.textConfig,i=n.local,r=e.innerTransformable,o=void 0,a=void 0,s=!1;r.parent=i?this:null;var l=!1;r.copyTransform(e);var u=null!=n.position,h=n.autoOverflowArea,c=void 0;if((h||u)&&(c=mr,n.layoutRect?c.copy(n.layoutRect):c.copy(this.getBoundingRect()),i||c.applyTransform(this.transform)),u){this.calculateTextPosition?this.calculateTextPosition(yr,n,c):fr(yr,n,c),r.x=yr.x,r.y=yr.y,o=yr.align,a=yr.verticalAlign;var p=n.origin;if(p&&null!=n.rotation){var f=void 0,d=void 0;"center"===p?(f=.5*c.width,d=.5*c.height):(f=pr(p[0],c.width),d=pr(p[1],c.height)),l=!0,r.originX=-r.x+f+(i?0:c.x),r.originY=-r.y+d+(i?0:c.y)}}null!=n.rotation&&(r.rotation=n.rotation);var g=n.offset;g&&(r.x+=g[0],r.y+=g[1],l||(r.originX=-g[0],r.originY=-g[1]));var v=this._innerTextDefaultStyle||(this._innerTextDefaultStyle={});if(h){var y=v.overflowRect=v.overflowRect||new Oe(0,0,0,0);r.getLocalTransform(_r),ye(_r,_r),Oe.copy(y,c),y.applyTransform(_r)}else v.overflowRect=null;var m=void 0,_=void 0,x=void 0;(null==n.inside?"string"==typeof n.position&&n.position.indexOf("inside")>=0:n.inside)&&this.canBeInsideText()?(m=n.insideFill,_=n.insideStroke,null!=m&&"auto"!==m||(m=this.getInsideTextFill()),null!=_&&"auto"!==_||(_=this.getInsideTextStroke(m),x=!0)):(m=n.outsideFill,_=n.outsideStroke,null!=m&&"auto"!==m||(m=this.getOutsideFill()),null!=_&&"auto"!==_||(_=this.getOutsideStroke(m),x=!0)),(m=m||"#000")===v.fill&&_===v.stroke&&x===v.autoStroke&&o===v.align&&a===v.verticalAlign||(s=!0,v.fill=m,v.stroke=_,v.autoStroke=x,v.align=o,v.verticalAlign=a,e.setDefaultTextStyle(v)),e.__dirty|=1,s&&e.dirtyStyle(!0)}},t.prototype.canBeInsideText=function(){return!0},t.prototype.getInsideTextFill=function(){return"#fff"},t.prototype.getInsideTextStroke=function(t){return"#000"},t.prototype.getOutsideFill=function(){return this.__zr&&this.__zr.isDarkMode()?Ui:Gi},t.prototype.getOutsideStroke=function(t){var e=this.__zr&&this.__zr.getBackgroundColor(),n="string"==typeof e&&qn(e);n||(n=[255,255,255,1]);for(var i=n[3],r=this.__zr.isDarkMode(),o=0;o<3;o++)n[o]=n[o]*i+(r?0:255)*(1-i);return n[3]=1,ei(n,"rgba")},t.prototype.traverse=function(t,e){},t.prototype.attrKV=function(t,e){"textConfig"===t?this.setTextConfig(e):"textContent"===t?this.setTextContent(e):"clipPath"===t?this.setClipPath(e):"extra"===t?(this.extra=this.extra||{},C(this.extra,e)):this[t]=e},t.prototype.hide=function(){this.ignore=!0,this.markRedraw()},t.prototype.show=function(){this.ignore=!1,this.markRedraw()},t.prototype.attr=function(t,e){if("string"==typeof t)this.attrKV(t,e);else if(q(t))for(var n=F(t),i=0;i<n.length;i++){var r=n[i];this.attrKV(r,t[r])}return this.markRedraw(),this},t.prototype.saveCurrentToNormalState=function(t){this._innerSaveToNormal(t);for(var e=this._normalState,n=0;n<this.animators.length;n++){var i=this.animators[n],r=i.__fromStateTransition;if(!(i.getLoop()||r&&r!==dr)){var o=i.targetName,a=o?e[o]:e;i.saveTo(a)}}},t.prototype._innerSaveToNormal=function(t){var e=this._normalState;e||(e=this._normalState={}),t.textConfig&&!e.textConfig&&(e.textConfig=this.textConfig),this._savePrimaryToNormal(t,e,gr)},t.prototype._savePrimaryToNormal=function(t,e,n){for(var i=0;i<n.length;i++){var r=n[i];null==t[r]||r in e||(e[r]=this[r])}},t.prototype.hasState=function(){return this.currentStates.length>0},t.prototype.getState=function(t){return this.states[t]},t.prototype.ensureState=function(t){var e=this.states;return e[t]||(e[t]={}),e[t]},t.prototype.clearStates=function(t){this.useState(dr,!1,t)},t.prototype.useState=function(t,e,n,i){var r=t===dr;if(this.hasState()||!r){var o=this.currentStates,a=this.stateTransition;if(!(A(o,t)>=0)||!e&&1!==o.length){var s;if(this.stateProxy&&!r&&(s=this.stateProxy(t)),s||(s=this.states&&this.states[t]),s||r){r||this.saveCurrentToNormalState(s);var l=!!(s&&s.hoverLayer||i);l&&this._toggleHoverLayerFlag(!0),this._applyStateObj(t,s,this._normalState,e,!n&&!this.__inHover&&a&&a.duration>0,a);var u=this._textContent,h=this._textGuide;return u&&u.useState(t,e,n,l),h&&h.useState(t,e,n,l),r?(this.currentStates=[],this._normalState={}):e?this.currentStates.push(t):this.currentStates=[t],this._updateAnimationTargets(),this.markRedraw(),!l&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=-2),s}M("State "+t+" not exists.")}}},t.prototype.useStates=function(t,e,n){if(t.length){var i=[],r=this.currentStates,o=t.length,a=o===r.length;if(a)for(var s=0;s<o;s++)if(t[s]!==r[s]){a=!1;break}if(a)return;for(s=0;s<o;s++){var l=t[s],u=void 0;this.stateProxy&&(u=this.stateProxy(l,t)),u||(u=this.states[l]),u&&i.push(u)}var h=i[o-1],c=!!(h&&h.hoverLayer||n);c&&this._toggleHoverLayerFlag(!0);var p=this._mergeStates(i),f=this.stateTransition;this.saveCurrentToNormalState(p),this._applyStateObj(t.join(","),p,this._normalState,!1,!e&&!this.__inHover&&f&&f.duration>0,f);var d=this._textContent,g=this._textGuide;d&&d.useStates(t,e,c),g&&g.useStates(t,e,c),this._updateAnimationTargets(),this.currentStates=t.slice(),this.markRedraw(),!c&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=-2)}else this.clearStates()},t.prototype.isSilent=function(){for(var t=this;t;){if(t.silent)return!0;var e=t.__hostTarget;t=e?t.ignoreHostSilent?null:e:t.parent}return!1},t.prototype._updateAnimationTargets=function(){for(var t=0;t<this.animators.length;t++){var e=this.animators[t];e.targetName&&e.changeTarget(this[e.targetName])}},t.prototype.removeState=function(t){var e=A(this.currentStates,t);if(e>=0){var n=this.currentStates.slice();n.splice(e,1),this.useStates(n)}},t.prototype.replaceState=function(t,e,n){var i=this.currentStates.slice(),r=A(i,t),o=A(i,e)>=0;r>=0?o?i.splice(r,1):i[r]=e:n&&!o&&i.push(e),this.useStates(i)},t.prototype.toggleState=function(t,e){e?this.useState(t,!0):this.removeState(t)},t.prototype._mergeStates=function(t){for(var e,n={},i=0;i<t.length;i++){var r=t[i];C(n,r),r.textConfig&&C(e=e||{},r.textConfig)}return e&&(n.textConfig=e),n},t.prototype._applyStateObj=function(t,e,n,i,r,o){var a=!(e&&i);e&&e.textConfig?(this.textConfig=C({},i?this.textConfig:n.textConfig),C(this.textConfig,e.textConfig)):a&&n.textConfig&&(this.textConfig=n.textConfig);for(var s={},l=!1,u=0;u<gr.length;u++){var h=gr[u],c=r&&vr[h];e&&null!=e[h]?c?(l=!0,s[h]=e[h]):this[h]=e[h]:a&&null!=n[h]&&(c?(l=!0,s[h]=n[h]):this[h]=n[h])}if(!r)for(u=0;u<this.animators.length;u++){var p=this.animators[u],f=p.targetName;p.getLoop()||p.__changeFinalValue(f?(e||n)[f]:e||n)}l&&this._transitionState(t,s,o)},t.prototype._attachComponent=function(t){if((!t.__zr||t.__hostTarget)&&t!==this){var e=this.__zr;e&&t.addSelfToZr(e),t.__zr=e,t.__hostTarget=this}},t.prototype._detachComponent=function(t){t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__hostTarget=null},t.prototype.getClipPath=function(){return this._clipPath},t.prototype.setClipPath=function(t){this._clipPath&&this._clipPath!==t&&this.removeClipPath(),this._attachComponent(t),this._clipPath=t,this.markRedraw()},t.prototype.removeClipPath=function(){var t=this._clipPath;t&&(this._detachComponent(t),this._clipPath=null,this.markRedraw())},t.prototype.getTextContent=function(){return this._textContent},t.prototype.setTextContent=function(t){var e=this._textContent;e!==t&&(e&&e!==t&&this.removeTextContent(),t.innerTransformable=new Ji,this._attachComponent(t),this._textContent=t,this.markRedraw())},t.prototype.setTextConfig=function(t){this.textConfig||(this.textConfig={}),C(this.textConfig,t),this.markRedraw()},t.prototype.removeTextConfig=function(){this.textConfig=null,this.markRedraw()},t.prototype.removeTextContent=function(){var t=this._textContent;t&&(t.innerTransformable=null,this._detachComponent(t),this._textContent=null,this._innerTextDefaultStyle=null,this.markRedraw())},t.prototype.getTextGuideLine=function(){return this._textGuide},t.prototype.setTextGuideLine=function(t){this._textGuide&&this._textGuide!==t&&this.removeTextGuideLine(),this._attachComponent(t),this._textGuide=t,this.markRedraw()},t.prototype.removeTextGuideLine=function(){var t=this._textGuide;t&&(this._detachComponent(t),this._textGuide=null,this.markRedraw())},t.prototype.markRedraw=function(){this.__dirty|=1;var t=this.__zr;t&&(this.__inHover?t.refreshHover():t.refresh()),this.__hostTarget&&this.__hostTarget.markRedraw()},t.prototype.dirty=function(){this.markRedraw()},t.prototype._toggleHoverLayerFlag=function(t){this.__inHover=t;var e=this._textContent,n=this._textGuide;e&&(e.__inHover=t),n&&(n.__inHover=t)},t.prototype.addSelfToZr=function(t){if(this.__zr!==t){this.__zr=t;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.addAnimator(e[n]);this._clipPath&&this._clipPath.addSelfToZr(t),this._textContent&&this._textContent.addSelfToZr(t),this._textGuide&&this._textGuide.addSelfToZr(t)}},t.prototype.removeSelfFromZr=function(t){if(this.__zr){this.__zr=null;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.removeAnimator(e[n]);this._clipPath&&this._clipPath.removeSelfFromZr(t),this._textContent&&this._textContent.removeSelfFromZr(t),this._textGuide&&this._textGuide.removeSelfFromZr(t)}},t.prototype.animate=function(t,e,n){var i=t?this[t]:this;var r=new _i(i,e,n);return t&&(r.targetName=t),this.addAnimator(r,t),r},t.prototype.addAnimator=function(t,e){var n=this.__zr,i=this;t.during((function(){i.updateDuringAnimation(e)})).done((function(){var e=i.animators,n=A(e,t);n>=0&&e.splice(n,1)})),this.animators.push(t),n&&n.animation.addAnimator(t),n&&n.wakeUp()},t.prototype.updateDuringAnimation=function(t){this.markRedraw()},t.prototype.stopAnimation=function(t,e){for(var n=this.animators,i=n.length,r=[],o=0;o<i;o++){var a=n[o];t&&t!==a.scope?r.push(a):a.stop(e)}return this.animators=r,this},t.prototype.animateTo=function(t,e,n){br(this,t,e,n)},t.prototype.animateFrom=function(t,e,n){br(this,t,e,n,!0)},t.prototype._transitionState=function(t,e,n,i){for(var r=br(this,e,n,i),o=0;o<r.length;o++)r[o].__fromStateTransition=t},t.prototype.getBoundingRect=function(){return null},t.prototype.getPaintRect=function(){return null},t.initDefaultProps=function(){var e=t.prototype;e.type="element",e.name="",e.ignore=e.silent=e.ignoreHostSilent=e.isGroup=e.draggable=e.dragging=e.ignoreClip=e.__inHover=!1,e.__dirty=1;function n(t,n,i,r){function o(t,e){Object.defineProperty(e,0,{get:function(){return t[i]},set:function(e){t[i]=e}}),Object.defineProperty(e,1,{get:function(){return t[r]},set:function(e){t[r]=e}})}Object.defineProperty(e,t,{get:function(){this[n]||o(this,this[n]=[]);return this[n]},set:function(t){this[i]=t[0],this[r]=t[1],this[n]=t,o(this,t)}})}Object.defineProperty&&(n("position","_legacyPos","x","y"),n("scale","_legacyScale","scaleX","scaleY"),n("origin","_legacyOrigin","originX","originY"))}(),t}();function br(t,e,n,i,r){var o=[];Mr(t,"",t,e,n=n||{},i,o,r);var a=o.length,s=!1,l=n.done,u=n.aborted,h=function(){s=!0,--a<=0&&(s?l&&l():u&&u())},c=function(){--a<=0&&(s?l&&l():u&&u())};a||l&&l(),o.length>0&&n.during&&o[0].during((function(t,e){n.during(e)}));for(var p=0;p<o.length;p++){var f=o[p];h&&f.done(h),c&&f.aborted(c),n.force&&f.duration(n.duration),f.start(n.easing)}return o}function wr(t,e,n){for(var i=0;i<n;i++)t[i]=e[i]}function Sr(t,e,n){if(O(e[n]))if(O(t[n])||(t[n]=[]),j(e[n])){var i=e[n].length;t[n].length!==i&&(t[n]=new e[n].constructor(i),wr(t[n],e[n],i))}else{var r=e[n],o=t[n],a=r.length;if(O(r[0]))for(var s=r[0].length,l=0;l<a;l++)o[l]?wr(o[l],r[l],s):o[l]=Array.prototype.slice.call(r[l]);else wr(o,r,a);o.length=r.length}else t[n]=e[n]}function Mr(t,e,n,i,r,o,a,s){for(var l=F(i),u=r.duration,h=r.delay,c=r.additive,p=r.setToFinal,f=!q(o),d=t.animators,g=[],v=0;v<l.length;v++){var y=l[v],m=i[y];if(null!=m&&null!=n[y]&&(f||o[y]))if(!q(m)||O(m)||$(m))g.push(y);else{if(e){s||(n[y]=m,t.updateDuringAnimation(e));continue}Mr(t,y,n[y],m,r,o&&o[y],a,s)}else s||(n[y]=m,t.updateDuringAnimation(e),g.push(y))}var _=g.length;if(!c&&_)for(var x=0;x<d.length;x++){if((w=d[x]).targetName===e)if(w.stopTracks(g)){var b=A(d,w);d.splice(b,1)}}if(r.force||(g=E(g,(function(t){return e=i[t],r=n[t],!(e===r||O(e)&&O(r)&&function(t,e){var n=t.length;if(n!==e.length)return!1;for(var i=0;i<n;i++)if(t[i]!==e[i])return!1;return!0}(e,r));var e,r})),_=g.length),_>0||r.force&&!a.length){var w,S=void 0,M=void 0,T=void 0;if(s){M={},p&&(S={});for(x=0;x<_;x++){M[y=g[x]]=n[y],p?S[y]=i[y]:n[y]=i[y]}}else if(p){T={};for(x=0;x<_;x++){T[y=g[x]]=fi(n[y]),Sr(n,i,y)}}(w=new _i(n,!1,!1,c?E(d,(function(t){return t.targetName===e})):null)).targetName=e,r.scope&&(w.scope=r.scope),p&&S&&w.whenWithKeys(0,S,g),T&&w.whenWithKeys(0,T,g),w.whenWithKeys(null==u?500:u,s?M:i,g).delay(h||0),t.addAnimator(w,e),a.push(w)}}P(xr,Ut),P(xr,Ji);var Tr=function(t){function e(e){var n=t.call(this)||this;return n.isGroup=!0,n._children=[],n.attr(e),n}return n(e,t),e.prototype.childrenRef=function(){return this._children},e.prototype.children=function(){return this._children.slice()},e.prototype.childAt=function(t){return this._children[t]},e.prototype.childOfName=function(t){for(var e=this._children,n=0;n<e.length;n++)if(e[n].name===t)return e[n]},e.prototype.childCount=function(){return this._children.length},e.prototype.add=function(t){return t&&t!==this&&t.parent!==this&&(this._children.push(t),this._doAdd(t)),this},e.prototype.addBefore=function(t,e){if(t&&t!==this&&t.parent!==this&&e&&e.parent===this){var n=this._children,i=n.indexOf(e);i>=0&&(n.splice(i,0,t),this._doAdd(t))}return this},e.prototype.replace=function(t,e){var n=A(this._children,t);return n>=0&&this.replaceAt(e,n),this},e.prototype.replaceAt=function(t,e){var n=this._children,i=n[e];if(t&&t!==this&&t.parent!==this&&t!==i){n[e]=t,i.parent=null;var r=this.__zr;r&&i.removeSelfFromZr(r),this._doAdd(t)}return this},e.prototype._doAdd=function(t){t.parent&&t.parent.remove(t),t.parent=this;var e=this.__zr;e&&e!==t.__zr&&t.addSelfToZr(e),e&&e.refresh()},e.prototype.remove=function(t){var e=this.__zr,n=this._children,i=A(n,t);return i<0||(n.splice(i,1),t.parent=null,e&&t.removeSelfFromZr(e),e&&e.refresh()),this},e.prototype.removeAll=function(){for(var t=this._children,e=this.__zr,n=0;n<t.length;n++){var i=t[n];e&&i.removeSelfFromZr(e),i.parent=null}return t.length=0,this},e.prototype.eachChild=function(t,e){for(var n=this._children,i=0;i<n.length;i++){var r=n[i];t.call(e,r,i)}return this},e.prototype.traverse=function(t,e){for(var n=0;n<this._children.length;n++){var i=this._children[n],r=t.call(e,i);i.isGroup&&!r&&i.traverse(t,e)}return this},e.prototype.addSelfToZr=function(e){t.prototype.addSelfToZr.call(this,e);for(var n=0;n<this._children.length;n++){this._children[n].addSelfToZr(e)}},e.prototype.removeSelfFromZr=function(e){t.prototype.removeSelfFromZr.call(this,e);for(var n=0;n<this._children.length;n++){this._children[n].removeSelfFromZr(e)}},e.prototype.getBoundingRect=function(t){for(var e=new Oe(0,0,0,0),n=t||this._children,i=[],r=null,o=0;o<n.length;o++){var a=n[o];if(!a.ignore&&!a.invisible){var s=a.getBoundingRect(),l=a.getLocalTransform(i);l?(Oe.applyTransform(e,s,l),(r=r||e.clone()).union(e)):(r=r||s.clone()).union(s)}}return r||e},e}(xr);Tr.prototype.type="group";
/*!
    * ZRender, a high performance 2d drawing library.
    *
    * Copyright (c) 2013, Baidu Inc.
    * All rights reserved.
    *
    * LICENSE
    * https://github.com/ecomfe/zrender/blob/master/LICENSE.txt
    */
var kr={},Cr={};var Dr,Ir=function(){function t(t,e,n){var i=this;this._sleepAfterStill=10,this._stillFrameAccum=0,this._needsRefresh=!0,this._needsRefreshHover=!0,this._darkMode=!1,n=n||{},this.dom=e,this.id=t;var o=new rn,a=n.renderer||"canvas";kr[a]||(a=F(kr)[0]),n.useDirtyRect=null!=n.useDirtyRect&&n.useDirtyRect;var s=new kr[a](e,o,n,t),l=n.ssr||s.ssrOnly;this.storage=o,this.painter=s;var u,h=r.node||r.worker||l?null:new Vi(s.getViewportRoot(),s.root),c=n.useCoarsePointer;(null==c||"auto"===c?r.touchEventsSupported:!!c)&&(u=nt(n.pointerSize,44)),this.handler=new Ue(o,s,h,s.root,u),this.animation=new Si({stage:{update:l?null:function(){return i._flush(!0)}}}),l||this.animation.start()}return t.prototype.add=function(t){!this._disposed&&t&&(this.storage.addRoot(t),t.addSelfToZr(this),this.refresh())},t.prototype.remove=function(t){!this._disposed&&t&&(this.storage.delRoot(t),t.removeSelfFromZr(this),this.refresh())},t.prototype.configLayer=function(t,e){this._disposed||(this.painter.configLayer&&this.painter.configLayer(t,e),this.refresh())},t.prototype.setBackgroundColor=function(t){this._disposed||(this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this.refresh(),this._backgroundColor=t,this._darkMode=function(t){if(!t)return!1;if("string"==typeof t)return ni(t,1)<.4;if(t.colorStops){for(var e=t.colorStops,n=0,i=e.length,r=0;r<i;r++)n+=ni(e[r].color,1);return(n/=i)<.4}return!1}(t))},t.prototype.getBackgroundColor=function(){return this._backgroundColor},t.prototype.setDarkMode=function(t){this._darkMode=t},t.prototype.isDarkMode=function(){return this._darkMode},t.prototype.refreshImmediately=function(t){this._disposed||(t||this.animation.update(!0),this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1)},t.prototype.refresh=function(){this._disposed||(this._needsRefresh=!0,this.animation.start())},t.prototype.flush=function(){this._disposed||this._flush(!1)},t.prototype._flush=function(t){var e,n=xi();this._needsRefresh&&(e=!0,this.refreshImmediately(t)),this._needsRefreshHover&&(e=!0,this.refreshHoverImmediately());var i=xi();e?(this._stillFrameAccum=0,this.trigger("rendered",{elapsedTime:i-n})):this._sleepAfterStill>0&&(this._stillFrameAccum++,this._stillFrameAccum>this._sleepAfterStill&&this.animation.stop())},t.prototype.setSleepAfterStill=function(t){this._sleepAfterStill=t},t.prototype.wakeUp=function(){this._disposed||(this.animation.start(),this._stillFrameAccum=0)},t.prototype.refreshHover=function(){this._needsRefreshHover=!0},t.prototype.refreshHoverImmediately=function(){this._disposed||(this._needsRefreshHover=!1,this.painter.refreshHover&&"canvas"===this.painter.getType()&&this.painter.refreshHover())},t.prototype.resize=function(t){this._disposed||(t=t||{},this.painter.resize(t.width,t.height),this.handler.resize())},t.prototype.clearAnimation=function(){this._disposed||this.animation.clear()},t.prototype.getWidth=function(){if(!this._disposed)return this.painter.getWidth()},t.prototype.getHeight=function(){if(!this._disposed)return this.painter.getHeight()},t.prototype.setCursorStyle=function(t){this._disposed||this.handler.setCursorStyle(t)},t.prototype.findHover=function(t,e){if(!this._disposed)return this.handler.findHover(t,e)},t.prototype.on=function(t,e,n){return this._disposed||this.handler.on(t,e,n),this},t.prototype.off=function(t,e){this._disposed||this.handler.off(t,e)},t.prototype.trigger=function(t,e){this._disposed||this.handler.trigger(t,e)},t.prototype.clear=function(){if(!this._disposed){for(var t=this.storage.getRoots(),e=0;e<t.length;e++)t[e]instanceof Tr&&t[e].removeSelfFromZr(this);this.storage.delAllRoots(),this.painter.clear()}},t.prototype.dispose=function(){var t;this._disposed||(this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,this._disposed=!0,t=this.id,delete Cr[t])},t}();function Ar(t,e){var n=new Ir(S(),t,e);return Cr[n.id]=n,n}function Lr(t,e){kr[t]=e}function Pr(t){Dr=t}var Or=Object.freeze({__proto__:null,init:Ar,dispose:function(t){t.dispose()},disposeAll:function(){for(var t in Cr)Cr.hasOwnProperty(t)&&Cr[t].dispose();Cr={}},getInstance:function(t){return Cr[t]},registerPainter:Lr,getElementSSRData:function(t){if("function"==typeof Dr)return Dr(t)},registerSSRDataGetter:Pr,version:"6.0.0"}),Rr=1e-4;var Nr=Math.min,Br=Math.max,Er=Math.abs;function zr(t,e,n,i){var r=e[0],o=e[1],a=n[0],s=n[1],l=o-r,u=s-a;if(0===l)return 0===u?a:(a+s)/2;if(i)if(l>0){if(t<=r)return a;if(t>=o)return s}else{if(t>=r)return a;if(t<=o)return s}else{if(t===r)return a;if(t===o)return s}return(t-r)/l*u+a}var Fr=function(t,e,n){switch(t){case"center":case"middle":t="50%";break;case"left":case"top":t="0%";break;case"right":case"bottom":t="100%"}return Vr(t,e,n)};function Vr(t,e,n){return U(t)?(i=t,i.replace(/^\s+|\s+$/g,"")).match(/%$/)?parseFloat(t)/100*e+(n||0):parseFloat(t):null==t?NaN:+t;var i}function Wr(t,e,n){return null==e&&(e=10),e=Math.min(Math.max(0,e),20),t=(+t).toFixed(e),n?t:+t}function Hr(t){if(t=+t,isNaN(t))return 0;if(t>1e-14)for(var e=1,n=0;n<15;n++,e*=10)if(Math.round(t*e)/e===t)return n;return Gr(t)}function Gr(t){var e=t.toString().toLowerCase(),n=e.indexOf("e"),i=n>0?+e.slice(n+1):0,r=n>0?n:e.length,o=e.indexOf("."),a=o<0?0:r-1-o;return Math.max(0,a-i)}function Ur(t,e){var n=Math.log,i=Math.LN10,r=Math.floor(n(t[1]-t[0])/i),o=Math.round(n(Er(e[1]-e[0]))/i),a=Math.min(Math.max(-r+o,0),20);return isFinite(a)?a:20}function Xr(t,e){var n=B(t,(function(t,e){return t+(isNaN(e)?0:e)}),0);if(0===n)return[];for(var i=Math.pow(10,e),r=N(t,(function(t){return(isNaN(t)?0:t)/n*i*100})),o=100*i,a=N(r,(function(t){return Math.floor(t)})),s=B(a,(function(t,e){return t+e}),0),l=N(r,(function(t,e){return t-a[e]}));s<o;){for(var u=Number.NEGATIVE_INFINITY,h=null,c=0,p=l.length;c<p;++c)l[c]>u&&(u=l[c],h=c);++a[h],l[h]=0,++s}return N(a,(function(t){return t/i}))}function Yr(t,e){var n=Math.max(Hr(t),Hr(e)),i=t+e;return n>20?i:Wr(i,n)}function qr(t){var e=2*Math.PI;return(t%e+e)%e}function Zr(t){return t>-1e-4&&t<Rr}var jr=/^(?:(\d{4})(?:[-\/](\d{1,2})(?:[-\/](\d{1,2})(?:[T ](\d{1,2})(?::(\d{1,2})(?::(\d{1,2})(?:[.,](\d+))?)?)?(Z|[\+\-]\d\d:?\d\d)?)?)?)?)?$/;function Kr(t){if(t instanceof Date)return t;if(U(t)){var e=jr.exec(t);if(!e)return new Date(NaN);if(e[8]){var n=+e[4]||0;return"Z"!==e[8].toUpperCase()&&(n-=+e[8].slice(0,3)),new Date(Date.UTC(+e[1],+(e[2]||1)-1,+e[3]||1,n,+(e[5]||0),+e[6]||0,e[7]?+e[7].substring(0,3):0))}return new Date(+e[1],+(e[2]||1)-1,+e[3]||1,+e[4]||0,+(e[5]||0),+e[6]||0,e[7]?+e[7].substring(0,3):0)}return null==t?new Date(NaN):new Date(Math.round(t))}function $r(t){return Math.pow(10,Qr(t))}function Qr(t){if(0===t)return 0;var e=Math.floor(Math.log(t)/Math.LN10);return t/Math.pow(10,e)>=10&&e++,e}function Jr(t,e){var n=Qr(t),i=Math.pow(10,n),r=t/i;return t=(e?r<1.5?1:r<2.5?2:r<4?3:r<7?5:10:r<1?1:r<2?2:r<3?3:r<5?5:10)*i,n>=-20?+t.toFixed(n<0?-n:0):t}function to(t){var e=parseFloat(t);return e==t&&(0!==e||!U(t)||t.indexOf("x")<=0)?e:NaN}function eo(t){return!isNaN(to(t))}function no(t,e){return 0===e?t:no(e,t%e)}function io(t,e){return null==t?e:null==e?t:t*e/no(t,e)}var ro={},oo="undefined"!=typeof console&&console.warn&&console.log;function ao(t,e,n){if(oo){if(n){if(ro[e])return;ro[e]=!0}console[t]("[ECharts] "+e)}}function so(t,e){ao("error",t,e)}function lo(t){0}function uo(t){throw new Error(t)}function ho(t,e,n){return(e-t)*n+t}var co="series\0";function po(t){return t instanceof Array?t:null==t?[]:[t]}function fo(t,e,n){if(t){t[e]=t[e]||{},t.emphasis=t.emphasis||{},t.emphasis[e]=t.emphasis[e]||{};for(var i=0,r=n.length;i<r;i++){var o=n[i];!t.emphasis[e].hasOwnProperty(o)&&t[e].hasOwnProperty(o)&&(t.emphasis[e][o]=t[e][o])}}}var go=["fontStyle","fontWeight","fontSize","fontFamily","rich","tag","color","textBorderColor","textBorderWidth","width","height","lineHeight","align","verticalAlign","baseline","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY","backgroundColor","borderColor","borderWidth","borderRadius","padding"];function vo(t){return!q(t)||H(t)||t instanceof Date?t:t.value}function yo(t){return q(t)&&!(t instanceof Array)}function mo(t,e,n){var i="normalMerge"===n,r="replaceMerge"===n,o="replaceAll"===n;t=t||[],e=(e||[]).slice();var a=dt();R(e,(function(t,n){q(t)||(e[n]=null)}));var s,l,u=function(t,e,n){var i=[];if("replaceAll"===n)return i;for(var r=0;r<t.length;r++){var o=t[r];o&&null!=o.id&&e.set(o.id,r),i.push({existing:"replaceMerge"===n||So(o)?null:o,newOption:null,keyInfo:null,brandNew:null})}return i}(t,a,n);return(i||r)&&function(t,e,n,i){R(i,(function(r,o){if(r&&null!=r.id){var a=xo(r.id),s=n.get(a);if(null!=s){var l=t[s];at(!l.newOption,'Duplicated option on id "'+a+'".'),l.newOption=r,l.existing=e[s],i[o]=null}}}))}(u,t,a,e),i&&function(t,e){R(e,(function(n,i){if(n&&null!=n.name)for(var r=0;r<t.length;r++){var o=t[r].existing;if(!t[r].newOption&&o&&(null==o.id||null==n.id)&&!So(n)&&!So(o)&&_o("name",o,n))return t[r].newOption=n,void(e[i]=null)}}))}(u,e),i||r?function(t,e,n){R(e,(function(e){if(e){for(var i,r=0;(i=t[r])&&(i.newOption||So(i.existing)||i.existing&&null!=e.id&&!_o("id",e,i.existing));)r++;i?(i.newOption=e,i.brandNew=n):t.push({newOption:e,brandNew:n,existing:null,keyInfo:null}),r++}}))}(u,e,r):o&&function(t,e){R(e,(function(e){t.push({newOption:e,brandNew:!0,existing:null,keyInfo:null})}))}(u,e),s=u,l=dt(),R(s,(function(t){var e=t.existing;e&&l.set(e.id,t)})),R(s,(function(t){var e=t.newOption;at(!e||null==e.id||!l.get(e.id)||l.get(e.id)===t,"id duplicates: "+(e&&e.id)),e&&null!=e.id&&l.set(e.id,t),!t.keyInfo&&(t.keyInfo={})})),R(s,(function(t,e){var n=t.existing,i=t.newOption,r=t.keyInfo;if(q(i)){if(r.name=null!=i.name?xo(i.name):n?n.name:co+e,n)r.id=xo(n.id);else if(null!=i.id)r.id=xo(i.id);else{var o=0;do{r.id="\0"+r.name+"\0"+o++}while(l.get(r.id))}l.set(r.id,t)}})),u}function _o(t,e,n){var i=bo(e[t],null),r=bo(n[t],null);return null!=i&&null!=r&&i===r}function xo(t){return bo(t,"")}function bo(t,e){return null==t?e:U(t)?t:Y(t)||X(t)?t+"":e}function wo(t){var e=t.name;return!(!e||!e.indexOf(co))}function So(t){return t&&null!=t.id&&0===xo(t.id).indexOf("\0_ec_\0")}function Mo(t,e){return null!=e.dataIndexInside?e.dataIndexInside:null!=e.dataIndex?H(e.dataIndex)?N(e.dataIndex,(function(e){return t.indexOfRawIndex(e)})):t.indexOfRawIndex(e.dataIndex):null!=e.name?H(e.name)?N(e.name,(function(e){return t.indexOfName(e)})):t.indexOfName(e.name):void 0}function To(){var t="__ec_inner_"+ko++;return function(e){return e[t]||(e[t]={})}}var ko=Math.round(9*Math.random());function Co(t,e,n){var i=Do(e,n),r=i.mainTypeSpecified,o=i.queryOptionMap,a=i.others,s=n?n.defaultMainType:null;return!r&&s&&o.set(s,{}),o.each((function(e,i){var r=Ao(t,i,e,{useDefault:s===i,enableAll:!n||null==n.enableAll||n.enableAll,enableNone:!n||null==n.enableNone||n.enableNone});a[i+"Models"]=r.models,a[i+"Model"]=r.models[0]})),a}function Do(t,e){var n;if(U(t)){var i={};i[t+"Index"]=0,n=i}else n=t;var r=dt(),o={},a=!1;return R(n,(function(t,n){if("dataIndex"!==n&&"dataIndexInside"!==n){var i=n.match(/^(\w+)(Index|Id|Name)$/)||[],s=i[1],l=(i[2]||"").toLowerCase();if(s&&l&&!(e&&e.includeMainTypes&&A(e.includeMainTypes,s)<0))a=a||!!s,(r.get(s)||r.set(s,{}))[l]=t}else o[n]=t})),{mainTypeSpecified:a,queryOptionMap:r,others:o}}var Io={useDefault:!0,enableAll:!1,enableNone:!1};function Ao(t,e,n,i){i=i||Io;var r=n.index,o=n.id,a=n.name,s={models:null,specified:null!=r||null!=o||null!=a};if(!s.specified){var l=void 0;return s.models=i.useDefault&&(l=t.getComponent(e))?[l]:[],s}if("none"===r||!1===r){if(i.enableNone)return s.models=[],s;r=-1}return"all"===r&&(r=i.enableAll?o=a=null:-1),s.models=t.queryComponents({mainType:e,index:r,id:o,name:a}),s}function Lo(t,e,n){t.setAttribute?t.setAttribute(e,n):t[e]=n}var Po="___EC__COMPONENT__CONTAINER___",Oo="___EC__EXTENDED_CLASS___";function Ro(t){var e={main:"",sub:""};if(t){var n=t.split(".");e.main=n[0]||"",e.sub=n[1]||""}return e}function No(t,e){t.$constructor=t,t.extend=function(t){var e,i,r=this;return G(i=r)&&/^class\s/.test(Function.prototype.toString.call(i))?e=function(t){function e(){return t.apply(this,arguments)||this}return n(e,t),e}(r):(e=function(){(t.$constructor||r).apply(this,arguments)},L(e,this)),C(e.prototype,t),e[Oo]=!0,e.extend=this.extend,e.superCall=zo,e.superApply=Fo,e.superClass=r,e}}function Bo(t,e){t.extend=e.extend}var Eo=Math.round(10*Math.random());function zo(t,e){for(var n=[],i=2;i<arguments.length;i++)n[i-2]=arguments[i];return this.superClass.prototype[e].apply(t,n)}function Fo(t,e,n){return this.superClass.prototype[e].apply(t,n)}function Vo(t){var e={};t.registerClass=function(t){var n,i=t.type||t.prototype.type;if(i){at(/^[a-zA-Z0-9_]+([.][a-zA-Z0-9_]+)?$/.test(n=i),'componentType "'+n+'" illegal'),t.prototype.type=i;var r=Ro(i);if(r.sub){if(r.sub!==Po){var o=function(t){var n=e[t.main];n&&n[Po]||((n=e[t.main]={})[Po]=!0);return n}(r);o[r.sub]=t}}else e[r.main]=t}return t},t.getClass=function(t,n,i){var r=e[t];if(r&&r[Po]&&(r=n?r[n]:null),i&&!r)throw new Error(n?"Component "+t+"."+(n||"")+" is used but not imported.":t+".type should be specified.");return r},t.getClassesByMainType=function(t){var n=Ro(t),i=[],r=e[n.main];return r&&r[Po]?R(r,(function(t,e){e!==Po&&i.push(t)})):i.push(r),i},t.hasClass=function(t){var n=Ro(t);return!!e[n.main]},t.getAllClassMainTypes=function(){var t=[];return R(e,(function(e,n){t.push(n)})),t},t.hasSubTypes=function(t){var n=Ro(t),i=e[n.main];return i&&i[Po]}}function Wo(t,e){for(var n=0;n<t.length;n++)t[n][1]||(t[n][1]=t[n][0]);return e=e||!1,function(n,i,r){for(var o={},a=0;a<t.length;a++){var s=t[a][1];if(!(i&&A(i,s)>=0||r&&A(r,s)<0)){var l=n.getShallow(s,e);null!=l&&(o[t[a][0]]=l)}}return o}}var Ho=Wo([["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["opacity"],["shadowColor"]]),Go=function(){function t(){}return t.prototype.getAreaStyle=function(t,e){return Ho(this,t,e)},t}(),Uo=new Rn(50);function Xo(t){if("string"==typeof t){var e=Uo.get(t);return e&&e.image}return t}function Yo(t,e,n,i,r){if(t){if("string"==typeof t){if(e&&e.__zrImageSrc===t||!n)return e;var o=Uo.get(t),a={hostEl:n,cb:i,cbPayload:r};return o?!Zo(e=o.image)&&o.pending.push(a):((e=u.loadImage(t,qo,qo)).__zrImageSrc=t,Uo.put(t,e.__cachedImgObj={image:e,pending:[a]})),e}return t}return e}function qo(){var t=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var e=0;e<t.pending.length;e++){var n=t.pending[e],i=n.cb;i&&i(this,n.cbPayload),n.hostEl.dirty()}t.pending.length=0}function Zo(t){return t&&t.width&&t.height}var jo=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g;function Ko(t,e,n,i,r,o){if(!n)return t.text="",void(t.isTruncated=!1);var a=(e+"").split("\n");o=$o(n,i,r,o);for(var s=!1,l={},u=0,h=a.length;u<h;u++)Qo(l,a[u],o),a[u]=l.textLine,s=s||l.isTruncated;t.text=a.join("\n"),t.isTruncated=s}function $o(t,e,n,i){var r=C({},i=i||{});n=nt(n,"..."),r.maxIterations=nt(i.maxIterations,2);var o=r.minChar=nt(i.minChar,0),a=r.fontMeasureInfo=nr(e),s=a.asciiCharWidth;r.placeholder=nt(i.placeholder,"");for(var l=t=Math.max(0,t-1),u=0;u<o&&l>=s;u++)l-=s;var h=ar(a,n);return h>l&&(n="",h=0),l=t-h,r.ellipsis=n,r.ellipsisWidth=h,r.contentWidth=l,r.containerWidth=t,r}function Qo(t,e,n){var i=n.containerWidth,r=n.contentWidth,o=n.fontMeasureInfo;if(!i)return t.textLine="",void(t.isTruncated=!1);var a=ar(o,e);if(a<=i)return t.textLine=e,void(t.isTruncated=!1);for(var s=0;;s++){if(a<=r||s>=n.maxIterations){e+=n.ellipsis;break}var l=0===s?Jo(e,r,o):a>0?Math.floor(e.length*r/a):0;a=ar(o,e=e.substr(0,l))}""===e&&(e=n.placeholder),t.textLine=e,t.isTruncated=!0}function Jo(t,e,n){for(var i=0,r=0,o=t.length;r<o&&i<e;r++)i+=or(n,t.charCodeAt(r));return r}var ta=function(){},ea=function(t){this.tokens=[],t&&(this.tokens=t)},na=function(){this.width=0,this.height=0,this.contentWidth=0,this.contentHeight=0,this.outerWidth=0,this.outerHeight=0,this.lines=[],this.isTruncated=!1};function ia(t,e,n,i,r){var o,a,s=""===e,l=r&&n.rich[r]||{},u=t.lines,h=l.font||n.font,c=!1;if(i){var p=l.padding,f=p?p[1]+p[3]:0;if(null!=l.width&&"auto"!==l.width){var d=pr(l.width,i.width)+f;u.length>0&&d+i.accumWidth>i.width&&(o=e.split("\n"),c=!0),i.accumWidth=d}else{var g=aa(e,h,i.width,i.breakAll,i.accumWidth);i.accumWidth=g.accumWidth+f,a=g.linesWidths,o=g.lines}}o||(o=e.split("\n"));for(var v=nr(h),y=0;y<o.length;y++){var m=o[y],_=new ta;if(_.styleName=r,_.text=m,_.isLineHolder=!m&&!s,"number"==typeof l.width?_.width=l.width:_.width=a?a[y]:ar(v,m),y||c)u.push(new ea([_]));else{var x=(u[u.length-1]||(u[0]=new ea)).tokens,b=x.length;1===b&&x[0].isLineHolder?x[0]=_:(m||!b||s)&&x.push(_)}}}var ra=B(",&?/;] ".split(""),(function(t,e){return t[e]=!0,t}),{});function oa(t){return!function(t){var e=t.charCodeAt(0);return e>=32&&e<=591||e>=880&&e<=4351||e>=4608&&e<=5119||e>=7680&&e<=8303}(t)||!!ra[t]}function aa(t,e,n,i,r){for(var o=[],a=[],s="",l="",u=0,h=0,c=nr(e),p=0;p<t.length;p++){var f=t.charAt(p);if("\n"!==f){var d=or(c,f.charCodeAt(0)),g=!i&&!oa(f);(o.length?h+d>n:r+h+d>n)?h?(s||l)&&(g?(s||(s=l,l="",h=u=0),o.push(s),a.push(h-u),l+=f,s="",h=u+=d):(l&&(s+=l,l="",u=0),o.push(s),a.push(h),s=f,h=d)):g?(o.push(l),a.push(u),l=f,u=d):(o.push(f),a.push(d)):(h+=d,g?(l+=f,u+=d):(l&&(s+=l,l="",u=0),s+=f))}else l&&(s+=l,h+=u),o.push(s),a.push(h),s="",l="",u=0,h=0}return l&&(s+=l),s&&(o.push(s),a.push(h)),1===o.length&&(h+=r),{accumWidth:h,lines:o,linesWidths:a}}function sa(t,e,n,i,r,o){if(t.baseX=n,t.baseY=i,t.outerWidth=t.outerHeight=null,e){var a=2*e.width,s=2*e.height;Oe.set(la,ur(n,a,r),hr(i,s,o),a,s),Oe.intersect(e,la,null,ua);var l=ua.outIntersectRect;t.outerWidth=l.width,t.outerHeight=l.height,t.baseX=ur(l.x,l.width,r,!0),t.baseY=hr(l.y,l.height,o,!0)}}var la=new Oe(0,0,0,0),ua={outIntersectRect:{},clamp:!0};function ha(t){return null!=t?t+="":t=""}function ca(t,e,n,i){var r=new Oe(ur(t.x||0,e,t.textAlign),hr(t.y||0,n,t.textBaseline),e,n),o=null!=i?i:pa(t)?t.lineWidth:0;return o>0&&(r.x-=o/2,r.y-=o/2,r.width+=o,r.height+=o),r}function pa(t){var e=t.stroke;return null!=e&&"none"!==e&&t.lineWidth>0}var fa="__zr_style_"+Math.round(10*Math.random()),da={shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,shadowColor:"#000",opacity:1,blend:"source-over"},ga={style:{shadowBlur:!0,shadowOffsetX:!0,shadowOffsetY:!0,shadowColor:!0,opacity:!0}};da[fa]=!0;var va=["z","z2","invisible"],ya=["invisible"],ma=function(t){function e(e){return t.call(this,e)||this}var i;return n(e,t),e.prototype._init=function(e){for(var n=F(e),i=0;i<n.length;i++){var r=n[i];"style"===r?this.useStyle(e[r]):t.prototype.attrKV.call(this,r,e[r])}this.style||this.useStyle({})},e.prototype.beforeBrush=function(){},e.prototype.afterBrush=function(){},e.prototype.innerBeforeBrush=function(){},e.prototype.innerAfterBrush=function(){},e.prototype.shouldBePainted=function(t,e,n,i){var r=this.transform;if(this.ignore||this.invisible||0===this.style.opacity||this.culling&&function(t,e,n){_a.copy(t.getBoundingRect()),t.transform&&_a.applyTransform(t.transform);return xa.width=e,xa.height=n,!_a.intersect(xa)}(this,t,e)||r&&!r[0]&&!r[3])return!1;if(n&&this.__clipPaths&&this.__clipPaths.length)for(var o=0;o<this.__clipPaths.length;++o)if(this.__clipPaths[o].isZeroArea())return!1;if(i&&this.parent)for(var a=this.parent;a;){if(a.ignore)return!1;a=a.parent}return!0},e.prototype.contain=function(t,e){return this.rectContain(t,e)},e.prototype.traverse=function(t,e){t.call(e,this)},e.prototype.rectContain=function(t,e){var n=this.transformCoordToLocal(t,e);return this.getBoundingRect().contain(n[0],n[1])},e.prototype.getPaintRect=function(){var t=this._paintRect;if(!this._paintRect||this.__dirty){var e=this.transform,n=this.getBoundingRect(),i=this.style,r=i.shadowBlur||0,o=i.shadowOffsetX||0,a=i.shadowOffsetY||0;t=this._paintRect||(this._paintRect=new Oe(0,0,0,0)),e?Oe.applyTransform(t,n,e):t.copy(n),(r||o||a)&&(t.width+=2*r+Math.abs(o),t.height+=2*r+Math.abs(a),t.x=Math.min(t.x,t.x+o-r),t.y=Math.min(t.y,t.y+a-r));var s=this.dirtyRectTolerance;t.isZero()||(t.x=Math.floor(t.x-s),t.y=Math.floor(t.y-s),t.width=Math.ceil(t.width+1+2*s),t.height=Math.ceil(t.height+1+2*s))}return t},e.prototype.setPrevPaintRect=function(t){t?(this._prevPaintRect=this._prevPaintRect||new Oe(0,0,0,0),this._prevPaintRect.copy(t)):this._prevPaintRect=null},e.prototype.getPrevPaintRect=function(){return this._prevPaintRect},e.prototype.animateStyle=function(t){return this.animate("style",t)},e.prototype.updateDuringAnimation=function(t){"style"===t?this.dirtyStyle():this.markRedraw()},e.prototype.attrKV=function(e,n){"style"!==e?t.prototype.attrKV.call(this,e,n):this.style?this.setStyle(n):this.useStyle(n)},e.prototype.setStyle=function(t,e){return"string"==typeof t?this.style[t]=e:C(this.style,t),this.dirtyStyle(),this},e.prototype.dirtyStyle=function(t){t||this.markRedraw(),this.__dirty|=2,this._rect&&(this._rect=null)},e.prototype.dirty=function(){this.dirtyStyle()},e.prototype.styleChanged=function(){return!!(2&this.__dirty)},e.prototype.styleUpdated=function(){this.__dirty&=-3},e.prototype.createStyle=function(t){return vt(da,t)},e.prototype.useStyle=function(t){t[fa]||(t=this.createStyle(t)),this.__inHover?this.__hoverStyle=t:this.style=t,this.dirtyStyle()},e.prototype.isStyleObject=function(t){return t[fa]},e.prototype._innerSaveToNormal=function(e){t.prototype._innerSaveToNormal.call(this,e);var n=this._normalState;e.style&&!n.style&&(n.style=this._mergeStyle(this.createStyle(),this.style)),this._savePrimaryToNormal(e,n,va)},e.prototype._applyStateObj=function(e,n,i,r,o,a){t.prototype._applyStateObj.call(this,e,n,i,r,o,a);var s,l=!(n&&r);if(n&&n.style?o?r?s=n.style:(s=this._mergeStyle(this.createStyle(),i.style),this._mergeStyle(s,n.style)):(s=this._mergeStyle(this.createStyle(),r?this.style:i.style),this._mergeStyle(s,n.style)):l&&(s=i.style),s)if(o){var u=this.style;if(this.style=this.createStyle(l?{}:u),l)for(var h=F(u),c=0;c<h.length;c++){(f=h[c])in s&&(s[f]=s[f],this.style[f]=u[f])}var p=F(s);for(c=0;c<p.length;c++){var f=p[c];this.style[f]=this.style[f]}this._transitionState(e,{style:s},a,this.getAnimationStyleProps())}else this.useStyle(s);var d=this.__inHover?ya:va;for(c=0;c<d.length;c++){f=d[c];n&&null!=n[f]?this[f]=n[f]:l&&null!=i[f]&&(this[f]=i[f])}},e.prototype._mergeStates=function(e){for(var n,i=t.prototype._mergeStates.call(this,e),r=0;r<e.length;r++){var o=e[r];o.style&&(n=n||{},this._mergeStyle(n,o.style))}return n&&(i.style=n),i},e.prototype._mergeStyle=function(t,e){return C(t,e),t},e.prototype.getAnimationStyleProps=function(){return ga},e.initDefaultProps=((i=e.prototype).type="displayable",i.invisible=!1,i.z=0,i.z2=0,i.zlevel=0,i.culling=!1,i.cursor="pointer",i.rectHover=!1,i.incremental=!1,i._rect=null,i.dirtyRectTolerance=0,void(i.__dirty=3)),e}(xr),_a=new Oe(0,0,0,0),xa=new Oe(0,0,0,0);var ba=Math.min,wa=Math.max,Sa=Math.sin,Ma=Math.cos,Ta=2*Math.PI,ka=St(),Ca=St(),Da=St();function Ia(t,e,n,i,r,o){r[0]=ba(t,n),r[1]=ba(e,i),o[0]=wa(t,n),o[1]=wa(e,i)}var Aa=[],La=[];function Pa(t,e,n,i,r,o,a,s,l,u){var h=bn,c=mn,p=h(t,n,r,a,Aa);l[0]=1/0,l[1]=1/0,u[0]=-1/0,u[1]=-1/0;for(var f=0;f<p;f++){var d=c(t,n,r,a,Aa[f]);l[0]=ba(d,l[0]),u[0]=wa(d,u[0])}p=h(e,i,o,s,La);for(f=0;f<p;f++){var g=c(e,i,o,s,La[f]);l[1]=ba(g,l[1]),u[1]=wa(g,u[1])}l[0]=ba(t,l[0]),u[0]=wa(t,u[0]),l[0]=ba(a,l[0]),u[0]=wa(a,u[0]),l[1]=ba(e,l[1]),u[1]=wa(e,u[1]),l[1]=ba(s,l[1]),u[1]=wa(s,u[1])}function Oa(t,e,n,i,r,o,a,s){var l=kn,u=Mn,h=wa(ba(l(t,n,r),1),0),c=wa(ba(l(e,i,o),1),0),p=u(t,n,r,h),f=u(e,i,o,c);a[0]=ba(t,r,p),a[1]=ba(e,o,f),s[0]=wa(t,r,p),s[1]=wa(e,o,f)}function Ra(t,e,n,i,r,o,a,s,l){var u=Ft,h=Vt,c=Math.abs(r-o);if(c%Ta<1e-4&&c>1e-4)return s[0]=t-n,s[1]=e-i,l[0]=t+n,void(l[1]=e+i);if(ka[0]=Ma(r)*n+t,ka[1]=Sa(r)*i+e,Ca[0]=Ma(o)*n+t,Ca[1]=Sa(o)*i+e,u(s,ka,Ca),h(l,ka,Ca),(r%=Ta)<0&&(r+=Ta),(o%=Ta)<0&&(o+=Ta),r>o&&!a?o+=Ta:r<o&&a&&(r+=Ta),a){var p=o;o=r,r=p}for(var f=0;f<o;f+=Math.PI/2)f>r&&(Da[0]=Ma(f)*n+t,Da[1]=Sa(f)*i+e,u(s,Da,s),h(l,Da,l))}var Na={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},Ba=[],Ea=[],za=[],Fa=[],Va=[],Wa=[],Ha=Math.min,Ga=Math.max,Ua=Math.cos,Xa=Math.sin,Ya=Math.abs,qa=Math.PI,Za=2*qa,ja="undefined"!=typeof Float32Array,Ka=[];function $a(t){return Math.round(t/qa*1e8)/1e8%2*qa}function Qa(t,e){var n=$a(t[0]);n<0&&(n+=Za);var i=n-t[0],r=t[1];r+=i,!e&&r-n>=Za?r=n+Za:e&&n-r>=Za?r=n-Za:!e&&n>r?r=n+(Za-$a(n-r)):e&&n<r&&(r=n-(Za-$a(r-n))),t[0]=n,t[1]=r}var Ja=function(){function t(t){this.dpr=1,this._xi=0,this._yi=0,this._x0=0,this._y0=0,this._len=0,t&&(this._saveData=!1),this._saveData&&(this.data=[])}var e;return t.prototype.increaseVersion=function(){this._version++},t.prototype.getVersion=function(){return this._version},t.prototype.setScale=function(t,e,n){(n=n||0)>0&&(this._ux=Ya(n/Hi/t)||0,this._uy=Ya(n/Hi/e)||0)},t.prototype.setDPR=function(t){this.dpr=t},t.prototype.setContext=function(t){this._ctx=t},t.prototype.getContext=function(){return this._ctx},t.prototype.beginPath=function(){return this._ctx&&this._ctx.beginPath(),this.reset(),this},t.prototype.reset=function(){this._saveData&&(this._len=0),this._pathSegLen&&(this._pathSegLen=null,this._pathLen=0),this._version++},t.prototype.moveTo=function(t,e){return this._drawPendingPt(),this.addData(Na.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},t.prototype.lineTo=function(t,e){var n=Ya(t-this._xi),i=Ya(e-this._yi),r=n>this._ux||i>this._uy;if(this.addData(Na.L,t,e),this._ctx&&r&&this._ctx.lineTo(t,e),r)this._xi=t,this._yi=e,this._pendingPtDist=0;else{var o=n*n+i*i;o>this._pendingPtDist&&(this._pendingPtX=t,this._pendingPtY=e,this._pendingPtDist=o)}return this},t.prototype.bezierCurveTo=function(t,e,n,i,r,o){return this._drawPendingPt(),this.addData(Na.C,t,e,n,i,r,o),this._ctx&&this._ctx.bezierCurveTo(t,e,n,i,r,o),this._xi=r,this._yi=o,this},t.prototype.quadraticCurveTo=function(t,e,n,i){return this._drawPendingPt(),this.addData(Na.Q,t,e,n,i),this._ctx&&this._ctx.quadraticCurveTo(t,e,n,i),this._xi=n,this._yi=i,this},t.prototype.arc=function(t,e,n,i,r,o){this._drawPendingPt(),Ka[0]=i,Ka[1]=r,Qa(Ka,o),i=Ka[0];var a=(r=Ka[1])-i;return this.addData(Na.A,t,e,n,n,i,a,0,o?0:1),this._ctx&&this._ctx.arc(t,e,n,i,r,o),this._xi=Ua(r)*n+t,this._yi=Xa(r)*n+e,this},t.prototype.arcTo=function(t,e,n,i,r){return this._drawPendingPt(),this._ctx&&this._ctx.arcTo(t,e,n,i,r),this},t.prototype.rect=function(t,e,n,i){return this._drawPendingPt(),this._ctx&&this._ctx.rect(t,e,n,i),this.addData(Na.R,t,e,n,i),this},t.prototype.closePath=function(){this._drawPendingPt(),this.addData(Na.Z);var t=this._ctx,e=this._x0,n=this._y0;return t&&t.closePath(),this._xi=e,this._yi=n,this},t.prototype.fill=function(t){t&&t.fill(),this.toStatic()},t.prototype.stroke=function(t){t&&t.stroke(),this.toStatic()},t.prototype.len=function(){return this._len},t.prototype.setData=function(t){if(this._saveData){var e=t.length;this.data&&this.data.length===e||!ja||(this.data=new Float32Array(e));for(var n=0;n<e;n++)this.data[n]=t[n];this._len=e}},t.prototype.appendPath=function(t){if(this._saveData){t instanceof Array||(t=[t]);for(var e=t.length,n=0,i=this._len,r=0;r<e;r++)n+=t[r].len();var o=this.data;if(ja&&(o instanceof Float32Array||!o)&&(this.data=new Float32Array(i+n),i>0&&o))for(var a=0;a<i;a++)this.data[a]=o[a];for(r=0;r<e;r++){var s=t[r].data;for(a=0;a<s.length;a++)this.data[i++]=s[a]}this._len=i}},t.prototype.addData=function(t,e,n,i,r,o,a,s,l){if(this._saveData){var u=this.data;this._len+arguments.length>u.length&&(this._expandData(),u=this.data);for(var h=0;h<arguments.length;h++)u[this._len++]=arguments[h]}},t.prototype._drawPendingPt=function(){this._pendingPtDist>0&&(this._ctx&&this._ctx.lineTo(this._pendingPtX,this._pendingPtY),this._pendingPtDist=0)},t.prototype._expandData=function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},t.prototype.toStatic=function(){if(this._saveData){this._drawPendingPt();var t=this.data;t instanceof Array&&(t.length=this._len,ja&&this._len>11&&(this.data=new Float32Array(t)))}},t.prototype.getBoundingRect=function(){za[0]=za[1]=Va[0]=Va[1]=Number.MAX_VALUE,Fa[0]=Fa[1]=Wa[0]=Wa[1]=-Number.MAX_VALUE;var t,e=this.data,n=0,i=0,r=0,o=0;for(t=0;t<this._len;){var a=e[t++],s=1===t;switch(s&&(r=n=e[t],o=i=e[t+1]),a){case Na.M:n=r=e[t++],i=o=e[t++],Va[0]=r,Va[1]=o,Wa[0]=r,Wa[1]=o;break;case Na.L:Ia(n,i,e[t],e[t+1],Va,Wa),n=e[t++],i=e[t++];break;case Na.C:Pa(n,i,e[t++],e[t++],e[t++],e[t++],e[t],e[t+1],Va,Wa),n=e[t++],i=e[t++];break;case Na.Q:Oa(n,i,e[t++],e[t++],e[t],e[t+1],Va,Wa),n=e[t++],i=e[t++];break;case Na.A:var l=e[t++],u=e[t++],h=e[t++],c=e[t++],p=e[t++],f=e[t++]+p;t+=1;var d=!e[t++];s&&(r=Ua(p)*h+l,o=Xa(p)*c+u),Ra(l,u,h,c,p,f,d,Va,Wa),n=Ua(f)*h+l,i=Xa(f)*c+u;break;case Na.R:Ia(r=n=e[t++],o=i=e[t++],r+e[t++],o+e[t++],Va,Wa);break;case Na.Z:n=r,i=o}Ft(za,za,Va),Vt(Fa,Fa,Wa)}return 0===t&&(za[0]=za[1]=Fa[0]=Fa[1]=0),new Oe(za[0],za[1],Fa[0]-za[0],Fa[1]-za[1])},t.prototype._calculateLength=function(){var t=this.data,e=this._len,n=this._ux,i=this._uy,r=0,o=0,a=0,s=0;this._pathSegLen||(this._pathSegLen=[]);for(var l=this._pathSegLen,u=0,h=0,c=0;c<e;){var p=t[c++],f=1===c;f&&(a=r=t[c],s=o=t[c+1]);var d=-1;switch(p){case Na.M:r=a=t[c++],o=s=t[c++];break;case Na.L:var g=t[c++],v=(_=t[c++])-o;(Ya(I=g-r)>n||Ya(v)>i||c===e-1)&&(d=Math.sqrt(I*I+v*v),r=g,o=_);break;case Na.C:var y=t[c++],m=t[c++],_=(g=t[c++],t[c++]),x=t[c++],b=t[c++];d=Sn(r,o,y,m,g,_,x,b,10),r=x,o=b;break;case Na.Q:d=Dn(r,o,y=t[c++],m=t[c++],g=t[c++],_=t[c++],10),r=g,o=_;break;case Na.A:var w=t[c++],S=t[c++],M=t[c++],T=t[c++],k=t[c++],C=t[c++],D=C+k;c+=1,f&&(a=Ua(k)*M+w,s=Xa(k)*T+S),d=Ga(M,T)*Ha(Za,Math.abs(C)),r=Ua(D)*M+w,o=Xa(D)*T+S;break;case Na.R:a=r=t[c++],s=o=t[c++],d=2*t[c++]+2*t[c++];break;case Na.Z:var I=a-r;v=s-o;d=Math.sqrt(I*I+v*v),r=a,o=s}d>=0&&(l[h++]=d,u+=d)}return this._pathLen=u,u},t.prototype.rebuildPath=function(t,e){var n,i,r,o,a,s,l,u,h,c,p=this.data,f=this._ux,d=this._uy,g=this._len,v=e<1,y=0,m=0,_=0;if(!v||(this._pathSegLen||this._calculateLength(),l=this._pathSegLen,u=e*this._pathLen))t:for(var x=0;x<g;){var b=p[x++],w=1===x;switch(w&&(n=r=p[x],i=o=p[x+1]),b!==Na.L&&_>0&&(t.lineTo(h,c),_=0),b){case Na.M:n=r=p[x++],i=o=p[x++],t.moveTo(r,o);break;case Na.L:a=p[x++],s=p[x++];var S=Ya(a-r),M=Ya(s-o);if(S>f||M>d){if(v){if(y+(Z=l[m++])>u){var T=(u-y)/Z;t.lineTo(r*(1-T)+a*T,o*(1-T)+s*T);break t}y+=Z}t.lineTo(a,s),r=a,o=s,_=0}else{var k=S*S+M*M;k>_&&(h=a,c=s,_=k)}break;case Na.C:var C=p[x++],D=p[x++],I=p[x++],A=p[x++],L=p[x++],P=p[x++];if(v){if(y+(Z=l[m++])>u){wn(r,C,I,L,T=(u-y)/Z,Ba),wn(o,D,A,P,T,Ea),t.bezierCurveTo(Ba[1],Ea[1],Ba[2],Ea[2],Ba[3],Ea[3]);break t}y+=Z}t.bezierCurveTo(C,D,I,A,L,P),r=L,o=P;break;case Na.Q:C=p[x++],D=p[x++],I=p[x++],A=p[x++];if(v){if(y+(Z=l[m++])>u){Cn(r,C,I,T=(u-y)/Z,Ba),Cn(o,D,A,T,Ea),t.quadraticCurveTo(Ba[1],Ea[1],Ba[2],Ea[2]);break t}y+=Z}t.quadraticCurveTo(C,D,I,A),r=I,o=A;break;case Na.A:var O=p[x++],R=p[x++],N=p[x++],B=p[x++],E=p[x++],z=p[x++],F=p[x++],V=!p[x++],W=N>B?N:B,H=Ya(N-B)>.001,G=E+z,U=!1;if(v)y+(Z=l[m++])>u&&(G=E+z*(u-y)/Z,U=!0),y+=Z;if(H&&t.ellipse?t.ellipse(O,R,N,B,F,E,G,V):t.arc(O,R,W,E,G,V),U)break t;w&&(n=Ua(E)*N+O,i=Xa(E)*B+R),r=Ua(G)*N+O,o=Xa(G)*B+R;break;case Na.R:n=r=p[x],i=o=p[x+1],a=p[x++],s=p[x++];var X=p[x++],Y=p[x++];if(v){if(y+(Z=l[m++])>u){var q=u-y;t.moveTo(a,s),t.lineTo(a+Ha(q,X),s),(q-=X)>0&&t.lineTo(a+X,s+Ha(q,Y)),(q-=Y)>0&&t.lineTo(a+Ga(X-q,0),s+Y),(q-=X)>0&&t.lineTo(a,s+Ga(Y-q,0));break t}y+=Z}t.rect(a,s,X,Y);break;case Na.Z:if(v){var Z;if(y+(Z=l[m++])>u){T=(u-y)/Z;t.lineTo(r*(1-T)+n*T,o*(1-T)+i*T);break t}y+=Z}t.closePath(),r=n,o=i}}},t.prototype.clone=function(){var e=new t,n=this.data;return e.data=n.slice?n.slice():Array.prototype.slice.call(n),e._len=this._len,e},t.prototype.canSave=function(){return!!this._saveData},t.CMD=Na,t.initDefaultProps=((e=t.prototype)._saveData=!0,e._ux=0,e._uy=0,e._pendingPtDist=0,void(e._version=0)),t}();function ts(t,e,n,i,r,o,a){if(0===r)return!1;var s=r,l=0;if(a>e+s&&a>i+s||a<e-s&&a<i-s||o>t+s&&o>n+s||o<t-s&&o<n-s)return!1;if(t===n)return Math.abs(o-t)<=s/2;var u=(l=(e-i)/(t-n))*o-a+(t*i-n*e)/(t-n);return u*u/(l*l+1)<=s/2*s/2}function es(t,e,n,i,r,o,a,s,l,u,h){if(0===l)return!1;var c=l;if(h>e+c&&h>i+c&&h>o+c&&h>s+c||h<e-c&&h<i-c&&h<o-c&&h<s-c||u>t+c&&u>n+c&&u>r+c&&u>a+c||u<t-c&&u<n-c&&u<r-c&&u<a-c)return!1;var p=function(t,e,n,i,r,o,a,s,l,u,h){var c,p,f,d,g,v=.005,y=1/0;fn[0]=l,fn[1]=u;for(var m=0;m<1;m+=.05)dn[0]=mn(t,n,r,a,m),dn[1]=mn(e,i,o,s,m),(d=Bt(fn,dn))<y&&(c=m,y=d);y=1/0;for(var _=0;_<32&&!(v<hn);_++)p=c-v,f=c+v,dn[0]=mn(t,n,r,a,p),dn[1]=mn(e,i,o,s,p),d=Bt(dn,fn),p>=0&&d<y?(c=p,y=d):(gn[0]=mn(t,n,r,a,f),gn[1]=mn(e,i,o,s,f),g=Bt(gn,fn),f<=1&&g<y?(c=f,y=g):v*=.5);return h&&(h[0]=mn(t,n,r,a,c),h[1]=mn(e,i,o,s,c)),ln(y)}(t,e,n,i,r,o,a,s,u,h,null);return p<=c/2}function ns(t,e,n,i,r,o,a,s,l){if(0===a)return!1;var u=a;if(l>e+u&&l>i+u&&l>o+u||l<e-u&&l<i-u&&l<o-u||s>t+u&&s>n+u&&s>r+u||s<t-u&&s<n-u&&s<r-u)return!1;var h=function(t,e,n,i,r,o,a,s,l){var u,h=.005,c=1/0;fn[0]=a,fn[1]=s;for(var p=0;p<1;p+=.05)dn[0]=Mn(t,n,r,p),dn[1]=Mn(e,i,o,p),(v=Bt(fn,dn))<c&&(u=p,c=v);c=1/0;for(var f=0;f<32&&!(h<hn);f++){var d=u-h,g=u+h;dn[0]=Mn(t,n,r,d),dn[1]=Mn(e,i,o,d);var v=Bt(dn,fn);if(d>=0&&v<c)u=d,c=v;else{gn[0]=Mn(t,n,r,g),gn[1]=Mn(e,i,o,g);var y=Bt(gn,fn);g<=1&&y<c?(u=g,c=y):h*=.5}}return l&&(l[0]=Mn(t,n,r,u),l[1]=Mn(e,i,o,u)),ln(c)}(t,e,n,i,r,o,s,l,null);return h<=u/2}var is=2*Math.PI;function rs(t){return(t%=is)<0&&(t+=is),t}var os=2*Math.PI;function as(t,e,n,i,r,o,a,s,l){if(0===a)return!1;var u=a;s-=t,l-=e;var h=Math.sqrt(s*s+l*l);if(h-u>n||h+u<n)return!1;if(Math.abs(i-r)%os<1e-4)return!0;if(o){var c=i;i=rs(r),r=rs(c)}else i=rs(i),r=rs(r);i>r&&(r+=os);var p=Math.atan2(l,s);return p<0&&(p+=os),p>=i&&p<=r||p+os>=i&&p+os<=r}function ss(t,e,n,i,r,o){if(o>e&&o>i||o<e&&o<i)return 0;if(i===e)return 0;var a=(o-e)/(i-e),s=i<e?1:-1;1!==a&&0!==a||(s=i<e?.5:-.5);var l=a*(n-t)+t;return l===r?1/0:l>r?s:0}var ls=Ja.CMD,us=2*Math.PI;var hs=[-1,-1,-1],cs=[-1,-1];function ps(t,e,n,i,r,o,a,s,l,u){if(u>e&&u>i&&u>o&&u>s||u<e&&u<i&&u<o&&u<s)return 0;var h,c=xn(e,i,o,s,u,hs);if(0===c)return 0;for(var p=0,f=-1,d=void 0,g=void 0,v=0;v<c;v++){var y=hs[v],m=0===y||1===y?.5:1;mn(t,n,r,a,y)<l||(f<0&&(f=bn(e,i,o,s,cs),cs[1]<cs[0]&&f>1&&(h=void 0,h=cs[0],cs[0]=cs[1],cs[1]=h),d=mn(e,i,o,s,cs[0]),f>1&&(g=mn(e,i,o,s,cs[1]))),2===f?y<cs[0]?p+=d<e?m:-m:y<cs[1]?p+=g<d?m:-m:p+=s<g?m:-m:y<cs[0]?p+=d<e?m:-m:p+=s<d?m:-m)}return p}function fs(t,e,n,i,r,o,a,s){if(s>e&&s>i&&s>o||s<e&&s<i&&s<o)return 0;var l=function(t,e,n,i,r){var o=t-2*e+n,a=2*(e-t),s=t-i,l=0;if(vn(o))yn(a)&&(h=-s/a)>=0&&h<=1&&(r[l++]=h);else{var u=a*a-4*o*s;if(vn(u))(h=-a/(2*o))>=0&&h<=1&&(r[l++]=h);else if(u>0){var h,c=ln(u),p=(-a-c)/(2*o);(h=(-a+c)/(2*o))>=0&&h<=1&&(r[l++]=h),p>=0&&p<=1&&(r[l++]=p)}}return l}(e,i,o,s,hs);if(0===l)return 0;var u=kn(e,i,o);if(u>=0&&u<=1){for(var h=0,c=Mn(e,i,o,u),p=0;p<l;p++){var f=0===hs[p]||1===hs[p]?.5:1;Mn(t,n,r,hs[p])<a||(hs[p]<u?h+=c<e?f:-f:h+=o<c?f:-f)}return h}f=0===hs[0]||1===hs[0]?.5:1;return Mn(t,n,r,hs[0])<a?0:o<e?f:-f}function ds(t,e,n,i,r,o,a,s){if((s-=e)>n||s<-n)return 0;var l=Math.sqrt(n*n-s*s);hs[0]=-l,hs[1]=l;var u=Math.abs(i-r);if(u<1e-4)return 0;if(u>=us-1e-4){i=0,r=us;var h=o?1:-1;return a>=hs[0]+t&&a<=hs[1]+t?h:0}if(i>r){var c=i;i=r,r=c}i<0&&(i+=us,r+=us);for(var p=0,f=0;f<2;f++){var d=hs[f];if(d+t>a){var g=Math.atan2(s,d);h=o?1:-1;g<0&&(g=us+g),(g>=i&&g<=r||g+us>=i&&g+us<=r)&&(g>Math.PI/2&&g<1.5*Math.PI&&(h=-h),p+=h)}}return p}function gs(t,e,n,i,r){for(var o,a,s,l,u=t.data,h=t.len(),c=0,p=0,f=0,d=0,g=0,v=0;v<h;){var y=u[v++],m=1===v;switch(y===ls.M&&v>1&&(n||(c+=ss(p,f,d,g,i,r))),m&&(d=p=u[v],g=f=u[v+1]),y){case ls.M:p=d=u[v++],f=g=u[v++];break;case ls.L:if(n){if(ts(p,f,u[v],u[v+1],e,i,r))return!0}else c+=ss(p,f,u[v],u[v+1],i,r)||0;p=u[v++],f=u[v++];break;case ls.C:if(n){if(es(p,f,u[v++],u[v++],u[v++],u[v++],u[v],u[v+1],e,i,r))return!0}else c+=ps(p,f,u[v++],u[v++],u[v++],u[v++],u[v],u[v+1],i,r)||0;p=u[v++],f=u[v++];break;case ls.Q:if(n){if(ns(p,f,u[v++],u[v++],u[v],u[v+1],e,i,r))return!0}else c+=fs(p,f,u[v++],u[v++],u[v],u[v+1],i,r)||0;p=u[v++],f=u[v++];break;case ls.A:var _=u[v++],x=u[v++],b=u[v++],w=u[v++],S=u[v++],M=u[v++];v+=1;var T=!!(1-u[v++]);o=Math.cos(S)*b+_,a=Math.sin(S)*w+x,m?(d=o,g=a):c+=ss(p,f,o,a,i,r);var k=(i-_)*w/b+_;if(n){if(as(_,x,w,S,S+M,T,e,k,r))return!0}else c+=ds(_,x,w,S,S+M,T,k,r);p=Math.cos(S+M)*b+_,f=Math.sin(S+M)*w+x;break;case ls.R:if(d=p=u[v++],g=f=u[v++],o=d+u[v++],a=g+u[v++],n){if(ts(d,g,o,g,e,i,r)||ts(o,g,o,a,e,i,r)||ts(o,a,d,a,e,i,r)||ts(d,a,d,g,e,i,r))return!0}else c+=ss(o,g,o,a,i,r),c+=ss(d,a,d,g,i,r);break;case ls.Z:if(n){if(ts(p,f,d,g,e,i,r))return!0}else c+=ss(p,f,d,g,i,r);p=d,f=g}}return n||(s=f,l=g,Math.abs(s-l)<1e-4)||(c+=ss(p,f,d,g,i,r)||0),0!==c}var vs=D({fill:"#000",stroke:null,strokePercent:1,fillOpacity:1,strokeOpacity:1,lineDashOffset:0,lineWidth:1,lineCap:"butt",miterLimit:10,strokeNoScale:!1,strokeFirst:!1},da),ys={style:D({fill:!0,stroke:!0,strokePercent:!0,fillOpacity:!0,strokeOpacity:!0,lineDashOffset:!0,lineWidth:!0,miterLimit:!0},ga.style)},ms=tr.concat(["invisible","culling","z","z2","zlevel","parent"]),_s=function(t){function e(e){return t.call(this,e)||this}var i;return n(e,t),e.prototype.update=function(){var n=this;t.prototype.update.call(this);var i=this.style;if(i.decal){var r=this._decalEl=this._decalEl||new e;r.buildPath===e.prototype.buildPath&&(r.buildPath=function(t){n.buildPath(t,n.shape)}),r.silent=!0;var o=r.style;for(var a in i)o[a]!==i[a]&&(o[a]=i[a]);o.fill=i.fill?i.decal:null,o.decal=null,o.shadowColor=null,i.strokeFirst&&(o.stroke=null);for(var s=0;s<ms.length;++s)r[ms[s]]=this[ms[s]];r.__dirty|=1}else this._decalEl&&(this._decalEl=null)},e.prototype.getDecalElement=function(){return this._decalEl},e.prototype._init=function(e){var n=F(e);this.shape=this.getDefaultShape();var i=this.getDefaultStyle();i&&this.useStyle(i);for(var r=0;r<n.length;r++){var o=n[r],a=e[o];"style"===o?this.style?C(this.style,a):this.useStyle(a):"shape"===o?C(this.shape,a):t.prototype.attrKV.call(this,o,a)}this.style||this.useStyle({})},e.prototype.getDefaultStyle=function(){return null},e.prototype.getDefaultShape=function(){return{}},e.prototype.canBeInsideText=function(){return this.hasFill()},e.prototype.getInsideTextFill=function(){var t=this.style.fill;if("none"!==t){if(U(t)){var e=ni(t,0);return e>.5?Gi:e>.2?"#eee":Ui}if(t)return Ui}return Gi},e.prototype.getInsideTextStroke=function(t){var e=this.style.fill;if(U(e)){var n=this.__zr;if(!(!n||!n.isDarkMode())===ni(t,0)<.4)return e}},e.prototype.buildPath=function(t,e,n){},e.prototype.pathUpdated=function(){this.__dirty&=-5},e.prototype.getUpdatedPathProxy=function(t){return!this.path&&this.createPathProxy(),this.path.beginPath(),this.buildPath(this.path,this.shape,t),this.path},e.prototype.createPathProxy=function(){this.path=new Ja(!1)},e.prototype.hasStroke=function(){var t=this.style,e=t.stroke;return!(null==e||"none"===e||!(t.lineWidth>0))},e.prototype.hasFill=function(){var t=this.style.fill;return null!=t&&"none"!==t},e.prototype.getBoundingRect=function(){var t=this._rect,e=this.style,n=!t;if(n){var i=!1;this.path||(i=!0,this.createPathProxy());var r=this.path;(i||4&this.__dirty)&&(r.beginPath(),this.buildPath(r,this.shape,!1),this.pathUpdated()),t=r.getBoundingRect()}if(this._rect=t,this.hasStroke()&&this.path&&this.path.len()>0){var o=this._rectStroke||(this._rectStroke=t.clone());if(this.__dirty||n){o.copy(t);var a=e.strokeNoScale?this.getLineScale():1,s=e.lineWidth;if(!this.hasFill()){var l=this.strokeContainThreshold;s=Math.max(s,null==l?4:l)}a>1e-10&&(o.width+=s/a,o.height+=s/a,o.x-=s/a/2,o.y-=s/a/2)}return o}return t},e.prototype.contain=function(t,e){var n=this.transformCoordToLocal(t,e),i=this.getBoundingRect(),r=this.style;if(t=n[0],e=n[1],i.contain(t,e)){var o=this.path;if(this.hasStroke()){var a=r.lineWidth,s=r.strokeNoScale?this.getLineScale():1;if(s>1e-10&&(this.hasFill()||(a=Math.max(a,this.strokeContainThreshold)),function(t,e,n,i){return gs(t,e,!0,n,i)}(o,a/s,t,e)))return!0}if(this.hasFill())return function(t,e,n){return gs(t,0,!1,e,n)}(o,t,e)}return!1},e.prototype.dirtyShape=function(){this.__dirty|=4,this._rect&&(this._rect=null),this._decalEl&&this._decalEl.dirtyShape(),this.markRedraw()},e.prototype.dirty=function(){this.dirtyStyle(),this.dirtyShape()},e.prototype.animateShape=function(t){return this.animate("shape",t)},e.prototype.updateDuringAnimation=function(t){"style"===t?this.dirtyStyle():"shape"===t?this.dirtyShape():this.markRedraw()},e.prototype.attrKV=function(e,n){"shape"===e?this.setShape(n):t.prototype.attrKV.call(this,e,n)},e.prototype.setShape=function(t,e){var n=this.shape;return n||(n=this.shape={}),"string"==typeof t?n[t]=e:C(n,t),this.dirtyShape(),this},e.prototype.shapeChanged=function(){return!!(4&this.__dirty)},e.prototype.createStyle=function(t){return vt(vs,t)},e.prototype._innerSaveToNormal=function(e){t.prototype._innerSaveToNormal.call(this,e);var n=this._normalState;e.shape&&!n.shape&&(n.shape=C({},this.shape))},e.prototype._applyStateObj=function(e,n,i,r,o,a){t.prototype._applyStateObj.call(this,e,n,i,r,o,a);var s,l=!(n&&r);if(n&&n.shape?o?r?s=n.shape:(s=C({},i.shape),C(s,n.shape)):(s=C({},r?this.shape:i.shape),C(s,n.shape)):l&&(s=i.shape),s)if(o){this.shape=C({},this.shape);for(var u={},h=F(s),c=0;c<h.length;c++){var p=h[c];"object"==typeof s[p]?this.shape[p]=s[p]:u[p]=s[p]}this._transitionState(e,{shape:u},a)}else this.shape=s,this.dirtyShape()},e.prototype._mergeStates=function(e){for(var n,i=t.prototype._mergeStates.call(this,e),r=0;r<e.length;r++){var o=e[r];o.shape&&(n=n||{},this._mergeStyle(n,o.shape))}return n&&(i.shape=n),i},e.prototype.getAnimationStyleProps=function(){return ys},e.prototype.isZeroArea=function(){return!1},e.extend=function(t){var i=function(e){function i(n){var i=e.call(this,n)||this;return t.init&&t.init.call(i,n),i}return n(i,e),i.prototype.getDefaultStyle=function(){return T(t.style)},i.prototype.getDefaultShape=function(){return T(t.shape)},i}(e);for(var r in t)"function"==typeof t[r]&&(i.prototype[r]=t[r]);return i},e.initDefaultProps=((i=e.prototype).type="path",i.strokeContainThreshold=5,i.segmentIgnoreThreshold=0,i.subPixelOptimize=!1,i.autoBatch=!1,void(i.__dirty=7)),e}(ma),xs=D({strokeFirst:!0,font:o,x:0,y:0,textAlign:"left",textBaseline:"top",miterLimit:2},vs),bs=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.hasStroke=function(){return pa(this.style)},e.prototype.hasFill=function(){var t=this.style.fill;return null!=t&&"none"!==t},e.prototype.createStyle=function(t){return vt(xs,t)},e.prototype.setBoundingRect=function(t){this._rect=t},e.prototype.getBoundingRect=function(){var t,e,n;return this._rect||(this._rect=(t=this.style,e=ha(t.text),n=t.font,ca(t,ar(nr(n),e),cr(n),null))),this._rect},e.initDefaultProps=void(e.prototype.dirtyRectTolerance=10),e}(ma);bs.prototype.type="tspan";var ws=D({x:0,y:0},da),Ss={style:D({x:!0,y:!0,width:!0,height:!0,sx:!0,sy:!0,sWidth:!0,sHeight:!0},ga.style)};var Ms=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.createStyle=function(t){return vt(ws,t)},e.prototype._getSize=function(t){var e=this.style,n=e[t];if(null!=n)return n;var i,r=(i=e.image)&&"string"!=typeof i&&i.width&&i.height?e.image:this.__image;if(!r)return 0;var o="width"===t?"height":"width",a=e[o];return null==a?r[t]:r[t]/r[o]*a},e.prototype.getWidth=function(){return this._getSize("width")},e.prototype.getHeight=function(){return this._getSize("height")},e.prototype.getAnimationStyleProps=function(){return Ss},e.prototype.getBoundingRect=function(){var t=this.style;return this._rect||(this._rect=new Oe(t.x||0,t.y||0,this.getWidth(),this.getHeight())),this._rect},e}(ma);Ms.prototype.type="image";var Ts=Math.round;function ks(t,e,n){if(e){var i=e.x1,r=e.x2,o=e.y1,a=e.y2;t.x1=i,t.x2=r,t.y1=o,t.y2=a;var s=n&&n.lineWidth;return s?(Ts(2*i)===Ts(2*r)&&(t.x1=t.x2=Cs(i,s,!0)),Ts(2*o)===Ts(2*a)&&(t.y1=t.y2=Cs(o,s,!0)),t):t}}function Cs(t,e,n){if(!e)return t;var i=Ts(2*t);return(i+Ts(e))%2==0?i/2:(i+(n?1:-1))/2}var Ds=function(){this.x=0,this.y=0,this.width=0,this.height=0},Is={},As=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.getDefaultShape=function(){return new Ds},e.prototype.buildPath=function(t,e){var n,i,r,o;if(this.subPixelOptimize){var a=function(t,e,n){if(e){var i=e.x,r=e.y,o=e.width,a=e.height;t.x=i,t.y=r,t.width=o,t.height=a;var s=n&&n.lineWidth;return s?(t.x=Cs(i,s,!0),t.y=Cs(r,s,!0),t.width=Math.max(Cs(i+o,s,!1)-t.x,0===o?0:1),t.height=Math.max(Cs(r+a,s,!1)-t.y,0===a?0:1),t):t}}(Is,e,this.style);n=a.x,i=a.y,r=a.width,o=a.height,a.r=e.r,e=a}else n=e.x,i=e.y,r=e.width,o=e.height;e.r?function(t,e){var n,i,r,o,a,s=e.x,l=e.y,u=e.width,h=e.height,c=e.r;u<0&&(s+=u,u=-u),h<0&&(l+=h,h=-h),"number"==typeof c?n=i=r=o=c:c instanceof Array?1===c.length?n=i=r=o=c[0]:2===c.length?(n=r=c[0],i=o=c[1]):3===c.length?(n=c[0],i=o=c[1],r=c[2]):(n=c[0],i=c[1],r=c[2],o=c[3]):n=i=r=o=0,n+i>u&&(n*=u/(a=n+i),i*=u/a),r+o>u&&(r*=u/(a=r+o),o*=u/a),i+r>h&&(i*=h/(a=i+r),r*=h/a),n+o>h&&(n*=h/(a=n+o),o*=h/a),t.moveTo(s+n,l),t.lineTo(s+u-i,l),0!==i&&t.arc(s+u-i,l+i,i,-Math.PI/2,0),t.lineTo(s+u,l+h-r),0!==r&&t.arc(s+u-r,l+h-r,r,0,Math.PI/2),t.lineTo(s+o,l+h),0!==o&&t.arc(s+o,l+h-o,o,Math.PI/2,Math.PI),t.lineTo(s,l+n),0!==n&&t.arc(s+n,l+n,n,Math.PI,1.5*Math.PI)}(t,e):t.rect(n,i,r,o)},e.prototype.isZeroArea=function(){return!this.shape.width||!this.shape.height},e}(_s);As.prototype.type="rect";var Ls={fill:"#000"},Ps={},Os={style:D({fill:!0,stroke:!0,fillOpacity:!0,strokeOpacity:!0,lineWidth:!0,fontSize:!0,lineHeight:!0,width:!0,height:!0,textShadowColor:!0,textShadowBlur:!0,textShadowOffsetX:!0,textShadowOffsetY:!0,backgroundColor:!0,padding:!0,borderColor:!0,borderWidth:!0,borderRadius:!0},ga.style)},Rs=function(t){function e(e){var n=t.call(this)||this;return n.type="text",n._children=[],n._defaultStyle=Ls,n.attr(e),n}return n(e,t),e.prototype.childrenRef=function(){return this._children},e.prototype.update=function(){t.prototype.update.call(this),this.styleChanged()&&this._updateSubTexts();for(var e=0;e<this._children.length;e++){var n=this._children[e];n.zlevel=this.zlevel,n.z=this.z,n.z2=this.z2,n.culling=this.culling,n.cursor=this.cursor,n.invisible=this.invisible}},e.prototype.updateTransform=function(){var e=this.innerTransformable;e?(e.updateTransform(),e.transform&&(this.transform=e.transform)):t.prototype.updateTransform.call(this)},e.prototype.getLocalTransform=function(e){var n=this.innerTransformable;return n?n.getLocalTransform(e):t.prototype.getLocalTransform.call(this,e)},e.prototype.getComputedTransform=function(){return this.__hostTarget&&(this.__hostTarget.getComputedTransform(),this.__hostTarget.updateInnerText(!0)),t.prototype.getComputedTransform.call(this)},e.prototype._updateSubTexts=function(){var t;this._childCursor=0,Vs(t=this.style),R(t.rich,Vs),this.style.rich?this._updateRichTexts():this._updatePlainTexts(),this._children.length=this._childCursor,this.styleUpdated()},e.prototype.addSelfToZr=function(e){t.prototype.addSelfToZr.call(this,e);for(var n=0;n<this._children.length;n++)this._children[n].__zr=e},e.prototype.removeSelfFromZr=function(e){t.prototype.removeSelfFromZr.call(this,e);for(var n=0;n<this._children.length;n++)this._children[n].__zr=null},e.prototype.getBoundingRect=function(){if(this.styleChanged()&&this._updateSubTexts(),!this._rect){for(var t=new Oe(0,0,0,0),e=this._children,n=[],i=null,r=0;r<e.length;r++){var o=e[r],a=o.getBoundingRect(),s=o.getLocalTransform(n);s?(t.copy(a),t.applyTransform(s),(i=i||t.clone()).union(t)):(i=i||a.clone()).union(a)}this._rect=i||t}return this._rect},e.prototype.setDefaultTextStyle=function(t){this._defaultStyle=t||Ls},e.prototype.setTextContent=function(t){0},e.prototype._mergeStyle=function(t,e){if(!e)return t;var n=e.rich,i=t.rich||n&&{};return C(t,e),n&&i?(this._mergeRich(i,n),t.rich=i):i&&(t.rich=i),t},e.prototype._mergeRich=function(t,e){for(var n=F(e),i=0;i<n.length;i++){var r=n[i];t[r]=t[r]||{},C(t[r],e[r])}},e.prototype.getAnimationStyleProps=function(){return Os},e.prototype._getOrCreateChild=function(t){var e=this._children[this._childCursor];return e&&e instanceof t||(e=new t),this._children[this._childCursor++]=e,e.__zr=this.__zr,e.parent=this,e},e.prototype._updatePlainTexts=function(){var t=this.style,e=t.font||o,n=t.padding,i=this._defaultStyle,r=t.x||0,a=t.y||0,s=t.align||i.align||"left",l=t.verticalAlign||i.verticalAlign||"top";sa(Ps,i.overflowRect,r,a,s,l),r=Ps.baseX,a=Ps.baseY;var u=function(t,e,n,i){var r=ha(t),o=e.overflow,a=e.padding,s=a?a[1]+a[3]:0,l=a?a[0]+a[2]:0,u=e.font,h="truncate"===o,c=cr(u),p=nt(e.lineHeight,c),f="truncate"===e.lineOverflow,d=!1,g=e.width;null==g&&null!=n&&(g=n-s);var v,y=e.height;null==y&&null!=i&&(y=i-l);var m=(v=null==g||"break"!==o&&"breakAll"!==o?r?r.split("\n"):[]:r?aa(r,e.font,g,"breakAll"===o,0).lines:[]).length*p;if(null==y&&(y=m),m>y&&f){var _=Math.floor(y/p);d=d||v.length>_,m=(v=v.slice(0,_)).length*p}if(r&&h&&null!=g)for(var x=$o(g,u,e.ellipsis,{minChar:e.truncateMinChar,placeholder:e.placeholder}),b={},w=0;w<v.length;w++)Qo(b,v[w],x),v[w]=b.textLine,d=d||b.isTruncated;var S=y,M=0,T=nr(u);for(w=0;w<v.length;w++)M=Math.max(ar(T,v[w]),M);null==g&&(g=M);var k=g;return{lines:v,height:y,outerWidth:k+=s,outerHeight:S+=l,lineHeight:p,calculatedLineHeight:c,contentWidth:M,contentHeight:m,width:g,isTruncated:d}}(Us(t),t,Ps.outerWidth,Ps.outerHeight),h=Xs(t),c=!!t.backgroundColor,p=u.outerHeight,f=u.outerWidth,d=u.lines,g=u.lineHeight;this.isTruncated=!!u.isTruncated;var v=r,y=hr(a,u.contentHeight,l);if(h||n){var m=ur(r,f,s),_=hr(a,p,l);h&&this._renderBackground(t,t,m,_,f,p)}y+=g/2,n&&(v=Gs(r,s,n),"top"===l?y+=n[0]:"bottom"===l&&(y-=n[2]));for(var x=0,b=!1,w=!1,S=(Hs("fill"in t?t.fill:(w=!0,i.fill))),M=(Ws("stroke"in t?t.stroke:c||i.autoStroke&&!w?null:(x=2,b=!0,i.stroke))),T=t.textShadowBlur>0,k=0;k<d.length;k++){var C=this._getOrCreateChild(bs),D=C.createStyle();C.useStyle(D),D.text=d[k],D.x=v,D.y=y,s&&(D.textAlign=s),D.textBaseline="middle",D.opacity=t.opacity,D.strokeFirst=!0,T&&(D.shadowBlur=t.textShadowBlur||0,D.shadowColor=t.textShadowColor||"transparent",D.shadowOffsetX=t.textShadowOffsetX||0,D.shadowOffsetY=t.textShadowOffsetY||0),D.stroke=M,D.fill=S,M&&(D.lineWidth=t.lineWidth||x,D.lineDash=t.lineDash,D.lineDashOffset=t.lineDashOffset||0),D.font=e,Fs(D,t),y+=g,C.setBoundingRect(ca(D,u.contentWidth,u.calculatedLineHeight,b?0:null))}},e.prototype._updateRichTexts=function(){var t=this.style,e=this._defaultStyle,n=t.align||e.align,i=t.verticalAlign||e.verticalAlign,r=t.x||0,o=t.y||0;sa(Ps,e.overflowRect,r,o,n,i),r=Ps.baseX,o=Ps.baseY;var a=function(t,e,n,i,r){var o=new na,a=ha(t);if(!a)return o;var s=e.padding,l=s?s[1]+s[3]:0,u=s?s[0]+s[2]:0,h=e.width;null==h&&null!=n&&(h=n-l);var c=e.height;null==c&&null!=i&&(c=i-u);for(var p,f=e.overflow,d="break"!==f&&"breakAll"!==f||null==h?null:{width:h,accumWidth:0,breakAll:"breakAll"===f},g=jo.lastIndex=0;null!=(p=jo.exec(a));){var v=p.index;v>g&&ia(o,a.substring(g,v),e,d),ia(o,p[2],e,d,p[1]),g=jo.lastIndex}g<a.length&&ia(o,a.substring(g,a.length),e,d);var y=[],m=0,_=0,x="truncate"===f,b="truncate"===e.lineOverflow,w={};function S(t,e,n){t.width=e,t.lineHeight=n,m+=n,_=Math.max(_,e)}t:for(var M=0;M<o.lines.length;M++){for(var T=o.lines[M],k=0,C=0,D=0;D<T.tokens.length;D++){var I=(V=T.tokens[D]).styleName&&e.rich[V.styleName]||{},A=V.textPadding=I.padding,L=A?A[1]+A[3]:0,P=V.font=I.font||e.font;V.contentHeight=cr(P);var O=nt(I.height,V.contentHeight);if(V.innerHeight=O,A&&(O+=A[0]+A[2]),V.height=O,V.lineHeight=it(I.lineHeight,e.lineHeight,O),V.align=I&&I.align||r,V.verticalAlign=I&&I.verticalAlign||"middle",b&&null!=c&&m+V.lineHeight>c){var R=o.lines.length;D>0?(T.tokens=T.tokens.slice(0,D),S(T,C,k),o.lines=o.lines.slice(0,M+1)):o.lines=o.lines.slice(0,M),o.isTruncated=o.isTruncated||o.lines.length<R;break t}var N=I.width,B=null==N||"auto"===N;if("string"==typeof N&&"%"===N.charAt(N.length-1))V.percentWidth=N,y.push(V),V.contentWidth=ar(nr(P),V.text);else{if(B){var E=I.backgroundColor,z=E&&E.image;z&&Zo(z=Xo(z))&&(V.width=Math.max(V.width,z.width*O/z.height))}var F=x&&null!=h?h-C:null;null!=F&&F<V.width?!B||F<L?(V.text="",V.width=V.contentWidth=0):(Ko(w,V.text,F-L,P,e.ellipsis,{minChar:e.truncateMinChar}),V.text=w.text,o.isTruncated=o.isTruncated||w.isTruncated,V.width=V.contentWidth=ar(nr(P),V.text)):V.contentWidth=ar(nr(P),V.text)}V.width+=L,C+=V.width,I&&(k=Math.max(k,V.lineHeight))}S(T,C,k)}for(o.outerWidth=o.width=nt(h,_),o.outerHeight=o.height=nt(c,m),o.contentHeight=m,o.contentWidth=_,o.outerWidth+=l,o.outerHeight+=u,M=0;M<y.length;M++){var V,W=(V=y[M]).percentWidth;V.width=parseInt(W,10)/100*o.width}return o}(Us(t),t,Ps.outerWidth,Ps.outerHeight,n),s=a.width,l=a.outerWidth,u=a.outerHeight,h=t.padding;this.isTruncated=!!a.isTruncated;var c=ur(r,l,n),p=hr(o,u,i),f=c,d=p;h&&(f+=h[3],d+=h[0]);var g=f+s;Xs(t)&&this._renderBackground(t,t,c,p,l,u);for(var v=!!t.backgroundColor,y=0;y<a.lines.length;y++){for(var m=a.lines[y],_=m.tokens,x=_.length,b=m.lineHeight,w=m.width,S=0,M=f,T=g,k=x-1,C=void 0;S<x&&(!(C=_[S]).align||"left"===C.align);)this._placeToken(C,t,b,d,M,"left",v),w-=C.width,M+=C.width,S++;for(;k>=0&&"right"===(C=_[k]).align;)this._placeToken(C,t,b,d,T,"right",v),w-=C.width,T-=C.width,k--;for(M+=(s-(M-f)-(g-T)-w)/2;S<=k;)C=_[S],this._placeToken(C,t,b,d,M+C.width/2,"center",v),M+=C.width,S++;d+=b}},e.prototype._placeToken=function(t,e,n,i,r,a,s){var l=e.rich[t.styleName]||{};l.text=t.text;var u=t.verticalAlign,h=i+n/2;"top"===u?h=i+t.height/2:"bottom"===u&&(h=i+n-t.height/2),!t.isLineHolder&&Xs(l)&&this._renderBackground(l,e,"right"===a?r-t.width:"center"===a?r-t.width/2:r,h-t.height/2,t.width,t.height);var c=!!l.backgroundColor,p=t.textPadding;p&&(r=Gs(r,a,p),h-=t.height/2-p[0]-t.innerHeight/2);var f=this._getOrCreateChild(bs),d=f.createStyle();f.useStyle(d);var g=this._defaultStyle,v=!1,y=0,m=!1,_=Hs("fill"in l?l.fill:"fill"in e?e.fill:(v=!0,g.fill)),x=Ws("stroke"in l?l.stroke:"stroke"in e?e.stroke:c||s||g.autoStroke&&!v?null:(y=2,m=!0,g.stroke)),b=l.textShadowBlur>0||e.textShadowBlur>0;d.text=t.text,d.x=r,d.y=h,b&&(d.shadowBlur=l.textShadowBlur||e.textShadowBlur||0,d.shadowColor=l.textShadowColor||e.textShadowColor||"transparent",d.shadowOffsetX=l.textShadowOffsetX||e.textShadowOffsetX||0,d.shadowOffsetY=l.textShadowOffsetY||e.textShadowOffsetY||0),d.textAlign=a,d.textBaseline="middle",d.font=t.font||o,d.opacity=it(l.opacity,e.opacity,1),Fs(d,l),x&&(d.lineWidth=it(l.lineWidth,e.lineWidth,y),d.lineDash=nt(l.lineDash,e.lineDash),d.lineDashOffset=e.lineDashOffset||0,d.stroke=x),_&&(d.fill=_),f.setBoundingRect(ca(d,t.contentWidth,t.contentHeight,m?0:null))},e.prototype._renderBackground=function(t,e,n,i,r,o){var a,s,l,u=t.backgroundColor,h=t.borderWidth,c=t.borderColor,p=u&&u.image,f=u&&!p,d=t.borderRadius,g=this;if(f||t.lineHeight||h&&c){(a=this._getOrCreateChild(As)).useStyle(a.createStyle()),a.style.fill=null;var v=a.shape;v.x=n,v.y=i,v.width=r,v.height=o,v.r=d,a.dirtyShape()}if(f)(l=a.style).fill=u||null,l.fillOpacity=nt(t.fillOpacity,1);else if(p){(s=this._getOrCreateChild(Ms)).onload=function(){g.dirtyStyle()};var y=s.style;y.image=u.image,y.x=n,y.y=i,y.width=r,y.height=o}h&&c&&((l=a.style).lineWidth=h,l.stroke=c,l.strokeOpacity=nt(t.strokeOpacity,1),l.lineDash=t.borderDash,l.lineDashOffset=t.borderDashOffset||0,a.strokeContainThreshold=0,a.hasFill()&&a.hasStroke()&&(l.strokeFirst=!0,l.lineWidth*=2));var m=(a||s).style;m.shadowBlur=t.shadowBlur||0,m.shadowColor=t.shadowColor||"transparent",m.shadowOffsetX=t.shadowOffsetX||0,m.shadowOffsetY=t.shadowOffsetY||0,m.opacity=it(t.opacity,e.opacity,1)},e.makeFont=function(t){var e="";return function(t){return null!=t.fontSize||t.fontFamily||t.fontWeight}(t)&&(e=[t.fontStyle,t.fontWeight,zs(t.fontSize),t.fontFamily||"sans-serif"].join(" ")),e&&st(e)||t.textFont||t.font},e}(ma),Ns={left:!0,right:1,center:1},Bs={top:1,bottom:1,middle:1},Es=["fontStyle","fontWeight","fontSize","fontFamily"];function zs(t){return"string"!=typeof t||-1===t.indexOf("px")&&-1===t.indexOf("rem")&&-1===t.indexOf("em")?isNaN(+t)?"12px":t+"px":t}function Fs(t,e){for(var n=0;n<Es.length;n++){var i=Es[n],r=e[i];null!=r&&(t[i]=r)}}function Vs(t){if(t){t.font=Rs.makeFont(t);var e=t.align;"middle"===e&&(e="center"),t.align=null==e||Ns[e]?e:"left";var n=t.verticalAlign;"center"===n&&(n="middle"),t.verticalAlign=null==n||Bs[n]?n:"top",t.padding&&(t.padding=ot(t.padding))}}function Ws(t,e){return null==t||e<=0||"transparent"===t||"none"===t?null:t.image||t.colorStops?"#000":t}function Hs(t){return null==t||"none"===t?null:t.image||t.colorStops?"#000":t}function Gs(t,e,n){return"right"===e?t-n[1]:"center"===e?t+n[3]/2-n[1]/2:t+n[3]}function Us(t){var e=t.text;return null!=e&&(e+=""),e}function Xs(t){return!!(t.backgroundColor||t.lineHeight||t.borderWidth&&t.borderColor)}var Ys=To(),qs=1,Zs={},js=To(),Ks=To(),$s=["emphasis","blur","select"],Qs=["normal","emphasis","blur","select"],Js="highlight",tl="downplay",el="select",nl="unselect",il="toggleSelect",rl="selectchanged";function ol(t){return null!=t&&"none"!==t}function al(t,e,n){t.onHoverStateChange&&(t.hoverState||0)!==n&&t.onHoverStateChange(e),t.hoverState=n}function sl(t){al(t,"emphasis",2)}function ll(t){2===t.hoverState&&al(t,"normal",0)}function ul(t){al(t,"blur",1)}function hl(t){1===t.hoverState&&al(t,"normal",0)}function cl(t){t.selected=!0}function pl(t){t.selected=!1}function fl(t,e,n){e(t,n)}function dl(t,e,n){fl(t,e,n),t.isGroup&&t.traverse((function(t){fl(t,e,n)}))}function gl(t,e){switch(e){case"emphasis":t.hoverState=2;break;case"normal":t.hoverState=0;break;case"blur":t.hoverState=1;break;case"select":t.selected=!0}}function vl(t,e){var n=this.states[t];if(this.style){if("emphasis"===t)return function(t,e,n,i){var r=n&&A(n,"select")>=0,o=!1;if(t instanceof _s){var a=js(t),s=r&&a.selectFill||a.normalFill,l=r&&a.selectStroke||a.normalStroke;if(ol(s)||ol(l)){var u=(i=i||{}).style||{};"inherit"===u.fill?(o=!0,i=C({},i),(u=C({},u)).fill=s):!ol(u.fill)&&ol(s)?(o=!0,i=C({},i),(u=C({},u)).fill=ri(s)):!ol(u.stroke)&&ol(l)&&(o||(i=C({},i),u=C({},u)),u.stroke=ri(l)),i.style=u}}if(i&&null==i.z2){o||(i=C({},i));var h=t.z2EmphasisLift;i.z2=t.z2+(null!=h?h:10)}return i}(this,0,e,n);if("blur"===t)return function(t,e,n){var i=A(t.currentStates,e)>=0,r=t.style.opacity,o=i?null:function(t,e,n,i){for(var r=t.style,o={},a=0;a<e.length;a++){var s=e[a],l=r[s];o[s]=null==l?i&&i[s]:l}for(a=0;a<t.animators.length;a++){var u=t.animators[a];u.__fromStateTransition&&u.__fromStateTransition.indexOf(n)<0&&"style"===u.targetName&&u.saveTo(o,e)}return o}(t,["opacity"],e,{opacity:1}),a=(n=n||{}).style||{};return null==a.opacity&&(n=C({},n),a=C({opacity:i?r:.1*o.opacity},a),n.style=a),n}(this,t,n);if("select"===t)return function(t,e,n){if(n&&null==n.z2){n=C({},n);var i=t.z2SelectLift;n.z2=t.z2+(null!=i?i:9)}return n}(this,0,n)}return n}function yl(t){t.stateProxy=vl;var e=t.getTextContent(),n=t.getTextGuideLine();e&&(e.stateProxy=vl),n&&(n.stateProxy=vl)}function ml(t,e){!Tl(t,e)&&!t.__highByOuter&&dl(t,sl)}function _l(t,e){!Tl(t,e)&&!t.__highByOuter&&dl(t,ll)}function xl(t,e){t.__highByOuter|=1<<(e||0),dl(t,sl)}function bl(t,e){!(t.__highByOuter&=~(1<<(e||0)))&&dl(t,ll)}function wl(t){dl(t,hl)}function Sl(t){dl(t,cl)}function Ml(t){dl(t,pl)}function Tl(t,e){return t.__highDownSilentOnTouch&&e.zrByTouch}function kl(t){var e=t.getModel(),n=[],i=[];e.eachComponent((function(e,r){var o=Ks(r),a="series"===e,s=a?t.getViewOfSeriesModel(r):t.getViewOfComponentModel(r);!a&&i.push(s),o.isBlured&&(s.group.traverse((function(t){hl(t)})),a&&n.push(r)),o.isBlured=!1})),R(i,(function(t){t&&t.toggleBlurSeries&&t.toggleBlurSeries(n,!1,e)}))}function Cl(t,e,n,i){var r=i.getModel();function o(t,e){for(var n=0;n<e.length;n++){var i=t.getItemGraphicEl(e[n]);i&&wl(i)}}if(n=n||"coordinateSystem",null!=t&&e&&"none"!==e){var a=r.getSeriesByIndex(t),s=a.coordinateSystem;s&&s.master&&(s=s.master);var l=[];r.eachSeries((function(t){var r=a===t,u=t.coordinateSystem;if(u&&u.master&&(u=u.master),!("series"===n&&!r||"coordinateSystem"===n&&!(u&&s?u===s:r)||"series"===e&&r)){if(i.getViewOfSeriesModel(t).group.traverse((function(t){t.__highByOuter&&r&&"self"===e||ul(t)})),O(e))o(t.getData(),e);else if(q(e))for(var h=F(e),c=0;c<h.length;c++)o(t.getData(h[c]),e[h[c]]);l.push(t),Ks(t).isBlured=!0}})),r.eachComponent((function(t,e){if("series"!==t){var n=i.getViewOfComponentModel(e);n&&n.toggleBlurSeries&&n.toggleBlurSeries(l,!0,r)}}))}}function Dl(t,e,n){if(null!=t&&null!=e){var i=n.getModel().getComponent(t,e);if(i){Ks(i).isBlured=!0;var r=n.getViewOfComponentModel(i);r&&r.focusBlurEnabled&&r.group.traverse((function(t){ul(t)}))}}}function Il(t,e,n,i){var r={focusSelf:!1,dispatchers:null};if(null==t||"series"===t||null==e||null==n)return r;var o=i.getModel().getComponent(t,e);if(!o)return r;var a=i.getViewOfComponentModel(o);if(!a||!a.findHighDownDispatchers)return r;for(var s,l=a.findHighDownDispatchers(n),u=0;u<l.length;u++)if("self"===Ys(l[u]).focus){s=!0;break}return{focusSelf:s,dispatchers:l}}function Al(t){R(t.getAllData(),(function(e){var n=e.data,i=e.type;n.eachItemGraphicEl((function(e,n){t.isSelected(n,i)?Sl(e):Ml(e)}))}))}function Ll(t){var e=[];return t.eachSeries((function(t){R(t.getAllData(),(function(n){n.data;var i=n.type,r=t.getSelectedDataIndices();if(r.length>0){var o={dataIndex:r,seriesIndex:t.seriesIndex};null!=i&&(o.dataType=i),e.push(o)}}))})),e}function Pl(t,e,n){El(t,!0),dl(t,yl),function(t,e,n){var i=Ys(t);null!=e?(i.focus=e,i.blurScope=n):i.focus&&(i.focus=null)}(t,e,n)}function Ol(t,e,n,i){i?function(t){El(t,!1)}(t):Pl(t,e,n)}var Rl=["emphasis","blur","select"],Nl={itemStyle:"getItemStyle",lineStyle:"getLineStyle",areaStyle:"getAreaStyle"};function Bl(t,e,n,i){n=n||"itemStyle";for(var r=0;r<Rl.length;r++){var o=Rl[r],a=e.getModel([o,n]);t.ensureState(o).style=i?i(a):a[Nl[n]]()}}function El(t,e){var n=!1===e,i=t;t.highDownSilentOnTouch&&(i.__highDownSilentOnTouch=t.highDownSilentOnTouch),n&&!i.__highDownDispatcher||(i.__highByOuter=i.__highByOuter||0,i.__highDownDispatcher=!n)}function zl(t){return!(!t||!t.__highDownDispatcher)}function Fl(t){var e=t.type;return e===el||e===nl||e===il}function Vl(t){var e=t.type;return e===Js||e===tl}var Wl=Ja.CMD,Hl=[[],[],[]],Gl=Math.sqrt,Ul=Math.atan2;var Xl=Math.sqrt,Yl=Math.sin,ql=Math.cos,Zl=Math.PI;function jl(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])}function Kl(t,e){return(t[0]*e[0]+t[1]*e[1])/(jl(t)*jl(e))}function $l(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(Kl(t,e))}function Ql(t,e,n,i,r,o,a,s,l,u,h){var c=l*(Zl/180),p=ql(c)*(t-n)/2+Yl(c)*(e-i)/2,f=-1*Yl(c)*(t-n)/2+ql(c)*(e-i)/2,d=p*p/(a*a)+f*f/(s*s);d>1&&(a*=Xl(d),s*=Xl(d));var g=(r===o?-1:1)*Xl((a*a*(s*s)-a*a*(f*f)-s*s*(p*p))/(a*a*(f*f)+s*s*(p*p)))||0,v=g*a*f/s,y=g*-s*p/a,m=(t+n)/2+ql(c)*v-Yl(c)*y,_=(e+i)/2+Yl(c)*v+ql(c)*y,x=$l([1,0],[(p-v)/a,(f-y)/s]),b=[(p-v)/a,(f-y)/s],w=[(-1*p-v)/a,(-1*f-y)/s],S=$l(b,w);if(Kl(b,w)<=-1&&(S=Zl),Kl(b,w)>=1&&(S=0),S<0){var M=Math.round(S/Zl*1e6)/1e6;S=2*Zl+M%2*Zl}h.addData(u,m,_,a,s,x,S,c,o)}var Jl=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/gi,tu=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;var eu=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.applyTransform=function(t){},e}(_s);function nu(t){return null!=t.setData}function iu(t,e){var n=function(t){var e=new Ja;if(!t)return e;var n,i=0,r=0,o=i,a=r,s=Ja.CMD,l=t.match(Jl);if(!l)return e;for(var u=0;u<l.length;u++){for(var h=l[u],c=h.charAt(0),p=void 0,f=h.match(tu)||[],d=f.length,g=0;g<d;g++)f[g]=parseFloat(f[g]);for(var v=0;v<d;){var y=void 0,m=void 0,_=void 0,x=void 0,b=void 0,w=void 0,S=void 0,M=i,T=r,k=void 0,C=void 0;switch(c){case"l":i+=f[v++],r+=f[v++],p=s.L,e.addData(p,i,r);break;case"L":i=f[v++],r=f[v++],p=s.L,e.addData(p,i,r);break;case"m":i+=f[v++],r+=f[v++],p=s.M,e.addData(p,i,r),o=i,a=r,c="l";break;case"M":i=f[v++],r=f[v++],p=s.M,e.addData(p,i,r),o=i,a=r,c="L";break;case"h":i+=f[v++],p=s.L,e.addData(p,i,r);break;case"H":i=f[v++],p=s.L,e.addData(p,i,r);break;case"v":r+=f[v++],p=s.L,e.addData(p,i,r);break;case"V":r=f[v++],p=s.L,e.addData(p,i,r);break;case"C":p=s.C,e.addData(p,f[v++],f[v++],f[v++],f[v++],f[v++],f[v++]),i=f[v-2],r=f[v-1];break;case"c":p=s.C,e.addData(p,f[v++]+i,f[v++]+r,f[v++]+i,f[v++]+r,f[v++]+i,f[v++]+r),i+=f[v-2],r+=f[v-1];break;case"S":y=i,m=r,k=e.len(),C=e.data,n===s.C&&(y+=i-C[k-4],m+=r-C[k-3]),p=s.C,M=f[v++],T=f[v++],i=f[v++],r=f[v++],e.addData(p,y,m,M,T,i,r);break;case"s":y=i,m=r,k=e.len(),C=e.data,n===s.C&&(y+=i-C[k-4],m+=r-C[k-3]),p=s.C,M=i+f[v++],T=r+f[v++],i+=f[v++],r+=f[v++],e.addData(p,y,m,M,T,i,r);break;case"Q":M=f[v++],T=f[v++],i=f[v++],r=f[v++],p=s.Q,e.addData(p,M,T,i,r);break;case"q":M=f[v++]+i,T=f[v++]+r,i+=f[v++],r+=f[v++],p=s.Q,e.addData(p,M,T,i,r);break;case"T":y=i,m=r,k=e.len(),C=e.data,n===s.Q&&(y+=i-C[k-4],m+=r-C[k-3]),i=f[v++],r=f[v++],p=s.Q,e.addData(p,y,m,i,r);break;case"t":y=i,m=r,k=e.len(),C=e.data,n===s.Q&&(y+=i-C[k-4],m+=r-C[k-3]),i+=f[v++],r+=f[v++],p=s.Q,e.addData(p,y,m,i,r);break;case"A":_=f[v++],x=f[v++],b=f[v++],w=f[v++],S=f[v++],Ql(M=i,T=r,i=f[v++],r=f[v++],w,S,_,x,b,p=s.A,e);break;case"a":_=f[v++],x=f[v++],b=f[v++],w=f[v++],S=f[v++],Ql(M=i,T=r,i+=f[v++],r+=f[v++],w,S,_,x,b,p=s.A,e)}}"z"!==c&&"Z"!==c||(p=s.Z,e.addData(p),i=o,r=a),n=p}return e.toStatic(),e}(t),i=C({},e);return i.buildPath=function(t){var e,i=nu(t);i&&t.canSave()?(t.appendPath(n),(e=t.getContext())&&t.rebuildPath(e,1)):(e=i?t.getContext():t)&&n.rebuildPath(e,1)},i.applyTransform=function(t){!function(t,e){if(e){var n,i,r,o,a,s,l=t.data,u=t.len(),h=Wl.M,c=Wl.C,p=Wl.L,f=Wl.R,d=Wl.A,g=Wl.Q;for(r=0,o=0;r<u;){switch(n=l[r++],o=r,i=0,n){case h:case p:i=1;break;case c:i=3;break;case g:i=2;break;case d:var v=e[4],y=e[5],m=Gl(e[0]*e[0]+e[1]*e[1]),_=Gl(e[2]*e[2]+e[3]*e[3]),x=Ul(-e[1]/_,e[0]/m);l[r]*=m,l[r++]+=v,l[r]*=_,l[r++]+=y,l[r++]*=m,l[r++]*=_,l[r++]+=x,l[r++]+=x,o=r+=2;break;case f:s[0]=l[r++],s[1]=l[r++],zt(s,s,e),l[o++]=s[0],l[o++]=s[1],s[0]+=l[r++],s[1]+=l[r++],zt(s,s,e),l[o++]=s[0],l[o++]=s[1]}for(a=0;a<i;a++){var b=Hl[a];b[0]=l[r++],b[1]=l[r++],zt(b,b,e),l[o++]=b[0],l[o++]=b[1]}}t.increaseVersion()}}(n,t),this.dirtyShape()},i}var ru=function(){this.cx=0,this.cy=0,this.r=0},ou=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.getDefaultShape=function(){return new ru},e.prototype.buildPath=function(t,e){t.moveTo(e.cx+e.r,e.cy),t.arc(e.cx,e.cy,e.r,0,2*Math.PI)},e}(_s);ou.prototype.type="circle";var au=function(){this.cx=0,this.cy=0,this.rx=0,this.ry=0},su=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.getDefaultShape=function(){return new au},e.prototype.buildPath=function(t,e){var n=.5522848,i=e.cx,r=e.cy,o=e.rx,a=e.ry,s=o*n,l=a*n;t.moveTo(i-o,r),t.bezierCurveTo(i-o,r-l,i-s,r-a,i,r-a),t.bezierCurveTo(i+s,r-a,i+o,r-l,i+o,r),t.bezierCurveTo(i+o,r+l,i+s,r+a,i,r+a),t.bezierCurveTo(i-s,r+a,i-o,r+l,i-o,r),t.closePath()},e}(_s);su.prototype.type="ellipse";var lu=Math.PI,uu=2*lu,hu=Math.sin,cu=Math.cos,pu=Math.acos,fu=Math.atan2,du=Math.abs,gu=Math.sqrt,vu=Math.max,yu=Math.min,mu=1e-4;function _u(t,e,n,i,r,o,a){var s=t-n,l=e-i,u=(a?o:-o)/gu(s*s+l*l),h=u*l,c=-u*s,p=t+h,f=e+c,d=n+h,g=i+c,v=(p+d)/2,y=(f+g)/2,m=d-p,_=g-f,x=m*m+_*_,b=r-o,w=p*g-d*f,S=(_<0?-1:1)*gu(vu(0,b*b*x-w*w)),M=(w*_-m*S)/x,T=(-w*m-_*S)/x,k=(w*_+m*S)/x,C=(-w*m+_*S)/x,D=M-v,I=T-y,A=k-v,L=C-y;return D*D+I*I>A*A+L*L&&(M=k,T=C),{cx:M,cy:T,x0:-h,y0:-c,x1:M*(r/b-1),y1:T*(r/b-1)}}function xu(t,e){var n,i=vu(e.r,0),r=vu(e.r0||0,0),o=i>0;if(o||r>0){if(o||(i=r,r=0),r>i){var a=i;i=r,r=a}var s=e.startAngle,l=e.endAngle;if(!isNaN(s)&&!isNaN(l)){var u=e.cx,h=e.cy,c=!!e.clockwise,p=du(l-s),f=p>uu&&p%uu;if(f>mu&&(p=f),i>mu)if(p>uu-mu)t.moveTo(u+i*cu(s),h+i*hu(s)),t.arc(u,h,i,s,l,!c),r>mu&&(t.moveTo(u+r*cu(l),h+r*hu(l)),t.arc(u,h,r,l,s,c));else{var d=void 0,g=void 0,v=void 0,y=void 0,m=void 0,_=void 0,x=void 0,b=void 0,w=void 0,S=void 0,M=void 0,T=void 0,k=void 0,C=void 0,D=void 0,I=void 0,A=i*cu(s),L=i*hu(s),P=r*cu(l),O=r*hu(l),R=p>mu;if(R){var N=e.cornerRadius;N&&(n=function(t){var e;if(H(t)){var n=t.length;if(!n)return t;e=1===n?[t[0],t[0],0,0]:2===n?[t[0],t[0],t[1],t[1]]:3===n?t.concat(t[2]):t}else e=[t,t,t,t];return e}(N),d=n[0],g=n[1],v=n[2],y=n[3]);var B=du(i-r)/2;if(m=yu(B,v),_=yu(B,y),x=yu(B,d),b=yu(B,g),M=w=vu(m,_),T=S=vu(x,b),(w>mu||S>mu)&&(k=i*cu(l),C=i*hu(l),D=r*cu(s),I=r*hu(s),p<lu)){var E=function(t,e,n,i,r,o,a,s){var l=n-t,u=i-e,h=a-r,c=s-o,p=c*l-h*u;if(!(p*p<mu))return[t+(p=(h*(e-o)-c*(t-r))/p)*l,e+p*u]}(A,L,D,I,k,C,P,O);if(E){var z=A-E[0],F=L-E[1],V=k-E[0],W=C-E[1],G=1/hu(pu((z*V+F*W)/(gu(z*z+F*F)*gu(V*V+W*W)))/2),U=gu(E[0]*E[0]+E[1]*E[1]);M=yu(w,(i-U)/(G+1)),T=yu(S,(r-U)/(G-1))}}}if(R)if(M>mu){var X=yu(v,M),Y=yu(y,M),q=_u(D,I,A,L,i,X,c),Z=_u(k,C,P,O,i,Y,c);t.moveTo(u+q.cx+q.x0,h+q.cy+q.y0),M<w&&X===Y?t.arc(u+q.cx,h+q.cy,M,fu(q.y0,q.x0),fu(Z.y0,Z.x0),!c):(X>0&&t.arc(u+q.cx,h+q.cy,X,fu(q.y0,q.x0),fu(q.y1,q.x1),!c),t.arc(u,h,i,fu(q.cy+q.y1,q.cx+q.x1),fu(Z.cy+Z.y1,Z.cx+Z.x1),!c),Y>0&&t.arc(u+Z.cx,h+Z.cy,Y,fu(Z.y1,Z.x1),fu(Z.y0,Z.x0),!c))}else t.moveTo(u+A,h+L),t.arc(u,h,i,s,l,!c);else t.moveTo(u+A,h+L);if(r>mu&&R)if(T>mu){X=yu(d,T),q=_u(P,O,k,C,r,-(Y=yu(g,T)),c),Z=_u(A,L,D,I,r,-X,c);t.lineTo(u+q.cx+q.x0,h+q.cy+q.y0),T<S&&X===Y?t.arc(u+q.cx,h+q.cy,T,fu(q.y0,q.x0),fu(Z.y0,Z.x0),!c):(Y>0&&t.arc(u+q.cx,h+q.cy,Y,fu(q.y0,q.x0),fu(q.y1,q.x1),!c),t.arc(u,h,r,fu(q.cy+q.y1,q.cx+q.x1),fu(Z.cy+Z.y1,Z.cx+Z.x1),c),X>0&&t.arc(u+Z.cx,h+Z.cy,X,fu(Z.y1,Z.x1),fu(Z.y0,Z.x0),!c))}else t.lineTo(u+P,h+O),t.arc(u,h,r,l,s,c);else t.lineTo(u+P,h+O)}else t.moveTo(u,h);t.closePath()}}}var bu=function(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0,this.cornerRadius=0},wu=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.getDefaultShape=function(){return new bu},e.prototype.buildPath=function(t,e){xu(t,e)},e.prototype.isZeroArea=function(){return this.shape.startAngle===this.shape.endAngle||this.shape.r===this.shape.r0},e}(_s);wu.prototype.type="sector";var Su=function(){this.cx=0,this.cy=0,this.r=0,this.r0=0},Mu=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.getDefaultShape=function(){return new Su},e.prototype.buildPath=function(t,e){var n=e.cx,i=e.cy,r=2*Math.PI;t.moveTo(n+e.r,i),t.arc(n,i,e.r,0,r,!1),t.moveTo(n+e.r0,i),t.arc(n,i,e.r0,0,r,!0)},e}(_s);function Tu(t,e,n){var i=e.smooth,r=e.points;if(r&&r.length>=2){if(i){var o=function(t,e,n,i){var r,o,a,s,l=[],u=[],h=[],c=[];if(i){a=[1/0,1/0],s=[-1/0,-1/0];for(var p=0,f=t.length;p<f;p++)Ft(a,a,t[p]),Vt(s,s,t[p]);Ft(a,a,i[0]),Vt(s,s,i[1])}for(p=0,f=t.length;p<f;p++){var d=t[p];if(n)r=t[p?p-1:f-1],o=t[(p+1)%f];else{if(0===p||p===f-1){l.push(Mt(t[p]));continue}r=t[p-1],o=t[p+1]}kt(u,o,r),Lt(u,u,e);var g=Ot(d,r),v=Ot(d,o),y=g+v;0!==y&&(g/=y,v/=y),Lt(h,u,-g),Lt(c,u,v);var m=Tt([],d,h),_=Tt([],d,c);i&&(Vt(m,m,a),Ft(m,m,s),Vt(_,_,a),Ft(_,_,s)),l.push(m),l.push(_)}return n&&l.push(l.shift()),l}(r,i,n,e.smoothConstraint);t.moveTo(r[0][0],r[0][1]);for(var a=r.length,s=0;s<(n?a:a-1);s++){var l=o[2*s],u=o[2*s+1],h=r[(s+1)%a];t.bezierCurveTo(l[0],l[1],u[0],u[1],h[0],h[1])}}else{t.moveTo(r[0][0],r[0][1]);s=1;for(var c=r.length;s<c;s++)t.lineTo(r[s][0],r[s][1])}n&&t.closePath()}}Mu.prototype.type="ring";var ku=function(){this.points=null,this.smooth=0,this.smoothConstraint=null},Cu=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.getDefaultShape=function(){return new ku},e.prototype.buildPath=function(t,e){Tu(t,e,!0)},e}(_s);Cu.prototype.type="polygon";var Du=function(){this.points=null,this.percent=1,this.smooth=0,this.smoothConstraint=null},Iu=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new Du},e.prototype.buildPath=function(t,e){Tu(t,e,!1)},e}(_s);Iu.prototype.type="polyline";var Au={},Lu=function(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1},Pu=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new Lu},e.prototype.buildPath=function(t,e){var n,i,r,o;if(this.subPixelOptimize){var a=ks(Au,e,this.style);n=a.x1,i=a.y1,r=a.x2,o=a.y2}else n=e.x1,i=e.y1,r=e.x2,o=e.y2;var s=e.percent;0!==s&&(t.moveTo(n,i),s<1&&(r=n*(1-s)+r*s,o=i*(1-s)+o*s),t.lineTo(r,o))},e.prototype.pointAt=function(t){var e=this.shape;return[e.x1*(1-t)+e.x2*t,e.y1*(1-t)+e.y2*t]},e}(_s);Pu.prototype.type="line";var Ou=[],Ru=function(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.percent=1};function Nu(t,e,n){var i=t.cpx2,r=t.cpy2;return null!=i||null!=r?[(n?_n:mn)(t.x1,t.cpx1,t.cpx2,t.x2,e),(n?_n:mn)(t.y1,t.cpy1,t.cpy2,t.y2,e)]:[(n?Tn:Mn)(t.x1,t.cpx1,t.x2,e),(n?Tn:Mn)(t.y1,t.cpy1,t.y2,e)]}var Bu=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new Ru},e.prototype.buildPath=function(t,e){var n=e.x1,i=e.y1,r=e.x2,o=e.y2,a=e.cpx1,s=e.cpy1,l=e.cpx2,u=e.cpy2,h=e.percent;0!==h&&(t.moveTo(n,i),null==l||null==u?(h<1&&(Cn(n,a,r,h,Ou),a=Ou[1],r=Ou[2],Cn(i,s,o,h,Ou),s=Ou[1],o=Ou[2]),t.quadraticCurveTo(a,s,r,o)):(h<1&&(wn(n,a,l,r,h,Ou),a=Ou[1],l=Ou[2],r=Ou[3],wn(i,s,u,o,h,Ou),s=Ou[1],u=Ou[2],o=Ou[3]),t.bezierCurveTo(a,s,l,u,r,o)))},e.prototype.pointAt=function(t){return Nu(this.shape,t,!1)},e.prototype.tangentAt=function(t){var e=Nu(this.shape,t,!0);return Pt(e,e)},e}(_s);Bu.prototype.type="bezier-curve";var Eu=function(){this.cx=0,this.cy=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0},zu=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new Eu},e.prototype.buildPath=function(t,e){var n=e.cx,i=e.cy,r=Math.max(e.r,0),o=e.startAngle,a=e.endAngle,s=e.clockwise,l=Math.cos(o),u=Math.sin(o);t.moveTo(l*r+n,u*r+i),t.arc(n,i,r,o,a,!s)},e}(_s);zu.prototype.type="arc";var Fu=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="compound",e}return n(e,t),e.prototype._updatePathDirty=function(){for(var t=this.shape.paths,e=this.shapeChanged(),n=0;n<t.length;n++)e=e||t[n].shapeChanged();e&&this.dirtyShape()},e.prototype.beforeBrush=function(){this._updatePathDirty();for(var t=this.shape.paths||[],e=this.getGlobalScale(),n=0;n<t.length;n++)t[n].path||t[n].createPathProxy(),t[n].path.setScale(e[0],e[1],t[n].segmentIgnoreThreshold)},e.prototype.buildPath=function(t,e){for(var n=e.paths||[],i=0;i<n.length;i++)n[i].buildPath(t,n[i].shape,!0)},e.prototype.afterBrush=function(){for(var t=this.shape.paths||[],e=0;e<t.length;e++)t[e].pathUpdated()},e.prototype.getBoundingRect=function(){return this._updatePathDirty.call(this),_s.prototype.getBoundingRect.call(this)},e}(_s),Vu=function(){function t(t){this.colorStops=t||[]}return t.prototype.addColorStop=function(t,e){this.colorStops.push({offset:t,color:e})},t}(),Wu=function(t){function e(e,n,i,r,o,a){var s=t.call(this,o)||this;return s.x=null==e?0:e,s.y=null==n?0:n,s.x2=null==i?1:i,s.y2=null==r?0:r,s.type="linear",s.global=a||!1,s}return n(e,t),e}(Vu),Hu=function(t){function e(e,n,i,r,o){var a=t.call(this,r)||this;return a.x=null==e?.5:e,a.y=null==n?.5:n,a.r=null==i?.5:i,a.type="radial",a.global=o||!1,a}return n(e,t),e}(Vu),Gu=Math.min,Uu=Math.max,Xu=Math.abs,Yu=[0,0],qu=[0,0],Zu=Ee(),ju=Zu.minTv,Ku=Zu.maxTv,$u=function(){function t(t,e){this._corners=[],this._axes=[],this._origin=[0,0];for(var n=0;n<4;n++)this._corners[n]=new _e;for(n=0;n<2;n++)this._axes[n]=new _e;t&&this.fromBoundingRect(t,e)}return t.prototype.fromBoundingRect=function(t,e){var n=this._corners,i=this._axes,r=t.x,o=t.y,a=r+t.width,s=o+t.height;if(n[0].set(r,o),n[1].set(a,o),n[2].set(a,s),n[3].set(r,s),e)for(var l=0;l<4;l++)n[l].transform(e);_e.sub(i[0],n[1],n[0]),_e.sub(i[1],n[3],n[0]),i[0].normalize(),i[1].normalize();for(l=0;l<2;l++)this._origin[l]=i[l].dot(n[0])},t.prototype.intersect=function(t,e,n){var i=!0,r=!e;return e&&_e.set(e,0,0),Zu.reset(n,!r),!this._intersectCheckOneSide(this,t,r,1)&&(i=!1,r)||!this._intersectCheckOneSide(t,this,r,-1)&&(i=!1,r)||r||Zu.negativeSize||_e.copy(e,i?Zu.useDir?Zu.dirMinTv:ju:Ku),i},t.prototype._intersectCheckOneSide=function(t,e,n,i){for(var r=!0,o=0;o<2;o++){var a=t._axes[o];if(t._getProjMinMaxOnAxis(o,t._corners,Yu),t._getProjMinMaxOnAxis(o,e._corners,qu),Zu.negativeSize||Yu[1]<qu[0]||Yu[0]>qu[1]){if(r=!1,Zu.negativeSize||n)return r;var s=Xu(qu[0]-Yu[1]),l=Xu(Yu[0]-qu[1]);Gu(s,l)>Ku.len()&&(s<l?_e.scale(Ku,a,-s*i):_e.scale(Ku,a,l*i))}else if(!n){s=Xu(qu[0]-Yu[1]),l=Xu(Yu[0]-qu[1]);(Zu.useDir||Gu(s,l)<ju.len())&&((s<l||!Zu.bidirectional)&&(_e.scale(ju,a,s*i),Zu.useDir&&Zu.calcDirMTV()),(s>=l||!Zu.bidirectional)&&(_e.scale(ju,a,-l*i),Zu.useDir&&Zu.calcDirMTV()))}}return r},t.prototype._getProjMinMaxOnAxis=function(t,e,n){for(var i=this._axes[t],r=this._origin,o=e[0].dot(i)+r[t],a=o,s=o,l=1;l<e.length;l++){var u=e[l].dot(i)+r[t];a=Gu(u,a),s=Uu(u,s)}n[0]=a+Zu.touchThreshold,n[1]=s-Zu.touchThreshold,Zu.negativeSize=n[1]<n[0]},t}(),Qu=[],Ju=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.notClear=!0,e.incremental=!0,e._displayables=[],e._temporaryDisplayables=[],e._cursor=0,e}return n(e,t),e.prototype.traverse=function(t,e){t.call(e,this)},e.prototype.useStyle=function(){this.style={}},e.prototype.getCursor=function(){return this._cursor},e.prototype.innerAfterBrush=function(){this._cursor=this._displayables.length},e.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.markRedraw(),this.notClear=!1},e.prototype.clearTemporalDisplayables=function(){this._temporaryDisplayables=[]},e.prototype.addDisplayable=function(t,e){e?this._temporaryDisplayables.push(t):this._displayables.push(t),this.markRedraw()},e.prototype.addDisplayables=function(t,e){e=e||!1;for(var n=0;n<t.length;n++)this.addDisplayable(t[n],e)},e.prototype.getDisplayables=function(){return this._displayables},e.prototype.getTemporalDisplayables=function(){return this._temporaryDisplayables},e.prototype.eachPendingDisplayable=function(t){for(var e=this._cursor;e<this._displayables.length;e++)t&&t(this._displayables[e]);for(e=0;e<this._temporaryDisplayables.length;e++)t&&t(this._temporaryDisplayables[e])},e.prototype.update=function(){this.updateTransform();for(var t=this._cursor;t<this._displayables.length;t++){(e=this._displayables[t]).parent=this,e.update(),e.parent=null}for(t=0;t<this._temporaryDisplayables.length;t++){var e;(e=this._temporaryDisplayables[t]).parent=this,e.update(),e.parent=null}},e.prototype.getBoundingRect=function(){if(!this._rect){for(var t=new Oe(1/0,1/0,-1/0,-1/0),e=0;e<this._displayables.length;e++){var n=this._displayables[e],i=n.getBoundingRect().clone();n.needLocalTransform()&&i.applyTransform(n.getLocalTransform(Qu)),t.union(i)}this._rect=t}return this._rect},e.prototype.contain=function(t,e){var n=this.transformCoordToLocal(t,e);if(this.getBoundingRect().contain(n[0],n[1]))for(var i=0;i<this._displayables.length;i++){if(this._displayables[i].contain(t,e))return!0}return!1},e}(ma),th=To();function eh(t,e,n,i,r,o,a){var s,l=!1;G(r)?(a=o,o=r,r=null):q(r)&&(o=r.cb,a=r.during,l=r.isFrom,s=r.removeOpt,r=r.dataIndex);var u="leave"===t;u||e.stopAnimation("leave");var h=function(t,e,n,i,r){var o;if(e&&e.ecModel){var a=e.ecModel.getUpdatePayload();o=a&&a.animation}var s="update"===t;if(e&&e.isAnimationEnabled()){var l=void 0,u=void 0,h=void 0;return i?(l=nt(i.duration,200),u=nt(i.easing,"cubicOut"),h=0):(l=e.getShallow(s?"animationDurationUpdate":"animationDuration"),u=e.getShallow(s?"animationEasingUpdate":"animationEasing"),h=e.getShallow(s?"animationDelayUpdate":"animationDelay")),o&&(null!=o.duration&&(l=o.duration),null!=o.easing&&(u=o.easing),null!=o.delay&&(h=o.delay)),G(h)&&(h=h(n,r)),G(l)&&(l=l(n)),{duration:l||0,delay:h,easing:u}}return null}(t,i,r,u?s||{}:null,i&&i.getAnimationDelayParams?i.getAnimationDelayParams(e,r):null);if(h&&h.duration>0){var c={duration:h.duration,delay:h.delay||0,easing:h.easing,done:o,force:!!o||!!a,setToFinal:!u,scope:t,during:a};l?e.animateFrom(n,c):e.animateTo(n,c)}else e.stopAnimation(),!l&&e.attr(n),a&&a(1),o&&o()}function nh(t,e,n,i,r,o){eh("update",t,e,n,i,r,o)}function ih(t,e,n,i,r,o){eh("enter",t,e,n,i,r,o)}function rh(t){if(!t.__zr)return!0;for(var e=0;e<t.animators.length;e++){if("leave"===t.animators[e].scope)return!0}return!1}function oh(t,e,n,i,r,o){rh(t)||eh("leave",t,e,n,i,r,o)}function ah(t,e,n,i){t.removeTextContent(),t.removeTextGuideLine(),oh(t,{style:{opacity:0}},e,n,i)}function sh(t,e,n){function i(){t.parent&&t.parent.remove(t)}t.isGroup?t.traverse((function(t){t.isGroup||ah(t,e,n,i)})):ah(t,e,n,i)}function lh(t){th(t).oldStyle=t.style}var uh={},hh=["x","y"],ch=["width","height"];var ph=function(t,e){var i=iu(t,e);return function(t){function e(e){var n=t.call(this,e)||this;return n.applyTransform=i.applyTransform,n.buildPath=i.buildPath,n}return n(e,t),e}(eu)};function fh(t,e){uh[t]=e}function dh(t,e,n,i){var r=function(t,e){return new eu(iu(t,e))}(t,e);return n&&("center"===i&&(n=vh(n,r.getBoundingRect())),mh(r,n)),r}function gh(t,e,n){var i=new Ms({style:{image:t,x:e.x,y:e.y,width:e.width,height:e.height},onload:function(t){if("center"===n){var r={width:t.width,height:t.height};i.setStyle(vh(e,r))}}});return i}function vh(t,e){var n,i=e.width/e.height,r=t.height*i;return n=r<=t.width?t.height:(r=t.width)/i,{x:t.x+t.width/2-r/2,y:t.y+t.height/2-n/2,width:r,height:n}}var yh=function(t,e){for(var n=[],i=t.length,r=0;r<i;r++){var o=t[r];n.push(o.getUpdatedPathProxy(!0))}var a=new _s(e);return a.createPathProxy(),a.buildPath=function(t){if(nu(t)){t.appendPath(n);var e=t.getContext();e&&t.rebuildPath(e,1)}},a};function mh(t,e){if(t.applyTransform){var n=t.getBoundingRect().calculateTransform(e);t.applyTransform(n)}}function _h(t,e){return ks(t,t,{lineWidth:e}),t}function xh(t){return!t.isGroup}function bh(t,e,n,i,r){return null==e||(Y(e)?wh[0]=wh[1]=wh[2]=wh[3]=e:(wh[0]=e[0],wh[1]=e[1],wh[2]=e[2],wh[3]=e[3]),i&&(wh[0]=Br(0,wh[0]),wh[1]=Br(0,wh[1]),wh[2]=Br(0,wh[2]),wh[3]=Br(0,wh[3])),n&&(wh[0]=-wh[0],wh[1]=-wh[1],wh[2]=-wh[2],wh[3]=-wh[3]),Sh(t,wh,"x","width",3,1,r&&r[0]||0),Sh(t,wh,"y","height",0,2,r&&r[1]||0)),t}var wh=[0,0,0,0];function Sh(t,e,n,i,r,o,a){var s=e[o]+e[r],l=t[i];t[i]+=s,a=Br(0,Nr(a,l)),t[i]<a?(t[i]=a,t[n]+=e[r]>=0?-e[r]:e[o]>=0?l+e[o]:Er(s)>1e-8?(l-a)*e[r]/s:0):t[n]-=e[r]}function Mh(t){var e=t.itemTooltipOption,n=t.componentModel,i=t.itemName,r=U(e)?{formatter:e}:e,o=n.mainType,a=n.componentIndex,s={componentType:o,name:i,$vars:["name"]};s[o+"Index"]=a;var l=t.formatterParamsExtra;l&&R(F(l),(function(t){mt(s,t)||(s[t]=l[t],s.$vars.push(t))}));var u=Ys(t.el);u.componentMainType=o,u.componentIndex=a,u.tooltipConfig={name:i,option:D({content:i,encodeHTMLContent:!0,formatterParams:s},r)}}function Th(t,e){var n;t.isGroup&&(n=e(t)),n||t.traverse(e)}function kh(t,e){if(t)if(H(t))for(var n=0;n<t.length;n++)Th(t[n],e);else Th(t,e)}function Ch(t){return!t||Er(t[1])<Dh&&Er(t[2])<Dh||Er(t[0])<Dh&&Er(t[3])<Dh}var Dh=1e-5;function Ih(t,e){return t?Oe.copy(t,e):e.clone()}function Ah(t,e){return e?pe(t||[1,0,0,1,0,0],e):void 0}function Lh(t,e,n){Ph(t,e,n,-1/0)}function Ph(t,e,n,i){if(t.ignoreModelZ)return i;var r=t.getTextContent(),o=t.getTextGuideLine();if(t.isGroup)for(var a=t.childrenRef(),s=0;s<a.length;s++)i=Br(Ph(a[s],e,n,i),i);else t.z=e,t.zlevel=n,i=Br(t.z2||0,i);if(r&&(r.z=e,r.zlevel=n,isFinite(i)&&(r.z2=i+2)),o){var l=t.textGuideLineConfig;o.z=e,o.zlevel=n,isFinite(i)&&(o.z2=i+(l&&l.showAbove?1:-1))}return i}fh("circle",ou),fh("ellipse",su),fh("sector",wu),fh("ring",Mu),fh("polygon",Cu),fh("polyline",Iu),fh("rect",As),fh("line",Pu),fh("bezierCurve",Bu),fh("arc",zu);var Oh={};function Rh(t,e,n){var i,r=t.labelFetcher,o=t.labelDataIndex,a=t.labelDimIndex,s=e.normal;r&&(i=r.getFormattedLabel(o,"normal",null,a,s&&s.get("formatter"),null!=n?{interpolatedValue:n}:null)),null==i&&(i=G(t.defaultText)?t.defaultText(o,t,n):t.defaultText);for(var l={normal:i},u=0;u<$s.length;u++){var h=$s[u],c=e[h];l[h]=nt(r?r.getFormattedLabel(o,h,null,a,c&&c.get("formatter")):null,i)}return l}function Nh(t,e,n,i){n=n||Oh;for(var r=t instanceof Rs,o=!1,a=0;a<Qs.length;a++){if((p=e[Qs[a]])&&p.getShallow("show")){o=!0;break}}var s=r?t:t.getTextContent();if(o){r||(s||(s=new Rs,t.setTextContent(s)),t.stateProxy&&(s.stateProxy=t.stateProxy));var l=Rh(n,e),u=e.normal,h=!!u.getShallow("show"),c=Eh(u,i&&i.normal,n,!1,!r);c.text=l.normal,r||t.setTextConfig(zh(u,n,!1));for(a=0;a<$s.length;a++){var p,f=$s[a];if(p=e[f]){var d=s.ensureState(f),g=!!nt(p.getShallow("show"),h);if(g!==h&&(d.ignore=!g),d.style=Eh(p,i&&i[f],n,!0,!r),d.style.text=l[f],!r)t.ensureState(f).textConfig=zh(p,n,!0)}}s.silent=!!u.getShallow("silent"),null!=s.style.x&&(c.x=s.style.x),null!=s.style.y&&(c.y=s.style.y),s.ignore=!h,s.useStyle(c),s.dirty(),n.enableTextSetter&&(Gh(s).setLabelText=function(t){var i=Rh(n,e,t);!function(t,e){for(var n=0;n<$s.length;n++){var i=$s[n],r=e[i],o=t.ensureState(i);o.style=o.style||{},o.style.text=r}var a=t.currentStates.slice();t.clearStates(!0),t.setStyle({text:e.normal}),t.useStates(a,!0)}(s,i)})}else s&&(s.ignore=!0);t.dirty()}function Bh(t,e){e=e||"label";for(var n={normal:t.getModel(e)},i=0;i<$s.length;i++){var r=$s[i];n[r]=t.getModel([r,e])}return n}function Eh(t,e,n,i,r){var o={};return function(t,e,n,i,r){n=n||Oh;var o,a=e.ecModel,s=a&&a.option.textStyle,l=function(t){var e;for(;t&&t!==t.ecModel;){var n=(t.option||Oh).rich;if(n){e=e||{};for(var i=F(n),r=0;r<i.length;r++){e[i[r]]=1}}t=t.parentModel}return e}(e);if(l){o={};var u="richInheritPlainLabel",h=nt(e.get(u),a?a.get(u):void 0);for(var c in l)if(l.hasOwnProperty(c)){var p=e.getModel(["rich",c]);Hh(o[c]={},p,s,e,h,n,i,r,!1,!0)}}o&&(t.rich=o);var f=e.get("overflow");f&&(t.overflow=f);var d=e.get("lineOverflow");d&&(t.lineOverflow=d);var g=t,v=e.get("minMargin");if(null!=v)v=Y(v)?v/2:0,g.margin=[v,v,v,v],g.__marginType=Yh.minMargin;else{var y=e.get("textMargin");null!=y&&(g.margin=ot(y),g.__marginType=Yh.textMargin)}Hh(t,e,s,null,null,n,i,r,!0,!1)}(o,t,n,i,r),e&&C(o,e),o}function zh(t,e,n){e=e||{};var i,r={},o=t.getShallow("rotate"),a=nt(t.getShallow("distance"),n?null:5),s=t.getShallow("offset");return"outside"===(i=t.getShallow("position")||(n?null:"inside"))&&(i=e.defaultOutsidePosition||"top"),null!=i&&(r.position=i),null!=s&&(r.offset=s),null!=o&&(o*=Math.PI/180,r.rotation=o),null!=a&&(r.distance=a),r.outsideFill="inherit"===t.get("color")?e.inheritColor||null:"auto",null!=e.autoOverflowArea&&(r.autoOverflowArea=e.autoOverflowArea),null!=e.layoutRect&&(r.layoutRect=e.layoutRect),r}var Fh=["fontStyle","fontWeight","fontSize","fontFamily","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY"],Vh=["align","lineHeight","width","height","tag","verticalAlign","ellipsis"],Wh=["padding","borderWidth","borderRadius","borderDashOffset","backgroundColor","borderColor","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"];function Hh(t,e,n,i,r,o,a,s,l,u){n=!a&&n||Oh;var h=o&&o.inheritColor,c=e.getShallow("color"),p=e.getShallow("textBorderColor"),f=nt(e.getShallow("opacity"),n.opacity);"inherit"!==c&&"auto"!==c||(c=h||null),"inherit"!==p&&"auto"!==p||(p=h||null),s||(c=c||n.color,p=p||n.textBorderColor),null!=c&&(t.fill=c),null!=p&&(t.stroke=p);var d=nt(e.getShallow("textBorderWidth"),n.textBorderWidth);null!=d&&(t.lineWidth=d);var g=nt(e.getShallow("textBorderType"),n.textBorderType);null!=g&&(t.lineDash=g);var v=nt(e.getShallow("textBorderDashOffset"),n.textBorderDashOffset);null!=v&&(t.lineDashOffset=v),a||null!=f||u||(f=o&&o.defaultOpacity),null!=f&&(t.opacity=f),a||s||null==t.fill&&o.inheritColor&&(t.fill=o.inheritColor);for(var y=0;y<Fh.length;y++){var m=Fh[y];null!=(x=!1!==r&&i?it(e.getShallow(m),i.getShallow(m),n[m]):nt(e.getShallow(m),n[m]))&&(t[m]=x)}for(y=0;y<Vh.length;y++){m=Vh[y];null!=(x=e.getShallow(m))&&(t[m]=x)}if(null==t.verticalAlign){var _=e.getShallow("baseline");null!=_&&(t.verticalAlign=_)}if(!l||!o.disableBox){for(y=0;y<Wh.length;y++){var x;m=Wh[y];null!=(x=e.getShallow(m))&&(t[m]=x)}var b=e.getShallow("borderType");null!=b&&(t.borderDash=b),"auto"!==t.backgroundColor&&"inherit"!==t.backgroundColor||!h||(t.backgroundColor=h),"auto"!==t.borderColor&&"inherit"!==t.borderColor||!h||(t.borderColor=h)}}var Gh=To();var Uh,Xh,Yh={minMargin:1,textMargin:2},qh=["textStyle","color"],Zh=["fontStyle","fontWeight","fontSize","fontFamily","padding","lineHeight","rich","width","height","overflow"],jh=new Rs,Kh=function(){function t(){}return t.prototype.getTextColor=function(t){var e=this.ecModel;return this.getShallow("color")||(!t&&e?e.get(qh):null)},t.prototype.getFont=function(){return t={fontStyle:this.getShallow("fontStyle"),fontWeight:this.getShallow("fontWeight"),fontSize:this.getShallow("fontSize"),fontFamily:this.getShallow("fontFamily")},e=this.ecModel,n=e&&e.getModel("textStyle"),st([t.fontStyle||n&&n.getShallow("fontStyle")||"",t.fontWeight||n&&n.getShallow("fontWeight")||"",(t.fontSize||n&&n.getShallow("fontSize")||12)+"px",t.fontFamily||n&&n.getShallow("fontFamily")||"sans-serif"].join(" "));var t,e,n},t.prototype.getTextRect=function(t){for(var e={text:t,verticalAlign:this.getShallow("verticalAlign")||this.getShallow("baseline")},n=0;n<Zh.length;n++)e[Zh[n]]=this.getShallow(Zh[n]);return jh.useStyle(e),jh.update(),jh.getBoundingRect()},t}(),$h=[["lineWidth","width"],["stroke","color"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","type"],["lineDashOffset","dashOffset"],["lineCap","cap"],["lineJoin","join"],["miterLimit"]],Qh=Wo($h),Jh=function(){function t(){}return t.prototype.getLineStyle=function(t){return Qh(this,t)},t}(),tc=[["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","borderType"],["lineDashOffset","borderDashOffset"],["lineCap","borderCap"],["lineJoin","borderJoin"],["miterLimit","borderMiterLimit"]],ec=Wo(tc),nc=function(){function t(){}return t.prototype.getItemStyle=function(t,e){return ec(this,t,e)},t}(),ic=function(){function t(t,e,n){this.parentModel=e,this.ecModel=n,this.option=t}return t.prototype.init=function(t,e,n){for(var i=[],r=3;r<arguments.length;r++)i[r-3]=arguments[r]},t.prototype.mergeOption=function(t,e){k(this.option,t,!0)},t.prototype.get=function(t,e){return null==t?this.option:this._doGet(this.parsePath(t),!e&&this.parentModel)},t.prototype.getShallow=function(t,e){var n=this.option,i=null==n?n:n[t];if(null==i&&!e){var r=this.parentModel;r&&(i=r.getShallow(t))}return i},t.prototype.getModel=function(e,n){var i=null!=e,r=i?this.parsePath(e):null;return new t(i?this._doGet(r):this.option,n=n||this.parentModel&&this.parentModel.getModel(this.resolveParentPath(r)),this.ecModel)},t.prototype.isEmpty=function(){return null==this.option},t.prototype.restoreData=function(){},t.prototype.clone=function(){return new(0,this.constructor)(T(this.option))},t.prototype.parsePath=function(t){return"string"==typeof t?t.split("."):t},t.prototype.resolveParentPath=function(t){return t},t.prototype.isAnimationEnabled=function(){if(!r.node&&this.option){if(null!=this.option.animation)return!!this.option.animation;if(this.parentModel)return this.parentModel.isAnimationEnabled()}},t.prototype._doGet=function(t,e){var n=this.option;if(!t)return n;for(var i=0;i<t.length&&(!t[i]||null!=(n=n&&"object"==typeof n?n[t[i]]:null));i++);return null==n&&e&&(n=e._doGet(this.resolveParentPath(t),e.parentModel)),n},t}();No(ic),Uh=ic,Xh=["__\0is_clz",Eo++].join("_"),Uh.prototype[Xh]=!0,Uh.isInstance=function(t){return!(!t||!t[Xh])},P(ic,Jh),P(ic,nc),P(ic,Go),P(ic,Kh);var rc=Math.round(10*Math.random());function oc(t){return[t||"",rc++].join("_")}var ac="ZH",sc="EN",lc=sc,uc={},hc={},cc=r.domSupported&&(document.documentElement.lang||navigator.language||navigator.browserLanguage||lc).toUpperCase().indexOf(ac)>-1?ac:lc;function pc(t,e){t=t.toUpperCase(),hc[t]=new ic(e),uc[t]=e}pc(sc,{time:{month:["January","February","March","April","May","June","July","August","September","October","November","December"],monthAbbr:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],dayOfWeek:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayOfWeekAbbr:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"]},legend:{selector:{all:"All",inverse:"Inv"}},toolbox:{brush:{title:{rect:"Box Select",polygon:"Lasso Select",lineX:"Horizontally Select",lineY:"Vertically Select",keep:"Keep Selections",clear:"Clear Selections"}},dataView:{title:"Data View",lang:["Data View","Close","Refresh"]},dataZoom:{title:{zoom:"Zoom",back:"Zoom Reset"}},magicType:{title:{line:"Switch to Line Chart",bar:"Switch to Bar Chart",stack:"Stack",tiled:"Tile"}},restore:{title:"Restore"},saveAsImage:{title:"Save as Image",lang:["Right Click to Save Image"]}},series:{typeNames:{pie:"Pie chart",bar:"Bar chart",line:"Line chart",scatter:"Scatter plot",effectScatter:"Ripple scatter plot",radar:"Radar chart",tree:"Tree",treemap:"Treemap",boxplot:"Boxplot",candlestick:"Candlestick",k:"K line chart",heatmap:"Heat map",map:"Map",parallel:"Parallel coordinate map",lines:"Line graph",graph:"Relationship graph",sankey:"Sankey diagram",funnel:"Funnel chart",gauge:"Gauge",pictorialBar:"Pictorial bar",themeRiver:"Theme River Map",sunburst:"Sunburst",custom:"Custom chart",chart:"Chart"}},aria:{general:{withTitle:'This is a chart about "{title}"',withoutTitle:"This is a chart"},series:{single:{prefix:"",withName:" with type {seriesType} named {seriesName}.",withoutName:" with type {seriesType}."},multiple:{prefix:". It consists of {seriesCount} series count.",withName:" The {seriesId} series is a {seriesType} representing {seriesName}.",withoutName:" The {seriesId} series is a {seriesType}.",separator:{middle:"",end:""}}},data:{allData:"The data is as follows: ",partialData:"The first {displayCnt} items are: ",withName:"the data for {name} is {value}",withoutName:"{value}",separator:{middle:", ",end:". "}}}}),pc(ac,{time:{month:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],monthAbbr:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],dayOfWeek:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"],dayOfWeekAbbr:["日","一","二","三","四","五","六"]},legend:{selector:{all:"全选",inverse:"反选"}},toolbox:{brush:{title:{rect:"矩形选择",polygon:"圈选",lineX:"横向选择",lineY:"纵向选择",keep:"保持选择",clear:"清除选择"}},dataView:{title:"数据视图",lang:["数据视图","关闭","刷新"]},dataZoom:{title:{zoom:"区域缩放",back:"区域缩放还原"}},magicType:{title:{line:"切换为折线图",bar:"切换为柱状图",stack:"切换为堆叠",tiled:"切换为平铺"}},restore:{title:"还原"},saveAsImage:{title:"保存为图片",lang:["右键另存为图片"]}},series:{typeNames:{pie:"饼图",bar:"柱状图",line:"折线图",scatter:"散点图",effectScatter:"涟漪散点图",radar:"雷达图",tree:"树图",treemap:"矩形树图",boxplot:"箱型图",candlestick:"K线图",k:"K线图",heatmap:"热力图",map:"地图",parallel:"平行坐标图",lines:"线图",graph:"关系图",sankey:"桑基图",funnel:"漏斗图",gauge:"仪表盘图",pictorialBar:"象形柱图",themeRiver:"主题河流图",sunburst:"旭日图",custom:"自定义图表",chart:"图表"}},aria:{general:{withTitle:"这是一个关于“{title}”的图表。",withoutTitle:"这是一个图表，"},series:{single:{prefix:"",withName:"图表类型是{seriesType}，表示{seriesName}。",withoutName:"图表类型是{seriesType}。"},multiple:{prefix:"它由{seriesCount}个图表系列组成。",withName:"第{seriesId}个系列是一个表示{seriesName}的{seriesType}，",withoutName:"第{seriesId}个系列是一个{seriesType}，",separator:{middle:"；",end:"。"}}},data:{allData:"其数据是——",partialData:"其中，前{displayCnt}项是——",withName:"{name}的数据是{value}",withoutName:"{value}",separator:{middle:"，",end:""}}}});var fc=1e3,dc=6e4,gc=36e5,vc=864e5,yc=31536e6,mc={year:/({yyyy}|{yy})/,month:/({MMMM}|{MMM}|{MM}|{M})/,day:/({dd}|{d})/,hour:/({HH}|{H}|{hh}|{h})/,minute:/({mm}|{m})/,second:/({ss}|{s})/,millisecond:/({SSS}|{S})/},_c={year:"{yyyy}",month:"{MMM}",day:"{d}",hour:"{HH}:{mm}",minute:"{HH}:{mm}",second:"{HH}:{mm}:{ss}",millisecond:"{HH}:{mm}:{ss} {SSS}"},xc="{yyyy}-{MM}-{dd}",bc={year:"{yyyy}",month:"{yyyy}-{MM}",day:xc,hour:xc+" "+_c.hour,minute:xc+" "+_c.minute,second:xc+" "+_c.second,millisecond:"{yyyy}-{MM}-{dd} {HH}:{mm}:{ss} {SSS}"},wc=["year","month","day","hour","minute","second","millisecond"],Sc=["year","half-year","quarter","month","week","half-week","day","half-day","quarter-day","hour","minute","second","millisecond"];function Mc(t){return U(t)||G(t)?t:function(t){t=t||{};var e={},n=!0;return R(wc,(function(e){n&&(n=null==t[e])})),R(wc,(function(i,r){var o=t[i];e[i]={};for(var a=null,s=r;s>=0;s--){var l=wc[s],u=q(o)&&!H(o)?o[l]:o,h=void 0;H(u)?a=(h=u.slice())[0]||"":U(u)?h=[a=u]:(null==a?a=_c[i]:mc[l].test(a)||(a=e[l][l][0]+" "+a),h=[a],n&&(h[1]="{primary|"+a+"}")),e[i][l]=h}})),e}(t)}function Tc(t,e){return"0000".substr(0,e-(t+="").length)+t}function kc(t){switch(t){case"half-year":case"quarter":return"month";case"week":case"half-week":return"day";case"half-day":case"quarter-day":return"hour";default:return t}}function Cc(t){return t===kc(t)}function Dc(t,e,n,i){var r=Kr(t),o=r[Lc(n)](),a=r[Pc(n)]()+1,s=Math.floor((a-1)/3)+1,l=r[Oc(n)](),u=r["get"+(n?"UTC":"")+"Day"](),h=r[Rc(n)](),c=(h-1)%12+1,p=r[Nc(n)](),f=r[Bc(n)](),d=r[Ec(n)](),g=h>=12?"pm":"am",v=g.toUpperCase(),y=i instanceof ic?i:function(t){return hc[t]}(i||cc)||hc[lc],m=y.getModel("time"),_=m.get("month"),x=m.get("monthAbbr"),b=m.get("dayOfWeek"),w=m.get("dayOfWeekAbbr");return(e||"").replace(/{a}/g,g+"").replace(/{A}/g,v+"").replace(/{yyyy}/g,o+"").replace(/{yy}/g,Tc(o%100+"",2)).replace(/{Q}/g,s+"").replace(/{MMMM}/g,_[a-1]).replace(/{MMM}/g,x[a-1]).replace(/{MM}/g,Tc(a,2)).replace(/{M}/g,a+"").replace(/{dd}/g,Tc(l,2)).replace(/{d}/g,l+"").replace(/{eeee}/g,b[u]).replace(/{ee}/g,w[u]).replace(/{e}/g,u+"").replace(/{HH}/g,Tc(h,2)).replace(/{H}/g,h+"").replace(/{hh}/g,Tc(c+"",2)).replace(/{h}/g,c+"").replace(/{mm}/g,Tc(p,2)).replace(/{m}/g,p+"").replace(/{ss}/g,Tc(f,2)).replace(/{s}/g,f+"").replace(/{SSS}/g,Tc(d,3)).replace(/{S}/g,d+"")}function Ic(t,e){var n=Kr(t),i=n[Pc(e)]()+1,r=n[Oc(e)](),o=n[Rc(e)](),a=n[Nc(e)](),s=n[Bc(e)](),l=0===n[Ec(e)](),u=l&&0===s,h=u&&0===a,c=h&&0===o,p=c&&1===r;return p&&1===i?"year":p?"month":c?"day":h?"hour":u?"minute":l?"second":"millisecond"}function Ac(t,e,n){switch(e){case"year":t[Fc(n)](0);case"month":t[Vc(n)](1);case"day":t[Wc(n)](0);case"hour":t[Hc(n)](0);case"minute":t[Gc(n)](0);case"second":t[Uc(n)](0)}return t}function Lc(t){return t?"getUTCFullYear":"getFullYear"}function Pc(t){return t?"getUTCMonth":"getMonth"}function Oc(t){return t?"getUTCDate":"getDate"}function Rc(t){return t?"getUTCHours":"getHours"}function Nc(t){return t?"getUTCMinutes":"getMinutes"}function Bc(t){return t?"getUTCSeconds":"getSeconds"}function Ec(t){return t?"getUTCMilliseconds":"getMilliseconds"}function zc(t){return t?"setUTCFullYear":"setFullYear"}function Fc(t){return t?"setUTCMonth":"setMonth"}function Vc(t){return t?"setUTCDate":"setDate"}function Wc(t){return t?"setUTCHours":"setHours"}function Hc(t){return t?"setUTCMinutes":"setMinutes"}function Gc(t){return t?"setUTCSeconds":"setSeconds"}function Uc(t){return t?"setUTCMilliseconds":"setMilliseconds"}function Xc(t){if(!eo(t))return U(t)?t:"-";var e=(t+"").split(".");return e[0].replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(e.length>1?"."+e[1]:"")}var Yc=ot,qc=["a","b","c","d","e","f","g"],Zc=function(t,e){return"{"+t+(null==e?"":e)+"}"};function jc(t,e,n){H(e)||(e=[e]);var i=e.length;if(!i)return"";for(var r=e[0].$vars||[],o=0;o<r.length;o++){var a=qc[o];t=t.replace(Zc(a),Zc(a,0))}for(var s=0;s<i;s++)for(var l=0;l<r.length;l++){var u=e[s][r[l]];t=t.replace(Zc(qc[l],s),n?Jt(u):u)}return t}function Kc(t,e){return e=e||"transparent",U(t)?t:q(t)&&t.colorStops&&(t.colorStops[0]||{}).color||e}var $c={},Qc={},Jc=function(){function t(){this._normalMasterList=[],this._nonSeriesBoxMasterList=[]}return t.prototype.create=function(t,e){function n(n,i){var r=[];return R(n,(function(n,i){var o=n.create(t,e);r=r.concat(o||[])})),r}this._nonSeriesBoxMasterList=n($c,!0),this._normalMasterList=n(Qc,!1)},t.prototype.update=function(t,e){R(this._normalMasterList,(function(n){n.update&&n.update(t,e)}))},t.prototype.getCoordinateSystems=function(){return this._normalMasterList.concat(this._nonSeriesBoxMasterList)},t.register=function(t,e){"matrix"!==t&&"calendar"!==t?Qc[t]=e:$c[t]=e},t.get=function(t){return Qc[t]||$c[t]},t}();var tp=1,ep=2;var np=dt();var ip=0,rp=1,op=2;function ap(t,e){var n=t.getShallow("coordinateSystem"),i=t.getShallow("coordinateSystemUsage",!0),r=ip;if(n){var o="series"===t.mainType;null==i&&(i=o?"data":"box"),"data"===i?(r=rp,o||(r=ip)):"box"===i&&(r=op,o||function(t){return!!$c[t]}(n)||(r=ip))}return{coordSysType:n,kind:r}}var sp=R,lp=["left","right","top","bottom","width","height"],up=[["width","left","right"],["height","top","bottom"]];function hp(t,e,n,i,r){var o=0,a=0;null==i&&(i=1/0),null==r&&(r=1/0);var s=0;e.eachChild((function(l,u){var h,c,p=l.getBoundingRect(),f=e.childAt(u+1),d=f&&f.getBoundingRect();if("horizontal"===t){var g=p.width+(d?-d.x+p.x:0);(h=o+g)>i||l.newline?(o=0,h=g,a+=s+n,s=p.height):s=Math.max(s,p.height)}else{var v=p.height+(d?-d.y+p.y:0);(c=a+v)>r||l.newline?(o+=s+n,a=0,c=v,s=p.width):s=Math.max(s,p.width)}l.newline||(l.x=o,l.y=a,l.markRedraw(),"horizontal"===t?o=h+n:a=c+n)}))}W(hp,"vertical"),W(hp,"horizontal");function cp(t,e){var n=function(t,e){var n,i,r=dp(t,e,{enableLayoutOnlyByCenter:!0}),o=t.getBoxLayoutParams();if(r.type===fp.point)i=r.refPoint,n=pp(o,{width:e.getWidth(),height:e.getHeight()});else{var a=t.get("center"),s=H(a)?a:[a,a];n=pp(o,r.refContainer),i=r.boxCoordFrom===ep?r.refPoint:[Fr(s[0],n.width)+n.x,Fr(s[1],n.height)+n.y]}return{viewRect:n,center:i}}(t,e),i=n.viewRect,r=n.center,o=t.get("radius");H(o)||(o=[0,o]);var a=Fr(i.width,e.getWidth()),s=Fr(i.height,e.getHeight()),l=Math.min(a,s),u=Fr(o[0],l/2),h=Fr(o[1],l/2);return{cx:r[0],cy:r[1],r0:u,r:h,viewRect:i}}function pp(t,e,n){n=Yc(n||0);var i=e.width,r=e.height,o=Fr(t.left,i),a=Fr(t.top,r),s=Fr(t.right,i),l=Fr(t.bottom,r),u=Fr(t.width,i),h=Fr(t.height,r),c=n[2]+n[0],p=n[1]+n[3],f=t.aspect;switch(isNaN(u)&&(u=i-s-p-o),isNaN(h)&&(h=r-l-c-a),null!=f&&(isNaN(u)&&isNaN(h)&&(f>i/r?u=.8*i:h=.8*r),isNaN(u)&&(u=f*h),isNaN(h)&&(h=u/f)),isNaN(o)&&(o=i-s-u-p),isNaN(a)&&(a=r-l-h-c),t.left||t.right){case"center":o=i/2-u/2-n[3];break;case"right":o=i-u-p}switch(t.top||t.bottom){case"middle":case"center":a=r/2-h/2-n[0];break;case"bottom":a=r-h-c}o=o||0,a=a||0,isNaN(u)&&(u=i-p-o-(s||0)),isNaN(h)&&(h=r-c-a-(l||0));var d=new Oe((e.x||0)+o+n[3],(e.y||0)+a+n[0],u,h);return d.margin=n,d}var fp={rect:1,point:2};function dp(t,e,n){var i,r,o,a,s=t.boxCoordinateSystem;if(s){var l=function(t){var e=t.getShallow("coord",!0),n=tp;if(null==e){var i=np.get(t.type);i&&i.getCoord2&&(n=ep,e=i.getCoord2(t))}return{coord:e,from:n}}(t),u=l.coord,h=l.from;if(s.dataToLayout){o=fp.rect,a=h;var c=s.dataToLayout(u);i=c.contentRect||c.rect}else n&&n.enableLayoutOnlyByCenter&&s.dataToPoint&&(o=fp.point,a=h,r=s.dataToPoint(u))}return null==o&&(o=fp.rect),o===fp.rect&&(i||(i={x:0,y:0,width:e.getWidth(),height:e.getHeight()}),r=[i.x+i.width/2,i.y+i.height/2]),{type:o,refContainer:i,refPoint:r,boxCoordFrom:a}}function gp(t){var e=t.layoutMode||t.constructor.layoutMode;return q(e)?e:e?{type:e}:null}function vp(t,e,n){var i=n&&n.ignoreSize;!H(i)&&(i=[i,i]);var r=a(up[0],0),o=a(up[1],1);function a(n,r){var o={},a=0,l={},u=0;if(sp(n,(function(e){l[e]=t[e]})),sp(n,(function(t){mt(e,t)&&(o[t]=l[t]=e[t]),s(o,t)&&a++,s(l,t)&&u++})),i[r])return s(e,n[1])?l[n[2]]=null:s(e,n[2])&&(l[n[1]]=null),l;if(2!==u&&a){if(a>=2)return o;for(var h=0;h<n.length;h++){var c=n[h];if(!mt(o,c)&&mt(t,c)){o[c]=t[c];break}}return o}return l}function s(t,e){return null!=t[e]&&"auto"!==t[e]}function l(t,e,n){sp(t,(function(t){e[t]=n[t]}))}l(up[0],t,r),l(up[1],t,o)}function yp(t){return function(t,e){return e&&t&&sp(lp,(function(n){mt(e,n)&&(t[n]=e[n])})),t}({},t)}var mp=To(),_p=function(t){function e(e,n,i){var r=t.call(this,e,n,i)||this;return r.uid=oc("ec_cpt_model"),r}var i;return n(e,t),e.prototype.init=function(t,e,n){this.mergeDefaultAndTheme(t,n)},e.prototype.mergeDefaultAndTheme=function(t,e){var n=gp(this),i=n?yp(t):{};k(t,e.getTheme().get(this.mainType)),k(t,this.getDefaultOption()),n&&vp(t,i,n)},e.prototype.mergeOption=function(t,e){k(this.option,t,!0);var n=gp(this);n&&vp(this.option,t,n)},e.prototype.optionUpdated=function(t,e){},e.prototype.getDefaultOption=function(){var t=this.constructor;if(!function(t){return!(!t||!t[Oo])}(t))return t.defaultOption;var e=mp(this);if(!e.defaultOption){for(var n=[],i=t;i;){var r=i.prototype.defaultOption;r&&n.push(r),i=i.superClass}for(var o={},a=n.length-1;a>=0;a--)o=k(o,n[a],!0);e.defaultOption=o}return e.defaultOption},e.prototype.getReferringComponents=function(t,e){var n=t+"Index",i=t+"Id";return Ao(this.ecModel,t,{index:this.get(n,!0),id:this.get(i,!0)},e)},e.prototype.getBoxLayoutParams=function(){return e=!1,{left:(t=this).getShallow("left",e),top:t.getShallow("top",e),right:t.getShallow("right",e),bottom:t.getShallow("bottom",e),width:t.getShallow("width",e),height:t.getShallow("height",e)};var t,e},e.prototype.getZLevelKey=function(){return""},e.prototype.setZLevel=function(t){this.option.zlevel=t},e.protoInitialize=((i=e.prototype).type="component",i.id="",i.name="",i.mainType="",i.subType="",void(i.componentIndex=0)),e}(ic);Bo(_p,ic),Vo(_p),function(t){var e={};t.registerSubTypeDefaulter=function(t,n){var i=Ro(t);e[i.main]=n},t.determineSubType=function(n,i){var r=i.type;if(!r){var o=Ro(n).main;t.hasSubTypes(n)&&e[o]&&(r=e[o](i))}return r}}(_p),function(t,e){function n(t,e){return t[e]||(t[e]={predecessor:[],successor:[]}),t[e]}t.topologicalTravel=function(t,i,r,o){if(t.length){var a=function(t){var i={},r=[];return R(t,(function(o){var a=n(i,o),s=function(t,e){var n=[];return R(t,(function(t){A(e,t)>=0&&n.push(t)})),n}(a.originalDeps=e(o),t);a.entryCount=s.length,0===a.entryCount&&r.push(o),R(s,(function(t){A(a.predecessor,t)<0&&a.predecessor.push(t);var e=n(i,t);A(e.successor,t)<0&&e.successor.push(o)}))})),{graph:i,noEntryList:r}}(i),s=a.graph,l=a.noEntryList,u={};for(R(t,(function(t){u[t]=!0}));l.length;){var h=l.pop(),c=s[h],p=!!u[h];p&&(r.call(o,h,c.originalDeps.slice()),delete u[h]),R(c.successor,p?d:f)}R(u,(function(){var t="";throw new Error(t)}))}function f(t){s[t].entryCount--,0===s[t].entryCount&&l.push(t)}function d(t){u[t]=!0,f(t)}}}(_p,(function(t){var e=[];R(_p.getClassesByMainType(t),(function(t){e=e.concat(t.dependencies||t.prototype.dependencies||[])})),e=N(e,(function(t){return Ro(t).main})),"dataset"!==t&&A(e,"dataset")<=0&&e.unshift("dataset");return e}));var xp={color:{},darkColor:{},size:{}},bp=xp.color={theme:["#5070dd","#b6d634","#505372","#ff994d","#0ca8df","#ffd10a","#fb628b","#785db0","#3fbe95"],neutral00:"#fff",neutral05:"#f4f7fd",neutral10:"#e8ebf0",neutral15:"#dbdee4",neutral20:"#cfd2d7",neutral25:"#c3c5cb",neutral30:"#b7b9be",neutral35:"#aaacb2",neutral40:"#9ea0a5",neutral45:"#929399",neutral50:"#86878c",neutral55:"#797b7f",neutral60:"#6d6e73",neutral65:"#616266",neutral70:"#54555a",neutral75:"#48494d",neutral80:"#3c3c41",neutral85:"#303034",neutral90:"#232328",neutral95:"#17171b",neutral99:"#000",accent05:"#eff1f9",accent10:"#e0e4f2",accent15:"#d0d6ec",accent20:"#c0c9e6",accent25:"#b1bbdf",accent30:"#a1aed9",accent35:"#91a0d3",accent40:"#8292cc",accent45:"#7285c6",accent50:"#6578ba",accent55:"#5c6da9",accent60:"#536298",accent65:"#4a5787",accent70:"#404c76",accent75:"#374165",accent80:"#2e3654",accent85:"#252b43",accent90:"#1b2032",accent95:"#121521",transparent:"rgba(0,0,0,0)",highlight:"rgba(255,231,130,0.8)"};for(var wp in C(bp,{primary:bp.neutral80,secondary:bp.neutral70,tertiary:bp.neutral60,quaternary:bp.neutral50,disabled:bp.neutral20,border:bp.neutral30,borderTint:bp.neutral20,borderShade:bp.neutral40,background:bp.neutral05,backgroundTint:"rgba(234,237,245,0.5)",backgroundTransparent:"rgba(255,255,255,0)",backgroundShade:bp.neutral10,shadow:"rgba(0,0,0,0.2)",shadowTint:"rgba(129,130,136,0.2)",axisLine:bp.neutral70,axisLineTint:bp.neutral40,axisTick:bp.neutral70,axisTickMinor:bp.neutral60,axisLabel:bp.neutral70,axisSplitLine:bp.neutral15,axisMinorSplitLine:bp.neutral05}),bp)if(bp.hasOwnProperty(wp)){var Sp=bp[wp];"theme"===wp?xp.darkColor.theme=bp.theme.slice():"highlight"===wp?xp.darkColor.highlight="rgba(255,231,130,0.4)":0===wp.indexOf("accent")?xp.darkColor[wp]=ti(Sp,null,(function(t){return.5*t}),(function(t){return Math.min(1,1.3-t)})):xp.darkColor[wp]=ti(Sp,null,(function(t){return.9*t}),(function(t){return 1-Math.pow(t,1.5)}))}xp.size={xxs:2,xs:5,s:10,m:15,l:20,xl:30,xxl:40,xxxl:50};var Mp="";"undefined"!=typeof navigator&&(Mp=navigator.platform||"");var Tp="rgba(0, 0, 0, 0.2)",kp=xp.color.theme[0],Cp=ti(kp,null,null,.9),Dp={darkMode:"auto",colorBy:"series",color:xp.color.theme,gradientColor:[Cp,kp],aria:{decal:{decals:[{color:Tp,dashArrayX:[1,0],dashArrayY:[2,5],symbolSize:1,rotation:Math.PI/6},{color:Tp,symbol:"circle",dashArrayX:[[8,8],[0,8,8,0]],dashArrayY:[6,0],symbolSize:.8},{color:Tp,dashArrayX:[1,0],dashArrayY:[4,3],rotation:-Math.PI/4},{color:Tp,dashArrayX:[[6,6],[0,6,6,0]],dashArrayY:[6,0]},{color:Tp,dashArrayX:[[1,0],[1,6]],dashArrayY:[1,0,6,0],rotation:Math.PI/4},{color:Tp,symbol:"triangle",dashArrayX:[[9,9],[0,9,9,0]],dashArrayY:[7,2],symbolSize:.75}]}},textStyle:{fontFamily:Mp.match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,stateAnimation:{duration:300,easing:"cubicOut"},animation:"auto",animationDuration:1e3,animationDurationUpdate:500,animationEasing:"cubicInOut",animationEasingUpdate:"cubicInOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3,useUTC:!1},Ip=dt(["tooltip","label","itemName","itemId","itemGroupId","itemChildGroupId","seriesName"]),Ap="original",Lp="arrayRows",Pp="objectRows",Op="keyedColumns",Rp="typedArray",Np="unknown",Bp="column",Ep="row",zp=1,Fp=2,Vp=3,Wp=To();function Hp(t,e,n){var i={},r=Up(e);if(!r||!t)return i;var o,a,s=[],l=[],u=e.ecModel,h=Wp(u).datasetMap,c=r.uid+"_"+n.seriesLayoutBy;R(t=t.slice(),(function(e,n){var r=q(e)?e:t[n]={name:e};"ordinal"===r.type&&null==o&&(o=n,a=d(r)),i[r.name]=[]}));var p=h.get(c)||h.set(c,{categoryWayDim:a,valueWayDim:0});function f(t,e,n){for(var i=0;i<n;i++)t.push(e+i)}function d(t){var e=t.dimsDef;return e?e.length:1}return R(t,(function(t,e){var n=t.name,r=d(t);if(null==o){var a=p.valueWayDim;f(i[n],a,r),f(l,a,r),p.valueWayDim+=r}else if(o===e)f(i[n],0,r),f(s,0,r);else{a=p.categoryWayDim;f(i[n],a,r),f(l,a,r),p.categoryWayDim+=r}})),s.length&&(i.itemName=s),l.length&&(i.seriesName=l),i}function Gp(t,e,n){var i={};if(!Up(t))return i;var r,o=e.sourceFormat,a=e.dimensionsDefine;o!==Pp&&o!==Op||R(a,(function(t,e){"name"===(q(t)?t.name:t)&&(r=e)}));var s=function(){for(var t={},i={},s=[],l=0,u=Math.min(5,n);l<u;l++){var h=Yp(e.data,o,e.seriesLayoutBy,a,e.startIndex,l);s.push(h);var c=h===Vp;if(c&&null==t.v&&l!==r&&(t.v=l),(null==t.n||t.n===t.v||!c&&s[t.n]===Vp)&&(t.n=l),p(t)&&s[t.n]!==Vp)return t;c||(h===Fp&&null==i.v&&l!==r&&(i.v=l),null!=i.n&&i.n!==i.v||(i.n=l))}function p(t){return null!=t.v&&null!=t.n}return p(t)?t:p(i)?i:null}();if(s){i.value=[s.v];var l=null!=r?r:s.n;i.itemName=[l],i.seriesName=[l]}return i}function Up(t){if(!t.get("data",!0))return Ao(t.ecModel,"dataset",{index:t.get("datasetIndex",!0),id:t.get("datasetId",!0)},Io).models[0]}function Xp(t,e){return Yp(t.data,t.sourceFormat,t.seriesLayoutBy,t.dimensionsDefine,t.startIndex,e)}function Yp(t,e,n,i,r,o){var a,s,l;if(j(t))return Vp;if(i){var u=i[o];q(u)?(s=u.name,l=u.type):U(u)&&(s=u)}if(null!=l)return"ordinal"===l?zp:Vp;if(e===Lp){var h=t;if(n===Ep){for(var c=h[o],p=0;p<(c||[]).length&&p<5;p++)if(null!=(a=m(c[r+p])))return a}else for(p=0;p<h.length&&p<5;p++){var f=h[r+p];if(f&&null!=(a=m(f[o])))return a}}else if(e===Pp){var d=t;if(!s)return Vp;for(p=0;p<d.length&&p<5;p++){if((v=d[p])&&null!=(a=m(v[s])))return a}}else if(e===Op){if(!s)return Vp;if(!(c=t[s])||j(c))return Vp;for(p=0;p<c.length&&p<5;p++)if(null!=(a=m(c[p])))return a}else if(e===Ap){var g=t;for(p=0;p<g.length&&p<5;p++){var v,y=vo(v=g[p]);if(!H(y))return Vp;if(null!=(a=m(y[o])))return a}}function m(t){var e=U(t);return null!=t&&Number.isFinite(Number(t))&&""!==t?e?Fp:Vp:e&&"-"!==t?zp:void 0}return Vp}var qp=dt();var Zp,jp,Kp,$p=To(),Qp=To(),Jp=function(){function t(){}return t.prototype.getColorFromPalette=function(t,e,n){var i=po(this.get("color",!0)),r=this.get("colorLayer",!0);return ef(this,$p,i,r,t,e,n)},t.prototype.clearColorPalette=function(){!function(t,e){e(t).paletteIdx=0,e(t).paletteNameMap={}}(this,$p)},t}();function tf(t,e,n,i){var r=po(t.get(["aria","decal","decals"]));return ef(t,Qp,r,null,e,n,i)}function ef(t,e,n,i,r,o,a){var s=e(o=o||t),l=s.paletteIdx||0,u=s.paletteNameMap=s.paletteNameMap||{};if(u.hasOwnProperty(r))return u[r];var h=null!=a&&i?function(t,e){for(var n=t.length,i=0;i<n;i++)if(t[i].length>e)return t[i];return t[n-1]}(i,a):n;if((h=h||n)&&h.length){var c=h[l];return r&&(u[r]=c),s.paletteIdx=(l+1)%h.length,c}}var nf="\0_ec_inner";var rf=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.init=function(t,e,n,i,r,o){i=i||{},this.option=null,this._theme=new ic(i),this._locale=new ic(r),this._optionManager=o},e.prototype.setOption=function(t,e,n){var i=sf(e);this._optionManager.setOption(t,n,i),this._resetOption(null,i)},e.prototype.resetOption=function(t,e){return this._resetOption(t,sf(e))},e.prototype._resetOption=function(t,e){var n=!1,i=this._optionManager;if(!t||"recreate"===t){var r=i.mountOption("recreate"===t);0,this.option&&"recreate"!==t?(this.restoreData(),this._mergeOption(r,e)):Kp(this,r),n=!0}if("timeline"!==t&&"media"!==t||this.restoreData(),!t||"recreate"===t||"timeline"===t){var o=i.getTimelineOption(this);o&&(n=!0,this._mergeOption(o,e))}if(!t||"recreate"===t||"media"===t){var a=i.getMediaOption(this);a.length&&R(a,(function(t){n=!0,this._mergeOption(t,e)}),this)}return n},e.prototype.mergeOption=function(t){this._mergeOption(t,null)},e.prototype._mergeOption=function(t,e){var n=this.option,i=this._componentsMap,r=this._componentsCount,o=[],a=dt(),s=e&&e.replaceMergeMainTypeMap;Wp(this).datasetMap=dt(),R(t,(function(t,e){null!=t&&(_p.hasClass(e)?e&&(o.push(e),a.set(e,!0)):n[e]=null==n[e]?T(t):k(n[e],t,!0))})),s&&s.each((function(t,e){_p.hasClass(e)&&!a.get(e)&&(o.push(e),a.set(e,!0))})),_p.topologicalTravel(o,_p.getAllClassMainTypes(),(function(e){var o=function(t,e,n){var i=qp.get(e);if(!i)return n;var r=i(t);return r?n.concat(r):n}(this,e,po(t[e])),a=i.get(e),l=a?s&&s.get(e)?"replaceMerge":"normalMerge":"replaceAll",u=mo(a,o,l);(function(t,e,n){R(t,(function(t){var i=t.newOption;q(i)&&(t.keyInfo.mainType=e,t.keyInfo.subType=function(t,e,n,i){return e.type?e.type:n?n.subType:i.determineSubType(t,e)}(e,i,t.existing,n))}))})(u,e,_p),n[e]=null,i.set(e,null),r.set(e,0);var h,c=[],p=[],f=0;R(u,(function(t,n){var i=t.existing,r=t.newOption;if(r){var o="series"===e,a=_p.getClass(e,t.keyInfo.subType,!o);if(!a)return;if("tooltip"===e){if(h)return void 0;h=!0}if(i&&i.constructor===a)i.name=t.keyInfo.name,i.mergeOption(r,this),i.optionUpdated(r,!1);else{var s=C({componentIndex:n},t.keyInfo);C(i=new a(r,this,this,s),s),t.brandNew&&(i.__requireNewView=!0),i.init(r,this,this),i.optionUpdated(null,!0)}}else i&&(i.mergeOption({},this),i.optionUpdated({},!1));i?(c.push(i.option),p.push(i),f++):(c.push(void 0),p.push(void 0))}),this),n[e]=c,i.set(e,p),r.set(e,f),"series"===e&&Zp(this)}),this),this._seriesIndices||Zp(this)},e.prototype.getOption=function(){var t=T(this.option);return R(t,(function(e,n){if(_p.hasClass(n)){for(var i=po(e),r=i.length,o=!1,a=r-1;a>=0;a--)i[a]&&!So(i[a])?o=!0:(i[a]=null,!o&&r--);i.length=r,t[n]=i}})),delete t[nf],t},e.prototype.setTheme=function(t){this._theme=new ic(t),this._resetOption("recreate",null)},e.prototype.getTheme=function(){return this._theme},e.prototype.getLocaleModel=function(){return this._locale},e.prototype.setUpdatePayload=function(t){this._payload=t},e.prototype.getUpdatePayload=function(){return this._payload},e.prototype.getComponent=function(t,e){var n=this._componentsMap.get(t);if(n){var i=n[e||0];if(i)return i;if(null==e)for(var r=0;r<n.length;r++)if(n[r])return n[r]}},e.prototype.queryComponents=function(t){var e=t.mainType;if(!e)return[];var n,i=t.index,r=t.id,o=t.name,a=this._componentsMap.get(e);return a&&a.length?(null!=i?(n=[],R(po(i),(function(t){a[t]&&n.push(a[t])}))):n=null!=r?of("id",r,a):null!=o?of("name",o,a):E(a,(function(t){return!!t})),af(n,t)):[]},e.prototype.findComponents=function(t){var e,n,i,r,o,a=t.query,s=t.mainType,l=(n=s+"Index",i=s+"Id",r=s+"Name",!(e=a)||null==e[n]&&null==e[i]&&null==e[r]?null:{mainType:s,index:e[n],id:e[i],name:e[r]}),u=l?this.queryComponents(l):E(this._componentsMap.get(s),(function(t){return!!t}));return o=af(u,t),t.filter?E(o,t.filter):o},e.prototype.eachComponent=function(t,e,n){var i=this._componentsMap;if(G(t)){var r=e,o=t;i.each((function(t,e){for(var n=0;t&&n<t.length;n++){var i=t[n];i&&o.call(r,e,i,i.componentIndex)}}))}else for(var a=U(t)?i.get(t):q(t)?this.findComponents(t):null,s=0;a&&s<a.length;s++){var l=a[s];l&&e.call(n,l,l.componentIndex)}},e.prototype.getSeriesByName=function(t){var e=bo(t,null);return E(this._componentsMap.get("series"),(function(t){return!!t&&null!=e&&t.name===e}))},e.prototype.getSeriesByIndex=function(t){return this._componentsMap.get("series")[t]},e.prototype.getSeriesByType=function(t){return E(this._componentsMap.get("series"),(function(e){return!!e&&e.subType===t}))},e.prototype.getSeries=function(){return E(this._componentsMap.get("series"),(function(t){return!!t}))},e.prototype.getSeriesCount=function(){return this._componentsCount.get("series")},e.prototype.eachSeries=function(t,e){jp(this),R(this._seriesIndices,(function(n){var i=this._componentsMap.get("series")[n];t.call(e,i,n)}),this)},e.prototype.eachRawSeries=function(t,e){R(this._componentsMap.get("series"),(function(n){n&&t.call(e,n,n.componentIndex)}))},e.prototype.eachSeriesByType=function(t,e,n){jp(this),R(this._seriesIndices,(function(i){var r=this._componentsMap.get("series")[i];r.subType===t&&e.call(n,r,i)}),this)},e.prototype.eachRawSeriesByType=function(t,e,n){return R(this.getSeriesByType(t),e,n)},e.prototype.isSeriesFiltered=function(t){return jp(this),null==this._seriesIndicesMap.get(t.componentIndex)},e.prototype.getCurrentSeriesIndices=function(){return(this._seriesIndices||[]).slice()},e.prototype.filterSeries=function(t,e){jp(this);var n=[];R(this._seriesIndices,(function(i){var r=this._componentsMap.get("series")[i];t.call(e,r,i)&&n.push(i)}),this),this._seriesIndices=n,this._seriesIndicesMap=dt(n)},e.prototype.restoreData=function(t){Zp(this);var e=this._componentsMap,n=[];e.each((function(t,e){_p.hasClass(e)&&n.push(e)})),_p.topologicalTravel(n,_p.getAllClassMainTypes(),(function(n){R(e.get(n),(function(e){!e||"series"===n&&function(t,e){if(e){var n=e.seriesIndex,i=e.seriesId,r=e.seriesName;return null!=n&&t.componentIndex!==n||null!=i&&t.id!==i||null!=r&&t.name!==r}}(e,t)||e.restoreData()}))}))},e.internalField=(Zp=function(t){var e=t._seriesIndices=[];R(t._componentsMap.get("series"),(function(t){t&&e.push(t.componentIndex)})),t._seriesIndicesMap=dt(e)},jp=function(t){},void(Kp=function(t,e){t.option={},t.option[nf]=1,t._componentsMap=dt({series:[]}),t._componentsCount=dt();var n=e.aria;q(n)&&null==n.enabled&&(n.enabled=!0),function(t,e){var n=t.color&&!t.colorLayer;R(e,(function(e,i){"colorLayer"===i&&n||"color"===i&&t.color||_p.hasClass(i)||("object"==typeof e?t[i]=t[i]?k(t[i],e,!1):T(e):null==t[i]&&(t[i]=e))}))}(e,t._theme.option),k(e,Dp,!1),t._mergeOption(e,null)})),e}(ic);function of(t,e,n){if(H(e)){var i=dt();return R(e,(function(t){null!=t&&(null!=bo(t,null)&&i.set(t,!0))})),E(n,(function(e){return e&&i.get(e[t])}))}var r=bo(e,null);return E(n,(function(e){return e&&null!=r&&e[t]===r}))}function af(t,e){return e.hasOwnProperty("subType")?E(t,(function(t){return t&&t.subType===e.subType})):t}function sf(t){var e=dt();return t&&R(po(t.replaceMerge),(function(t){e.set(t,!0)})),{replaceMergeMainTypeMap:e}}P(rf,Jp);var lf=["getDom","getZr","getWidth","getHeight","getDevicePixelRatio","dispatchAction","isSSR","isDisposed","on","off","getDataURL","getConnectedDataURL","getOption","getId","updateLabelLayout"],uf=function(t){R(lf,(function(e){this[e]=V(t[e],t)}),this)},hf=/^(min|max)?(.+)$/,cf=function(){function t(t){this._timelineOptions=[],this._mediaList=[],this._currentMediaIndices=[],this._api=t}return t.prototype.setOption=function(t,e,n){t&&(R(po(t.series),(function(t){t&&t.data&&j(t.data)&&ut(t.data)})),R(po(t.dataset),(function(t){t&&t.source&&j(t.source)&&ut(t.source)}))),t=T(t);var i=this._optionBackup,r=function(t,e,n){var i,r,o=[],a=t.baseOption,s=t.timeline,l=t.options,u=t.media,h=!!t.media,c=!!(l||s||a&&a.timeline);a?(r=a).timeline||(r.timeline=s):((c||h)&&(t.options=t.media=null),r=t);h&&H(u)&&R(u,(function(t){t&&t.option&&(t.query?o.push(t):i||(i=t))}));function p(t){R(e,(function(e){e(t,n)}))}return p(r),R(l,(function(t){return p(t)})),R(o,(function(t){return p(t.option)})),{baseOption:r,timelineOptions:l||[],mediaDefault:i,mediaList:o}}(t,e,!i);this._newBaseOption=r.baseOption,i?(r.timelineOptions.length&&(i.timelineOptions=r.timelineOptions),r.mediaList.length&&(i.mediaList=r.mediaList),r.mediaDefault&&(i.mediaDefault=r.mediaDefault)):this._optionBackup=r},t.prototype.mountOption=function(t){var e=this._optionBackup;return this._timelineOptions=e.timelineOptions,this._mediaList=e.mediaList,this._mediaDefault=e.mediaDefault,this._currentMediaIndices=[],T(t?e.baseOption:this._newBaseOption)},t.prototype.getTimelineOption=function(t){var e,n=this._timelineOptions;if(n.length){var i=t.getComponent("timeline");i&&(e=T(n[i.getCurrentIndex()]))}return e},t.prototype.getMediaOption=function(t){var e,n,i=this._api.getWidth(),r=this._api.getHeight(),o=this._mediaList,a=this._mediaDefault,s=[],l=[];if(!o.length&&!a)return l;for(var u=0,h=o.length;u<h;u++)pf(o[u].query,i,r)&&s.push(u);return!s.length&&a&&(s=[-1]),s.length&&(e=s,n=this._currentMediaIndices,e.join(",")!==n.join(","))&&(l=N(s,(function(t){return T(-1===t?a.option:o[t].option)}))),this._currentMediaIndices=s,l},t}();function pf(t,e,n){var i={width:e,height:n,aspectratio:e/n},r=!0;return R(t,(function(t,e){var n=e.match(hf);if(n&&n[1]&&n[2]){var o=n[1],a=n[2].toLowerCase();(function(t,e,n){return"min"===n?t>=e:"max"===n?t<=e:t===e})(i[a],t,o)||(r=!1)}})),r}var ff=R,df=q,gf=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"];function vf(t){var e=t&&t.itemStyle;if(e)for(var n=0,i=gf.length;n<i;n++){var r=gf[n],o=e.normal,a=e.emphasis;o&&o[r]&&(t[r]=t[r]||{},t[r].normal?k(t[r].normal,o[r]):t[r].normal=o[r],o[r]=null),a&&a[r]&&(t[r]=t[r]||{},t[r].emphasis?k(t[r].emphasis,a[r]):t[r].emphasis=a[r],a[r]=null)}}function yf(t,e,n){if(t&&t[e]&&(t[e].normal||t[e].emphasis)){var i=t[e].normal,r=t[e].emphasis;i&&(n?(t[e].normal=t[e].emphasis=null,D(t[e],i)):t[e]=i),r&&(t.emphasis=t.emphasis||{},t.emphasis[e]=r,r.focus&&(t.emphasis.focus=r.focus),r.blurScope&&(t.emphasis.blurScope=r.blurScope))}}function mf(t){yf(t,"itemStyle"),yf(t,"lineStyle"),yf(t,"areaStyle"),yf(t,"label"),yf(t,"labelLine"),yf(t,"upperLabel"),yf(t,"edgeLabel")}function _f(t,e){var n=df(t)&&t[e],i=df(n)&&n.textStyle;if(i){0;for(var r=0,o=go.length;r<o;r++){var a=go[r];i.hasOwnProperty(a)&&(n[a]=i[a])}}}function xf(t){t&&(mf(t),_f(t,"label"),t.emphasis&&_f(t.emphasis,"label"))}function bf(t){return H(t)?t:t?[t]:[]}function wf(t){return(H(t)?t[0]:t)||{}}function Sf(t,e){ff(bf(t.series),(function(t){df(t)&&function(t){if(df(t)){vf(t),mf(t),_f(t,"label"),_f(t,"upperLabel"),_f(t,"edgeLabel"),t.emphasis&&(_f(t.emphasis,"label"),_f(t.emphasis,"upperLabel"),_f(t.emphasis,"edgeLabel"));var e=t.markPoint;e&&(vf(e),xf(e));var n=t.markLine;n&&(vf(n),xf(n));var i=t.markArea;i&&xf(i);var r=t.data;if("graph"===t.type){r=r||t.nodes;var o=t.links||t.edges;if(o&&!j(o))for(var a=0;a<o.length;a++)xf(o[a]);R(t.categories,(function(t){mf(t)}))}if(r&&!j(r))for(a=0;a<r.length;a++)xf(r[a]);if((e=t.markPoint)&&e.data){var s=e.data;for(a=0;a<s.length;a++)xf(s[a])}if((n=t.markLine)&&n.data){var l=n.data;for(a=0;a<l.length;a++)H(l[a])?(xf(l[a][0]),xf(l[a][1])):xf(l[a])}"gauge"===t.type?(_f(t,"axisLabel"),_f(t,"title"),_f(t,"detail")):"treemap"===t.type?(yf(t.breadcrumb,"itemStyle"),R(t.levels,(function(t){mf(t)}))):"tree"===t.type&&mf(t.leaves)}}(t)}));var n=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","parallelAxis","radar"];e&&n.push("valueAxis","categoryAxis","logAxis","timeAxis"),ff(n,(function(e){ff(bf(t[e]),(function(t){t&&(_f(t,"axisLabel"),_f(t.axisPointer,"label"))}))})),ff(bf(t.parallel),(function(t){var e=t&&t.parallelAxisDefault;_f(e,"axisLabel"),_f(e&&e.axisPointer,"label")})),ff(bf(t.calendar),(function(t){yf(t,"itemStyle"),_f(t,"dayLabel"),_f(t,"monthLabel"),_f(t,"yearLabel")})),ff(bf(t.radar),(function(t){_f(t,"name"),t.name&&null==t.axisName&&(t.axisName=t.name,delete t.name),null!=t.nameGap&&null==t.axisNameGap&&(t.axisNameGap=t.nameGap,delete t.nameGap)})),ff(bf(t.geo),(function(t){df(t)&&(xf(t),ff(bf(t.regions),(function(t){xf(t)})))})),ff(bf(t.timeline),(function(t){xf(t),yf(t,"label"),yf(t,"itemStyle"),yf(t,"controlStyle",!0);var e=t.data;H(e)&&R(e,(function(t){q(t)&&(yf(t,"label"),yf(t,"itemStyle"))}))})),ff(bf(t.toolbox),(function(t){yf(t,"iconStyle"),ff(t.feature,(function(t){yf(t,"iconStyle")}))})),_f(wf(t.axisPointer),"label"),_f(wf(t.tooltip).axisPointer,"label")}function Mf(t){t&&R(Tf,(function(e){e[0]in t&&!(e[1]in t)&&(t[e[1]]=t[e[0]])}))}var Tf=[["x","left"],["y","top"],["x2","right"],["y2","bottom"]],kf=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"],Cf=[["borderRadius","barBorderRadius"],["borderColor","barBorderColor"],["borderWidth","barBorderWidth"]];function Df(t){var e=t&&t.itemStyle;if(e)for(var n=0;n<Cf.length;n++){var i=Cf[n][1],r=Cf[n][0];null!=e[i]&&(e[r]=e[i])}}function If(t){t&&"edge"===t.alignTo&&null!=t.margin&&null==t.edgeDistance&&(t.edgeDistance=t.margin)}function Af(t){t&&t.downplay&&!t.blur&&(t.blur=t.downplay)}function Lf(t,e){if(t)for(var n=0;n<t.length;n++)e(t[n]),t[n]&&Lf(t[n].children,e)}function Pf(t,e){Sf(t,e),t.series=po(t.series),R(t.series,(function(t){if(q(t)){var e=t.type;if("line"===e)null!=t.clipOverflow&&(t.clip=t.clipOverflow);else if("pie"===e||"gauge"===e){if(null!=t.clockWise&&(t.clockwise=t.clockWise),If(t.label),(r=t.data)&&!j(r))for(var n=0;n<r.length;n++)If(r[n]);null!=t.hoverOffset&&(t.emphasis=t.emphasis||{},(t.emphasis.scaleSize=null)&&(t.emphasis.scaleSize=t.hoverOffset))}else if("gauge"===e){var i=function(t,e){for(var n=e.split(","),i=t,r=0;r<n.length&&null!=(i=i&&i[n[r]]);r++);return i}(t,"pointer.color");null!=i&&function(t,e,n,i){for(var r,o=e.split(","),a=t,s=0;s<o.length-1;s++)null==a[r=o[s]]&&(a[r]={}),a=a[r];(i||null==a[o[s]])&&(a[o[s]]=n)}(t,"itemStyle.color",i)}else if("bar"===e){var r;if(Df(t),Df(t.backgroundStyle),Df(t.emphasis),(r=t.data)&&!j(r))for(n=0;n<r.length;n++)"object"==typeof r[n]&&(Df(r[n]),Df(r[n]&&r[n].emphasis))}else if("sunburst"===e){var o=t.highlightPolicy;o&&(t.emphasis=t.emphasis||{},t.emphasis.focus||(t.emphasis.focus=o)),Af(t),Lf(t.data,Af)}else"graph"===e||"sankey"===e?function(t){t&&null!=t.focusNodeAdjacency&&(t.emphasis=t.emphasis||{},null==t.emphasis.focus&&(t.emphasis.focus="adjacency"))}(t):"map"===e&&(t.mapType&&!t.map&&(t.map=t.mapType),t.mapLocation&&D(t,t.mapLocation));null!=t.hoverAnimation&&(t.emphasis=t.emphasis||{},t.emphasis&&null==t.emphasis.scale&&(t.emphasis.scale=t.hoverAnimation)),Mf(t)}})),t.dataRange&&(t.visualMap=t.dataRange),R(kf,(function(e){var n=t[e];n&&(H(n)||(n=[n]),R(n,(function(t){Mf(t)})))}))}var Of,Rf,Nf,Bf,Ef,zf,Ff=function(t){this.data=t.data||(t.sourceFormat===Op?{}:[]),this.sourceFormat=t.sourceFormat||Np,this.seriesLayoutBy=t.seriesLayoutBy||Bp,this.startIndex=t.startIndex||0,this.dimensionsDetectedCount=t.dimensionsDetectedCount,this.metaRawOption=t.metaRawOption;var e=this.dimensionsDefine=t.dimensionsDefine;if(e)for(var n=0;n<e.length;n++){var i=e[n];null==i.type&&Xp(this,n)===zp&&(i.type="ordinal")}};function Vf(t){return t instanceof Ff}function Wf(t,e,n){n=n||Gf(t);var i=e.seriesLayoutBy,r=function(t,e,n,i,r){var o,a;if(!t)return{dimensionsDefine:Uf(r),startIndex:a,dimensionsDetectedCount:o};if(e===Lp){var s=t;"auto"===i||null==i?Xf((function(t){null!=t&&"-"!==t&&(U(t)?null==a&&(a=1):a=0)}),n,s,10):a=Y(i)?i:i?1:0,r||1!==a||(r=[],Xf((function(t,e){r[e]=null!=t?t+"":""}),n,s,1/0)),o=r?r.length:n===Ep?s.length:s[0]?s[0].length:null}else if(e===Pp)r||(r=function(t){var e,n=0;for(;n<t.length&&!(e=t[n++]););if(e)return F(e)}(t));else if(e===Op)r||(r=[],R(t,(function(t,e){r.push(e)})));else if(e===Ap){var l=vo(t[0]);o=H(l)&&l.length||1}return{startIndex:a,dimensionsDefine:Uf(r),dimensionsDetectedCount:o}}(t,n,i,e.sourceHeader,e.dimensions);return new Ff({data:t,sourceFormat:n,seriesLayoutBy:i,dimensionsDefine:r.dimensionsDefine,startIndex:r.startIndex,dimensionsDetectedCount:r.dimensionsDetectedCount,metaRawOption:T(e)})}function Hf(t){return new Ff({data:t,sourceFormat:j(t)?Rp:Ap})}function Gf(t){var e=Np;if(j(t))e=Rp;else if(H(t)){0===t.length&&(e=Lp);for(var n=0,i=t.length;n<i;n++){var r=t[n];if(null!=r){if(H(r)||j(r)){e=Lp;break}if(q(r)){e=Pp;break}}}}else if(q(t))for(var o in t)if(mt(t,o)&&O(t[o])){e=Op;break}return e}function Uf(t){if(t){var e=dt();return N(t,(function(t,n){var i={name:(t=q(t)?t:{name:t}).name,displayName:t.displayName,type:t.type};if(null==i.name)return i;i.name+="",null==i.displayName&&(i.displayName=i.name);var r=e.get(i.name);return r?i.name+="-"+r.count++:e.set(i.name,{count:1}),i}))}}function Xf(t,e,n,i){if(e===Ep)for(var r=0;r<n.length&&r<i;r++)t(n[r]?n[r][0]:null,r);else{var o=n[0]||[];for(r=0;r<o.length&&r<i;r++)t(o[r],r)}}function Yf(t){var e=t.sourceFormat;return e===Pp||e===Op}var qf=function(){function t(t,e){var n=Vf(t)?t:Hf(t);this._source=n;var i=this._data=n.data,r=n.sourceFormat;n.seriesLayoutBy;r===Rp&&(this._offset=0,this._dimSize=e,this._data=i),zf(this,i,n)}var e;return t.prototype.getSource=function(){return this._source},t.prototype.count=function(){return 0},t.prototype.getItem=function(t,e){},t.prototype.appendData=function(t){},t.prototype.clean=function(){},t.protoInitialize=((e=t.prototype).pure=!1,void(e.persistent=!0)),t.internalField=function(){var t;zf=function(t,r,o){var a=o.sourceFormat,s=o.seriesLayoutBy,l=o.startIndex,u=o.dimensionsDefine,h=Ef[rd(a,s)];if(C(t,h),a===Rp)t.getItem=e,t.count=i,t.fillStorage=n;else{var c=$f(a,s);t.getItem=V(c,null,r,l,u);var p=td(a,s);t.count=V(p,null,r,l,u)}};var e=function(t,e){t-=this._offset,e=e||[];for(var n=this._data,i=this._dimSize,r=i*t,o=0;o<i;o++)e[o]=n[r+o];return e},n=function(t,e,n,i){for(var r=this._data,o=this._dimSize,a=0;a<o;a++){for(var s=i[a],l=null==s[0]?1/0:s[0],u=null==s[1]?-1/0:s[1],h=e-t,c=n[a],p=0;p<h;p++){var f=r[p*o+a];c[t+p]=f,f<l&&(l=f),f>u&&(u=f)}s[0]=l,s[1]=u}},i=function(){return this._data?this._data.length/this._dimSize:0};function r(t){for(var e=0;e<t.length;e++)this._data.push(t[e])}(t={})[Lp+"_"+Bp]={pure:!0,appendData:r},t[Lp+"_"+Ep]={pure:!0,appendData:function(){throw new Error('Do not support appendData when set seriesLayoutBy: "row".')}},t[Pp]={pure:!0,appendData:r},t[Op]={pure:!0,appendData:function(t){var e=this._data;R(t,(function(t,n){for(var i=e[n]||(e[n]=[]),r=0;r<(t||[]).length;r++)i.push(t[r])}))}},t[Ap]={appendData:r},t[Rp]={persistent:!1,pure:!0,appendData:function(t){this._data=t},clean:function(){this._offset+=this.count(),this._data=null}},Ef=t}(),t}(),Zf=function(t){H(t)||so("series.data or dataset.source must be an array.")},jf=((Of={})[Lp+"_"+Bp]=Zf,Of[Lp+"_"+Ep]=Zf,Of[Pp]=Zf,Of[Op]=function(t,e){for(var n=0;n<e.length;n++){null==e[n].name&&so("dimension name must not be null/undefined.")}},Of[Ap]=Zf,function(t,e,n,i){return t[i]}),Kf=((Rf={})[Lp+"_"+Bp]=function(t,e,n,i){return t[i+e]},Rf[Lp+"_"+Ep]=function(t,e,n,i,r){i+=e;for(var o=r||[],a=t,s=0;s<a.length;s++){var l=a[s];o[s]=l?l[i]:null}return o},Rf[Pp]=jf,Rf[Op]=function(t,e,n,i,r){for(var o=r||[],a=0;a<n.length;a++){var s=n[a].name,l=null!=s?t[s]:null;o[a]=l?l[i]:null}return o},Rf[Ap]=jf,Rf);function $f(t,e){var n=Kf[rd(t,e)];return n}var Qf=function(t,e,n){return t.length},Jf=((Nf={})[Lp+"_"+Bp]=function(t,e,n){return Math.max(0,t.length-e)},Nf[Lp+"_"+Ep]=function(t,e,n){var i=t[0];return i?Math.max(0,i.length-e):0},Nf[Pp]=Qf,Nf[Op]=function(t,e,n){var i=n[0].name,r=null!=i?t[i]:null;return r?r.length:0},Nf[Ap]=Qf,Nf);function td(t,e){var n=Jf[rd(t,e)];return n}var ed=function(t,e,n){return t[e]},nd=((Bf={})[Lp]=ed,Bf[Pp]=function(t,e,n){return t[n]},Bf[Op]=ed,Bf[Ap]=function(t,e,n){var i=vo(t);return i instanceof Array?i[e]:i},Bf[Rp]=ed,Bf);function id(t){var e=nd[t];return e}function rd(t,e){return t===Lp?t+"_"+e:t}function od(t,e,n){if(t){var i=t.getRawDataItem(e);if(null!=i){var r=t.getStore(),o=r.getSource().sourceFormat;if(null!=n){var a=t.getDimensionIndex(n),s=r.getDimensionProperty(a);return id(o)(i,a,s)}var l=i;return o===Ap&&(l=vo(i)),l}}}var ad=/\{@(.+?)\}/g,sd=function(){function t(){}return t.prototype.getDataParams=function(t,e){var n=this.getData(e),i=this.getRawValue(t,e),r=n.getRawIndex(t),o=n.getName(t),a=n.getRawDataItem(t),s=n.getItemVisual(t,"style"),l=s&&s[n.getItemVisual(t,"drawType")||"fill"],u=s&&s.stroke,h=this.mainType,c="series"===h,p=n.userOutput&&n.userOutput.get();return{componentType:h,componentSubType:this.subType,componentIndex:this.componentIndex,seriesType:c?this.subType:null,seriesIndex:this.seriesIndex,seriesId:c?this.id:null,seriesName:c?this.name:null,name:o,dataIndex:r,data:a,dataType:e,value:i,color:l,borderColor:u,dimensionNames:p?p.fullDimensions:null,encode:p?p.encode:null,$vars:["seriesName","name","value"]}},t.prototype.getFormattedLabel=function(t,e,n,i,r,o){e=e||"normal";var a=this.getData(n),s=this.getDataParams(t,n);(o&&(s.value=o.interpolatedValue),null!=i&&H(s.value)&&(s.value=s.value[i]),r)||(r=a.getItemModel(t).get("normal"===e?["label","formatter"]:[e,"label","formatter"]));return G(r)?(s.status=e,s.dimensionIndex=i,r(s)):U(r)?jc(r,s).replace(ad,(function(e,n){var i=n.length,r=n;"["===r.charAt(0)&&"]"===r.charAt(i-1)&&(r=+r.slice(1,i-1));var s=od(a,t,r);if(o&&H(o.interpolatedValue)){var l=a.getDimensionIndex(r);l>=0&&(s=o.interpolatedValue[l])}return null!=s?s+"":""})):void 0},t.prototype.getRawValue=function(t,e){return od(this.getData(e),t)},t.prototype.formatTooltip=function(t,e,n){},t}();function ld(t){return new ud(t)}var ud=function(){function t(t){t=t||{},this._reset=t.reset,this._plan=t.plan,this._count=t.count,this._onDirty=t.onDirty,this._dirty=!0}return t.prototype.perform=function(t){var e,n=this._upstream,i=t&&t.skip;if(this._dirty&&n){var r=this.context;r.data=r.outputData=n.context.outputData}this.__pipeline&&(this.__pipeline.currentTask=this),this._plan&&!i&&(e=this._plan(this.context));var o,a=h(this._modBy),s=this._modDataCount||0,l=h(t&&t.modBy),u=t&&t.modDataCount||0;function h(t){return!(t>=1)&&(t=1),t}a===l&&s===u||(e="reset"),(this._dirty||"reset"===e)&&(this._dirty=!1,o=this._doReset(i)),this._modBy=l,this._modDataCount=u;var c=t&&t.step;if(this._dueEnd=n?n._outputDueEnd:this._count?this._count(this.context):1/0,this._progress){var p=this._dueIndex,f=Math.min(null!=c?this._dueIndex+c:1/0,this._dueEnd);if(!i&&(o||p<f)){var d=this._progress;if(H(d))for(var g=0;g<d.length;g++)this._doProgress(d[g],p,f,l,u);else this._doProgress(d,p,f,l,u)}this._dueIndex=f;var v=null!=this._settedOutputEnd?this._settedOutputEnd:f;0,this._outputDueEnd=v}else this._dueIndex=this._outputDueEnd=null!=this._settedOutputEnd?this._settedOutputEnd:this._dueEnd;return this.unfinished()},t.prototype.dirty=function(){this._dirty=!0,this._onDirty&&this._onDirty(this.context)},t.prototype._doProgress=function(t,e,n,i,r){hd.reset(e,n,i,r),this._callingProgress=t,this._callingProgress({start:e,end:n,count:n-e,next:hd.next},this.context)},t.prototype._doReset=function(t){var e,n;this._dueIndex=this._outputDueEnd=this._dueEnd=0,this._settedOutputEnd=null,!t&&this._reset&&((e=this._reset(this.context))&&e.progress&&(n=e.forceFirstProgress,e=e.progress),H(e)&&!e.length&&(e=null)),this._progress=e,this._modBy=this._modDataCount=null;var i=this._downstream;return i&&i.dirty(),n},t.prototype.unfinished=function(){return this._progress&&this._dueIndex<this._dueEnd},t.prototype.pipe=function(t){(this._downstream!==t||this._dirty)&&(this._downstream=t,t._upstream=this,t.dirty())},t.prototype.dispose=function(){this._disposed||(this._upstream&&(this._upstream._downstream=null),this._downstream&&(this._downstream._upstream=null),this._dirty=!1,this._disposed=!0)},t.prototype.getUpstream=function(){return this._upstream},t.prototype.getDownstream=function(){return this._downstream},t.prototype.setOutputEnd=function(t){this._outputDueEnd=this._settedOutputEnd=t},t}(),hd=function(){var t,e,n,i,r,o={reset:function(l,u,h,c){e=l,t=u,n=h,i=c,r=Math.ceil(i/n),o.next=n>1&&i>0?s:a}};return o;function a(){return e<t?e++:null}function s(){var o=e%r*n+Math.ceil(e/r),a=e>=t?null:o<i?o:e;return e++,a}}();function cd(t,e){var n=e&&e.type;return"ordinal"===n?t:("time"!==n||Y(t)||null==t||"-"===t||(t=+Kr(t)),null==t||""===t?NaN:Number(t))}dt({number:function(t){return parseFloat(t)},time:function(t){return+Kr(t)},trim:function(t){return U(t)?st(t):t}});var pd=function(){function t(){}return t.prototype.getRawData=function(){throw new Error("not supported")},t.prototype.getRawDataItem=function(t){throw new Error("not supported")},t.prototype.cloneRawData=function(){},t.prototype.getDimensionInfo=function(t){},t.prototype.cloneAllDimensionInfo=function(){},t.prototype.count=function(){},t.prototype.retrieveValue=function(t,e){},t.prototype.retrieveValueFromItem=function(t,e){},t.prototype.convertValue=function(t,e){return cd(t,e)},t}();function fd(t){var e=t.sourceFormat;if(!_d(e)){var n="";0,uo(n)}return t.data}function dd(t){var e=t.sourceFormat,n=t.data;if(!_d(e)){var i="";0,uo(i)}if(e===Lp){for(var r=[],o=0,a=n.length;o<a;o++)r.push(n[o].slice());return r}if(e===Pp){for(r=[],o=0,a=n.length;o<a;o++)r.push(C({},n[o]));return r}}function gd(t,e,n){if(null!=n)return Y(n)||!isNaN(n)&&!mt(e,n)?t[n]:mt(e,n)?e[n]:void 0}function vd(t){return T(t)}var yd=dt();function md(t,e,n,i){var r="";e.length||uo(r),q(t)||uo(r);var o=t.type,a=yd.get(o);a||uo(r);var s=N(e,(function(t){return function(t,e){var n=new pd,i=t.data,r=n.sourceFormat=t.sourceFormat,o=t.startIndex,a="";t.seriesLayoutBy!==Bp&&uo(a);var s=[],l={},u=t.dimensionsDefine;if(u)R(u,(function(t,e){var n=t.name,i={index:e,name:n,displayName:t.displayName};if(s.push(i),null!=n){var r="";mt(l,n)&&uo(r),l[n]=i}}));else for(var h=0;h<t.dimensionsDetectedCount;h++)s.push({index:h});var c=$f(r,Bp);e.__isBuiltIn&&(n.getRawDataItem=function(t){return c(i,o,s,t)},n.getRawData=V(fd,null,t)),n.cloneRawData=V(dd,null,t);var p=td(r,Bp);n.count=V(p,null,i,o,s);var f=id(r);n.retrieveValue=function(t,e){var n=c(i,o,s,t);return d(n,e)};var d=n.retrieveValueFromItem=function(t,e){if(null!=t){var n=s[e];return n?f(t,e,n.name):void 0}};return n.getDimensionInfo=V(gd,null,s,l),n.cloneAllDimensionInfo=V(vd,null,s),n}(t,a)})),l=po(a.transform({upstream:s[0],upstreamList:s,config:T(t.config)}));return N(l,(function(t,n){var i,r="";q(t)||uo(r),t.data||uo(r),_d(Gf(t.data))||uo(r);var o=e[0];if(o&&0===n&&!t.dimensions){var a=o.startIndex;a&&(t.data=o.data.slice(0,a).concat(t.data)),i={seriesLayoutBy:Bp,sourceHeader:a,dimensions:o.metaRawOption.dimensions}}else i={seriesLayoutBy:Bp,sourceHeader:0,dimensions:t.dimensions};return Wf(t.data,i,null)}))}function _d(t){return t===Lp||t===Pp}var xd,bd="undefined",wd=typeof Uint32Array===bd?Array:Uint32Array,Sd=typeof Uint16Array===bd?Array:Uint16Array,Md=typeof Int32Array===bd?Array:Int32Array,Td=typeof Float64Array===bd?Array:Float64Array,kd={float:Td,int:Md,ordinal:Array,number:Array,time:Td};function Cd(t){return t>65535?wd:Sd}function Dd(t,e,n,i,r){var o=kd[n||"float"];if(r){var a=t[e],s=a&&a.length;if(s!==i){for(var l=new o(i),u=0;u<s;u++)l[u]=a[u];t[e]=l}}else t[e]=new o(i)}var Id=function(){function t(){this._chunks=[],this._rawExtent=[],this._extent=[],this._count=0,this._rawCount=0,this._calcDimNameToIdx=dt()}return t.prototype.initData=function(t,e,n){this._provider=t,this._chunks=[],this._indices=null,this.getRawIndex=this._getRawIdxIdentity;var i=t.getSource(),r=this.defaultDimValueGetter=xd[i.sourceFormat];this._dimValueGetter=n||r,this._rawExtent=[];Yf(i);this._dimensions=N(e,(function(t){return{type:t.type,property:t.property}})),this._initDataFromProvider(0,t.count())},t.prototype.getProvider=function(){return this._provider},t.prototype.getSource=function(){return this._provider.getSource()},t.prototype.ensureCalculationDimension=function(t,e){var n=this._calcDimNameToIdx,i=this._dimensions,r=n.get(t);if(null!=r){if(i[r].type===e)return r}else r=i.length;return i[r]={type:e},n.set(t,r),this._chunks[r]=new kd[e||"float"](this._rawCount),this._rawExtent[r]=[1/0,-1/0],r},t.prototype.collectOrdinalMeta=function(t,e){var n=this._chunks[t],i=this._dimensions[t],r=this._rawExtent,o=i.ordinalOffset||0,a=n.length;0===o&&(r[t]=[1/0,-1/0]);for(var s=r[t],l=o;l<a;l++){var u=n[l]=e.parseAndCollect(n[l]);isNaN(u)||(s[0]=Math.min(u,s[0]),s[1]=Math.max(u,s[1]))}i.ordinalMeta=e,i.ordinalOffset=a,i.type="ordinal"},t.prototype.getOrdinalMeta=function(t){return this._dimensions[t].ordinalMeta},t.prototype.getDimensionProperty=function(t){var e=this._dimensions[t];return e&&e.property},t.prototype.appendData=function(t){var e=this._provider,n=this.count();e.appendData(t);var i=e.count();return e.persistent||(i+=n),n<i&&this._initDataFromProvider(n,i,!0),[n,i]},t.prototype.appendValues=function(t,e){for(var n=this._chunks,i=this._dimensions,r=i.length,o=this._rawExtent,a=this.count(),s=a+Math.max(t.length,e||0),l=0;l<r;l++){Dd(n,l,(f=i[l]).type,s,!0)}for(var u=[],h=a;h<s;h++)for(var c=h-a,p=0;p<r;p++){var f=i[p],d=xd.arrayRows.call(this,t[c]||u,f.property,c,p);n[p][h]=d;var g=o[p];d<g[0]&&(g[0]=d),d>g[1]&&(g[1]=d)}return this._rawCount=this._count=s,{start:a,end:s}},t.prototype._initDataFromProvider=function(t,e,n){for(var i=this._provider,r=this._chunks,o=this._dimensions,a=o.length,s=this._rawExtent,l=N(o,(function(t){return t.property})),u=0;u<a;u++){var h=o[u];s[u]||(s[u]=[1/0,-1/0]),Dd(r,u,h.type,e,n)}if(i.fillStorage)i.fillStorage(t,e,r,s);else for(var c=[],p=t;p<e;p++){c=i.getItem(p,c);for(var f=0;f<a;f++){var d=r[f],g=this._dimValueGetter(c,l[f],p,f);d[p]=g;var v=s[f];g<v[0]&&(v[0]=g),g>v[1]&&(v[1]=g)}}!i.persistent&&i.clean&&i.clean(),this._rawCount=this._count=e,this._extent=[]},t.prototype.count=function(){return this._count},t.prototype.get=function(t,e){if(!(e>=0&&e<this._count))return NaN;var n=this._chunks[t];return n?n[this.getRawIndex(e)]:NaN},t.prototype.getValues=function(t,e){var n=[],i=[];if(null==e){e=t,t=[];for(var r=0;r<this._dimensions.length;r++)i.push(r)}else i=t;r=0;for(var o=i.length;r<o;r++)n.push(this.get(i[r],e));return n},t.prototype.getByRawIndex=function(t,e){if(!(e>=0&&e<this._rawCount))return NaN;var n=this._chunks[t];return n?n[e]:NaN},t.prototype.getSum=function(t){var e=0;if(this._chunks[t])for(var n=0,i=this.count();n<i;n++){var r=this.get(t,n);isNaN(r)||(e+=r)}return e},t.prototype.getMedian=function(t){var e=[];this.each([t],(function(t){isNaN(t)||e.push(t)}));var n=e.sort((function(t,e){return t-e})),i=this.count();return 0===i?0:i%2==1?n[(i-1)/2]:(n[i/2]+n[i/2-1])/2},t.prototype.indexOfRawIndex=function(t){if(t>=this._rawCount||t<0)return-1;if(!this._indices)return t;var e=this._indices,n=e[t];if(null!=n&&n<this._count&&n===t)return t;for(var i=0,r=this._count-1;i<=r;){var o=(i+r)/2|0;if(e[o]<t)i=o+1;else{if(!(e[o]>t))return o;r=o-1}}return-1},t.prototype.getIndices=function(){var t,e=this._indices;if(e){var n=e.constructor,i=this._count;if(n===Array){t=new n(i);for(var r=0;r<i;r++)t[r]=e[r]}else t=new n(e.buffer,0,i)}else{t=new(n=Cd(this._rawCount))(this.count());for(r=0;r<t.length;r++)t[r]=r}return t},t.prototype.filter=function(t,e){if(!this._count)return this;for(var n=this.clone(),i=n.count(),r=new(Cd(n._rawCount))(i),o=[],a=t.length,s=0,l=t[0],u=n._chunks,h=0;h<i;h++){var c=void 0,p=n.getRawIndex(h);if(0===a)c=e(h);else if(1===a){c=e(u[l][p],h)}else{for(var f=0;f<a;f++)o[f]=u[t[f]][p];o[f]=h,c=e.apply(null,o)}c&&(r[s++]=p)}return s<i&&(n._indices=r),n._count=s,n._extent=[],n._updateGetRawIdx(),n},t.prototype.selectRange=function(t){var e=this.clone(),n=e._count;if(!n)return this;var i=F(t),r=i.length;if(!r)return this;var o=e.count(),a=new(Cd(e._rawCount))(o),s=0,l=i[0],u=t[l][0],h=t[l][1],c=e._chunks,p=!1;if(!e._indices){var f=0;if(1===r){for(var d=c[i[0]],g=0;g<n;g++){((_=d[g])>=u&&_<=h||isNaN(_))&&(a[s++]=f),f++}p=!0}else if(2===r){d=c[i[0]];var v=c[i[1]],y=t[i[1]][0],m=t[i[1]][1];for(g=0;g<n;g++){var _=d[g],x=v[g];(_>=u&&_<=h||isNaN(_))&&(x>=y&&x<=m||isNaN(x))&&(a[s++]=f),f++}p=!0}}if(!p)if(1===r)for(g=0;g<o;g++){var b=e.getRawIndex(g);((_=c[i[0]][b])>=u&&_<=h||isNaN(_))&&(a[s++]=b)}else for(g=0;g<o;g++){for(var w=!0,S=(b=e.getRawIndex(g),0);S<r;S++){var M=i[S];((_=c[M][b])<t[M][0]||_>t[M][1])&&(w=!1)}w&&(a[s++]=e.getRawIndex(g))}return s<o&&(e._indices=a),e._count=s,e._extent=[],e._updateGetRawIdx(),e},t.prototype.map=function(t,e){var n=this.clone(t);return this._updateDims(n,t,e),n},t.prototype.modify=function(t,e){this._updateDims(this,t,e)},t.prototype._updateDims=function(t,e,n){for(var i=t._chunks,r=[],o=e.length,a=t.count(),s=[],l=t._rawExtent,u=0;u<e.length;u++)l[e[u]]=[1/0,-1/0];for(var h=0;h<a;h++){for(var c=t.getRawIndex(h),p=0;p<o;p++)s[p]=i[e[p]][c];s[o]=h;var f=n&&n.apply(null,s);if(null!=f){"object"!=typeof f&&(r[0]=f,f=r);for(u=0;u<f.length;u++){var d=e[u],g=f[u],v=l[d],y=i[d];y&&(y[c]=g),g<v[0]&&(v[0]=g),g>v[1]&&(v[1]=g)}}}},t.prototype.lttbDownSample=function(t,e){var n,i,r,o=this.clone([t],!0),a=o._chunks[t],s=this.count(),l=0,u=Math.floor(1/e),h=this.getRawIndex(0),c=new(Cd(this._rawCount))(Math.min(2*(Math.ceil(s/u)+2),s));c[l++]=h;for(var p=1;p<s-1;p+=u){for(var f=Math.min(p+u,s-1),d=Math.min(p+2*u,s),g=(d+f)/2,v=0,y=f;y<d;y++){var m=a[T=this.getRawIndex(y)];isNaN(m)||(v+=m)}v/=d-f;var _=p,x=Math.min(p+u,s),b=p-1,w=a[h];n=-1,r=_;var S=-1,M=0;for(y=_;y<x;y++){var T;m=a[T=this.getRawIndex(y)];isNaN(m)?(M++,S<0&&(S=T)):(i=Math.abs((b-g)*(m-w)-(b-y)*(v-w)))>n&&(n=i,r=T)}M>0&&M<x-_&&(c[l++]=Math.min(S,r),r=Math.max(S,r)),c[l++]=r,h=r}return c[l++]=this.getRawIndex(s-1),o._count=l,o._indices=c,o.getRawIndex=this._getRawIdx,o},t.prototype.minmaxDownSample=function(t,e){for(var n=this.clone([t],!0),i=n._chunks,r=Math.floor(1/e),o=i[t],a=this.count(),s=new(Cd(this._rawCount))(2*Math.ceil(a/r)),l=0,u=0;u<a;u+=r){var h=u,c=o[this.getRawIndex(h)],p=u,f=o[this.getRawIndex(p)],d=r;u+r>a&&(d=a-u);for(var g=0;g<d;g++){var v=o[this.getRawIndex(u+g)];v<c&&(c=v,h=u+g),v>f&&(f=v,p=u+g)}var y=this.getRawIndex(h),m=this.getRawIndex(p);h<p?(s[l++]=y,s[l++]=m):(s[l++]=m,s[l++]=y)}return n._count=l,n._indices=s,n._updateGetRawIdx(),n},t.prototype.downSample=function(t,e,n,i){for(var r=this.clone([t],!0),o=r._chunks,a=[],s=Math.floor(1/e),l=o[t],u=this.count(),h=r._rawExtent[t]=[1/0,-1/0],c=new(Cd(this._rawCount))(Math.ceil(u/s)),p=0,f=0;f<u;f+=s){s>u-f&&(s=u-f,a.length=s);for(var d=0;d<s;d++){var g=this.getRawIndex(f+d);a[d]=l[g]}var v=n(a),y=this.getRawIndex(Math.min(f+i(a,v)||0,u-1));l[y]=v,v<h[0]&&(h[0]=v),v>h[1]&&(h[1]=v),c[p++]=y}return r._count=p,r._indices=c,r._updateGetRawIdx(),r},t.prototype.each=function(t,e){if(this._count)for(var n=t.length,i=this._chunks,r=0,o=this.count();r<o;r++){var a=this.getRawIndex(r);switch(n){case 0:e(r);break;case 1:e(i[t[0]][a],r);break;case 2:e(i[t[0]][a],i[t[1]][a],r);break;default:for(var s=0,l=[];s<n;s++)l[s]=i[t[s]][a];l[s]=r,e.apply(null,l)}}},t.prototype.getDataExtent=function(t){var e=this._chunks[t],n=[1/0,-1/0];if(!e)return n;var i,r=this.count();if(!this._indices)return this._rawExtent[t].slice();if(i=this._extent[t])return i.slice();for(var o=(i=n)[0],a=i[1],s=0;s<r;s++){var l=e[this.getRawIndex(s)];l<o&&(o=l),l>a&&(a=l)}return i=[o,a],this._extent[t]=i,i},t.prototype.getRawDataItem=function(t){var e=this.getRawIndex(t);if(this._provider.persistent)return this._provider.getItem(e);for(var n=[],i=this._chunks,r=0;r<i.length;r++)n.push(i[r][e]);return n},t.prototype.clone=function(e,n){var i,r,o=new t,a=this._chunks,s=e&&B(e,(function(t,e){return t[e]=!0,t}),{});if(s)for(var l=0;l<a.length;l++)o._chunks[l]=s[l]?(i=a[l],r=void 0,(r=i.constructor)===Array?i.slice():new r(i)):a[l];else o._chunks=a;return this._copyCommonProps(o),n||(o._indices=this._cloneIndices()),o._updateGetRawIdx(),o},t.prototype._copyCommonProps=function(t){t._count=this._count,t._rawCount=this._rawCount,t._provider=this._provider,t._dimensions=this._dimensions,t._extent=T(this._extent),t._rawExtent=T(this._rawExtent)},t.prototype._cloneIndices=function(){if(this._indices){var t=this._indices.constructor,e=void 0;if(t===Array){var n=this._indices.length;e=new t(n);for(var i=0;i<n;i++)e[i]=this._indices[i]}else e=new t(this._indices);return e}return null},t.prototype._getRawIdxIdentity=function(t){return t},t.prototype._getRawIdx=function(t){return t<this._count&&t>=0?this._indices[t]:-1},t.prototype._updateGetRawIdx=function(){this.getRawIndex=this._indices?this._getRawIdx:this._getRawIdxIdentity},t.internalField=function(){function t(t,e,n,i){return cd(t[i],this._dimensions[i])}xd={arrayRows:t,objectRows:function(t,e,n,i){return cd(t[e],this._dimensions[i])},keyedColumns:t,original:function(t,e,n,i){var r=t&&(null==t.value?t:t.value);return cd(r instanceof Array?r[i]:r,this._dimensions[i])},typedArray:function(t,e,n,i){return t[i]}}}(),t}(),Ad=function(){function t(t){this._sourceList=[],this._storeList=[],this._upstreamSignList=[],this._versionSignBase=0,this._dirty=!0,this._sourceHost=t}return t.prototype.dirty=function(){this._setLocalSource([],[]),this._storeList=[],this._dirty=!0},t.prototype._setLocalSource=function(t,e){this._sourceList=t,this._upstreamSignList=e,this._versionSignBase++,this._versionSignBase>9e10&&(this._versionSignBase=0)},t.prototype._getVersionSign=function(){return this._sourceHost.uid+"_"+this._versionSignBase},t.prototype.prepareSource=function(){this._isDirty()&&(this._createSource(),this._dirty=!1)},t.prototype._createSource=function(){this._setLocalSource([],[]);var t,e,n=this._sourceHost,i=this._getUpstreamSourceManagers(),r=!!i.length;if(Pd(n)){var o=n,a=void 0,s=void 0,l=void 0;if(r){var u=i[0];u.prepareSource(),a=(l=u.getSource()).data,s=l.sourceFormat,e=[u._getVersionSign()]}else s=j(a=o.get("data",!0))?Rp:Ap,e=[];var h=this._getSourceMetaRawOption()||{},c=l&&l.metaRawOption||{},p=nt(h.seriesLayoutBy,c.seriesLayoutBy)||null,f=nt(h.sourceHeader,c.sourceHeader),d=nt(h.dimensions,c.dimensions);t=p!==c.seriesLayoutBy||!!f!=!!c.sourceHeader||d?[Wf(a,{seriesLayoutBy:p,sourceHeader:f,dimensions:d},s)]:[]}else{var g=n;if(r){var v=this._applyTransform(i);t=v.sourceList,e=v.upstreamSignList}else{t=[Wf(g.get("source",!0),this._getSourceMetaRawOption(),null)],e=[]}}this._setLocalSource(t,e)},t.prototype._applyTransform=function(t){var e,n=this._sourceHost,i=n.get("transform",!0),r=n.get("fromTransformResult",!0);if(null!=r){var o="";1!==t.length&&Od(o)}var a,s=[],l=[];return R(t,(function(t){t.prepareSource();var e=t.getSource(r||0),n="";null==r||e||Od(n),s.push(e),l.push(t._getVersionSign())})),i?e=function(t,e,n){var i=po(t),r=i.length,o="";r||uo(o);for(var a=0,s=r;a<s;a++)e=md(i[a],e),a!==s-1&&(e.length=Math.max(e.length,1));return e}(i,s,n.componentIndex):null!=r&&(e=[(a=s[0],new Ff({data:a.data,sourceFormat:a.sourceFormat,seriesLayoutBy:a.seriesLayoutBy,dimensionsDefine:T(a.dimensionsDefine),startIndex:a.startIndex,dimensionsDetectedCount:a.dimensionsDetectedCount}))]),{sourceList:e,upstreamSignList:l}},t.prototype._isDirty=function(){if(this._dirty)return!0;for(var t=this._getUpstreamSourceManagers(),e=0;e<t.length;e++){var n=t[e];if(n._isDirty()||this._upstreamSignList[e]!==n._getVersionSign())return!0}},t.prototype.getSource=function(t){t=t||0;var e=this._sourceList[t];if(!e){var n=this._getUpstreamSourceManagers();return n[0]&&n[0].getSource(t)}return e},t.prototype.getSharedDataStore=function(t){var e=t.makeStoreSchema();return this._innerGetDataStore(e.dimensions,t.source,e.hash)},t.prototype._innerGetDataStore=function(t,e,n){var i=this._storeList,r=i[0];r||(r=i[0]={});var o=r[n];if(!o){var a=this._getUpstreamSourceManagers()[0];Pd(this._sourceHost)&&a?o=a._innerGetDataStore(t,e,n):(o=new Id).initData(new qf(e,t.length),t),r[n]=o}return o},t.prototype._getUpstreamSourceManagers=function(){var t=this._sourceHost;if(Pd(t)){var e=Up(t);return e?[e.getSourceManager()]:[]}return N(function(t){return t.get("transform",!0)||t.get("fromTransformResult",!0)?Ao(t.ecModel,"dataset",{index:t.get("fromDatasetIndex",!0),id:t.get("fromDatasetId",!0)},Io).models:[]}(t),(function(t){return t.getSourceManager()}))},t.prototype._getSourceMetaRawOption=function(){var t,e,n,i=this._sourceHost;if(Pd(i))t=i.get("seriesLayoutBy",!0),e=i.get("sourceHeader",!0),n=i.get("dimensions",!0);else if(!this._getUpstreamSourceManagers().length){var r=i;t=r.get("seriesLayoutBy",!0),e=r.get("sourceHeader",!0),n=r.get("dimensions",!0)}return{seriesLayoutBy:t,sourceHeader:e,dimensions:n}},t}();function Ld(t){t.option.transform&&ut(t.option.transform)}function Pd(t){return"series"===t.mainType}function Od(t){throw new Error(t)}function Rd(t,e){return e.type=t,e}function Nd(t){var e,n,i,r,o=t.series,a=t.dataIndex,s=t.multipleSeries,l=o.getData(),u=l.mapDimensionsAll("defaultedTooltip"),h=u.length,c=o.getRawValue(a),p=H(c),f=function(t,e){return Kc(t.getData().getItemVisual(e,"style")[t.visualDrawType])}(o,a);if(h>1||p&&!h){var d=function(t,e,n,i,r){var o=e.getData(),a=B(t,(function(t,e,n){var i=o.getDimensionInfo(n);return t||i&&!1!==i.tooltip&&null!=i.displayName}),!1),s=[],l=[],u=[];function h(t,e){var n=o.getDimensionInfo(e);n&&!1!==n.otherDims.tooltip&&(a?u.push(Rd("nameValue",{markerType:"subItem",markerColor:r,name:n.displayName,value:t,valueType:n.type})):(s.push(t),l.push(n.type)))}return i.length?R(i,(function(t){h(od(o,n,t),t)})):R(t,h),{inlineValues:s,inlineValueTypes:l,blocks:u}}(c,o,a,u,f);e=d.inlineValues,n=d.inlineValueTypes,i=d.blocks,r=d.inlineValues[0]}else if(h){var g=l.getDimensionInfo(u[0]);r=e=od(l,a,u[0]),n=g.type}else r=e=p?c[0]:c;var v=wo(o),y=v&&o.name||"",m=l.getName(a),_=s?y:m;return Rd("section",{header:y,noHeader:s||!v,sortParam:r,blocks:[Rd("nameValue",{markerType:"item",markerColor:f,name:_,noName:!st(_),value:e,valueType:n,dataIndex:a})].concat(i||[])})}var Bd=To();function Ed(t,e){return t.getName(e)||t.getId(e)}var zd=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e._selectedDataIndicesMap={},e}var i;return n(e,t),e.prototype.init=function(t,e,n){this.seriesIndex=this.componentIndex,this.dataTask=ld({count:Vd,reset:Wd}),this.dataTask.context={model:this},this.mergeDefaultAndTheme(t,n),(Bd(this).sourceManager=new Ad(this)).prepareSource();var i=this.getInitialData(t,n);Gd(i,this),this.dataTask.context.data=i,Bd(this).dataBeforeProcessed=i,Fd(this),this._initSelectedMapFromData(i)},e.prototype.mergeDefaultAndTheme=function(t,e){var n=gp(this),i=n?yp(t):{},r=this.subType;_p.hasClass(r)&&(r+="Series"),k(t,e.getTheme().get(this.subType)),k(t,this.getDefaultOption()),fo(t,"label",["show"]),this.fillDataTextStyle(t.data),n&&vp(t,i,n)},e.prototype.mergeOption=function(t,e){t=k(this.option,t,!0),this.fillDataTextStyle(t.data);var n=gp(this);n&&vp(this.option,t,n);var i=Bd(this).sourceManager;i.dirty(),i.prepareSource();var r=this.getInitialData(t,e);Gd(r,this),this.dataTask.dirty(),this.dataTask.context.data=r,Bd(this).dataBeforeProcessed=r,Fd(this),this._initSelectedMapFromData(r)},e.prototype.fillDataTextStyle=function(t){if(t&&!j(t))for(var e=["show"],n=0;n<t.length;n++)t[n]&&t[n].label&&fo(t[n],"label",e)},e.prototype.getInitialData=function(t,e){},e.prototype.appendData=function(t){this.getRawData().appendData(t.data)},e.prototype.getData=function(t){var e=Xd(this);if(e){var n=e.context.data;return null!=t&&n.getLinkedData?n.getLinkedData(t):n}return Bd(this).data},e.prototype.getAllData=function(){var t=this.getData();return t&&t.getLinkedDataAll?t.getLinkedDataAll():[{data:t}]},e.prototype.setData=function(t){var e=Xd(this);if(e){var n=e.context;n.outputData=t,e!==this.dataTask&&(n.data=t)}Bd(this).data=t},e.prototype.getEncode=function(){var t=this.get("encode",!0);if(t)return dt(t)},e.prototype.getSourceManager=function(){return Bd(this).sourceManager},e.prototype.getSource=function(){return this.getSourceManager().getSource()},e.prototype.getRawData=function(){return Bd(this).dataBeforeProcessed},e.prototype.getColorBy=function(){return this.get("colorBy")||"series"},e.prototype.isColorBySeries=function(){return"series"===this.getColorBy()},e.prototype.getBaseAxis=function(){var t=this.coordinateSystem;return t&&t.getBaseAxis&&t.getBaseAxis()},e.prototype.indicesOfNearest=function(t,e,n,i){var r=this.getData(),o=this.coordinateSystem,a=o&&o.getAxis(t);if(!o||!a)return[];var s=a.dataToCoord(n);null==i&&(i=1/0);var l=[],u=1/0,h=-1,c=0;return r.each(e,(function(t,e){var n=a.dataToCoord(t),r=s-n,o=Math.abs(r);o<=i&&((o<u||o===u&&r>=0&&h<0)&&(u=o,h=r,c=0),r===h&&(l[c++]=e))})),l.length=c,l},e.prototype.formatTooltip=function(t,e,n){return Nd({series:this,dataIndex:t,multipleSeries:e})},e.prototype.isAnimationEnabled=function(){var t=this.ecModel;if(r.node&&(!t||!t.ssr))return!1;var e=this.getShallow("animation");return e&&this.getData().count()>this.getShallow("animationThreshold")&&(e=!1),!!e},e.prototype.restoreData=function(){this.dataTask.dirty()},e.prototype.getColorFromPalette=function(t,e,n){var i=this.ecModel,r=Jp.prototype.getColorFromPalette.call(this,t,e,n);return r||(r=i.getColorFromPalette(t,e,n)),r},e.prototype.coordDimToDataDim=function(t){return this.getRawData().mapDimensionsAll(t)},e.prototype.getProgressive=function(){return this.get("progressive")},e.prototype.getProgressiveThreshold=function(){return this.get("progressiveThreshold")},e.prototype.select=function(t,e){this._innerSelect(this.getData(e),t)},e.prototype.unselect=function(t,e){var n=this.option.selectedMap;if(n){var i=this.option.selectedMode,r=this.getData(e);if("series"===i||"all"===n)return this.option.selectedMap={},void(this._selectedDataIndicesMap={});for(var o=0;o<t.length;o++){var a=Ed(r,t[o]);n[a]=!1,this._selectedDataIndicesMap[a]=-1}}},e.prototype.toggleSelect=function(t,e){for(var n=[],i=0;i<t.length;i++)n[0]=t[i],this.isSelected(t[i],e)?this.unselect(n,e):this.select(n,e)},e.prototype.getSelectedDataIndices=function(){if("all"===this.option.selectedMap)return[].slice.call(this.getData().getIndices());for(var t=this._selectedDataIndicesMap,e=F(t),n=[],i=0;i<e.length;i++){var r=t[e[i]];r>=0&&n.push(r)}return n},e.prototype.isSelected=function(t,e){var n=this.option.selectedMap;if(!n)return!1;var i=this.getData(e);return("all"===n||n[Ed(i,t)])&&!i.getItemModel(t).get(["select","disabled"])},e.prototype.isUniversalTransitionEnabled=function(){if(this.__universalTransitionEnabled)return!0;var t=this.option.universalTransition;return!!t&&(!0===t||t&&t.enabled)},e.prototype._innerSelect=function(t,e){var n,i,r=this.option,o=r.selectedMode,a=e.length;if(o&&a)if("series"===o)r.selectedMap="all";else if("multiple"===o){q(r.selectedMap)||(r.selectedMap={});for(var s=r.selectedMap,l=0;l<a;l++){var u=e[l];s[c=Ed(t,u)]=!0,this._selectedDataIndicesMap[c]=t.getRawIndex(u)}}else if("single"===o||!0===o){var h=e[a-1],c=Ed(t,h);r.selectedMap=((n={})[c]=!0,n),this._selectedDataIndicesMap=((i={})[c]=t.getRawIndex(h),i)}},e.prototype._initSelectedMapFromData=function(t){if(!this.option.selectedMap){var e=[];t.hasItemOption&&t.each((function(n){var i=t.getRawDataItem(n);i&&i.selected&&e.push(n)})),e.length>0&&this._innerSelect(t,e)}},e.registerClass=function(t){return _p.registerClass(t)},e.protoInitialize=((i=e.prototype).type="series.__base__",i.seriesIndex=0,i.ignoreStyleOnData=!1,i.hasSymbolVisual=!1,i.defaultSymbol="circle",i.visualStyleAccessPath="itemStyle",void(i.visualDrawType="fill")),e}(_p);function Fd(t){var e=t.name;wo(t)||(t.name=function(t){var e=t.getRawData(),n=e.mapDimensionsAll("seriesName"),i=[];return R(n,(function(t){var n=e.getDimensionInfo(t);n.displayName&&i.push(n.displayName)})),i.join(" ")}(t)||e)}function Vd(t){return t.model.getRawData().count()}function Wd(t){var e=t.model;return e.setData(e.getRawData().cloneShallow()),Hd}function Hd(t,e){e.outputData&&t.end>e.outputData.count()&&e.model.getRawData().cloneShallow(e.outputData)}function Gd(t,e){R(gt(t.CHANGABLE_METHODS,t.DOWNSAMPLE_METHODS),(function(n){t.wrapMethod(n,W(Ud,e))}))}function Ud(t,e){var n=Xd(t);return n&&n.setOutputEnd((e||this).count()),e}function Xd(t){var e=(t.ecModel||{}).scheduler,n=e&&e.getPipeline(t.uid);if(n){var i=n.currentTask;if(i){var r=i.agentStubMap;r&&(i=r.get(t.uid))}return i}}P(zd,sd),P(zd,Jp),Bo(zd,_p);var Yd=function(){function t(){this.group=new Tr,this.uid=oc("viewComponent")}return t.prototype.init=function(t,e){},t.prototype.render=function(t,e,n,i){},t.prototype.dispose=function(t,e){},t.prototype.updateView=function(t,e,n,i){},t.prototype.updateLayout=function(t,e,n,i){},t.prototype.updateVisual=function(t,e,n,i){},t.prototype.toggleBlurSeries=function(t,e,n){},t.prototype.eachRendered=function(t){var e=this.group;e&&e.traverse(t)},t}();function qd(){var t=To();return function(e){var n=t(e),i=e.pipelineContext,r=!!n.large,o=!!n.progressiveRender,a=n.large=!(!i||!i.large),s=n.progressiveRender=!(!i||!i.progressiveRender);return!(r===a&&o===s)&&"reset"}}No(Yd),Vo(Yd);var Zd=To(),jd=qd(),Kd=function(){function t(){this.group=new Tr,this.uid=oc("viewChart"),this.renderTask=ld({plan:Jd,reset:tg}),this.renderTask.context={view:this}}return t.prototype.init=function(t,e){},t.prototype.render=function(t,e,n,i){0},t.prototype.highlight=function(t,e,n,i){var r=t.getData(i&&i.dataType);r&&Qd(r,i,"emphasis")},t.prototype.downplay=function(t,e,n,i){var r=t.getData(i&&i.dataType);r&&Qd(r,i,"normal")},t.prototype.remove=function(t,e){this.group.removeAll()},t.prototype.dispose=function(t,e){},t.prototype.updateView=function(t,e,n,i){this.render(t,e,n,i)},t.prototype.updateLayout=function(t,e,n,i){this.render(t,e,n,i)},t.prototype.updateVisual=function(t,e,n,i){this.render(t,e,n,i)},t.prototype.eachRendered=function(t){kh(this.group,t)},t.markUpdateMethod=function(t,e){Zd(t).updateMethod=e},t.protoInitialize=void(t.prototype.type="chart"),t}();function $d(t,e,n){t&&zl(t)&&("emphasis"===e?xl:bl)(t,n)}function Qd(t,e,n){var i=Mo(t,e),r=e&&null!=e.highlightKey?function(t){var e=Zs[t];return null==e&&qs<=32&&(e=Zs[t]=qs++),e}(e.highlightKey):null;null!=i?R(po(i),(function(e){$d(t.getItemGraphicEl(e),n,r)})):t.eachItemGraphicEl((function(t){$d(t,n,r)}))}function Jd(t){return jd(t.model)}function tg(t){var e=t.model,n=t.ecModel,i=t.api,r=t.payload,o=e.pipelineContext.progressiveRender,a=t.view,s=r&&Zd(r).updateMethod,l=o?"incrementalPrepareRender":s&&a[s]?s:"render";return"render"!==l&&a[l](e,n,i,r),eg[l]}No(Kd),Vo(Kd);var eg={incrementalPrepareRender:{progress:function(t,e){e.view.incrementalRender(t,e.model,e.ecModel,e.api,e.payload)}},render:{forceFirstProgress:!0,progress:function(t,e){e.view.render(e.model,e.ecModel,e.api,e.payload)}}};function ng(t,e,n){var i,r,o,a,s,l=0,u=0,h=null;function c(){u=(new Date).getTime(),h=null,t.apply(o,a||[])}e=e||0;var p=function(){for(var t=[],p=0;p<arguments.length;p++)t[p]=arguments[p];i=(new Date).getTime(),o=this,a=t;var f=s||e,d=s||n;s=null,r=i-(d?l:u)-f,clearTimeout(h),d?h=setTimeout(c,f):r>=0?c():h=setTimeout(c,-r),l=i};return p.clear=function(){h&&(clearTimeout(h),h=null)},p.debounceNextCall=function(t){s=t},p}var ig=To(),rg={itemStyle:Wo(tc,!0),lineStyle:Wo($h,!0)},og={lineStyle:"stroke",itemStyle:"fill"};function ag(t,e){var n=t.visualStyleMapper||rg[e];return n||(console.warn("Unknown style type '"+e+"'."),rg.itemStyle)}function sg(t,e){var n=t.visualDrawType||og[e];return n||(console.warn("Unknown style type '"+e+"'."),"fill")}var lg={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){var n=t.getData(),i=t.visualStyleAccessPath||"itemStyle",r=t.getModel(i),o=ag(t,i)(r),a=r.getShallow("decal");a&&(n.setVisual("decal",a),a.dirty=!0);var s=sg(t,i),l=o[s],u=G(l)?l:null,h="auto"===o.fill||"auto"===o.stroke;if(!o[s]||u||h){var c=t.getColorFromPalette(t.name,null,e.getSeriesCount());o[s]||(o[s]=c,n.setVisual("colorFromPalette",!0)),o.fill="auto"===o.fill||G(o.fill)?c:o.fill,o.stroke="auto"===o.stroke||G(o.stroke)?c:o.stroke}if(n.setVisual("style",o),n.setVisual("drawType",s),!e.isSeriesFiltered(t)&&u)return n.setVisual("colorFromPalette",!1),{dataEach:function(e,n){var i=t.getDataParams(n),r=C({},o);r[s]=u(i),e.setItemVisual(n,"style",r)}}}},ug=new ic,hg={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){if(!t.ignoreStyleOnData&&!e.isSeriesFiltered(t)){var n=t.getData(),i=t.visualStyleAccessPath||"itemStyle",r=ag(t,i),o=n.getVisual("drawType");return{dataEach:n.hasItemOption?function(t,e){var n=t.getRawDataItem(e);if(n&&n[i]){ug.option=n[i];var a=r(ug);C(t.ensureUniqueItemVisual(e,"style"),a),ug.option.decal&&(t.setItemVisual(e,"decal",ug.option.decal),ug.option.decal.dirty=!0),o in a&&t.setItemVisual(e,"colorFromPalette",!1)}}:null}}}},cg={performRawSeries:!0,overallReset:function(t){var e=dt();t.eachSeries((function(t){var n=t.getColorBy();if(!t.isColorBySeries()){var i=t.type+"-"+n,r=e.get(i);r||(r={},e.set(i,r)),ig(t).scope=r}})),t.eachSeries((function(e){if(!e.isColorBySeries()&&!t.isSeriesFiltered(e)){var n=e.getRawData(),i={},r=e.getData(),o=ig(e).scope,a=e.visualStyleAccessPath||"itemStyle",s=sg(e,a);r.each((function(t){var e=r.getRawIndex(t);i[e]=t})),n.each((function(t){var a=i[t];if(r.getItemVisual(a,"colorFromPalette")){var l=r.ensureUniqueItemVisual(a,"style"),u=n.getName(t)||t+"",h=n.count();l[s]=e.getColorFromPalette(u,o,h)}}))}}))}},pg=Math.PI;var fg=function(){function t(t,e,n,i){this._stageTaskMap=dt(),this.ecInstance=t,this.api=e,n=this._dataProcessorHandlers=n.slice(),i=this._visualHandlers=i.slice(),this._allHandlers=n.concat(i)}return t.prototype.restoreData=function(t,e){t.restoreData(e),this._stageTaskMap.each((function(t){var e=t.overallTask;e&&e.dirty()}))},t.prototype.getPerformArgs=function(t,e){if(t.__pipeline){var n=this._pipelineMap.get(t.__pipeline.id),i=n.context,r=!e&&n.progressiveEnabled&&(!i||i.progressiveRender)&&t.__idxInPipeline>n.blockIndex?n.step:null,o=i&&i.modDataCount;return{step:r,modBy:null!=o?Math.ceil(o/r):null,modDataCount:o}}},t.prototype.getPipeline=function(t){return this._pipelineMap.get(t)},t.prototype.updateStreamModes=function(t,e){var n=this._pipelineMap.get(t.uid),i=t.getData().count(),r=n.progressiveEnabled&&e.incrementalPrepareRender&&i>=n.threshold,o=t.get("large")&&i>=t.get("largeThreshold"),a="mod"===t.get("progressiveChunkMode")?i:null;t.pipelineContext=n.context={progressiveRender:r,modDataCount:a,large:o}},t.prototype.restorePipelines=function(t){var e=this,n=e._pipelineMap=dt();t.eachSeries((function(t){var i=t.getProgressive(),r=t.uid;n.set(r,{id:r,head:null,tail:null,threshold:t.getProgressiveThreshold(),progressiveEnabled:i&&!(t.preventIncremental&&t.preventIncremental()),blockIndex:-1,step:Math.round(i||700),count:0}),e._pipe(t,t.dataTask)}))},t.prototype.prepareStageTasks=function(){var t=this._stageTaskMap,e=this.api.getModel(),n=this.api;R(this._allHandlers,(function(i){var r=t.get(i.uid)||t.set(i.uid,{}),o="";at(!(i.reset&&i.overallReset),o),i.reset&&this._createSeriesStageTask(i,r,e,n),i.overallReset&&this._createOverallStageTask(i,r,e,n)}),this)},t.prototype.prepareView=function(t,e,n,i){var r=t.renderTask,o=r.context;o.model=e,o.ecModel=n,o.api=i,r.__block=!t.incrementalPrepareRender,this._pipe(e,r)},t.prototype.performDataProcessorTasks=function(t,e){this._performStageTasks(this._dataProcessorHandlers,t,e,{block:!0})},t.prototype.performVisualTasks=function(t,e,n){this._performStageTasks(this._visualHandlers,t,e,n)},t.prototype._performStageTasks=function(t,e,n,i){i=i||{};var r=!1,o=this;function a(t,e){return t.setDirty&&(!t.dirtyMap||t.dirtyMap.get(e.__pipeline.id))}R(t,(function(t,s){if(!i.visualType||i.visualType===t.visualType){var l=o._stageTaskMap.get(t.uid),u=l.seriesTaskMap,h=l.overallTask;if(h){var c,p=h.agentStubMap;p.each((function(t){a(i,t)&&(t.dirty(),c=!0)})),c&&h.dirty(),o.updatePayload(h,n);var f=o.getPerformArgs(h,i.block);p.each((function(t){t.perform(f)})),h.perform(f)&&(r=!0)}else u&&u.each((function(s,l){a(i,s)&&s.dirty();var u=o.getPerformArgs(s,i.block);u.skip=!t.performRawSeries&&e.isSeriesFiltered(s.context.model),o.updatePayload(s,n),s.perform(u)&&(r=!0)}))}})),this.unfinished=r||this.unfinished},t.prototype.performSeriesTasks=function(t){var e;t.eachSeries((function(t){e=t.dataTask.perform()||e})),this.unfinished=e||this.unfinished},t.prototype.plan=function(){this._pipelineMap.each((function(t){var e=t.tail;do{if(e.__block){t.blockIndex=e.__idxInPipeline;break}e=e.getUpstream()}while(e)}))},t.prototype.updatePayload=function(t,e){"remain"!==e&&(t.context.payload=e)},t.prototype._createSeriesStageTask=function(t,e,n,i){var r=this,o=e.seriesTaskMap,a=e.seriesTaskMap=dt(),s=t.seriesType,l=t.getTargetSeries;function u(e){var s=e.uid,l=a.set(s,o&&o.get(s)||ld({plan:mg,reset:_g,count:wg}));l.context={model:e,ecModel:n,api:i,useClearVisual:t.isVisual&&!t.isLayout,plan:t.plan,reset:t.reset,scheduler:r},r._pipe(e,l)}t.createOnAllSeries?n.eachRawSeries(u):s?n.eachRawSeriesByType(s,u):l&&l(n,i).each(u)},t.prototype._createOverallStageTask=function(t,e,n,i){var r=this,o=e.overallTask=e.overallTask||ld({reset:dg});o.context={ecModel:n,api:i,overallReset:t.overallReset,scheduler:r};var a=o.agentStubMap,s=o.agentStubMap=dt(),l=t.seriesType,u=t.getTargetSeries,h=!0,c=!1,p="";function f(t){var e=t.uid,n=s.set(e,a&&a.get(e)||(c=!0,ld({reset:gg,onDirty:yg})));n.context={model:t,overallProgress:h},n.agent=o,n.__block=h,r._pipe(t,n)}at(!t.createOnAllSeries,p),l?n.eachRawSeriesByType(l,f):u?u(n,i).each(f):(h=!1,R(n.getSeries(),f)),c&&o.dirty()},t.prototype._pipe=function(t,e){var n=t.uid,i=this._pipelineMap.get(n);!i.head&&(i.head=e),i.tail&&i.tail.pipe(e),i.tail=e,e.__idxInPipeline=i.count++,e.__pipeline=i},t.wrapStageHandler=function(t,e){return G(t)&&(t={overallReset:t,seriesType:Sg(t)}),t.uid=oc("stageHandler"),e&&(t.visualType=e),t},t}();function dg(t){t.overallReset(t.ecModel,t.api,t.payload)}function gg(t){return t.overallProgress&&vg}function vg(){this.agent.dirty(),this.getDownstream().dirty()}function yg(){this.agent&&this.agent.dirty()}function mg(t){return t.plan?t.plan(t.model,t.ecModel,t.api,t.payload):null}function _g(t){t.useClearVisual&&t.data.clearAllVisual();var e=t.resetDefines=po(t.reset(t.model,t.ecModel,t.api,t.payload));return e.length>1?N(e,(function(t,e){return bg(e)})):xg}var xg=bg(0);function bg(t){return function(e,n){var i=n.data,r=n.resetDefines[t];if(r&&r.dataEach)for(var o=e.start;o<e.end;o++)r.dataEach(i,o);else r&&r.progress&&r.progress(e,i)}}function wg(t){return t.data.count()}function Sg(t){Mg=null;try{t(Tg,kg)}catch(t){}return Mg}var Mg,Tg={},kg={};function Cg(t,e){for(var n in e.prototype)t[n]=_t}Cg(Tg,rf),Cg(kg,uf),Tg.eachSeriesByType=Tg.eachRawSeriesByType=function(t){Mg=t},Tg.eachComponent=function(t){"series"===t.mainType&&t.subType&&(Mg=t.subType)};var Dg,Ig=xp.darkColor,Ag=Ig.background,Lg=function(){return{axisLine:{lineStyle:{color:Ig.axisLine}},splitLine:{lineStyle:{color:Ig.axisSplitLine}},splitArea:{areaStyle:{color:[Ig.backgroundTint,Ig.backgroundTransparent]}},minorSplitLine:{lineStyle:{color:Ig.axisMinorSplitLine}},axisLabel:{color:Ig.axisLabel},axisName:{}}},Pg={label:{color:Ig.secondary},itemStyle:{borderColor:Ig.borderTint},dividerLineStyle:{color:Ig.border}},Og={darkMode:!0,color:Ig.theme,backgroundColor:Ag,axisPointer:{lineStyle:{color:Ig.border},crossStyle:{color:Ig.borderShade},label:{color:Ig.tertiary}},legend:{textStyle:{color:Ig.secondary},pageTextStyle:{color:Ig.tertiary}},textStyle:{color:Ig.secondary},title:{textStyle:{color:Ig.primary},subtextStyle:{color:Ig.quaternary}},toolbox:{iconStyle:{borderColor:Ig.accent50}},tooltip:{backgroundColor:Ig.neutral20,defaultBorderColor:Ig.border,textStyle:{color:Ig.tertiary}},dataZoom:{borderColor:Ig.accent10,textStyle:{color:Ig.tertiary},brushStyle:{color:Ig.backgroundTint},handleStyle:{color:Ig.neutral00,borderColor:Ig.accent20},moveHandleStyle:{color:Ig.accent40},emphasis:{handleStyle:{borderColor:Ig.accent50}},dataBackground:{lineStyle:{color:Ig.accent30},areaStyle:{color:Ig.accent20}},selectedDataBackground:{lineStyle:{color:Ig.accent50},areaStyle:{color:Ig.accent30}}},visualMap:{textStyle:{color:Ig.secondary},handleStyle:{borderColor:Ig.neutral30}},timeline:{lineStyle:{color:Ig.accent10},label:{color:Ig.tertiary},controlStyle:{color:Ig.accent30,borderColor:Ig.accent30}},calendar:{itemStyle:{color:Ig.neutral00,borderColor:Ig.neutral20},dayLabel:{color:Ig.tertiary},monthLabel:{color:Ig.secondary},yearLabel:{color:Ig.secondary}},matrix:{x:Pg,y:Pg,backgroundColor:{borderColor:Ig.axisLine},body:{itemStyle:{borderColor:Ig.borderTint}}},timeAxis:Lg(),logAxis:Lg(),valueAxis:Lg(),categoryAxis:Lg(),line:{symbol:"circle"},graph:{color:Ig.theme},gauge:{title:{color:Ig.secondary},axisLine:{lineStyle:{color:[[1,Ig.neutral05]]}},axisLabel:{color:Ig.axisLabel},detail:{color:Ig.primary}},candlestick:{itemStyle:{color:"#f64e56",color0:"#54ea92",borderColor:"#f64e56",borderColor0:"#54ea92"}},funnel:{itemStyle:{borderColor:Ig.background}},radar:(Dg=Lg(),Dg.axisName={color:Ig.axisLabel},Dg.axisLine.lineStyle.color=Ig.neutral20,Dg),treemap:{breadcrumb:{itemStyle:{color:Ig.neutral20,textStyle:{color:Ig.secondary}},emphasis:{itemStyle:{color:Ig.neutral30}}}},sunburst:{itemStyle:{borderColor:Ig.background}},map:{itemStyle:{borderColor:Ig.border,areaColor:Ig.neutral10},label:{color:Ig.tertiary},emphasis:{label:{color:Ig.primary},itemStyle:{areaColor:Ig.highlight}},select:{label:{color:Ig.primary},itemStyle:{areaColor:Ig.highlight}}},geo:{itemStyle:{borderColor:Ig.border,areaColor:Ig.neutral10},emphasis:{label:{color:Ig.primary},itemStyle:{areaColor:Ig.highlight}},select:{label:{color:Ig.primary},itemStyle:{color:Ig.highlight}}}};Og.categoryAxis.splitLine.show=!1;var Rg=function(){function t(){}return t.prototype.normalizeQuery=function(t){var e={},n={},i={};if(U(t)){var r=Ro(t);e.mainType=r.main||null,e.subType=r.sub||null}else{var o=["Index","Name","Id"],a={name:1,dataIndex:1,dataType:1};R(t,(function(t,r){for(var s=!1,l=0;l<o.length;l++){var u=o[l],h=r.lastIndexOf(u);if(h>0&&h===r.length-u.length){var c=r.slice(0,h);"data"!==c&&(e.mainType=c,e[u.toLowerCase()]=t,s=!0)}}a.hasOwnProperty(r)&&(n[r]=t,s=!0),s||(i[r]=t)}))}return{cptQuery:e,dataQuery:n,otherQuery:i}},t.prototype.filter=function(t,e){var n=this.eventInfo;if(!n)return!0;var i=n.targetEl,r=n.packedEvent,o=n.model,a=n.view;if(!o||!a)return!0;var s=e.cptQuery,l=e.dataQuery;return u(s,o,"mainType")&&u(s,o,"subType")&&u(s,o,"index","componentIndex")&&u(s,o,"name")&&u(s,o,"id")&&u(l,r,"name")&&u(l,r,"dataIndex")&&u(l,r,"dataType")&&(!a.filterForExposedEvent||a.filterForExposedEvent(t,e.otherQuery,i,r));function u(t,e,n,i){return null==t[n]||e[i||n]===t[n]}},t.prototype.afterTrigger=function(){this.eventInfo=null},t}(),Ng=["symbol","symbolSize","symbolRotate","symbolOffset"],Bg=Ng.concat(["symbolKeepAspect"]),Eg={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){var n=t.getData();if(t.legendIcon&&n.setVisual("legendIcon",t.legendIcon),t.hasSymbolVisual){for(var i={},r={},o=!1,a=0;a<Ng.length;a++){var s=Ng[a],l=t.get(s);G(l)?(o=!0,r[s]=l):i[s]=l}if(i.symbol=i.symbol||t.defaultSymbol,n.setVisual(C({legendIcon:t.legendIcon||i.symbol,symbolKeepAspect:t.get("symbolKeepAspect")},i)),!e.isSeriesFiltered(t)){var u=F(r);return{dataEach:o?function(e,n){for(var i=t.getRawValue(n),o=t.getDataParams(n),a=0;a<u.length;a++){var s=u[a];e.setItemVisual(n,s,r[s](i,o))}}:null}}}}},zg={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){if(t.hasSymbolVisual&&!e.isSeriesFiltered(t))return{dataEach:t.getData().hasItemOption?function(t,e){for(var n=t.getItemModel(e),i=0;i<Bg.length;i++){var r=Bg[i],o=n.getShallow(r,!0);null!=o&&t.setItemVisual(e,r,o)}}:null}}};function Fg(t,e,n,i,r){var o=t+e;n.isSilent(o)||i.eachComponent({mainType:"series",subType:"pie"},(function(t){for(var e=t.seriesIndex,i=t.option.selectedMap,a=r.selected,s=0;s<a.length;s++)if(a[s].seriesIndex===e){var l=t.getData(),u=Mo(l,r.fromActionPayload);n.trigger(o,{type:o,seriesId:t.id,name:H(u)?l.getName(u[0]):l.getName(u),selected:U(i)?i:C({},i)})}}))}function Vg(t,e,n){for(var i;t&&(!e(t)||(i=t,!n));)t=t.__hostTarget||t.parent;return i}var Wg=Math.round(9*Math.random()),Hg="function"==typeof Object.defineProperty,Gg=function(){function t(){this._id="__ec_inner_"+Wg++}return t.prototype.get=function(t){return this._guard(t)[this._id]},t.prototype.set=function(t,e){var n=this._guard(t);return Hg?Object.defineProperty(n,this._id,{value:e,enumerable:!1,configurable:!0}):n[this._id]=e,this},t.prototype.delete=function(t){return!!this.has(t)&&(delete this._guard(t)[this._id],!0)},t.prototype.has=function(t){return!!this._guard(t)[this._id]},t.prototype._guard=function(t){if(t!==Object(t))throw TypeError("Value of WeakMap is not a non-null object.");return t},t}(),Ug=_s.extend({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2,o=e.height/2;t.moveTo(n,i-o),t.lineTo(n+r,i+o),t.lineTo(n-r,i+o),t.closePath()}}),Xg=_s.extend({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2,o=e.height/2;t.moveTo(n,i-o),t.lineTo(n+r,i),t.lineTo(n,i+o),t.lineTo(n-r,i),t.closePath()}}),Yg=_s.extend({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.x,i=e.y,r=e.width/5*3,o=Math.max(r,e.height),a=r/2,s=a*a/(o-a),l=i-o+a+s,u=Math.asin(s/a),h=Math.cos(u)*a,c=Math.sin(u),p=Math.cos(u),f=.6*a,d=.7*a;t.moveTo(n-h,l+s),t.arc(n,l,a,Math.PI-u,2*Math.PI+u),t.bezierCurveTo(n+h-c*f,l+s+p*f,n,i-d,n,i),t.bezierCurveTo(n,i-d,n-h+c*f,l+s+p*f,n-h,l+s),t.closePath()}}),qg=_s.extend({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.height,i=e.width,r=e.x,o=e.y,a=i/3*2;t.moveTo(r,o),t.lineTo(r+a,o+n),t.lineTo(r,o+n/4*3),t.lineTo(r-a,o+n),t.lineTo(r,o),t.closePath()}}),Zg={line:function(t,e,n,i,r){r.x1=t,r.y1=e+i/2,r.x2=t+n,r.y2=e+i/2},rect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i},roundRect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i,r.r=Math.min(n,i)/4},square:function(t,e,n,i,r){var o=Math.min(n,i);r.x=t,r.y=e,r.width=o,r.height=o},circle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.r=Math.min(n,i)/2},diamond:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i},pin:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},arrow:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},triangle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i}},jg={};R({line:Pu,rect:As,roundRect:As,square:As,circle:ou,diamond:Xg,pin:Yg,arrow:qg,triangle:Ug},(function(t,e){jg[e]=new t}));var Kg=_s.extend({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},calculateTextPosition:function(t,e,n){var i=fr(t,e,n),r=this.shape;return r&&"pin"===r.symbolType&&"inside"===e.position&&(i.y=n.y+.4*n.height),i},buildPath:function(t,e,n){var i=e.symbolType;if("none"!==i){var r=jg[i];r||(r=jg[i="rect"]),Zg[i](e.x,e.y,e.width,e.height,r.shape),r.buildPath(t,r.shape,n)}}});function $g(t,e){if("image"!==this.type){var n=this.style;this.__isEmptyBrush?(n.stroke=t,n.fill=e||xp.color.neutral00,n.lineWidth=2):"line"===this.shape.symbolType?n.stroke=t:n.fill=t,this.markRedraw()}}function Qg(t,e,n,i,r,o,a){var s,l=0===t.indexOf("empty");return l&&(t=t.substr(5,1).toLowerCase()+t.substr(6)),(s=0===t.indexOf("image://")?gh(t.slice(8),new Oe(e,n,i,r),a?"center":"cover"):0===t.indexOf("path://")?dh(t.slice(7),{},new Oe(e,n,i,r),a?"center":"cover"):new Kg({shape:{symbolType:t,x:e,y:n,width:i,height:r}})).__isEmptyBrush=l,s.setColor=$g,o&&s.setColor(o),s}function Jg(t,e){if(null!=t)return H(t)||(t=[t,t]),[Fr(t[0],e[0])||0,Fr(nt(t[1],t[0]),e[1])||0]}function tv(t){return isFinite(t)}function ev(t,e,n){for(var i="radial"===e.type?function(t,e,n){var i=n.width,r=n.height,o=Math.min(i,r),a=null==e.x?.5:e.x,s=null==e.y?.5:e.y,l=null==e.r?.5:e.r;return e.global||(a=a*i+n.x,s=s*r+n.y,l*=o),a=tv(a)?a:.5,s=tv(s)?s:.5,l=l>=0&&tv(l)?l:.5,t.createRadialGradient(a,s,0,a,s,l)}(t,e,n):function(t,e,n){var i=null==e.x?0:e.x,r=null==e.x2?1:e.x2,o=null==e.y?0:e.y,a=null==e.y2?0:e.y2;return e.global||(i=i*n.width+n.x,r=r*n.width+n.x,o=o*n.height+n.y,a=a*n.height+n.y),i=tv(i)?i:0,r=tv(r)?r:1,o=tv(o)?o:0,a=tv(a)?a:0,t.createLinearGradient(i,o,r,a)}(t,e,n),r=e.colorStops,o=0;o<r.length;o++)i.addColorStop(r[o].offset,r[o].color);return i}function nv(t){return parseInt(t,10)}function iv(t,e,n){var i=["width","height"][e],r=["clientWidth","clientHeight"][e],o=["paddingLeft","paddingTop"][e],a=["paddingRight","paddingBottom"][e];if(null!=n[i]&&"auto"!==n[i])return parseFloat(n[i]);var s=document.defaultView.getComputedStyle(t);return(t[r]||nv(s[i])||nv(t.style[i]))-(nv(s[o])||0)-(nv(s[a])||0)|0}function rv(t){var e,n,i=t.style,r=i.lineDash&&i.lineWidth>0&&(e=i.lineDash,n=i.lineWidth,e&&"solid"!==e&&n>0?"dashed"===e?[4*n,2*n]:"dotted"===e?[n]:Y(e)?[e]:H(e)?e:null:null),o=i.lineDashOffset;if(r){var a=i.strokeNoScale&&t.getLineScale?t.getLineScale():1;a&&1!==a&&(r=N(r,(function(t){return t/a})),o/=a)}return[r,o]}var ov=new Ja(!0);function av(t){var e=t.stroke;return!(null==e||"none"===e||!(t.lineWidth>0))}function sv(t){return"string"==typeof t&&"none"!==t}function lv(t){var e=t.fill;return null!=e&&"none"!==e}function uv(t,e){if(null!=e.fillOpacity&&1!==e.fillOpacity){var n=t.globalAlpha;t.globalAlpha=e.fillOpacity*e.opacity,t.fill(),t.globalAlpha=n}else t.fill()}function hv(t,e){if(null!=e.strokeOpacity&&1!==e.strokeOpacity){var n=t.globalAlpha;t.globalAlpha=e.strokeOpacity*e.opacity,t.stroke(),t.globalAlpha=n}else t.stroke()}function cv(t,e,n){var i=Yo(e.image,e.__image,n);if(Zo(i)){var r=t.createPattern(i,e.repeat||"repeat");if("function"==typeof DOMMatrix&&r&&r.setTransform){var o=new DOMMatrix;o.translateSelf(e.x||0,e.y||0),o.rotateSelf(0,0,(e.rotation||0)*xt),o.scaleSelf(e.scaleX||1,e.scaleY||1),r.setTransform(o)}return r}}var pv=["shadowBlur","shadowOffsetX","shadowOffsetY"],fv=[["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]];function dv(t,e,n,i,r){var o=!1;if(!i&&e===(n=n||{}))return!1;if(i||e.opacity!==n.opacity){yv(t,r),o=!0;var a=Math.max(Math.min(e.opacity,1),0);t.globalAlpha=isNaN(a)?da.opacity:a}(i||e.blend!==n.blend)&&(o||(yv(t,r),o=!0),t.globalCompositeOperation=e.blend||da.blend);for(var s=0;s<pv.length;s++){var l=pv[s];(i||e[l]!==n[l])&&(o||(yv(t,r),o=!0),t[l]=t.dpr*(e[l]||0))}return(i||e.shadowColor!==n.shadowColor)&&(o||(yv(t,r),o=!0),t.shadowColor=e.shadowColor||da.shadowColor),o}function gv(t,e,n,i,r){var o=mv(e,r.inHover),a=i?null:n&&mv(n,r.inHover)||{};if(o===a)return!1;var s=dv(t,o,a,i,r);if((i||o.fill!==a.fill)&&(s||(yv(t,r),s=!0),sv(o.fill)&&(t.fillStyle=o.fill)),(i||o.stroke!==a.stroke)&&(s||(yv(t,r),s=!0),sv(o.stroke)&&(t.strokeStyle=o.stroke)),(i||o.opacity!==a.opacity)&&(s||(yv(t,r),s=!0),t.globalAlpha=null==o.opacity?1:o.opacity),e.hasStroke()){var l=o.lineWidth/(o.strokeNoScale&&e.getLineScale?e.getLineScale():1);t.lineWidth!==l&&(s||(yv(t,r),s=!0),t.lineWidth=l)}for(var u=0;u<fv.length;u++){var h=fv[u],c=h[0];(i||o[c]!==a[c])&&(s||(yv(t,r),s=!0),t[c]=o[c]||h[1])}return s}function vv(t,e){var n=e.transform,i=t.dpr||1;n?t.setTransform(i*n[0],i*n[1],i*n[2],i*n[3],i*n[4],i*n[5]):t.setTransform(i,0,0,i,0,0)}function yv(t,e){e.batchFill&&t.fill(),e.batchStroke&&t.stroke(),e.batchFill="",e.batchStroke=""}function mv(t,e){return e&&t.__hoverStyle||t.style}function _v(t,e){xv(t,e,{inHover:!1,viewWidth:0,viewHeight:0},!0)}function xv(t,e,n,i){var r=e.transform;if(!e.shouldBePainted(n.viewWidth,n.viewHeight,!1,!1))return e.__dirty&=-2,void(e.__isRendered=!1);var a=e.__clipPaths,s=n.prevElClipPaths,l=!1,u=!1;if(s&&!function(t,e){if(t===e||!t&&!e)return!1;if(!t||!e||t.length!==e.length)return!0;for(var n=0;n<t.length;n++)if(t[n]!==e[n])return!0;return!1}(a,s)||(s&&s.length&&(yv(t,n),t.restore(),u=l=!0,n.prevElClipPaths=null,n.allClipped=!1,n.prevEl=null),a&&a.length&&(yv(t,n),t.save(),function(t,e,n){for(var i=!1,r=0;r<t.length;r++){var o=t[r];i=i||o.isZeroArea(),vv(e,o),e.beginPath(),o.buildPath(e,o.shape),e.clip()}n.allClipped=i}(a,t,n),l=!0),n.prevElClipPaths=a),n.allClipped)e.__isRendered=!1;else{e.beforeBrush&&e.beforeBrush(),e.innerBeforeBrush();var h=n.prevEl;h||(u=l=!0);var c,p,f=e instanceof _s&&e.autoBatch&&function(t){var e=lv(t),n=av(t);return!(t.lineDash||!(+e^+n)||e&&"string"!=typeof t.fill||n&&"string"!=typeof t.stroke||t.strokePercent<1||t.strokeOpacity<1||t.fillOpacity<1)}(e.style);l||(c=r,p=h.transform,c&&p?c[0]!==p[0]||c[1]!==p[1]||c[2]!==p[2]||c[3]!==p[3]||c[4]!==p[4]||c[5]!==p[5]:c||p)?(yv(t,n),vv(t,e)):f||yv(t,n);var d=mv(e,n.inHover);e instanceof _s?(1!==n.lastDrawType&&(u=!0,n.lastDrawType=1),gv(t,e,h,u,n),f&&(n.batchFill||n.batchStroke)||t.beginPath(),function(t,e,n,i){var r,o=av(n),a=lv(n),s=n.strokePercent,l=s<1,u=!e.path;e.silent&&!l||!u||e.createPathProxy();var h=e.path||ov,c=e.__dirty;if(!i){var p=n.fill,f=n.stroke,d=a&&!!p.colorStops,g=o&&!!f.colorStops,v=a&&!!p.image,y=o&&!!f.image,m=void 0,_=void 0,x=void 0,b=void 0,w=void 0;(d||g)&&(w=e.getBoundingRect()),d&&(m=c?ev(t,p,w):e.__canvasFillGradient,e.__canvasFillGradient=m),g&&(_=c?ev(t,f,w):e.__canvasStrokeGradient,e.__canvasStrokeGradient=_),v&&(x=c||!e.__canvasFillPattern?cv(t,p,e):e.__canvasFillPattern,e.__canvasFillPattern=x),y&&(b=c||!e.__canvasStrokePattern?cv(t,f,e):e.__canvasStrokePattern,e.__canvasStrokePattern=b),d?t.fillStyle=m:v&&(x?t.fillStyle=x:a=!1),g?t.strokeStyle=_:y&&(b?t.strokeStyle=b:o=!1)}var S,M,T=e.getGlobalScale();h.setScale(T[0],T[1],e.segmentIgnoreThreshold),t.setLineDash&&n.lineDash&&(S=(r=rv(e))[0],M=r[1]);var k=!0;(u||4&c)&&(h.setDPR(t.dpr),l?h.setContext(null):(h.setContext(t),k=!1),h.reset(),e.buildPath(h,e.shape,i),h.toStatic(),e.pathUpdated()),k&&h.rebuildPath(t,l?s:1),S&&(t.setLineDash(S),t.lineDashOffset=M),i||(n.strokeFirst?(o&&hv(t,n),a&&uv(t,n)):(a&&uv(t,n),o&&hv(t,n))),S&&t.setLineDash([])}(t,e,d,f),f&&(n.batchFill=d.fill||"",n.batchStroke=d.stroke||"")):e instanceof bs?(3!==n.lastDrawType&&(u=!0,n.lastDrawType=3),gv(t,e,h,u,n),function(t,e,n){var i,r=n.text;if(null!=r&&(r+=""),r){t.font=n.font||o,t.textAlign=n.textAlign,t.textBaseline=n.textBaseline;var a=void 0,s=void 0;t.setLineDash&&n.lineDash&&(a=(i=rv(e))[0],s=i[1]),a&&(t.setLineDash(a),t.lineDashOffset=s),n.strokeFirst?(av(n)&&t.strokeText(r,n.x,n.y),lv(n)&&t.fillText(r,n.x,n.y)):(lv(n)&&t.fillText(r,n.x,n.y),av(n)&&t.strokeText(r,n.x,n.y)),a&&t.setLineDash([])}}(t,e,d)):e instanceof Ms?(2!==n.lastDrawType&&(u=!0,n.lastDrawType=2),function(t,e,n,i,r){dv(t,mv(e,r.inHover),n&&mv(n,r.inHover),i,r)}(t,e,h,u,n),function(t,e,n){var i=e.__image=Yo(n.image,e.__image,e,e.onload);if(i&&Zo(i)){var r=n.x||0,o=n.y||0,a=e.getWidth(),s=e.getHeight(),l=i.width/i.height;if(null==a&&null!=s?a=s*l:null==s&&null!=a?s=a/l:null==a&&null==s&&(a=i.width,s=i.height),n.sWidth&&n.sHeight){var u=n.sx||0,h=n.sy||0;t.drawImage(i,u,h,n.sWidth,n.sHeight,r,o,a,s)}else if(n.sx&&n.sy){var c=a-(u=n.sx),p=s-(h=n.sy);t.drawImage(i,u,h,c,p,r,o,a,s)}else t.drawImage(i,r,o,a,s)}}(t,e,d)):e.getTemporalDisplayables&&(4!==n.lastDrawType&&(u=!0,n.lastDrawType=4),function(t,e,n){var i=e.getDisplayables(),r=e.getTemporalDisplayables();t.save();var o,a,s={prevElClipPaths:null,prevEl:null,allClipped:!1,viewWidth:n.viewWidth,viewHeight:n.viewHeight,inHover:n.inHover};for(o=e.getCursor(),a=i.length;o<a;o++){(h=i[o]).beforeBrush&&h.beforeBrush(),h.innerBeforeBrush(),xv(t,h,s,o===a-1),h.innerAfterBrush(),h.afterBrush&&h.afterBrush(),s.prevEl=h}for(var l=0,u=r.length;l<u;l++){var h;(h=r[l]).beforeBrush&&h.beforeBrush(),h.innerBeforeBrush(),xv(t,h,s,l===u-1),h.innerAfterBrush(),h.afterBrush&&h.afterBrush(),s.prevEl=h}e.clearTemporalDisplayables(),e.notClear=!0,t.restore()}(t,e,n)),f&&i&&yv(t,n),e.innerAfterBrush(),e.afterBrush&&e.afterBrush(),n.prevEl=e,e.__dirty=0,e.__isRendered=!0}}var bv=new Gg,wv=new Rn(100),Sv=["symbol","symbolSize","symbolKeepAspect","color","backgroundColor","dashArrayX","dashArrayY","maxTileWidth","maxTileHeight"];function Mv(t,e){if("none"===t)return null;var n=e.getDevicePixelRatio(),i=e.getZr(),r="svg"===i.painter.type;t.dirty&&bv.delete(t);var o=bv.get(t);if(o)return o;var a=D(t,{symbol:"rect",symbolSize:1,symbolKeepAspect:!0,color:"rgba(0, 0, 0, 0.2)",backgroundColor:null,dashArrayX:5,dashArrayY:5,rotation:0,maxTileWidth:512,maxTileHeight:512});"none"===a.backgroundColor&&(a.backgroundColor=null);var s={repeat:"repeat"};return function(t){for(var e,o=[n],s=!0,l=0;l<Sv.length;++l){var h=a[Sv[l]];if(null!=h&&!H(h)&&!U(h)&&!Y(h)&&"boolean"!=typeof h){s=!1;break}o.push(h)}if(s){e=o.join(",")+(r?"-svg":"");var c=wv.get(e);c&&(r?t.svgElement=c:t.image=c)}var p,f=kv(a.dashArrayX),d=function(t){if(!t||"object"==typeof t&&0===t.length)return[0,0];if(Y(t)){var e=Math.ceil(t);return[e,e]}var n=N(t,(function(t){return Math.ceil(t)}));return t.length%2?n.concat(n):n}(a.dashArrayY),g=Tv(a.symbol),v=(b=f,N(b,(function(t){return Cv(t)}))),y=Cv(d),m=!r&&u.createCanvas(),_=r&&{tag:"g",attrs:{},key:"dcl",children:[]},x=function(){for(var t=1,e=0,n=v.length;e<n;++e)t=io(t,v[e]);var i=1;for(e=0,n=g.length;e<n;++e)i=io(i,g[e].length);t*=i;var r=y*v.length*g.length;return{width:Math.max(1,Math.min(t,a.maxTileWidth)),height:Math.max(1,Math.min(r,a.maxTileHeight))}}();var b;m&&(m.width=x.width*n,m.height=x.height*n,p=m.getContext("2d"));(function(){p&&(p.clearRect(0,0,m.width,m.height),a.backgroundColor&&(p.fillStyle=a.backgroundColor,p.fillRect(0,0,m.width,m.height)));for(var t=0,e=0;e<d.length;++e)t+=d[e];if(t<=0)return;var o=-y,s=0,l=0,u=0;for(;o<x.height;){if(s%2==0){for(var h=l/2%g.length,c=0,v=0,b=0;c<2*x.width;){var w=0;for(e=0;e<f[u].length;++e)w+=f[u][e];if(w<=0)break;if(v%2==0){var S=.5*(1-a.symbolSize),M=c+f[u][v]*S,T=o+d[s]*S,k=f[u][v]*a.symbolSize,C=d[s]*a.symbolSize,D=b/2%g[h].length;I(M,T,k,C,g[h][D])}c+=f[u][v],++b,++v===f[u].length&&(v=0)}++u===f.length&&(u=0)}o+=d[s],++l,++s===d.length&&(s=0)}function I(t,e,o,s,l){var u=r?1:n,h=Qg(l,t*u,e*u,o*u,s*u,a.color,a.symbolKeepAspect);if(r){var c=i.painter.renderOneToVNode(h);c&&_.children.push(c)}else _v(p,h)}})(),s&&wv.put(e,m||_);t.image=m,t.svgElement=_,t.svgWidth=x.width,t.svgHeight=x.height}(s),s.rotation=a.rotation,s.scaleX=s.scaleY=r?1:1/n,bv.set(t,s),t.dirty=!1,s}function Tv(t){if(!t||0===t.length)return[["rect"]];if(U(t))return[[t]];for(var e=!0,n=0;n<t.length;++n)if(!U(t[n])){e=!1;break}if(e)return Tv([t]);var i=[];for(n=0;n<t.length;++n)U(t[n])?i.push([t[n]]):i.push(t[n]);return i}function kv(t){if(!t||0===t.length)return[[0,0]];if(Y(t))return[[r=Math.ceil(t),r]];for(var e=!0,n=0;n<t.length;++n)if(!Y(t[n])){e=!1;break}if(e)return kv([t]);var i=[];for(n=0;n<t.length;++n)if(Y(t[n])){var r=Math.ceil(t[n]);i.push([r,r])}else{(r=N(t[n],(function(t){return Math.ceil(t)}))).length%2==1?i.push(r.concat(r)):i.push(r)}return i}function Cv(t){for(var e=0,n=0;n<t.length;++n)e+=t[n];return t.length%2==1?2*e:e}var Dv=new Ut,Iv={};function Av(t){return Iv[t]}var Lv=2e3,Pv=4500,Ov={PROCESSOR:{FILTER:1e3,SERIES_FILTER:800,STATISTIC:5e3},VISUAL:{LAYOUT:1e3,PROGRESSIVE_LAYOUT:1100,GLOBAL:Lv,CHART:3e3,POST_CHART_LAYOUT:4600,COMPONENT:4e3,BRUSH:5e3,CHART_ITEM:Pv,ARIA:6e3,DECAL:7e3}},Rv="__flagInMainProcess",Nv="__mainProcessVersion",Bv="__pendingUpdate",Ev="__needsUpdateStatus",zv=/^[a-zA-Z0-9_]+$/,Fv="__connectUpdateStatus";function Vv(t){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];if(!this.isDisposed())return Hv(this,t,e);fy(this.id)}}function Wv(t){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return Hv(this,t,e)}}function Hv(t,e,n){return n[0]=n[0]&&n[0].toLowerCase(),Ut.prototype[e].apply(t,n)}var Gv,Uv,Xv,Yv,qv,Zv,jv,Kv,$v,Qv,Jv,ty,ey,ny,iy,ry,oy,ay,sy,ly=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e}(Ut),uy=ly.prototype;uy.on=Wv("on"),uy.off=Wv("off");var hy=function(t){function e(e,n,i){var r=t.call(this,new Rg)||this;r._chartsViews=[],r._chartsMap={},r._componentsViews=[],r._componentsMap={},r._pendingActions=[],i=i||{},r._dom=e;var o="canvas",a="auto",s=!1;r[Nv]=1,i.ssr&&Pr((function(t){var e=Ys(t),n=e.dataIndex;if(null!=n){var i=dt();return i.set("series_index",e.seriesIndex),i.set("data_index",n),e.ssrType&&i.set("ssr_type",e.ssrType),i}}));var l=r._zr=Ar(e,{renderer:i.renderer||o,devicePixelRatio:i.devicePixelRatio,width:i.width,height:i.height,ssr:i.ssr,useDirtyRect:nt(i.useDirtyRect,s),useCoarsePointer:nt(i.useCoarsePointer,a),pointerSize:i.pointerSize});r._ssr=i.ssr,r._throttledZrFlush=ng(V(l.flush,l),17),r._updateTheme(n),r._locale=function(t){if(U(t)){var e=uc[t.toUpperCase()]||{};return t===ac||t===sc?T(e):k(T(e),T(uc[lc]),!1)}return k(T(t),T(uc[lc]),!1)}(i.locale||cc),r._coordSysMgr=new Jc;var u=r._api=iy(r);function h(t,e){return t.__prio-e.__prio}return Je(_y,h),Je(yy,h),r._scheduler=new fg(r,u,yy,_y),r._messageCenter=new ly,r._initEvents(),r.resize=V(r.resize,r),l.animation.on("frame",r._onframe,r),Qv(l,r),Jv(l,r),ut(r),r}return n(e,t),e.prototype._onframe=function(){if(!this._disposed){ay(this);var t=this._scheduler;if(this[Bv]){var e=this[Bv].silent;this[Rv]=!0,sy(this);try{Gv(this),Yv.update.call(this,null,this[Bv].updateParams)}catch(t){throw this[Rv]=!1,this[Bv]=null,t}this._zr.flush(),this[Rv]=!1,this[Bv]=null,Kv.call(this,e),$v.call(this,e)}else if(t.unfinished){var n=1,i=this._model,r=this._api;t.unfinished=!1;do{var o=+new Date;t.performSeriesTasks(i),t.performDataProcessorTasks(i),Zv(this,i),t.performVisualTasks(i),ny(this,this._model,r,"remain",{}),n-=+new Date-o}while(n>0&&t.unfinished);t.unfinished||this._zr.flush()}}},e.prototype.getDom=function(){return this._dom},e.prototype.getId=function(){return this.id},e.prototype.getZr=function(){return this._zr},e.prototype.isSSR=function(){return this._ssr},e.prototype.setOption=function(t,e,n){if(!this[Rv])if(this._disposed)fy(this.id);else{var i,r,o;if(q(e)&&(n=e.lazyUpdate,i=e.silent,r=e.replaceMerge,o=e.transition,e=e.notMerge),this[Rv]=!0,sy(this),!this._model||e){var a=new cf(this._api),s=this._theme,l=this._model=new rf;l.scheduler=this._scheduler,l.ssr=this._ssr,l.init(null,null,null,s,this._locale,a)}this._model.setOption(t,{replaceMerge:r},my);var u={seriesTransition:o,optionChanged:!0};if(n)this[Bv]={silent:i,updateParams:u},this[Rv]=!1,this.getZr().wakeUp();else{try{Gv(this),Yv.update.call(this,null,u)}catch(t){throw this[Bv]=null,this[Rv]=!1,t}this._ssr||this._zr.flush(),this[Bv]=null,this[Rv]=!1,Kv.call(this,i),$v.call(this,i)}}},e.prototype.setTheme=function(t,e){if(!this[Rv])if(this._disposed)fy(this.id);else{var n=this._model;if(n){var i=e&&e.silent,r=null;this[Bv]&&(null==i&&(i=this[Bv].silent),r=this[Bv].updateParams,this[Bv]=null),this[Rv]=!0,sy(this);try{this._updateTheme(t),n.setTheme(this._theme),Gv(this),Yv.update.call(this,{type:"setTheme"},r)}catch(t){throw this[Rv]=!1,t}this[Rv]=!1,Kv.call(this,i),$v.call(this,i)}}},e.prototype._updateTheme=function(t){U(t)&&(t=xy[t]),t&&((t=T(t))&&Pf(t,!0),this._theme=t)},e.prototype.getModel=function(){return this._model},e.prototype.getOption=function(){return this._model&&this._model.getOption()},e.prototype.getWidth=function(){return this._zr.getWidth()},e.prototype.getHeight=function(){return this._zr.getHeight()},e.prototype.getDevicePixelRatio=function(){return this._zr.painter.dpr||r.hasGlobalWindow&&window.devicePixelRatio||1},e.prototype.getRenderedCanvas=function(t){return this.renderToCanvas(t)},e.prototype.renderToCanvas=function(t){t=t||{};var e=this._zr.painter;return e.getRenderedCanvas({backgroundColor:t.backgroundColor||this._model.get("backgroundColor"),pixelRatio:t.pixelRatio||this.getDevicePixelRatio()})},e.prototype.renderToSVGString=function(t){t=t||{};var e=this._zr.painter;return e.renderToString({useViewBox:t.useViewBox})},e.prototype.getSvgDataURL=function(){var t=this._zr;return R(t.storage.getDisplayList(),(function(t){t.stopAnimation(null,!0)})),t.painter.toDataURL()},e.prototype.getDataURL=function(t){if(!this._disposed){var e=(t=t||{}).excludeComponents,n=this._model,i=[],r=this;R(e,(function(t){n.eachComponent({mainType:t},(function(t){var e=r._componentsMap[t.__viewId];e.group.ignore||(i.push(e),e.group.ignore=!0)}))}));var o="svg"===this._zr.painter.getType()?this.getSvgDataURL():this.renderToCanvas(t).toDataURL("image/"+(t&&t.type||"png"));return R(i,(function(t){t.group.ignore=!1})),o}fy(this.id)},e.prototype.getConnectedDataURL=function(t){if(!this._disposed){var e="svg"===t.type,n=this.group,i=Math.min,r=Math.max,o=1/0;if(Sy[n]){var a=o,s=o,l=-1/0,h=-1/0,c=[],p=t&&t.pixelRatio||this.getDevicePixelRatio();R(wy,(function(o,u){if(o.group===n){var p=e?o.getZr().painter.getSvgDom().innerHTML:o.renderToCanvas(T(t)),f=o.getDom().getBoundingClientRect();a=i(f.left,a),s=i(f.top,s),l=r(f.right,l),h=r(f.bottom,h),c.push({dom:p,left:f.left,top:f.top})}}));var f=(l*=p)-(a*=p),d=(h*=p)-(s*=p),g=u.createCanvas(),v=Ar(g,{renderer:e?"svg":"canvas"});if(v.resize({width:f,height:d}),e){var y="";return R(c,(function(t){var e=t.left-a,n=t.top-s;y+='<g transform="translate('+e+","+n+')">'+t.dom+"</g>"})),v.painter.getSvgRoot().innerHTML=y,t.connectedBackgroundColor&&v.painter.setBackgroundColor(t.connectedBackgroundColor),v.refreshImmediately(),v.painter.toDataURL()}return t.connectedBackgroundColor&&v.add(new As({shape:{x:0,y:0,width:f,height:d},style:{fill:t.connectedBackgroundColor}})),R(c,(function(t){var e=new Ms({style:{x:t.left*p-a,y:t.top*p-s,image:t.dom}});v.add(e)})),v.refreshImmediately(),g.toDataURL("image/"+(t&&t.type||"png"))}return this.getDataURL(t)}fy(this.id)},e.prototype.convertToPixel=function(t,e,n){return qv(this,"convertToPixel",t,e,n)},e.prototype.convertToLayout=function(t,e,n){return qv(this,"convertToLayout",t,e,n)},e.prototype.convertFromPixel=function(t,e,n){return qv(this,"convertFromPixel",t,e,n)},e.prototype.containPixel=function(t,e){var n;if(!this._disposed)return R(Co(this._model,t),(function(t,i){i.indexOf("Models")>=0&&R(t,(function(t){var r=t.coordinateSystem;if(r&&r.containPoint)n=n||!!r.containPoint(e);else if("seriesModels"===i){var o=this._chartsMap[t.__viewId];o&&o.containPoint&&(n=n||o.containPoint(e,t))}else 0}),this)}),this),!!n;fy(this.id)},e.prototype.getVisual=function(t,e){var n=Co(this._model,t,{defaultMainType:"series"}),i=n.seriesModel;var r=i.getData(),o=n.hasOwnProperty("dataIndexInside")?n.dataIndexInside:n.hasOwnProperty("dataIndex")?r.indexOfRawIndex(n.dataIndex):null;return null!=o?function(t,e,n){switch(n){case"color":return t.getItemVisual(e,"style")[t.getVisual("drawType")];case"opacity":return t.getItemVisual(e,"style").opacity;case"symbol":case"symbolSize":case"liftZ":return t.getItemVisual(e,n)}}(r,o,e):function(t,e){switch(e){case"color":return t.getVisual("style")[t.getVisual("drawType")];case"opacity":return t.getVisual("style").opacity;case"symbol":case"symbolSize":case"liftZ":return t.getVisual(e)}}(r,e)},e.prototype.getViewOfComponentModel=function(t){return this._componentsMap[t.__viewId]},e.prototype.getViewOfSeriesModel=function(t){return this._chartsMap[t.__viewId]},e.prototype._initEvents=function(){var t=this;R(py,(function(e){var n=function(n){var i,r=t.getModel(),o=n.target,a="globalout"===e;if(a?i={}:o&&Vg(o,(function(t){var e=Ys(t);if(e&&null!=e.dataIndex){var n=e.dataModel||r.getSeriesByIndex(e.seriesIndex);return i=n&&n.getDataParams(e.dataIndex,e.dataType,o)||{},!0}if(e.eventData)return i=C({},e.eventData),!0}),!0),i){var s=i.componentType,l=i.componentIndex;"markLine"!==s&&"markPoint"!==s&&"markArea"!==s||(s="series",l=i.seriesIndex);var u=s&&null!=l&&r.getComponent(s,l),h=u&&t["series"===u.mainType?"_chartsMap":"_componentsMap"][u.__viewId];0,i.event=n,i.type=e,t._$eventProcessor.eventInfo={targetEl:o,packedEvent:i,model:u,view:h},t.trigger(e,i)}};n.zrEventfulCallAtLast=!0,t._zr.on(e,n,t)}));var e=this._messageCenter;R(vy,(function(n,i){e.on(i,(function(e){t.trigger(i,e)}))})),function(t,e,n){t.on("selectchanged",(function(t){var i=n.getModel();t.isFromClick?(Fg("map","selectchanged",e,i,t),Fg("pie","selectchanged",e,i,t)):"select"===t.fromAction?(Fg("map","selected",e,i,t),Fg("pie","selected",e,i,t)):"unselect"===t.fromAction&&(Fg("map","unselected",e,i,t),Fg("pie","unselected",e,i,t))}))}(e,this,this._api)},e.prototype.isDisposed=function(){return this._disposed},e.prototype.clear=function(){this._disposed?fy(this.id):this.setOption({series:[]},!0)},e.prototype.dispose=function(){if(this._disposed)fy(this.id);else{this._disposed=!0,this.getDom()&&Lo(this.getDom(),ky,"");var t=this,e=t._api,n=t._model;R(t._componentsViews,(function(t){t.dispose(n,e)})),R(t._chartsViews,(function(t){t.dispose(n,e)})),t._zr.dispose(),t._dom=t._model=t._chartsMap=t._componentsMap=t._chartsViews=t._componentsViews=t._scheduler=t._api=t._zr=t._throttledZrFlush=t._theme=t._coordSysMgr=t._messageCenter=null,delete wy[t.id]}},e.prototype.resize=function(t){if(!this[Rv])if(this._disposed)fy(this.id);else{this._zr.resize(t);var e=this._model;if(this._loadingFX&&this._loadingFX.resize(),e){var n=e.resetOption("media"),i=t&&t.silent;this[Bv]&&(null==i&&(i=this[Bv].silent),n=!0,this[Bv]=null),this[Rv]=!0,sy(this);try{n&&Gv(this),Yv.update.call(this,{type:"resize",animation:C({duration:0},t&&t.animation)})}catch(t){throw this[Rv]=!1,t}this[Rv]=!1,Kv.call(this,i),$v.call(this,i)}}},e.prototype.showLoading=function(t,e){if(this._disposed)fy(this.id);else if(q(t)&&(e=t,t=""),t=t||"default",this.hideLoading(),by[t]){var n=by[t](this._api,e),i=this._zr;this._loadingFX=n,i.add(n)}},e.prototype.hideLoading=function(){this._disposed?fy(this.id):(this._loadingFX&&this._zr.remove(this._loadingFX),this._loadingFX=null)},e.prototype.makeActionFromEvent=function(t){var e=C({},t);return e.type=gy[t.type],e},e.prototype.dispatchAction=function(t,e){if(this._disposed)fy(this.id);else if(q(e)||(e={silent:!!e}),dy[t.type]&&this._model)if(this[Rv])this._pendingActions.push(t);else{var n=e.silent;jv.call(this,t,n);var i=e.flush;i?this._zr.flush():!1!==i&&r.browser.weChat&&this._throttledZrFlush(),Kv.call(this,n),$v.call(this,n)}},e.prototype.updateLabelLayout=function(){Dv.trigger("series:layoutlabels",this._model,this._api,{updatedSeries:[]})},e.prototype.appendData=function(t){if(this._disposed)fy(this.id);else{var e=t.seriesIndex,n=this.getModel().getSeriesByIndex(e);0,n.appendData(t),this._scheduler.unfinished=!0,this.getZr().wakeUp()}},e.internalField=function(){function t(t){t.clearColorPalette(),t.eachSeries((function(t){t.clearColorPalette()}))}function e(t){for(var e=[],n=t.currentStates,i=0;i<n.length;i++){var r=n[i];"emphasis"!==r&&"blur"!==r&&"select"!==r&&e.push(r)}t.selected&&t.states.select&&e.push("select"),2===t.hoverState&&t.states.emphasis?e.push("emphasis"):1===t.hoverState&&t.states.blur&&e.push("blur"),t.useStates(e)}function i(t,e){if(!t.preventAutoZ){var n=function(t){return{z:t.get("z")||0,zlevel:t.get("zlevel")||0}}(t);e.eachRendered((function(t){return Lh(t,n.z,n.zlevel),!0}))}}function o(t,e){e.eachRendered((function(t){if(!rh(t)){var e=t.getTextContent(),n=t.getTextGuideLine();t.stateTransition&&(t.stateTransition=null),e&&e.stateTransition&&(e.stateTransition=null),n&&n.stateTransition&&(n.stateTransition=null),t.hasState()?(t.prevStates=t.currentStates,t.clearStates()):t.prevStates&&(t.prevStates=null)}}))}function a(t,n){var i=t.getModel("stateAnimation"),r=t.isAnimationEnabled(),o=i.get("duration"),a=o>0?{duration:o,delay:i.get("delay"),easing:i.get("easing")}:null;n.eachRendered((function(t){if(t.states&&t.states.emphasis){if(rh(t))return;if(t instanceof _s&&function(t){var e=js(t);e.normalFill=t.style.fill,e.normalStroke=t.style.stroke;var n=t.states.select||{};e.selectFill=n.style&&n.style.fill||null,e.selectStroke=n.style&&n.style.stroke||null}(t),t.__dirty){var n=t.prevStates;n&&t.useStates(n)}if(r){t.stateTransition=a;var i=t.getTextContent(),o=t.getTextGuideLine();i&&(i.stateTransition=a),o&&(o.stateTransition=a)}t.__dirty&&e(t)}}))}Gv=function(t){var e=t._scheduler;e.restorePipelines(t._model),e.prepareStageTasks(),Uv(t,!0),Uv(t,!1),e.plan()},Uv=function(t,e){for(var n=t._model,i=t._scheduler,r=e?t._componentsViews:t._chartsViews,o=e?t._componentsMap:t._chartsMap,a=t._zr,s=t._api,l=0;l<r.length;l++)r[l].__alive=!1;function u(t){var l=t.__requireNewView;t.__requireNewView=!1;var u="_ec_"+t.id+"_"+t.type,h=!l&&o[u];if(!h){var c=Ro(t.type),p=e?Yd.getClass(c.main,c.sub):Kd.getClass(c.sub);0,(h=new p).init(n,s),o[u]=h,r.push(h),a.add(h.group)}t.__viewId=h.__id=u,h.__alive=!0,h.__model=t,h.group.__ecComponentInfo={mainType:t.mainType,index:t.componentIndex},!e&&i.prepareView(h,t,n,s)}e?n.eachComponent((function(t,e){"series"!==t&&u(e)})):n.eachSeries(u);for(l=0;l<r.length;){var h=r[l];h.__alive?l++:(!e&&h.renderTask.dispose(),a.remove(h.group),h.dispose(n,s),r.splice(l,1),o[h.__id]===h&&delete o[h.__id],h.__id=h.group.__ecComponentInfo=null)}},Xv=function(t,e,n,i,r){var o=t._model;if(o.setUpdatePayload(n),i){var a={};a[i+"Id"]=n[i+"Id"],a[i+"Index"]=n[i+"Index"],a[i+"Name"]=n[i+"Name"];var s={mainType:i,query:a};r&&(s.subType=r);var l,u=n.excludeSeriesId;null!=u&&(l=dt(),R(po(u),(function(t){var e=bo(t,null);null!=e&&l.set(e,!0)}))),o&&o.eachComponent(s,(function(e){if(!(l&&null!=l.get(e.id)))if(Vl(n))if(e instanceof zd)n.type!==Js||n.notBlur||e.get(["emphasis","disabled"])||function(t,e,n){var i=t.seriesIndex,r=t.getData(e.dataType);if(r){var o=Mo(r,e);o=(H(o)?o[0]:o)||0;var a=r.getItemGraphicEl(o);if(!a)for(var s=r.count(),l=0;!a&&l<s;)a=r.getItemGraphicEl(l++);if(a){var u=Ys(a);Cl(i,u.focus,u.blurScope,n)}else{var h=t.get(["emphasis","focus"]),c=t.get(["emphasis","blurScope"]);null!=h&&Cl(i,h,c,n)}}}(e,n,t._api);else{var i=Il(e.mainType,e.componentIndex,n.name,t._api),r=i.focusSelf,o=i.dispatchers;n.type===Js&&r&&!n.notBlur&&Dl(e.mainType,e.componentIndex,t._api),o&&R(o,(function(t){n.type===Js?xl(t):bl(t)}))}else Fl(n)&&e instanceof zd&&(!function(t,e,n){if(Fl(e)){var i=e.dataType,r=Mo(t.getData(i),e);H(r)||(r=[r]),t[e.type===il?"toggleSelect":e.type===el?"select":"unselect"](r,i)}}(e,n,t._api),Al(e),oy(t))}),t),o&&o.eachComponent(s,(function(e){l&&null!=l.get(e.id)||h(t["series"===i?"_chartsMap":"_componentsMap"][e.__viewId])}),t)}else R([].concat(t._componentsViews).concat(t._chartsViews),h);function h(i){i&&i.__alive&&i[e]&&i[e](i.__model,o,t._api,n)}},Yv={prepareAndUpdate:function(t){Gv(this),Yv.update.call(this,t,t&&{optionChanged:null!=t.newOption})},update:function(e,n){var i=this._model,r=this._api,o=this._zr,a=this._coordSysMgr,s=this._scheduler;if(i){i.setUpdatePayload(e),s.restoreData(i,e),s.performSeriesTasks(i),a.create(i,r),s.performDataProcessorTasks(i,e),Zv(this,i),a.update(i,r),t(i),s.performVisualTasks(i,e);var l=i.get("backgroundColor")||"transparent";o.setBackgroundColor(l);var u=i.get("darkMode");null!=u&&"auto"!==u&&o.setDarkMode(u),ty(this,i,r,e,n),Dv.trigger("afterupdate",i,r)}},updateTransform:function(e){var n=this,i=this._model,r=this._api;if(i){i.setUpdatePayload(e);var o=[];i.eachComponent((function(t,a){if("series"!==t){var s=n.getViewOfComponentModel(a);if(s&&s.__alive)if(s.updateTransform){var l=s.updateTransform(a,i,r,e);l&&l.update&&o.push(s)}else o.push(s)}}));var a=dt();i.eachSeries((function(t){var o=n._chartsMap[t.__viewId];if(o.updateTransform){var s=o.updateTransform(t,i,r,e);s&&s.update&&a.set(t.uid,1)}else a.set(t.uid,1)})),t(i),this._scheduler.performVisualTasks(i,e,{setDirty:!0,dirtyMap:a}),ny(this,i,r,e,{},a),Dv.trigger("afterupdate",i,r)}},updateView:function(e){var n=this._model;n&&(n.setUpdatePayload(e),Kd.markUpdateMethod(e,"updateView"),t(n),this._scheduler.performVisualTasks(n,e,{setDirty:!0}),ty(this,n,this._api,e,{}),Dv.trigger("afterupdate",n,this._api))},updateVisual:function(e){var n=this,i=this._model;i&&(i.setUpdatePayload(e),i.eachSeries((function(t){t.getData().clearAllVisual()})),Kd.markUpdateMethod(e,"updateVisual"),t(i),this._scheduler.performVisualTasks(i,e,{visualType:"visual",setDirty:!0}),i.eachComponent((function(t,r){if("series"!==t){var o=n.getViewOfComponentModel(r);o&&o.__alive&&o.updateVisual(r,i,n._api,e)}})),i.eachSeries((function(t){n._chartsMap[t.__viewId].updateVisual(t,i,n._api,e)})),Dv.trigger("afterupdate",i,this._api))},updateLayout:function(t){Yv.update.call(this,t)}},qv=function(t,e,n,i,r){if(t._disposed)fy(t.id);else{for(var o,a=t._model,s=t._coordSysMgr.getCoordinateSystems(),l=Co(a,n),u=0;u<s.length;u++){var h=s[u];if(h[e]&&null!=(o=h[e](a,l,i,r)))return o}0}},Zv=function(t,e){var n=t._chartsMap,i=t._scheduler;e.eachSeries((function(t){i.updateStreamModes(t,n[t.__viewId])}))},jv=function(t,e){var n=this,i=this.getModel(),r=t.type,o=t.escapeConnect,a=dy[r],s=(a.update||"update").split(":"),l=s.pop(),u=null!=s[0]&&Ro(s[0]);this[Rv]=!0,sy(this);var h=[t],c=!1;t.batch&&(c=!0,h=N(t.batch,(function(e){return(e=D(C({},e),t)).batch=null,e})));var p,f=[],d=[],g=a.nonRefinedEventType,v=Fl(t),y=Vl(t);if(y&&kl(this._api),R(h,(function(e){var r=a.action(e,i,n._api);if(a.refineEvent?d.push(r):p=r,(p=p||C({},e)).type=g,f.push(p),y){var o=Do(t),s=o.queryOptionMap,h=o.mainTypeSpecified?s.keys()[0]:"series";Xv(n,l,e,h),oy(n)}else v?(Xv(n,l,e,"series"),oy(n)):u&&Xv(n,l,e,u.main,u.sub)})),"none"!==l&&!y&&!v&&!u)try{this[Bv]?(Gv(this),Yv.update.call(this,t),this[Bv]=null):Yv[l].call(this,t)}catch(t){throw this[Rv]=!1,t}if(p=c?{type:g,escapeConnect:o,batch:f}:f[0],this[Rv]=!1,!e){var m=void 0;if(a.refineEvent){var _=a.refineEvent(d,t,i,this._api).eventContent;at(q(_)),(m=D({type:a.refinedEventType},_)).fromAction=t.type,m.fromActionPayload=t,m.escapeConnect=!0}var x=this._messageCenter;x.trigger(p.type,p),m&&x.trigger(m.type,m)}},Kv=function(t){for(var e=this._pendingActions;e.length;){var n=e.shift();jv.call(this,n,t)}},$v=function(t){!t&&this.trigger("updated")},Qv=function(t,e){t.on("rendered",(function(n){e.trigger("rendered",n),!t.animation.isFinished()||e[Bv]||e._scheduler.unfinished||e._pendingActions.length||e.trigger("finished")}))},Jv=function(t,e){t.on("mouseover",(function(t){var n=Vg(t.target,zl);n&&(!function(t,e,n){var i=Ys(t),r=Il(i.componentMainType,i.componentIndex,i.componentHighDownName,n),o=r.dispatchers,a=r.focusSelf;o?(a&&Dl(i.componentMainType,i.componentIndex,n),R(o,(function(t){return ml(t,e)}))):(Cl(i.seriesIndex,i.focus,i.blurScope,n),"self"===i.focus&&Dl(i.componentMainType,i.componentIndex,n),ml(t,e))}(n,t,e._api),oy(e))})).on("mouseout",(function(t){var n=Vg(t.target,zl);n&&(!function(t,e,n){kl(n);var i=Ys(t),r=Il(i.componentMainType,i.componentIndex,i.componentHighDownName,n).dispatchers;r?R(r,(function(t){return _l(t,e)})):_l(t,e)}(n,t,e._api),oy(e))})).on("click",(function(t){var n=Vg(t.target,(function(t){return null!=Ys(t).dataIndex}),!0);if(n){var i=n.selected?"unselect":"select",r=Ys(n);e._api.dispatchAction({type:i,dataType:r.dataType,dataIndexInside:r.dataIndex,seriesIndex:r.seriesIndex,isFromClick:!0})}}))},ty=function(t,e,n,i,r){!function(t){var e=[],n=[],i=!1;if(t.eachComponent((function(t,r){var o=r.get("zlevel")||0,a=r.get("z")||0,s=r.getZLevelKey();i=i||!!s,("series"===t?n:e).push({zlevel:o,z:a,idx:r.componentIndex,type:t,key:s})})),i){var r,o,a=e.concat(n);Je(a,(function(t,e){return t.zlevel===e.zlevel?t.z-e.z:t.zlevel-e.zlevel})),R(a,(function(e){var n=t.getComponent(e.type,e.idx),i=e.zlevel,a=e.key;null!=r&&(i=Math.max(r,i)),a?(i===r&&a!==o&&i++,o=a):o&&(i===r&&i++,o=""),r=i,n.setZLevel(i)}))}}(e),ey(t,e,n,i,r),R(t._chartsViews,(function(t){t.__alive=!1})),ny(t,e,n,i,r),R(t._chartsViews,(function(t){t.__alive||t.remove(e,n)}))},ey=function(t,e,n,r,s,l){R(l||t._componentsViews,(function(t){var s=t.__model;o(s,t),t.render(s,e,n,r),i(s,t),a(s,t)}))},ny=function(t,e,n,s,l,u){var h=t._scheduler;l=C(l||{},{updatedSeries:e.getSeries()}),Dv.trigger("series:beforeupdate",e,n,l);var c=!1;e.eachSeries((function(e){var n=t._chartsMap[e.__viewId];n.__alive=!0;var i=n.renderTask;h.updatePayload(i,s),o(e,n),u&&u.get(e.uid)&&i.dirty(),i.perform(h.getPerformArgs(i))&&(c=!0),n.group.silent=!!e.get("silent"),function(t,e){var n=t.get("blendMode")||null;e.eachRendered((function(t){t.isGroup||(t.style.blend=n)}))}(e,n),Al(e)})),h.unfinished=c||h.unfinished,Dv.trigger("series:layoutlabels",e,n,l),Dv.trigger("series:transition",e,n,l),e.eachSeries((function(e){var n=t._chartsMap[e.__viewId];i(e,n),a(e,n)})),function(t,e){var n=t._zr,i=n.storage,o=0;i.traverse((function(t){t.isGroup||o++})),o>e.get("hoverLayerThreshold")&&!r.node&&!r.worker&&e.eachSeries((function(e){if(!e.preventUsingHoverLayer){var n=t._chartsMap[e.__viewId];n.__alive&&n.eachRendered((function(t){t.states.emphasis&&(t.states.emphasis.hoverLayer=!0)}))}}))}(t,e),Dv.trigger("series:afterupdate",e,n,l)},oy=function(t){t[Ev]=!0,t.getZr().wakeUp()},sy=function(t){t[Nv]=(t[Nv]+1)%1e3},ay=function(t){t[Ev]&&(t.getZr().storage.traverse((function(t){rh(t)||e(t)})),t[Ev]=!1)},iy=function(t){return new(function(e){function i(){return null!==e&&e.apply(this,arguments)||this}return n(i,e),i.prototype.getCoordinateSystems=function(){return t._coordSysMgr.getCoordinateSystems()},i.prototype.getComponentByElement=function(e){for(;e;){var n=e.__ecComponentInfo;if(null!=n)return t._model.getComponent(n.mainType,n.index);e=e.parent}},i.prototype.enterEmphasis=function(e,n){xl(e,n),oy(t)},i.prototype.leaveEmphasis=function(e,n){bl(e,n),oy(t)},i.prototype.enterBlur=function(e){!function(t){dl(t,ul)}(e),oy(t)},i.prototype.leaveBlur=function(e){wl(e),oy(t)},i.prototype.enterSelect=function(e){Sl(e),oy(t)},i.prototype.leaveSelect=function(e){Ml(e),oy(t)},i.prototype.getModel=function(){return t.getModel()},i.prototype.getViewOfComponentModel=function(e){return t.getViewOfComponentModel(e)},i.prototype.getViewOfSeriesModel=function(e){return t.getViewOfSeriesModel(e)},i.prototype.getMainProcessVersion=function(){return t[Nv]},i}(uf))(t)},ry=function(t){function e(t,e){for(var n=0;n<t.length;n++){t[n][Fv]=e}}R(gy,(function(n,i){t._messageCenter.on(i,(function(n){if(Sy[t.group]&&0!==t[Fv]){if(n&&n.escapeConnect)return;var i=t.makeActionFromEvent(n),r=[];R(wy,(function(e){e!==t&&e.group===t.group&&r.push(e)})),e(r,0),R(r,(function(t){1!==t[Fv]&&t.dispatchAction(i)})),e(r,2)}}))}))}}(),e}(Ut),cy=hy.prototype;cy.on=Vv("on"),cy.off=Vv("off"),cy.one=function(t,e,n){var i=this;lo(),this.on.call(this,t,(function n(){for(var r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];e&&e.apply&&e.apply(this,r),i.off(t,n)}),n)};var py=["click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","globalout","contextmenu"];function fy(t){0}var dy={},gy={},vy={},yy=[],my=[],_y=[],xy={},by={},wy={},Sy={},My=+new Date-0,Ty=+new Date-0,ky="_echarts_instance_";function Cy(t){Sy[t]=!1}var Dy=Cy;function Iy(t){return wy[function(t,e){return t.getAttribute?t.getAttribute(e):t[e]}(t,ky)]}function Ay(t,e){xy[t]=e}function Ly(t){A(my,t)<0&&my.push(t)}function Py(t,e){Wy(yy,t,e,2e3)}function Oy(t){Ny("afterinit",t)}function Ry(t){Ny("afterupdate",t)}function Ny(t,e){Dv.on(t,e)}function By(t,e,n){var i,r,o,a,s;function l(t){return t.toLowerCase()}G(e)&&(n=e,e=""),q(t)?(i=t.type,r=t.event,a=t.update,s=t.publishNonRefinedEvent,n||(n=t.action),o=t.refineEvent):(i=t,r=e),r=l(r||i);var u=o?l(i):r;dy[i]||(at(zv.test(i)&&zv.test(r)),o&&at(r!==i),dy[i]={actionType:i,refinedEventType:r,nonRefinedEventType:u,update:a,action:n,refineEvent:o},vy[r]=1,o&&s&&(vy[u]=1),gy[u]=i)}function Ey(t,e){Jc.register(t,e)}function zy(t,e){Wy(_y,t,e,1e3,"layout")}function Fy(t,e){Wy(_y,t,e,3e3,"visual")}var Vy=[];function Wy(t,e,n,i,r){if((G(e)||q(e))&&(n=e,e=i),!(A(Vy,n)>=0)){Vy.push(n);var o=fg.wrapStageHandler(n,r);o.__prio=e,o.__raw=n,t.push(o)}}function Hy(t,e){by[t]=e}function Gy(t,e,n){var i=Av("registerMap");i&&i(t,e,n)}var Uy=function(t){var e=(t=T(t)).type,n="";e||uo(n);var i=e.split(":");2!==i.length&&uo(n);var r=!1;"echarts"===i[0]&&(e=i[1],r=!0),t.__isBuiltIn=r,yd.set(e,t)};function Xy(t,e,n,i){return{eventContent:{selected:Ll(n),isFromClick:e.isFromClick||!1}}}Fy(Lv,lg),Fy(Pv,hg),Fy(Pv,cg),Fy(Lv,Eg),Fy(Pv,zg),Fy(7e3,(function(t,e){t.eachRawSeries((function(n){if(!t.isSeriesFiltered(n)){var i=n.getData();i.hasItemVisual()&&i.each((function(t){var n=i.getItemVisual(t,"decal");n&&(i.ensureUniqueItemVisual(t,"style").decal=Mv(n,e))}));var r=i.getVisual("decal");if(r)i.getVisual("style").decal=Mv(r,e)}}))})),Ly(Pf),Py(900,(function(t){var e=dt();t.eachSeries((function(t){var n=t.get("stack");if(n){var i=e.get(n)||e.set(n,[]),r=t.getData(),o={stackResultDimension:r.getCalculationInfo("stackResultDimension"),stackedOverDimension:r.getCalculationInfo("stackedOverDimension"),stackedDimension:r.getCalculationInfo("stackedDimension"),stackedByDimension:r.getCalculationInfo("stackedByDimension"),isStackedByIndex:r.getCalculationInfo("isStackedByIndex"),data:r,seriesModel:t};if(!o.stackedDimension||!o.isStackedByIndex&&!o.stackedByDimension)return;i.push(o)}})),e.each((function(t){0!==t.length&&("seriesDesc"===(t[0].seriesModel.get("stackOrder")||"seriesAsc")&&t.reverse(),R(t,(function(e,n){e.data.setCalculationInfo("stackedOnSeries",n>0?t[n-1].seriesModel:null)})),function(t){R(t,(function(e,n){var i=[],r=[NaN,NaN],o=[e.stackResultDimension,e.stackedOverDimension],a=e.data,s=e.isStackedByIndex,l=e.seriesModel.get("stackStrategy")||"samesign";a.modify(o,(function(o,u,h){var c,p,f=a.get(e.stackedDimension,h);if(isNaN(f))return r;s?p=a.getRawIndex(h):c=a.get(e.stackedByDimension,h);for(var d=NaN,g=n-1;g>=0;g--){var v=t[g];if(s||(p=v.data.rawIndexOf(v.stackedByDimension,c)),p>=0){var y=v.data.getByRawIndex(v.stackResultDimension,p);if("all"===l||"positive"===l&&y>0||"negative"===l&&y<0||"samesign"===l&&f>=0&&y>0||"samesign"===l&&f<=0&&y<0){f=Yr(f,y),d=y;break}}}return i[0]=f,i[1]=d,i}))}))}(t))}))})),Hy("default",(function(t,e){D(e=e||{},{text:"loading",textColor:xp.color.primary,fontSize:12,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif",maskColor:"rgba(255,255,255,0.8)",showSpinner:!0,color:xp.color.theme[0],spinnerRadius:10,lineWidth:5,zlevel:0});var n=new Tr,i=new As({style:{fill:e.maskColor},zlevel:e.zlevel,z:1e4});n.add(i);var r,o=new Rs({style:{text:e.text,fill:e.textColor,fontSize:e.fontSize,fontWeight:e.fontWeight,fontStyle:e.fontStyle,fontFamily:e.fontFamily},zlevel:e.zlevel,z:10001}),a=new As({style:{fill:"none"},textContent:o,textConfig:{position:"right",distance:10},zlevel:e.zlevel,z:10001});return n.add(a),e.showSpinner&&((r=new zu({shape:{startAngle:-pg/2,endAngle:-pg/2+.1,r:e.spinnerRadius},style:{stroke:e.color,lineCap:"round",lineWidth:e.lineWidth},zlevel:e.zlevel,z:10001})).animateShape(!0).when(1e3,{endAngle:3*pg/2}).start("circularInOut"),r.animateShape(!0).when(1e3,{startAngle:3*pg/2}).delay(300).start("circularInOut"),n.add(r)),n.resize=function(){var n=o.getBoundingRect().width,s=e.showSpinner?e.spinnerRadius:0,l=(t.getWidth()-2*s-(e.showSpinner&&n?10:0)-n)/2-(e.showSpinner&&n?0:5+n/2)+(e.showSpinner?0:n/2)+(n?0:s),u=t.getHeight()/2;e.showSpinner&&r.setShape({cx:l,cy:u}),a.setShape({x:l-s,y:u-s,width:2*s,height:2*s}),i.setShape({x:0,y:0,width:t.getWidth(),height:t.getHeight()})},n.resize(),n})),By({type:Js,event:Js,update:Js},_t),By({type:tl,event:tl,update:tl},_t),By({type:el,event:rl,update:el,action:_t,refineEvent:Xy,publishNonRefinedEvent:!0}),By({type:nl,event:rl,update:nl,action:_t,refineEvent:Xy,publishNonRefinedEvent:!0}),By({type:il,event:rl,update:il,action:_t,refineEvent:Xy,publishNonRefinedEvent:!0}),Ay("default",{}),Ay("dark",Og);var Yy=[],qy={registerPreprocessor:Ly,registerProcessor:Py,registerPostInit:Oy,registerPostUpdate:Ry,registerUpdateLifecycle:Ny,registerAction:By,registerCoordinateSystem:Ey,registerLayout:zy,registerVisual:Fy,registerTransform:Uy,registerLoading:Hy,registerMap:Gy,registerImpl:function(t,e){Iv[t]=e},PRIORITY:Ov,ComponentModel:_p,ComponentView:Yd,SeriesModel:zd,ChartView:Kd,registerComponentModel:function(t){_p.registerClass(t)},registerComponentView:function(t){Yd.registerClass(t)},registerSeriesModel:function(t){zd.registerClass(t)},registerChartView:function(t){Kd.registerClass(t)},registerCustomSeries:function(t,e){},registerSubTypeDefaulter:function(t,e){_p.registerSubTypeDefaulter(t,e)},registerPainter:function(t,e){Lr(t,e)}};function Zy(t){H(t)?R(t,(function(t){Zy(t)})):A(Yy,t)>=0||(Yy.push(t),G(t)&&(t={install:t}),t.install(qy))}function jy(t){return null==t?0:t.length||1}function Ky(t){return t}var $y=function(){function t(t,e,n,i,r,o){this._old=t,this._new=e,this._oldKeyGetter=n||Ky,this._newKeyGetter=i||Ky,this.context=r,this._diffModeMultiple="multiple"===o}return t.prototype.add=function(t){return this._add=t,this},t.prototype.update=function(t){return this._update=t,this},t.prototype.updateManyToOne=function(t){return this._updateManyToOne=t,this},t.prototype.updateOneToMany=function(t){return this._updateOneToMany=t,this},t.prototype.updateManyToMany=function(t){return this._updateManyToMany=t,this},t.prototype.remove=function(t){return this._remove=t,this},t.prototype.execute=function(){this[this._diffModeMultiple?"_executeMultiple":"_executeOneToOne"]()},t.prototype._executeOneToOne=function(){var t=this._old,e=this._new,n={},i=new Array(t.length),r=new Array(e.length);this._initIndexMap(t,null,i,"_oldKeyGetter"),this._initIndexMap(e,n,r,"_newKeyGetter");for(var o=0;o<t.length;o++){var a=i[o],s=n[a],l=jy(s);if(l>1){var u=s.shift();1===s.length&&(n[a]=s[0]),this._update&&this._update(u,o)}else 1===l?(n[a]=null,this._update&&this._update(s,o)):this._remove&&this._remove(o)}this._performRestAdd(r,n)},t.prototype._executeMultiple=function(){var t=this._old,e=this._new,n={},i={},r=[],o=[];this._initIndexMap(t,n,r,"_oldKeyGetter"),this._initIndexMap(e,i,o,"_newKeyGetter");for(var a=0;a<r.length;a++){var s=r[a],l=n[s],u=i[s],h=jy(l),c=jy(u);if(h>1&&1===c)this._updateManyToOne&&this._updateManyToOne(u,l),i[s]=null;else if(1===h&&c>1)this._updateOneToMany&&this._updateOneToMany(u,l),i[s]=null;else if(1===h&&1===c)this._update&&this._update(u,l),i[s]=null;else if(h>1&&c>1)this._updateManyToMany&&this._updateManyToMany(u,l),i[s]=null;else if(h>1)for(var p=0;p<h;p++)this._remove&&this._remove(l[p]);else this._remove&&this._remove(l)}this._performRestAdd(o,i)},t.prototype._performRestAdd=function(t,e){for(var n=0;n<t.length;n++){var i=t[n],r=e[i],o=jy(r);if(o>1)for(var a=0;a<o;a++)this._add&&this._add(r[a]);else 1===o&&this._add&&this._add(r);e[i]=null}},t.prototype._initIndexMap=function(t,e,n,i){for(var r=this._diffModeMultiple,o=0;o<t.length;o++){var a="_ec_"+this[i](t[o],o);if(r||(n[o]=a),e){var s=e[a],l=jy(s);0===l?(e[a]=o,r&&n.push(a)):1===l?e[a]=[s,o]:s.push(o)}}},t}(),Qy=function(){function t(t,e){this._encode=t,this._schema=e}return t.prototype.get=function(){return{fullDimensions:this._getFullDimensionNames(),encode:this._encode}},t.prototype._getFullDimensionNames=function(){return this._cachedDimNames||(this._cachedDimNames=this._schema?this._schema.makeOutputDimensionNames():[]),this._cachedDimNames},t}();function Jy(t,e){return t.hasOwnProperty(e)||(t[e]=[]),t[e]}var tm=function(t){this.otherDims={},null!=t&&C(this,t)},em=To(),nm={float:"f",int:"i",ordinal:"o",number:"n",time:"t"},im=function(){function t(t){this.dimensions=t.dimensions,this._dimOmitted=t.dimensionOmitted,this.source=t.source,this._fullDimCount=t.fullDimensionCount,this._updateDimOmitted(t.dimensionOmitted)}return t.prototype.isDimensionOmitted=function(){return this._dimOmitted},t.prototype._updateDimOmitted=function(t){this._dimOmitted=t,t&&(this._dimNameMap||(this._dimNameMap=am(this.source)))},t.prototype.getSourceDimensionIndex=function(t){return nt(this._dimNameMap.get(t),-1)},t.prototype.getSourceDimension=function(t){var e=this.source.dimensionsDefine;if(e)return e[t]},t.prototype.makeStoreSchema=function(){for(var t=this._fullDimCount,e=Yf(this.source),n=!sm(t),i="",r=[],o=0,a=0;o<t;o++){var s=void 0,l=void 0,u=void 0,h=this.dimensions[a];if(h&&h.storeDimIndex===o)s=e?h.name:null,l=h.type,u=h.ordinalMeta,a++;else{var c=this.getSourceDimension(o);c&&(s=e?c.name:null,l=c.type)}r.push({property:s,type:l,ordinalMeta:u}),!e||null==s||h&&h.isCalculationCoord||(i+=n?s.replace(/\`/g,"`1").replace(/\$/g,"`2"):s),i+="$",i+=nm[l]||"f",u&&(i+=u.uid),i+="$"}var p=this.source;return{dimensions:r,hash:[p.seriesLayoutBy,p.startIndex,i].join("$$")}},t.prototype.makeOutputDimensionNames=function(){for(var t=[],e=0,n=0;e<this._fullDimCount;e++){var i=void 0,r=this.dimensions[n];if(r&&r.storeDimIndex===e)r.isCalculationCoord||(i=r.name),n++;else{var o=this.getSourceDimension(e);o&&(i=o.name)}t.push(i)}return t},t.prototype.appendCalculationDimension=function(t){this.dimensions.push(t),t.isCalculationCoord=!0,this._fullDimCount++,this._updateDimOmitted(!0)},t}();function rm(t){return t instanceof im}function om(t){for(var e=dt(),n=0;n<(t||[]).length;n++){var i=t[n],r=q(i)?i.name:i;null!=r&&null==e.get(r)&&e.set(r,n)}return e}function am(t){var e=em(t);return e.dimNameMap||(e.dimNameMap=om(t.dimensionsDefine))}function sm(t){return t>30}var lm,um,hm,cm,pm,fm,dm,gm=q,vm=N,ym="undefined"==typeof Int32Array?Array:Int32Array,mm=["hasItemOption","_nameList","_idList","_invertedIndicesMap","_dimSummary","userOutput","_rawData","_dimValueGetter","_nameDimIdx","_idDimIdx","_nameRepeatCount"],_m=["_approximateExtent"],xm=function(){function t(t,e){var n;this.type="list",this._dimOmitted=!1,this._nameList=[],this._idList=[],this._visual={},this._layout={},this._itemVisuals=[],this._itemLayouts=[],this._graphicEls=[],this._approximateExtent={},this._calculationInfo={},this.hasItemOption=!1,this.TRANSFERABLE_METHODS=["cloneShallow","downSample","minmaxDownSample","lttbDownSample","map"],this.CHANGABLE_METHODS=["filterSelf","selectRange"],this.DOWNSAMPLE_METHODS=["downSample","minmaxDownSample","lttbDownSample"];var i=!1;rm(t)?(n=t.dimensions,this._dimOmitted=t.isDimensionOmitted(),this._schema=t):(i=!0,n=t),n=n||["x","y"];for(var r={},o=[],a={},s=!1,l={},u=0;u<n.length;u++){var h=n[u],c=U(h)?new tm({name:h}):h instanceof tm?h:new tm(h),p=c.name;c.type=c.type||"float",c.coordDim||(c.coordDim=p,c.coordDimIndex=0);var f=c.otherDims=c.otherDims||{};o.push(p),r[p]=c,null!=l[p]&&(s=!0),c.createInvertedIndices&&(a[p]=[]);var d=u;Y(c.storeDimIndex)&&(d=c.storeDimIndex),0===f.itemName&&(this._nameDimIdx=d),0===f.itemId&&(this._idDimIdx=d),i&&(c.storeDimIndex=u)}if(this.dimensions=o,this._dimInfos=r,this._initGetDimensionInfo(s),this.hostModel=e,this._invertedIndicesMap=a,this._dimOmitted){var g=this._dimIdxToName=dt();R(o,(function(t){g.set(r[t].storeDimIndex,t)}))}}return t.prototype.getDimension=function(t){var e=this._recognizeDimIndex(t);if(null==e)return t;if(e=t,!this._dimOmitted)return this.dimensions[e];var n=this._dimIdxToName.get(e);if(null!=n)return n;var i=this._schema.getSourceDimension(e);return i?i.name:void 0},t.prototype.getDimensionIndex=function(t){var e=this._recognizeDimIndex(t);if(null!=e)return e;if(null==t)return-1;var n=this._getDimInfo(t);return n?n.storeDimIndex:this._dimOmitted?this._schema.getSourceDimensionIndex(t):-1},t.prototype._recognizeDimIndex=function(t){if(Y(t)||null!=t&&!isNaN(t)&&!this._getDimInfo(t)&&(!this._dimOmitted||this._schema.getSourceDimensionIndex(t)<0))return+t},t.prototype._getStoreDimIndex=function(t){var e=this.getDimensionIndex(t);return e},t.prototype.getDimensionInfo=function(t){return this._getDimInfo(this.getDimension(t))},t.prototype._initGetDimensionInfo=function(t){var e=this._dimInfos;this._getDimInfo=t?function(t){return e.hasOwnProperty(t)?e[t]:void 0}:function(t){return e[t]}},t.prototype.getDimensionsOnCoord=function(){return this._dimSummary.dataDimsOnCoord.slice()},t.prototype.mapDimension=function(t,e){var n=this._dimSummary;if(null==e)return n.encodeFirstDimNotExtra[t];var i=n.encode[t];return i?i[e]:null},t.prototype.mapDimensionsAll=function(t){return(this._dimSummary.encode[t]||[]).slice()},t.prototype.getStore=function(){return this._store},t.prototype.initData=function(t,e,n){var i,r=this;if(t instanceof Id&&(i=t),!i){var o=this.dimensions,a=Vf(t)||O(t)?new qf(t,o.length):t;i=new Id;var s=vm(o,(function(t){return{type:r._dimInfos[t].type,property:t}}));i.initData(a,s,n)}this._store=i,this._nameList=(e||[]).slice(),this._idList=[],this._nameRepeatCount={},this._doInit(0,i.count()),this._dimSummary=function(t,e){var n={},i=n.encode={},r=dt(),o=[],a=[],s={};R(t.dimensions,(function(e){var n,l=t.getDimensionInfo(e),u=l.coordDim;if(u){var h=l.coordDimIndex;Jy(i,u)[h]=e,l.isExtraCoord||(r.set(u,1),"ordinal"!==(n=l.type)&&"time"!==n&&(o[0]=e),Jy(s,u)[h]=t.getDimensionIndex(l.name)),l.defaultTooltip&&a.push(e)}Ip.each((function(t,e){var n=Jy(i,e),r=l.otherDims[e];null!=r&&!1!==r&&(n[r]=l.name)}))}));var l=[],u={};r.each((function(t,e){var n=i[e];u[e]=n[0],l=l.concat(n)})),n.dataDimsOnCoord=l,n.dataDimIndicesOnCoord=N(l,(function(e){return t.getDimensionInfo(e).storeDimIndex})),n.encodeFirstDimNotExtra=u;var h=i.label;h&&h.length&&(o=h.slice());var c=i.tooltip;return c&&c.length?a=c.slice():a.length||(a=o.slice()),i.defaultedLabel=o,i.defaultedTooltip=a,n.userOutput=new Qy(s,e),n}(this,this._schema),this.userOutput=this._dimSummary.userOutput},t.prototype.appendData=function(t){var e=this._store.appendData(t);this._doInit(e[0],e[1])},t.prototype.appendValues=function(t,e){var n=this._store.appendValues(t,e&&e.length),i=n.start,r=n.end,o=this._shouldMakeIdFromName();if(this._updateOrdinalMeta(),e)for(var a=i;a<r;a++){var s=a-i;this._nameList[a]=e[s],o&&dm(this,a)}},t.prototype._updateOrdinalMeta=function(){for(var t=this._store,e=this.dimensions,n=0;n<e.length;n++){var i=this._dimInfos[e[n]];i.ordinalMeta&&t.collectOrdinalMeta(i.storeDimIndex,i.ordinalMeta)}},t.prototype._shouldMakeIdFromName=function(){var t=this._store.getProvider();return null==this._idDimIdx&&t.getSource().sourceFormat!==Rp&&!t.fillStorage},t.prototype._doInit=function(t,e){if(!(t>=e)){var n=this._store.getProvider();this._updateOrdinalMeta();var i=this._nameList,r=this._idList;if(n.getSource().sourceFormat===Ap&&!n.pure)for(var o=[],a=t;a<e;a++){var s=n.getItem(a,o);if(!this.hasItemOption&&yo(s)&&(this.hasItemOption=!0),s){var l=s.name;null==i[a]&&null!=l&&(i[a]=bo(l,null));var u=s.id;null==r[a]&&null!=u&&(r[a]=bo(u,null))}}if(this._shouldMakeIdFromName())for(a=t;a<e;a++)dm(this,a);lm(this)}},t.prototype.getApproximateExtent=function(t){return this._approximateExtent[t]||this._store.getDataExtent(this._getStoreDimIndex(t))},t.prototype.setApproximateExtent=function(t,e){e=this.getDimension(e),this._approximateExtent[e]=t.slice()},t.prototype.getCalculationInfo=function(t){return this._calculationInfo[t]},t.prototype.setCalculationInfo=function(t,e){gm(t)?C(this._calculationInfo,t):this._calculationInfo[t]=e},t.prototype.getName=function(t){var e=this.getRawIndex(t),n=this._nameList[e];return null==n&&null!=this._nameDimIdx&&(n=hm(this,this._nameDimIdx,e)),null==n&&(n=""),n},t.prototype._getCategory=function(t,e){var n=this._store.get(t,e),i=this._store.getOrdinalMeta(t);return i?i.categories[n]:n},t.prototype.getId=function(t){return um(this,this.getRawIndex(t))},t.prototype.count=function(){return this._store.count()},t.prototype.get=function(t,e){var n=this._store,i=this._dimInfos[t];if(i)return n.get(i.storeDimIndex,e)},t.prototype.getByRawIndex=function(t,e){var n=this._store,i=this._dimInfos[t];if(i)return n.getByRawIndex(i.storeDimIndex,e)},t.prototype.getIndices=function(){return this._store.getIndices()},t.prototype.getDataExtent=function(t){return this._store.getDataExtent(this._getStoreDimIndex(t))},t.prototype.getSum=function(t){return this._store.getSum(this._getStoreDimIndex(t))},t.prototype.getMedian=function(t){return this._store.getMedian(this._getStoreDimIndex(t))},t.prototype.getValues=function(t,e){var n=this,i=this._store;return H(t)?i.getValues(vm(t,(function(t){return n._getStoreDimIndex(t)})),e):i.getValues(t)},t.prototype.hasValue=function(t){for(var e=this._dimSummary.dataDimIndicesOnCoord,n=0,i=e.length;n<i;n++)if(isNaN(this._store.get(e[n],t)))return!1;return!0},t.prototype.indexOfName=function(t){for(var e=0,n=this._store.count();e<n;e++)if(this.getName(e)===t)return e;return-1},t.prototype.getRawIndex=function(t){return this._store.getRawIndex(t)},t.prototype.indexOfRawIndex=function(t){return this._store.indexOfRawIndex(t)},t.prototype.rawIndexOf=function(t,e){var n=t&&this._invertedIndicesMap[t];var i=n&&n[e];return null==i||isNaN(i)?-1:i},t.prototype.each=function(t,e,n){G(t)&&(n=e,e=t,t=[]);var i=n||this,r=vm(cm(t),this._getStoreDimIndex,this);this._store.each(r,i?V(e,i):e)},t.prototype.filterSelf=function(t,e,n){G(t)&&(n=e,e=t,t=[]);var i=n||this,r=vm(cm(t),this._getStoreDimIndex,this);return this._store=this._store.filter(r,i?V(e,i):e),this},t.prototype.selectRange=function(t){var e=this,n={};return R(F(t),(function(i){var r=e._getStoreDimIndex(i);n[r]=t[i]})),this._store=this._store.selectRange(n),this},t.prototype.mapArray=function(t,e,n){G(t)&&(n=e,e=t,t=[]),n=n||this;var i=[];return this.each(t,(function(){i.push(e&&e.apply(this,arguments))}),n),i},t.prototype.map=function(t,e,n,i){var r=n||i||this,o=vm(cm(t),this._getStoreDimIndex,this),a=fm(this);return a._store=this._store.map(o,r?V(e,r):e),a},t.prototype.modify=function(t,e,n,i){var r=n||i||this;var o=vm(cm(t),this._getStoreDimIndex,this);this._store.modify(o,r?V(e,r):e)},t.prototype.downSample=function(t,e,n,i){var r=fm(this);return r._store=this._store.downSample(this._getStoreDimIndex(t),e,n,i),r},t.prototype.minmaxDownSample=function(t,e){var n=fm(this);return n._store=this._store.minmaxDownSample(this._getStoreDimIndex(t),e),n},t.prototype.lttbDownSample=function(t,e){var n=fm(this);return n._store=this._store.lttbDownSample(this._getStoreDimIndex(t),e),n},t.prototype.getRawDataItem=function(t){return this._store.getRawDataItem(t)},t.prototype.getItemModel=function(t){var e=this.hostModel,n=this.getRawDataItem(t);return new ic(n,e,e&&e.ecModel)},t.prototype.diff=function(t){var e=this;return new $y(t?t.getStore().getIndices():[],this.getStore().getIndices(),(function(e){return um(t,e)}),(function(t){return um(e,t)}))},t.prototype.getVisual=function(t){var e=this._visual;return e&&e[t]},t.prototype.setVisual=function(t,e){this._visual=this._visual||{},gm(t)?C(this._visual,t):this._visual[t]=e},t.prototype.getItemVisual=function(t,e){var n=this._itemVisuals[t],i=n&&n[e];return null==i?this.getVisual(e):i},t.prototype.hasItemVisual=function(){return this._itemVisuals.length>0},t.prototype.ensureUniqueItemVisual=function(t,e){var n=this._itemVisuals,i=n[t];i||(i=n[t]={});var r=i[e];return null==r&&(H(r=this.getVisual(e))?r=r.slice():gm(r)&&(r=C({},r)),i[e]=r),r},t.prototype.setItemVisual=function(t,e,n){var i=this._itemVisuals[t]||{};this._itemVisuals[t]=i,gm(e)?C(i,e):i[e]=n},t.prototype.clearAllVisual=function(){this._visual={},this._itemVisuals=[]},t.prototype.setLayout=function(t,e){gm(t)?C(this._layout,t):this._layout[t]=e},t.prototype.getLayout=function(t){return this._layout[t]},t.prototype.getItemLayout=function(t){return this._itemLayouts[t]},t.prototype.setItemLayout=function(t,e,n){this._itemLayouts[t]=n?C(this._itemLayouts[t]||{},e):e},t.prototype.clearItemLayouts=function(){this._itemLayouts.length=0},t.prototype.setItemGraphicEl=function(t,e){!function(t,e,n,i){if(i){var r=Ys(i);r.dataIndex=n,r.dataType=e,r.seriesIndex=t,r.ssrType="chart","group"===i.type&&i.traverse((function(i){var r=Ys(i);r.seriesIndex=t,r.dataIndex=n,r.dataType=e,r.ssrType="chart"}))}}(this.hostModel&&this.hostModel.seriesIndex,this.dataType,t,e),this._graphicEls[t]=e},t.prototype.getItemGraphicEl=function(t){return this._graphicEls[t]},t.prototype.eachItemGraphicEl=function(t,e){R(this._graphicEls,(function(n,i){n&&t&&t.call(e,n,i)}))},t.prototype.cloneShallow=function(e){return e||(e=new t(this._schema?this._schema:vm(this.dimensions,this._getDimInfo,this),this.hostModel)),pm(e,this),e._store=this._store,e},t.prototype.wrapMethod=function(t,e){var n=this[t];G(n)&&(this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(t),this[t]=function(){var t=n.apply(this,arguments);return e.apply(this,[t].concat(rt(arguments)))})},t.internalField=(lm=function(t){var e=t._invertedIndicesMap;R(e,(function(n,i){var r=t._dimInfos[i],o=r.ordinalMeta,a=t._store;if(o){n=e[i]=new ym(o.categories.length);for(var s=0;s<n.length;s++)n[s]=-1;for(s=0;s<a.count();s++)n[a.get(r.storeDimIndex,s)]=s}}))},hm=function(t,e,n){return bo(t._getCategory(e,n),null)},um=function(t,e){var n=t._idList[e];return null==n&&null!=t._idDimIdx&&(n=hm(t,t._idDimIdx,e)),null==n&&(n="e\0\0"+e),n},cm=function(t){return H(t)||(t=null!=t?[t]:[]),t},fm=function(e){var n=new t(e._schema?e._schema:vm(e.dimensions,e._getDimInfo,e),e.hostModel);return pm(n,e),n},pm=function(t,e){R(mm.concat(e.__wrappedMethods||[]),(function(n){e.hasOwnProperty(n)&&(t[n]=e[n])})),t.__wrappedMethods=e.__wrappedMethods,R(_m,(function(n){t[n]=T(e[n])})),t._calculationInfo=C({},e._calculationInfo)},void(dm=function(t,e){var n=t._nameList,i=t._idList,r=t._nameDimIdx,o=t._idDimIdx,a=n[e],s=i[e];if(null==a&&null!=r&&(n[e]=a=hm(t,r,e)),null==s&&null!=o&&(i[e]=s=hm(t,o,e)),null==s&&null!=a){var l=t._nameRepeatCount,u=l[a]=(l[a]||0)+1;s=a,u>1&&(s+="__ec__"+u),i[e]=s}})),t}();function bm(t,e){Vf(t)||(t=Hf(t));var n=(e=e||{}).coordDimensions||[],i=e.dimensionsDefine||t.dimensionsDefine||[],r=dt(),o=[],a=function(t,e,n,i){var r=Math.max(t.dimensionsDetectedCount||1,e.length,n.length,i||0);return R(e,(function(t){var e;q(t)&&(e=t.dimsDef)&&(r=Math.max(r,e.length))})),r}(t,n,i,e.dimensionsCount),s=e.canOmitUnusedDimensions&&sm(a),l=i===t.dimensionsDefine,u=l?am(t):om(i),h=e.encodeDefine;!h&&e.encodeDefaulter&&(h=e.encodeDefaulter(t,a));for(var c=dt(h),p=new Md(a),f=0;f<p.length;f++)p[f]=-1;function d(t){var e=p[t];if(e<0){var n=i[t],r=q(n)?n:{name:n},a=new tm,s=r.name;null!=s&&null!=u.get(s)&&(a.name=a.displayName=s),null!=r.type&&(a.type=r.type),null!=r.displayName&&(a.displayName=r.displayName);var l=o.length;return p[t]=l,a.storeDimIndex=t,o.push(a),a}return o[e]}if(!s)for(f=0;f<a;f++)d(f);c.each((function(t,e){var n=po(t).slice();if(1===n.length&&!U(n[0])&&n[0]<0)c.set(e,!1);else{var i=c.set(e,[]);R(n,(function(t,n){var r=U(t)?u.get(t):t;null!=r&&r<a&&(i[n]=r,v(d(r),e,n))}))}}));var g=0;function v(t,e,n){null!=Ip.get(e)?t.otherDims[e]=n:(t.coordDim=e,t.coordDimIndex=n,r.set(e,!0))}R(n,(function(t){var e,n,i,r;if(U(t))e=t,r={};else{e=(r=t).name;var o=r.ordinalMeta;r.ordinalMeta=null,(r=C({},r)).ordinalMeta=o,n=r.dimsDef,i=r.otherDims,r.name=r.coordDim=r.coordDimIndex=r.dimsDef=r.otherDims=null}var s=c.get(e);if(!1!==s){if(!(s=po(s)).length)for(var u=0;u<(n&&n.length||1);u++){for(;g<a&&null!=d(g).coordDim;)g++;g<a&&s.push(g++)}R(s,(function(t,o){var a=d(t);if(l&&null!=r.type&&(a.type=r.type),v(D(a,r),e,o),null==a.name&&n){var s=n[o];!q(s)&&(s={name:s}),a.name=a.displayName=s.name,a.defaultTooltip=s.defaultTooltip}i&&D(a.otherDims,i)}))}}));var y=e.generateCoord,m=e.generateCoordCount,_=null!=m;m=y?m||1:0;var x=y||"value";function b(t){null==t.name&&(t.name=t.coordDim)}if(s)R(o,(function(t){b(t)})),o.sort((function(t,e){return t.storeDimIndex-e.storeDimIndex}));else for(var w=0;w<a;w++){var S=d(w);null==S.coordDim&&(S.coordDim=wm(x,r,_),S.coordDimIndex=0,(!y||m<=0)&&(S.isExtraCoord=!0),m--),b(S),null!=S.type||Xp(t,w)!==zp&&(!S.isExtraCoord||null==S.otherDims.itemName&&null==S.otherDims.seriesName)||(S.type="ordinal")}return function(t){for(var e=dt(),n=0;n<t.length;n++){var i=t[n],r=i.name,o=e.get(r)||0;o>0&&(i.name=r+(o-1)),o++,e.set(r,o)}}(o),new im({source:t,dimensions:o,fullDimensionCount:a,dimensionOmitted:s})}function wm(t,e,n){if(n||e.hasKey(t)){for(var i=0;e.hasKey(t+i);)i++;t+=i}return e.set(t,!0),t}var Sm=function(t){this.coordSysDims=[],this.axisMap=dt(),this.categoryAxisMap=dt(),this.coordSysName=t};var Mm={cartesian2d:function(t,e,n,i){var r=t.getReferringComponents("xAxis",Io).models[0],o=t.getReferringComponents("yAxis",Io).models[0];e.coordSysDims=["x","y"],n.set("x",r),n.set("y",o),Tm(r)&&(i.set("x",r),e.firstCategoryDimIndex=0),Tm(o)&&(i.set("y",o),null==e.firstCategoryDimIndex&&(e.firstCategoryDimIndex=1))},singleAxis:function(t,e,n,i){var r=t.getReferringComponents("singleAxis",Io).models[0];e.coordSysDims=["single"],n.set("single",r),Tm(r)&&(i.set("single",r),e.firstCategoryDimIndex=0)},polar:function(t,e,n,i){var r=t.getReferringComponents("polar",Io).models[0],o=r.findAxisModel("radiusAxis"),a=r.findAxisModel("angleAxis");e.coordSysDims=["radius","angle"],n.set("radius",o),n.set("angle",a),Tm(o)&&(i.set("radius",o),e.firstCategoryDimIndex=0),Tm(a)&&(i.set("angle",a),null==e.firstCategoryDimIndex&&(e.firstCategoryDimIndex=1))},geo:function(t,e,n,i){e.coordSysDims=["lng","lat"]},parallel:function(t,e,n,i){var r=t.ecModel,o=r.getComponent("parallel",t.get("parallelIndex")),a=e.coordSysDims=o.dimensions.slice();R(o.parallelAxisIndex,(function(t,o){var s=r.getComponent("parallelAxis",t),l=a[o];n.set(l,s),Tm(s)&&(i.set(l,s),null==e.firstCategoryDimIndex&&(e.firstCategoryDimIndex=o))}))},matrix:function(t,e,n,i){var r=t.getReferringComponents("matrix",Io).models[0];e.coordSysDims=["x","y"];var o=r.getDimensionModel("x"),a=r.getDimensionModel("y");n.set("x",o),n.set("y",a),i.set("x",o),i.set("y",a)}};function Tm(t){return"category"===t.get("type")}function km(t,e,n){var i,r,o,a=(n=n||{}).byIndex,s=n.stackedCoordDimension;!function(t){return!rm(t.schema)}(e)?(r=e.schema,i=r.dimensions,o=e.store):i=e;var l,u,h,c,p=!(!t||!t.get("stack"));if(R(i,(function(t,e){U(t)&&(i[e]=t={name:t}),p&&!t.isExtraCoord&&(a||l||!t.ordinalMeta||(l=t),u||"ordinal"===t.type||"time"===t.type||s&&s!==t.coordDim||(u=t))})),!u||a||l||(a=!0),u){h="__\0ecstackresult_"+t.id,c="__\0ecstackedover_"+t.id,l&&(l.createInvertedIndices=!0);var f=u.coordDim,d=u.type,g=0;R(i,(function(t){t.coordDim===f&&g++}));var v={name:h,coordDim:f,coordDimIndex:g,type:d,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:i.length},y={name:c,coordDim:c,coordDimIndex:g+1,type:d,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:i.length+1};r?(o&&(v.storeDimIndex=o.ensureCalculationDimension(c,d),y.storeDimIndex=o.ensureCalculationDimension(h,d)),r.appendCalculationDimension(v),r.appendCalculationDimension(y)):(i.push(v),i.push(y))}return{stackedDimension:u&&u.name,stackedByDimension:l&&l.name,isStackedByIndex:a,stackedOverDimension:c,stackResultDimension:h}}function Cm(t,e){return!!e&&e===t.getCalculationInfo("stackedDimension")}function Dm(t,e){return Cm(t,e)?t.getCalculationInfo("stackResultDimension"):e}function Im(t,e,n){n=n||{};var i,r=e.getSourceManager(),o=!1;t?(o=!0,i=Hf(t)):o=(i=r.getSource()).sourceFormat===Ap;var a=function(t){var e=t.get("coordinateSystem"),n=new Sm(e),i=Mm[e];if(i)return i(t,n,n.axisMap,n.categoryAxisMap),n}(e),s=function(t,e){var n,i=t.get("coordinateSystem"),r=Jc.get(i);return e&&e.coordSysDims&&(n=N(e.coordSysDims,(function(t){var n={name:t},i=e.axisMap.get(t);if(i){var r=i.get("type");n.type=function(t){return"category"===t?"ordinal":"time"===t?"time":"float"}(r)}return n}))),n||(n=r&&(r.getDimensionsInfo?r.getDimensionsInfo():r.dimensions.slice())||["x","y"]),n}(e,a),l=n.useEncodeDefaulter,u=G(l)?l:l?W(Hp,s,e):null,h=bm(i,{coordDimensions:s,generateCoord:n.generateCoord,encodeDefine:e.getEncode(),encodeDefaulter:u,canOmitUnusedDimensions:!o}),c=function(t,e,n){var i,r;return n&&R(t,(function(t,o){var a=t.coordDim,s=n.categoryAxisMap.get(a);s&&(null==i&&(i=o),t.ordinalMeta=s.getOrdinalMeta(),e&&(t.createInvertedIndices=!0)),null!=t.otherDims.itemName&&(r=!0)})),r||null==i||(t[i].otherDims.itemName=0),i}(h.dimensions,n.createInvertedIndices,a),p=o?null:r.getSharedDataStore(h),f=km(e,{schema:h,store:p}),d=new xm(h,e);d.setCalculationInfo(f);var g=null!=c&&function(t){if(t.sourceFormat===Ap){return!H(vo(function(t){var e=0;for(;e<t.length&&null==t[e];)e++;return t[e]}(t.data||[])))}}(i)?function(t,e,n,i){return i===c?n:this.defaultDimValueGetter(t,e,n,i)}:null;return d.hasItemOption=!1,d.initData(o?i:p,null,g),d}function Am(t){return"interval"===t.type||"log"===t.type}function Lm(t,e,n,i,r){var o={},a=o.interval=Jr(e/n,!0);null!=i&&a<i&&(a=o.interval=i),null!=r&&a>r&&(a=o.interval=r);var s=o.intervalPrecision=Om(a);return function(t,e){!isFinite(t[0])&&(t[0]=e[0]),!isFinite(t[1])&&(t[1]=e[1]),Rm(t,0,e),Rm(t,1,e),t[0]>t[1]&&(t[0]=t[1])}(o.niceTickExtent=[Wr(Math.ceil(t[0]/a)*a,s),Wr(Math.floor(t[1]/a)*a,s)],t),o}function Pm(t){var e=Math.pow(10,Qr(t)),n=t/e;return n?2===n?n=3:3===n?n=5:n*=2:n=1,Wr(n*e)}function Om(t){return Hr(t)+2}function Rm(t,e,n){t[e]=Math.max(Math.min(t[e],n[1]),n[0])}function Nm(t,e){return t>=e[0]&&t<=e[1]}var Bm=function(){function t(){this.normalize=Em,this.scale=zm}return t.prototype.updateMethods=function(t){t.hasBreaks()?(this.normalize=V(t.normalize,t),this.scale=V(t.scale,t)):(this.normalize=Em,this.scale=zm)},t}();function Em(t,e){return e[1]===e[0]?.5:(t-e[0])/(e[1]-e[0])}function zm(t,e){return t*(e[1]-e[0])+e[0]}function Fm(t,e,n){var i=Math.log(t);return[Math.log(n?e[0]:Math.max(0,e[0]))/i,Math.log(n?e[1]:Math.max(0,e[1]))/i]}var Vm=function(){function t(t){this._calculator=new Bm,this._setting=t||{},this._extent=[1/0,-1/0]}return t.prototype.getSetting=function(t){return this._setting[t]},t.prototype._innerUnionExtent=function(t){var e=this._extent;this._innerSetExtent(t[0]<e[0]?t[0]:e[0],t[1]>e[1]?t[1]:e[1])},t.prototype.unionExtentFromData=function(t,e){this._innerUnionExtent(t.getApproximateExtent(e))},t.prototype.getExtent=function(){return this._extent.slice()},t.prototype.setExtent=function(t,e){this._innerSetExtent(t,e)},t.prototype._innerSetExtent=function(t,e){var n=this._extent;isNaN(t)||(n[0]=t),isNaN(e)||(n[1]=e),this._brkCtx&&this._brkCtx.update(n)},t.prototype.setBreaksFromOption=function(t){},t.prototype._innerSetBreak=function(t){this._brkCtx&&(this._brkCtx.setBreaks(t),this._calculator.updateMethods(this._brkCtx),this._brkCtx.update(this._extent))},t.prototype._innerGetBreaks=function(){return this._brkCtx?this._brkCtx.breaks:[]},t.prototype.hasBreaks=function(){return!!this._brkCtx&&this._brkCtx.hasBreaks()},t.prototype._getExtentSpanWithBreaks=function(){return this._brkCtx&&this._brkCtx.hasBreaks()?this._brkCtx.getExtentSpan():this._extent[1]-this._extent[0]},t.prototype.isInExtentRange=function(t){return this._extent[0]<=t&&this._extent[1]>=t},t.prototype.isBlank=function(){return this._isBlank},t.prototype.setBlank=function(t){this._isBlank=t},t}();Vo(Vm);var Wm=0,Hm=function(){function t(t){this.categories=t.categories||[],this._needCollect=t.needCollect,this._deduplication=t.deduplication,this.uid=++Wm,this._onCollect=t.onCollect}return t.createByAxisModel=function(e){var n=e.option,i=n.data,r=i&&N(i,Gm);return new t({categories:r,needCollect:!r,deduplication:!1!==n.dedplication})},t.prototype.getOrdinal=function(t){return this._getOrCreateMap().get(t)},t.prototype.parseAndCollect=function(t){var e,n=this._needCollect;if(!U(t)&&!n)return t;if(n&&!this._deduplication)return e=this.categories.length,this.categories[e]=t,this._onCollect&&this._onCollect(t,e),e;var i=this._getOrCreateMap();return null==(e=i.get(t))&&(n?(e=this.categories.length,this.categories[e]=t,i.set(t,e),this._onCollect&&this._onCollect(t,e)):e=NaN),e},t.prototype._getOrCreateMap=function(){return this._map||(this._map=dt(this.categories))},t}();function Gm(t){return q(t)&&null!=t.value?t.value:t+""}var Um=function(t){function e(e){var n=t.call(this,e)||this;n.type="ordinal";var i=n.getSetting("ordinalMeta");return i||(i=new Hm({})),H(i)&&(i=new Hm({categories:N(i,(function(t){return q(t)?t.value:t}))})),n._ordinalMeta=i,n._extent=n.getSetting("extent")||[0,i.categories.length-1],n}return n(e,t),e.prototype.parse=function(t){return null==t?NaN:U(t)?this._ordinalMeta.getOrdinal(t):Math.round(t)},e.prototype.contain=function(t){return Nm(t,this._extent)&&t>=0&&t<this._ordinalMeta.categories.length},e.prototype.normalize=function(t){return t=this._getTickNumber(t),this._calculator.normalize(t,this._extent)},e.prototype.scale=function(t){return t=Math.round(this._calculator.scale(t,this._extent)),this.getRawOrdinalNumber(t)},e.prototype.getTicks=function(){for(var t=[],e=this._extent,n=e[0];n<=e[1];)t.push({value:n}),n++;return t},e.prototype.getMinorTicks=function(t){},e.prototype.setSortInfo=function(t){if(null!=t){for(var e=t.ordinalNumbers,n=this._ordinalNumbersByTick=[],i=this._ticksByOrdinalNumber=[],r=0,o=this._ordinalMeta.categories.length,a=Math.min(o,e.length);r<a;++r){var s=e[r];n[r]=s,i[s]=r}for(var l=0;r<o;++r){for(;null!=i[l];)l++;n.push(l),i[l]=r}}else this._ordinalNumbersByTick=this._ticksByOrdinalNumber=null},e.prototype._getTickNumber=function(t){var e=this._ticksByOrdinalNumber;return e&&t>=0&&t<e.length?e[t]:t},e.prototype.getRawOrdinalNumber=function(t){var e=this._ordinalNumbersByTick;return e&&t>=0&&t<e.length?e[t]:t},e.prototype.getLabel=function(t){if(!this.isBlank()){var e=this.getRawOrdinalNumber(t.value),n=this._ordinalMeta.categories[e];return null==n?"":n+""}},e.prototype.count=function(){return this._extent[1]-this._extent[0]+1},e.prototype.isInExtentRange=function(t){return t=this._getTickNumber(t),this._extent[0]<=t&&this._extent[1]>=t},e.prototype.getOrdinalMeta=function(){return this._ordinalMeta},e.prototype.calcNiceTicks=function(){},e.prototype.calcNiceExtent=function(){},e.type="ordinal",e}(Vm);Vm.registerClass(Um);var Xm=Wr,Ym=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="interval",e._interval=0,e._intervalPrecision=2,e}return n(e,t),e.prototype.parse=function(t){return null==t||""===t?NaN:Number(t)},e.prototype.contain=function(t){return Nm(t,this._extent)},e.prototype.normalize=function(t){return this._calculator.normalize(t,this._extent)},e.prototype.scale=function(t){return this._calculator.scale(t,this._extent)},e.prototype.getInterval=function(){return this._interval},e.prototype.setInterval=function(t){this._interval=t,this._niceExtent=this._extent.slice(),this._intervalPrecision=Om(t)},e.prototype.getTicks=function(t){t=t||{};var e=this._interval,n=this._extent,i=this._niceExtent,r=this._intervalPrecision,o=null,a=[];if(!e)return a;if("only_break"===t.breakTicks&&o)return o.addBreaksToTicks(a,this._brkCtx.breaks,this._extent),a;n[0]<i[0]&&(t.expandToNicedExtent?a.push({value:Xm(i[0]-e,r)}):a.push({value:n[0]}));for(var s=function(t,n){return Math.round((n-t)/e)},l=i[0];l<=i[1];){if(a.push({value:l}),l=Xm(l+e,r),this._brkCtx){var u=this._brkCtx.calcNiceTickMultiple(l,s);u>=0&&(l=Xm(l+u*e,r))}if(a.length>0&&l===a[a.length-1].value)break;if(a.length>1e4)return[]}var h=a.length?a[a.length-1].value:i[1];return n[1]>h&&(t.expandToNicedExtent?a.push({value:Xm(h+e,r)}):a.push({value:n[1]})),"none"!==t.breakTicks&&o&&o.addBreaksToTicks(a,this._brkCtx.breaks,this._extent),a},e.prototype.getMinorTicks=function(t){for(var e=this.getTicks({expandToNicedExtent:!0}),n=[],i=this.getExtent(),r=1;r<e.length;r++){var o=e[r],a=e[r-1];if(!a.break&&!o.break){for(var s=0,l=[],u=(o.value-a.value)/t,h=Om(u);s<t-1;){var c=Xm(a.value+(s+1)*u,h);c>i[0]&&c<i[1]&&l.push(c),s++}var p=null;p&&p.pruneTicksByBreak("auto",l,this._getNonTransBreaks(),(function(t){return t}),this._interval,i),n.push(l)}}return n},e.prototype._getNonTransBreaks=function(){return this._brkCtx?this._brkCtx.breaks:[]},e.prototype.getLabel=function(t,e){if(null==t)return"";var n=e&&e.precision;return null==n?n=Hr(t.value)||0:"auto"===n&&(n=this._intervalPrecision),Xc(Xm(t.value,n,!0))},e.prototype.calcNiceTicks=function(t,e,n){t=t||5;var i=this._extent.slice(),r=this._getExtentSpanWithBreaks();if(isFinite(r)){r<0&&(r=-r,i.reverse(),this._innerSetExtent(i[0],i[1]),i=this._extent.slice());var o=Lm(i,r,t,e,n);this._intervalPrecision=o.intervalPrecision,this._interval=o.interval,this._niceExtent=o.niceTickExtent}},e.prototype.calcNiceExtent=function(t){var e=this._extent.slice();if(e[0]===e[1])if(0!==e[0]){var n=Math.abs(e[0]);t.fixMax||(e[1]+=n/2),e[0]-=n/2}else e[1]=1;var i=e[1]-e[0];isFinite(i)||(e[0]=0,e[1]=1),this._innerSetExtent(e[0],e[1]),e=this._extent.slice(),this.calcNiceTicks(t.splitNumber,t.minInterval,t.maxInterval);var r=this._interval,o=this._intervalPrecision;t.fixMin||(e[0]=Xm(Math.floor(e[0]/r)*r,o)),t.fixMax||(e[1]=Xm(Math.ceil(e[1]/r)*r,o)),this._innerSetExtent(e[0],e[1])},e.prototype.setNiceExtent=function(t,e){this._niceExtent=[t,e]},e.type="interval",e}(Vm);Vm.registerClass(Ym);var qm="undefined"!=typeof Float32Array,Zm=qm?Float32Array:Array;function jm(t){return H(t)?qm?new Float32Array(t):t:new Zm(t)}function Km(t){return t.get("stack")||"__ec_stack_"+t.seriesIndex}function $m(t){return t.dim+t.index}function Qm(t,e){var n=[];return e.eachSeriesByType(t,(function(t){e_(t)&&n.push(t)})),n}function Jm(t){var e=function(t){var e={};R(t,(function(t){var n=t.coordinateSystem.getBaseAxis();if("time"===n.type||"value"===n.type)for(var i=t.getData(),r=n.dim+"_"+n.index,o=i.getDimensionIndex(i.mapDimension(n.dim)),a=i.getStore(),s=0,l=a.count();s<l;++s){var u=a.get(o,s);e[r]?e[r].push(u):e[r]=[u]}}));var n={};for(var i in e)if(e.hasOwnProperty(i)){var r=e[i];if(r){r.sort((function(t,e){return t-e}));for(var o=null,a=1;a<r.length;++a){var s=r[a]-r[a-1];s>0&&(o=null===o?s:Math.min(o,s))}n[i]=o}}return n}(t),n=[];return R(t,(function(t){var i,r=t.coordinateSystem.getBaseAxis(),o=r.getExtent();if("category"===r.type)i=r.getBandWidth();else if("value"===r.type||"time"===r.type){var a=r.dim+"_"+r.index,s=e[a],l=Math.abs(o[1]-o[0]),u=r.scale.getExtent(),h=Math.abs(u[1]-u[0]);i=s?l/h*s:l}else{var c=t.getData();i=Math.abs(o[1]-o[0])/c.count()}var p=Fr(t.get("barWidth"),i),f=Fr(t.get("barMaxWidth"),i),d=Fr(t.get("barMinWidth")||(n_(t)?.5:1),i),g=t.get("barGap"),v=t.get("barCategoryGap"),y=t.get("defaultBarGap");n.push({bandWidth:i,barWidth:p,barMaxWidth:f,barMinWidth:d,barGap:g,barCategoryGap:v,defaultBarGap:y,axisKey:$m(r),stackId:Km(t)})})),function(t){var e={};R(t,(function(t,n){var i=t.axisKey,r=t.bandWidth,o=e[i]||{bandWidth:r,remainedWidth:r,autoWidthCount:0,categoryGap:null,gap:t.defaultBarGap||0,stacks:{}},a=o.stacks;e[i]=o;var s=t.stackId;a[s]||o.autoWidthCount++,a[s]=a[s]||{width:0,maxWidth:0};var l=t.barWidth;l&&!a[s].width&&(a[s].width=l,l=Math.min(o.remainedWidth,l),o.remainedWidth-=l);var u=t.barMaxWidth;u&&(a[s].maxWidth=u);var h=t.barMinWidth;h&&(a[s].minWidth=h);var c=t.barGap;null!=c&&(o.gap=c);var p=t.barCategoryGap;null!=p&&(o.categoryGap=p)}));var n={};return R(e,(function(t,e){n[e]={};var i=t.stacks,r=t.bandWidth,o=t.categoryGap;if(null==o){var a=F(i).length;o=Math.max(35-4*a,15)+"%"}var s=Fr(o,r),l=Fr(t.gap,1),u=t.remainedWidth,h=t.autoWidthCount,c=(u-s)/(h+(h-1)*l);c=Math.max(c,0),R(i,(function(t){var e=t.maxWidth,n=t.minWidth;if(t.width){i=t.width;e&&(i=Math.min(i,e)),n&&(i=Math.max(i,n)),t.width=i,u-=i+l*i,h--}else{var i=c;e&&e<i&&(i=Math.min(e,u)),n&&n>i&&(i=n),i!==c&&(t.width=i,u-=i+l*i,h--)}})),c=(u-s)/(h+(h-1)*l),c=Math.max(c,0);var p,f=0;R(i,(function(t,e){t.width||(t.width=c),p=t,f+=t.width*(1+l)})),p&&(f-=p.width*l);var d=-f/2;R(i,(function(t,i){n[e][i]=n[e][i]||{bandWidth:r,offset:d,width:t.width},d+=t.width*(1+l)}))})),n}(n)}function t_(t,e){var n=Qm(t,e),i=Jm(n);R(n,(function(t){var e=t.getData(),n=t.coordinateSystem.getBaseAxis(),r=Km(t),o=i[$m(n)][r],a=o.offset,s=o.width;e.setLayout({bandWidth:o.bandWidth,offset:a,size:s})}))}function e_(t){return t.coordinateSystem&&"cartesian2d"===t.coordinateSystem.type}function n_(t){return t.pipelineContext&&t.pipelineContext.large}var i_=function(t){function e(e){var n=t.call(this,e)||this;return n.type="time",n}return n(e,t),e.prototype.getLabel=function(t){var e=this.getSetting("useUTC");return Dc(t.value,bc[function(t){switch(t){case"year":case"month":return"day";case"millisecond":return"millisecond";default:return"second"}}(kc(this._minLevelUnit))]||bc.second,e,this.getSetting("locale"))},e.prototype.getFormattedLabel=function(t,e,n){var i=this.getSetting("useUTC");return function(t,e,n,i,r){var o=null;if(U(n))o=n;else if(G(n)){var a={time:t.time,level:t.time.level},s=null;s&&s.makeAxisLabelFormatterParamBreak(a,t.break),o=n(t.value,e,a)}else{var l=t.time;if(l){var u=n[l.lowerTimeUnit][l.upperTimeUnit];o=u[Math.min(l.level,u.length-1)]||""}else{var h=Ic(t.value,r);o=n[h][h][0]}}return Dc(new Date(t.value),o,r,i)}(t,e,n,this.getSetting("locale"),i)},e.prototype.getTicks=function(t){var e=this._interval,n=this._extent,i=[];if(!e)return i;var r=this.getSetting("useUTC"),o=Ic(n[1],r);i.push({value:n[0],time:{level:0,upperTimeUnit:o,lowerTimeUnit:o}});var a=function(t,e,n,i,r,o){var a=1e4,s=Sc,l=0;function u(t,e,n,r,s,u,h){for(var c=function(t,e){var n=new Date(0);n[t](1);var i=n.getTime();n[t](1+e);var r=n.getTime()-i;return function(t,e){return Math.max(0,Math.round((e-t)/r))}}(s,t),p=e,f=new Date(p);p<n&&p<=i[1];){if(h.push({value:p}),l++>a){0;break}if(f[s](f[r]()+t),p=f.getTime(),o){var d=o.calcNiceTickMultiple(p,c);d>0&&(f[s](f[r]()+d*t),p=f.getTime())}}h.push({value:p,notAdd:!0})}function h(t,r,o){var a=[],s=!r.length;if(!function(t,e,n,i){return Ac(new Date(e),t,i).getTime()===Ac(new Date(n),t,i).getTime()}(kc(t),i[0],i[1],n)){s&&(r=[{value:h_(i[0],t,n)},{value:i[1]}]);for(var l=0;l<r.length-1;l++){var h=r[l].value,c=r[l+1].value;if(h!==c){var p=void 0,f=void 0,d=void 0,g=!1;switch(t){case"year":p=Math.max(1,Math.round(e/vc/365)),f=Lc(n),d=zc(n);break;case"half-year":case"quarter":case"month":p=a_(e),f=Pc(n),d=Fc(n);break;case"week":case"half-week":case"day":p=o_(e),f=Oc(n),d=Vc(n),g=!0;break;case"half-day":case"quarter-day":case"hour":p=s_(e),f=Rc(n),d=Wc(n);break;case"minute":p=l_(e,!0),f=Nc(n),d=Hc(n);break;case"second":p=l_(e,!1),f=Bc(n),d=Gc(n);break;case"millisecond":p=u_(e),f=Ec(n),d=Uc(n)}c>=i[0]&&h<=i[1]&&u(p,h,c,f,d,g,a),"year"===t&&o.length>1&&0===l&&o.unshift({value:o[0].value-p})}}for(l=0;l<a.length;l++)o.push(a[l])}}for(var c=[],p=[],f=0,d=0,g=0;g<s.length;++g){var v=kc(s[g]);if(Cc(s[g]))if(h(s[g],c[c.length-1]||[],p),v!==(s[g+1]?kc(s[g+1]):null)){if(p.length){d=f,p.sort((function(t,e){return t.value-e.value}));for(var y=[],m=0;m<p.length;++m){var _=p[m].value;0!==m&&p[m-1].value===_||(y.push(p[m]),_>=i[0]&&_<=i[1]&&f++)}var x=r/e;if(f>1.5*x&&d>x/1.5)break;if(c.push(y),f>x||t===s[g])break}p=[]}}var b=E(N(c,(function(t){return E(t,(function(t){return t.value>=i[0]&&t.value<=i[1]&&!t.notAdd}))})),(function(t){return t.length>0})),w=[],S=b.length-1;for(g=0;g<b.length;++g)for(var M=b[g],T=0;T<M.length;++T){var k=Ic(M[T].value,n);w.push({value:M[T].value,time:{level:S-g,upperTimeUnit:k,lowerTimeUnit:k}})}w.sort((function(t,e){return t.value-e.value}));var C=[];for(g=0;g<w.length;++g)0!==g&&w[g].value===w[g-1].value||C.push(w[g]);return C}(this._minLevelUnit,this._approxInterval,r,n,this._getExtentSpanWithBreaks(),this._brkCtx);i=i.concat(a);var s=Ic(n[1],r);i.push({value:n[1],time:{level:0,upperTimeUnit:s,lowerTimeUnit:s}});this.getSetting("useUTC");var l=wc.length-1,u=0;return R(i,(function(t){l=Math.min(l,A(wc,t.time.upperTimeUnit)),u=Math.max(u,t.time.level)})),i},e.prototype.calcNiceExtent=function(t){var e=this.getExtent();if(e[0]===e[1]&&(e[0]-=vc,e[1]+=vc),e[1]===-1/0&&e[0]===1/0){var n=new Date;e[1]=+new Date(n.getFullYear(),n.getMonth(),n.getDate()),e[0]=e[1]-vc}this._innerSetExtent(e[0],e[1]),this.calcNiceTicks(t.splitNumber,t.minInterval,t.maxInterval)},e.prototype.calcNiceTicks=function(t,e,n){t=t||10;var i=this._getExtentSpanWithBreaks();this._approxInterval=i/t,null!=e&&this._approxInterval<e&&(this._approxInterval=e),null!=n&&this._approxInterval>n&&(this._approxInterval=n);var r=r_.length,o=Math.min(function(t,e,n,i){for(;n<i;){var r=n+i>>>1;t[r][1]<e?n=r+1:i=r}return n}(r_,this._approxInterval,0,r),r-1);this._interval=r_[o][1],this._intervalPrecision=Om(this._interval),this._minLevelUnit=r_[Math.max(o-1,0)][0]},e.prototype.parse=function(t){return Y(t)?t:+Kr(t)},e.prototype.contain=function(t){return Nm(t,this._extent)},e.prototype.normalize=function(t){return this._calculator.normalize(t,this._extent)},e.prototype.scale=function(t){return this._calculator.scale(t,this._extent)},e.type="time",e}(Ym),r_=[["second",fc],["minute",dc],["hour",gc],["quarter-day",216e5],["half-day",432e5],["day",10368e4],["half-week",3024e5],["week",6048e5],["month",26784e5],["quarter",8208e6],["half-year",yc/2],["year",yc]];function o_(t,e){return(t/=vc)>16?16:t>7.5?7:t>3.5?4:t>1.5?2:1}function a_(t){return(t/=2592e6)>6?6:t>3?3:t>2?2:1}function s_(t){return(t/=gc)>12?12:t>6?6:t>3.5?4:t>2?2:1}function l_(t,e){return(t/=e?dc:fc)>30?30:t>20?20:t>15?15:t>10?10:t>5?5:t>2?2:1}function u_(t){return Jr(t,!0)}function h_(t,e,n){var i=Math.max(0,A(wc,e)-1);return Ac(new Date(t),wc[i],n).getTime()}Vm.registerClass(i_);var c_=Wr,p_=Math.floor,f_=Math.ceil,d_=Math.pow,g_=Math.log,v_=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="log",e.base=10,e._originalScale=new Ym,e}return n(e,t),e.prototype.getTicks=function(e){e=e||{};var n=this._extent.slice(),i=this._originalScale.getExtent(),r=t.prototype.getTicks.call(this,e),o=this.base;this._originalScale._innerGetBreaks();return N(r,(function(t){var e=t.value,r=null,a=d_(o,e);return e===n[0]&&this._fixMin?r=i[0]:e===n[1]&&this._fixMax&&(r=i[1]),null!=r&&(a=y_(a,r)),{value:a,break:undefined}}),this)},e.prototype._getNonTransBreaks=function(){return this._originalScale._innerGetBreaks()},e.prototype.setExtent=function(e,n){this._originalScale.setExtent(e,n);var i=Fm(this.base,[e,n]);t.prototype.setExtent.call(this,i[0],i[1])},e.prototype.getExtent=function(){var e=this.base,n=t.prototype.getExtent.call(this);n[0]=d_(e,n[0]),n[1]=d_(e,n[1]);var i=this._originalScale.getExtent();return this._fixMin&&(n[0]=y_(n[0],i[0])),this._fixMax&&(n[1]=y_(n[1],i[1])),n},e.prototype.unionExtentFromData=function(t,e){this._originalScale.unionExtentFromData(t,e);var n=Fm(this.base,t.getApproximateExtent(e),!0);this._innerUnionExtent(n)},e.prototype.calcNiceTicks=function(t){t=t||10;var e=this._extent.slice(),n=this._getExtentSpanWithBreaks();if(isFinite(n)&&!(n<=0)){var i=$r(n);for(t/n*i<=.5&&(i*=10);!isNaN(i)&&Math.abs(i)<1&&Math.abs(i)>0;)i*=10;var r=[c_(f_(e[0]/i)*i),c_(p_(e[1]/i)*i)];this._interval=i,this._intervalPrecision=Om(i),this._niceExtent=r}},e.prototype.calcNiceExtent=function(e){t.prototype.calcNiceExtent.call(this,e),this._fixMin=e.fixMin,this._fixMax=e.fixMax},e.prototype.contain=function(e){return e=g_(e)/g_(this.base),t.prototype.contain.call(this,e)},e.prototype.normalize=function(e){return e=g_(e)/g_(this.base),t.prototype.normalize.call(this,e)},e.prototype.scale=function(e){return e=t.prototype.scale.call(this,e),d_(this.base,e)},e.prototype.setBreaksFromOption=function(t){},e.type="log",e}(Ym);function y_(t,e){return c_(t,Hr(e))}Vm.registerClass(v_);var m_=function(){function t(t,e,n){this._prepareParams(t,e,n)}return t.prototype._prepareParams=function(t,e,n){n[1]<n[0]&&(n=[NaN,NaN]),this._dataMin=n[0],this._dataMax=n[1];var i=this._isOrdinal="ordinal"===t.type;this._needCrossZero="interval"===t.type&&e.getNeedCrossZero&&e.getNeedCrossZero();var r=e.get("min",!0);null==r&&(r=e.get("startValue",!0));var o=this._modelMinRaw=r;G(o)?this._modelMinNum=b_(t,o({min:n[0],max:n[1]})):"dataMin"!==o&&(this._modelMinNum=b_(t,o));var a=this._modelMaxRaw=e.get("max",!0);if(G(a)?this._modelMaxNum=b_(t,a({min:n[0],max:n[1]})):"dataMax"!==a&&(this._modelMaxNum=b_(t,a)),i)this._axisDataLen=e.getCategories().length;else{var s=e.get("boundaryGap"),l=H(s)?s:[s||0,s||0];"boolean"==typeof l[0]||"boolean"==typeof l[1]?this._boundaryGapInner=[0,0]:this._boundaryGapInner=[pr(l[0],1),pr(l[1],1)]}},t.prototype.calculate=function(){var t=this._isOrdinal,e=this._dataMin,n=this._dataMax,i=this._axisDataLen,r=this._boundaryGapInner,o=t?null:n-e||Math.abs(e),a="dataMin"===this._modelMinRaw?e:this._modelMinNum,s="dataMax"===this._modelMaxRaw?n:this._modelMaxNum,l=null!=a,u=null!=s;null==a&&(a=t?i?0:NaN:e-r[0]*o),null==s&&(s=t?i?i-1:NaN:n+r[1]*o),(null==a||!isFinite(a))&&(a=NaN),(null==s||!isFinite(s))&&(s=NaN);var h=tt(a)||tt(s)||t&&!i;this._needCrossZero&&(a>0&&s>0&&!l&&(a=0),a<0&&s<0&&!u&&(s=0));var c=this._determinedMin,p=this._determinedMax;return null!=c&&(a=c,l=!0),null!=p&&(s=p,u=!0),{min:a,max:s,minFixed:l,maxFixed:u,isBlank:h}},t.prototype.modifyDataMinMax=function(t,e){this[x_[t]]=e},t.prototype.setDeterminedMinMax=function(t,e){var n=__[t];this[n]=e},t.prototype.freeze=function(){this.frozen=!0},t}(),__={min:"_determinedMin",max:"_determinedMax"},x_={min:"_dataMin",max:"_dataMax"};function b_(t,e){return null==e?null:tt(e)?NaN:t.parse(e)}function w_(t,e){var n=t.type,i=function(t,e,n){var i=t.rawExtentInfo;return i||(i=new m_(t,e,n),t.rawExtentInfo=i,i)}(t,e,t.getExtent()).calculate();t.setBlank(i.isBlank);var r=i.min,o=i.max,a=e.ecModel;if(a&&"time"===n){var s=Qm("bar",a),l=!1;if(R(s,(function(t){l=l||t.getBaseAxis()===e.axis})),l){var u=Jm(s),h=function(t,e,n,i){var r=n.axis.getExtent(),o=Math.abs(r[1]-r[0]),a=function(t,e,n){if(t&&e){var i=t[$m(e)];return null!=i&&null!=n?i[Km(n)]:i}}(i,n.axis);if(void 0===a)return{min:t,max:e};var s=1/0;R(a,(function(t){s=Math.min(t.offset,s)}));var l=-1/0;R(a,(function(t){l=Math.max(t.offset+t.width,l)})),s=Math.abs(s),l=Math.abs(l);var u=s+l,h=e-t,c=h/(1-(s+l)/o)-h;return e+=c*(l/u),t-=c*(s/u),{min:t,max:e}}(r,o,e,u);r=h.min,o=h.max}}return{extent:[r,o],fixMin:i.minFixed,fixMax:i.maxFixed}}function S_(t,e){var n=e,i=w_(t,n),r=i.extent,o=n.get("splitNumber");t instanceof v_&&(t.base=n.get("logBase"));var a=t.type,s=n.get("interval"),l="interval"===a||"time"===a;t.setBreaksFromOption(L_(n)),t.setExtent(r[0],r[1]),t.calcNiceExtent({splitNumber:o,fixMin:i.fixMin,fixMax:i.fixMax,minInterval:l?n.get("minInterval"):null,maxInterval:l?n.get("maxInterval"):null}),null!=s&&t.setInterval&&t.setInterval(s)}function M_(t,e){if(e=e||t.get("type"))switch(e){case"category":return new Um({ordinalMeta:t.getOrdinalMeta?t.getOrdinalMeta():t.getCategories(),extent:[1/0,-1/0]});case"time":return new i_({locale:t.ecModel.getLocaleModel(),useUTC:t.ecModel.get("useUTC")});default:return new(Vm.getClass(e)||Ym)}}function T_(t){var e=t.getLabelModel().get("formatter");if("time"===t.type){var n=Mc(e);return function(e,i){return t.scale.getFormattedLabel(e,i,n)}}if(U(e))return function(n){var i=t.scale.getLabel(n);return e.replace("{value}",null!=i?i:"")};if(G(e)){if("category"===t.type)return function(n,i){return e(k_(t,n),n.value-t.scale.getExtent()[0],null)};var i=null;return function(n,r){var o=null;return i&&(o=i.makeAxisLabelFormatterParamBreak(o,n.break)),e(k_(t,n),r,o)}}return function(e){return t.scale.getLabel(e)}}function k_(t,e){return"category"===t.type?t.scale.getLabel(e):e.value}function C_(t){var e=t.get("interval");return null==e?"auto":e}function D_(t){return"category"===t.type&&0===C_(t.getLabelModel())}function I_(t){return"middle"===t||"center"===t}function A_(t){return t.getShallow("show")}function L_(t){t.get("breaks",!0)}var P_=function(){function t(){}return t.prototype.getNeedCrossZero=function(){return!this.option.scale},t.prototype.getCoordSysModel=function(){},t}();var O_={isDimensionStacked:Cm,enableDataStack:km,getStackedDimension:Dm};var R_=Object.freeze({__proto__:null,createList:function(t){return Im(null,t)},getLayoutRect:pp,dataStack:O_,createScale:function(t,e){var n=e;e instanceof ic||(n=new ic(e));var i=M_(n);return i.setExtent(t[0],t[1]),S_(i,n),i},mixinAxisModelCommonMethods:function(t){P(t,P_)},getECData:Ys,createTextStyle:function(t,e){return Eh(t,null,null,"normal"!==(e=e||{}).state)},createDimensions:function(t,e){return bm(t,e).dimensions},createSymbol:Qg,enableHoverEmphasis:Pl});function N_(t,e){return Math.abs(t-e)<1e-8}function B_(t,e,n){var i=0,r=t[0];if(!r)return!1;for(var o=1;o<t.length;o++){var a=t[o];i+=ss(r[0],r[1],a[0],a[1],e,n),r=a}var s=t[0];return N_(r[0],s[0])&&N_(r[1],s[1])||(i+=ss(r[0],r[1],s[0],s[1],e,n)),0!==i}var E_=[];function z_(t,e){for(var n=0;n<t.length;n++)zt(t[n],t[n],e)}function F_(t,e,n,i){for(var r=0;r<t.length;r++){var o=t[r];i&&(o=i.project(o)),o&&isFinite(o[0])&&isFinite(o[1])&&(Ft(e,e,o),Vt(n,n,o))}}var V_=function(){function t(t){this.name=t}return t.prototype.setCenter=function(t){this._center=t},t.prototype.getCenter=function(){var t=this._center;return t||(t=this._center=this.calcCenter()),t},t}(),W_=function(t,e){this.type="polygon",this.exterior=t,this.interiors=e},H_=function(t){this.type="linestring",this.points=t},G_=function(t){function e(e,n,i){var r=t.call(this,e)||this;return r.type="geoJSON",r.geometries=n,r._center=i&&[i[0],i[1]],r}return n(e,t),e.prototype.calcCenter=function(){for(var t,e=this.geometries,n=0,i=0;i<e.length;i++){var r=e[i],o=r.exterior,a=o&&o.length;a>n&&(t=r,n=a)}if(t)return function(t){for(var e=0,n=0,i=0,r=t.length,o=t[r-1][0],a=t[r-1][1],s=0;s<r;s++){var l=t[s][0],u=t[s][1],h=o*u-l*a;e+=h,n+=(o+l)*h,i+=(a+u)*h,o=l,a=u}return e?[n/e/3,i/e/3,e]:[t[0][0]||0,t[0][1]||0]}(t.exterior);var s=this.getBoundingRect();return[s.x+s.width/2,s.y+s.height/2]},e.prototype.getBoundingRect=function(t){var e=this._rect;if(e&&!t)return e;var n=[1/0,1/0],i=[-1/0,-1/0];return R(this.geometries,(function(e){"polygon"===e.type?F_(e.exterior,n,i,t):R(e.points,(function(e){F_(e,n,i,t)}))})),isFinite(n[0])&&isFinite(n[1])&&isFinite(i[0])&&isFinite(i[1])||(n[0]=n[1]=i[0]=i[1]=0),e=new Oe(n[0],n[1],i[0]-n[0],i[1]-n[1]),t||(this._rect=e),e},e.prototype.contain=function(t){var e=this.getBoundingRect(),n=this.geometries;if(!e.contain(t[0],t[1]))return!1;t:for(var i=0,r=n.length;i<r;i++){var o=n[i];if("polygon"===o.type){var a=o.exterior,s=o.interiors;if(B_(a,t[0],t[1])){for(var l=0;l<(s?s.length:0);l++)if(B_(s[l],t[0],t[1]))continue t;return!0}}}return!1},e.prototype.transformTo=function(t,e,n,i){var r=this.getBoundingRect(),o=r.width/r.height;n?i||(i=n/o):n=o*i;for(var a=new Oe(t,e,n,i),s=r.calculateTransform(a),l=this.geometries,u=0;u<l.length;u++){var h=l[u];"polygon"===h.type?(z_(h.exterior,s),R(h.interiors,(function(t){z_(t,s)}))):R(h.points,(function(t){z_(t,s)}))}(r=this._rect).copy(a),this._center=[r.x+r.width/2,r.y+r.height/2]},e.prototype.cloneShallow=function(t){null==t&&(t=this.name);var n=new e(t,this.geometries,this._center);return n._rect=this._rect,n.transformTo=null,n},e}(V_);!function(t){function e(e,n){var i=t.call(this,e)||this;return i.type="geoSVG",i._elOnlyForCalculate=n,i}n(e,t),e.prototype.calcCenter=function(){for(var t=this._elOnlyForCalculate,e=t.getBoundingRect(),n=[e.x+e.width/2,e.y+e.height/2],i=ce(E_),r=t;r&&!r.isGeoSVGGraphicRoot;)fe(i,r.getLocalTransform(),i),r=r.parent;return ye(i,i),zt(n,n,i),n}}(V_);function U_(t,e,n){for(var i=0;i<t.length;i++)t[i]=X_(t[i],e[i],n)}function X_(t,e,n){for(var i=[],r=e[0],o=e[1],a=0;a<t.length;a+=2){var s=t.charCodeAt(a)-64,l=t.charCodeAt(a+1)-64;s=s>>1^-(1&s),l=l>>1^-(1&l),r=s+=r,o=l+=o,i.push([s/n,l/n])}return i}function Y_(t,e){return N(E((t=function(t){if(!t.UTF8Encoding)return t;var e=t,n=e.UTF8Scale;return null==n&&(n=1024),R(e.features,(function(t){var e=t.geometry,i=e.encodeOffsets,r=e.coordinates;if(i)switch(e.type){case"LineString":e.coordinates=X_(r,i,n);break;case"Polygon":case"MultiLineString":U_(r,i,n);break;case"MultiPolygon":R(r,(function(t,e){return U_(t,i[e],n)}))}})),e.UTF8Encoding=!1,e}(t)).features,(function(t){return t.geometry&&t.properties&&t.geometry.coordinates.length>0})),(function(t){var n=t.properties,i=t.geometry,r=[];switch(i.type){case"Polygon":var o=i.coordinates;r.push(new W_(o[0],o.slice(1)));break;case"MultiPolygon":R(i.coordinates,(function(t){t[0]&&r.push(new W_(t[0],t.slice(1)))}));break;case"LineString":r.push(new H_([i.coordinates]));break;case"MultiLineString":r.push(new H_(i.coordinates))}var a=new G_(n[e||"name"],r,n.cp);return a.properties=n,a}))}var q_=Object.freeze({__proto__:null,linearMap:zr,round:Wr,asc:function(t){return t.sort((function(t,e){return t-e})),t},getPrecision:Hr,getPrecisionSafe:Gr,getPixelPrecision:Ur,getPercentWithPrecision:function(t,e,n){return t[e]&&Xr(t,n)[e]||0},parsePercent:Fr,MAX_SAFE_INTEGER:9007199254740991,remRadian:qr,isRadianAroundZero:Zr,parseDate:Kr,quantity:$r,quantityExponent:Qr,nice:Jr,quantile:function(t,e){var n=(t.length-1)*e+1,i=Math.floor(n),r=+t[i-1],o=n-i;return o?r+o*(t[i]-r):r},reformIntervals:function(t){t.sort((function(t,e){return s(t,e,0)?-1:1}));for(var e=-1/0,n=1,i=0;i<t.length;){for(var r=t[i].interval,o=t[i].close,a=0;a<2;a++)r[a]<=e&&(r[a]=e,o[a]=a?1:1-n),e=r[a],n=o[a];r[0]===r[1]&&o[0]*o[1]!=1?t.splice(i,1):i++}return t;function s(t,e,n){return t.interval[n]<e.interval[n]||t.interval[n]===e.interval[n]&&(t.close[n]-e.close[n]==(n?-1:1)||!n&&s(t,e,1))}},isNumeric:eo,numericToNumber:to}),Z_=Object.freeze({__proto__:null,parse:Kr,format:Dc,roundTime:Ac}),j_=Object.freeze({__proto__:null,extendShape:function(t){return _s.extend(t)},extendPath:function(t,e){return ph(t,e)},makePath:dh,makeImage:gh,mergePath:yh,resizePath:mh,createIcon:function(t,e,n){var i=C({rectHover:!0},e),r=i.style={strokeNoScale:!0};if(n=n||{x:-1,y:-1,width:2,height:2},t)return 0===t.indexOf("image://")?(r.image=t.slice(8),D(r,n),new Ms(i)):dh(t.replace("path://",""),i,n,"center")},updateProps:nh,initProps:ih,getTransform:function(t,e){for(var n=ce([]);t&&t!==e;)fe(n,t.getLocalTransform(),n),t=t.parent;return n},clipPointsByRect:function(t,e){return N(t,(function(t){var n=t[0];n=Br(n,e.x),n=Nr(n,e.x+e.width);var i=t[1];return i=Br(i,e.y),[n,i=Nr(i,e.y+e.height)]}))},clipRectByRect:function(t,e){var n=Br(t.x,e.x),i=Nr(t.x+t.width,e.x+e.width),r=Br(t.y,e.y),o=Nr(t.y+t.height,e.y+e.height);if(i>=n&&o>=r)return{x:n,y:r,width:i-n,height:o-r}},registerShape:fh,getShapeClass:function(t){if(uh.hasOwnProperty(t))return uh[t]},Group:Tr,Image:Ms,Text:Rs,Circle:ou,Ellipse:su,Sector:wu,Ring:Mu,Polygon:Cu,Polyline:Iu,Rect:As,Line:Pu,BezierCurve:Bu,Arc:zu,IncrementalDisplayable:Ju,CompoundPath:Fu,LinearGradient:Wu,RadialGradient:Hu,BoundingRect:Oe}),K_=Object.freeze({__proto__:null,addCommas:Xc,toCamelCase:function(t,e){return t=(t||"").toLowerCase().replace(/-(.)/g,(function(t,e){return e.toUpperCase()})),e&&t&&(t=t.charAt(0).toUpperCase()+t.slice(1)),t},normalizeCssArray:Yc,encodeHTML:Jt,formatTpl:jc,getTooltipMarker:function(t,e){var n=U(t)?{color:t,extraCssText:e}:t||{},i=n.color,r=n.type;e=n.extraCssText;var o=n.renderMode||"html";return i?"html"===o?"subItem"===r?'<span style="display:inline-block;vertical-align:middle;margin-right:8px;margin-left:3px;border-radius:4px;width:4px;height:4px;background-color:'+Jt(i)+";"+(e||"")+'"></span>':'<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:'+Jt(i)+";"+(e||"")+'"></span>':{renderMode:o,content:"{"+(n.markerId||"markerX")+"|}  ",style:"subItem"===r?{width:4,height:4,borderRadius:2,backgroundColor:i}:{width:10,height:10,borderRadius:5,backgroundColor:i}}:""},formatTime:function(t,e,n){"week"!==t&&"month"!==t&&"quarter"!==t&&"half-year"!==t&&"year"!==t||(t="MM-dd\nyyyy");var i=Kr(e),r=n?"getUTC":"get",o=i[r+"FullYear"](),a=i[r+"Month"]()+1,s=i[r+"Date"](),l=i[r+"Hours"](),u=i[r+"Minutes"](),h=i[r+"Seconds"](),c=i[r+"Milliseconds"]();return t=t.replace("MM",Tc(a,2)).replace("M",a).replace("yyyy",o).replace("yy",Tc(o%100+"",2)).replace("dd",Tc(s,2)).replace("d",s).replace("hh",Tc(l,2)).replace("h",l).replace("mm",Tc(u,2)).replace("m",u).replace("ss",Tc(h,2)).replace("s",h).replace("SSS",Tc(c,3))},capitalFirst:function(t){return t?t.charAt(0).toUpperCase()+t.substr(1):t},truncateText:function(t,e,n,i,r){var o={};return Ko(o,t,e,n,i,r),o.text},getTextRect:function(t,e,n,i,r,o,a,s){return new Rs({style:{text:t,font:e,align:n,verticalAlign:i,padding:r,rich:o,overflow:a?"truncate":null,lineHeight:s}}).getBoundingRect()}}),$_=Object.freeze({__proto__:null,map:N,each:R,indexOf:A,inherits:L,reduce:B,filter:E,bind:V,curry:W,isArray:H,isString:U,isObject:q,isFunction:G,extend:C,defaults:D,clone:T,merge:k}),Q_=To(),J_=To(),tx=1,ex=2;function nx(t){return{out:{noPxChangeTryDetermine:[]},kind:t}}function ix(t,e){var n=N(e,(function(e){return t.scale.parse(e)}));return"time"===t.type&&n.length>0&&(n.sort(),n.unshift(n[0]),n.push(n[n.length-1])),n}function rx(t,e){var n=t.getLabelModel().get("customValues");if(n){var i=T_(t),r=t.scale.getExtent();return{labels:N(E(ix(t,n),(function(t){return t>=r[0]&&t<=r[1]})),(function(e){var n={value:e};return{formattedLabel:i(n),rawLabel:t.scale.getLabel(n),tickValue:e,time:void 0,break:void 0}}))}}return"category"===t.type?function(t,e){var n=t.getLabelModel(),i=ax(t,n,e);return!n.get("show")||t.scale.isBlank()?{labels:[]}:i}(t,e):function(t){var e=t.scale.getTicks(),n=T_(t);return{labels:N(e,(function(e,i){return{formattedLabel:n(e,i),rawLabel:t.scale.getLabel(e),tickValue:e.value,time:e.time,break:e.break}}))}}(t)}function ox(t,e,n){var i=t.getTickModel().get("customValues");if(i){var r=t.scale.getExtent();return{ticks:E(ix(t,i),(function(t){return t>=r[0]&&t<=r[1]}))}}return"category"===t.type?function(t,e){var n,i,r=sx(t),o=C_(e),a=hx(r,o);if(a)return a;e.get("show")&&!t.scale.isBlank()||(n=[]);if(G(o))n=gx(t,o,!0);else if("auto"===o){var s=ax(t,t.getLabelModel(),nx(ex));i=s.labelCategoryInterval,n=N(s.labels,(function(t){return t.tickValue}))}else n=dx(t,i=o,!0);return cx(r,o,{ticks:n,tickCategoryInterval:i})}(t,e):{ticks:N(t.scale.getTicks(n),(function(t){return t.value}))}}function ax(t,e,n){var i,r,o=lx(t),a=C_(e),s=n.kind===tx;if(!s){var l=hx(o,a);if(l)return l}G(a)?i=gx(t,a):(r="auto"===a?function(t,e){if(e.kind===tx){var n=t.calculateCategoryInterval(e);return e.out.noPxChangeTryDetermine.push((function(){return J_(t).autoInterval=n,!0})),n}var i=J_(t).autoInterval;return null!=i?i:J_(t).autoInterval=t.calculateCategoryInterval(e)}(t,n):a,i=dx(t,r));var u={labels:i,labelCategoryInterval:r};return s?n.out.noPxChangeTryDetermine.push((function(){return cx(o,a,u),!0})):cx(o,a,u),u}var sx=ux("axisTick"),lx=ux("axisLabel");function ux(t){return function(e){return J_(e)[t]||(J_(e)[t]={list:[]})}}function hx(t,e){for(var n=0;n<t.list.length;n++)if(t.list[n].key===e)return t.list[n].value}function cx(t,e,n){return t.list.push({key:e,value:n}),n}function px(t,e,n){return null==fx(t,e,n)}function fx(t,e,n){var i=Q_(t.model),r=t.getExtent(),o=i.lastAutoInterval,a=i.lastTickCount;if(null!=o&&null!=a&&Math.abs(o-e)<=1&&Math.abs(a-n)<=1&&o>e&&i.axisExtent0===r[0]&&i.axisExtent1===r[1])return o;i.lastTickCount=n,i.lastAutoInterval=e,i.axisExtent0=r[0],i.axisExtent1=r[1]}function dx(t,e,n){var i=T_(t),r=t.scale,o=r.getExtent(),a=t.getLabelModel(),s=[],l=Math.max((e||0)+1,1),u=o[0],h=r.count();0!==u&&l>1&&h/l>2&&(u=Math.round(Math.ceil(u/l)*l));var c=D_(t),p=a.get("showMinLabel")||c,f=a.get("showMaxLabel")||c;p&&u!==o[0]&&g(o[0]);for(var d=u;d<=o[1];d+=l)g(d);function g(t){var e={value:t};s.push(n?t:{formattedLabel:i(e),rawLabel:r.getLabel(e),tickValue:t,time:void 0,break:void 0})}return f&&d-l!==o[1]&&g(o[1]),s}function gx(t,e,n){var i=t.scale,r=T_(t),o=[];return R(i.getTicks(),(function(t){var a=i.getLabel(t),s=t.value;e(t.value,a)&&o.push(n?s:{formattedLabel:r(t),rawLabel:a,tickValue:s,time:void 0,break:void 0})})),o}var vx=[0,1],yx=function(){function t(t,e,n){this.onBand=!1,this.inverse=!1,this.dim=t,this.scale=e,this._extent=n||[0,0]}return t.prototype.contain=function(t){var e=this._extent,n=Math.min(e[0],e[1]),i=Math.max(e[0],e[1]);return t>=n&&t<=i},t.prototype.containData=function(t){return this.scale.contain(this.scale.parse(t))},t.prototype.getExtent=function(){return this._extent.slice()},t.prototype.getPixelPrecision=function(t){return Ur(t||this.scale.getExtent(),this._extent)},t.prototype.setExtent=function(t,e){var n=this._extent;n[0]=t,n[1]=e},t.prototype.dataToCoord=function(t,e){var n=this._extent,i=this.scale;return t=i.normalize(i.parse(t)),this.onBand&&"ordinal"===i.type&&mx(n=n.slice(),i.count()),zr(t,vx,n,e)},t.prototype.coordToData=function(t,e){var n=this._extent,i=this.scale;this.onBand&&"ordinal"===i.type&&mx(n=n.slice(),i.count());var r=zr(t,n,vx,e);return this.scale.scale(r)},t.prototype.pointToData=function(t,e){},t.prototype.getTicksCoords=function(t){var e=(t=t||{}).tickModel||this.getTickModel(),n=N(ox(this,e,{breakTicks:t.breakTicks,pruneByBreak:t.pruneByBreak}).ticks,(function(t){return{coord:this.dataToCoord("ordinal"===this.scale.type?this.scale.getRawOrdinalNumber(t):t),tickValue:t}}),this);return function(t,e,n,i){var r=e.length;if(!t.onBand||n||!r)return;var o,a,s=t.getExtent();if(1===r)e[0].coord=s[0],e[0].onBand=!0,o=e[1]={coord:s[1],tickValue:e[0].tickValue,onBand:!0};else{var l=e[r-1].tickValue-e[0].tickValue,u=(e[r-1].coord-e[0].coord)/l;R(e,(function(t){t.coord-=u/2,t.onBand=!0}));var h=t.scale.getExtent();a=1+h[1]-e[r-1].tickValue,o={coord:e[r-1].coord+u*a,tickValue:h[1]+1,onBand:!0},e.push(o)}var c=s[0]>s[1];p(e[0].coord,s[0])&&(i?e[0].coord=s[0]:e.shift());i&&p(s[0],e[0].coord)&&e.unshift({coord:s[0],onBand:!0});p(s[1],o.coord)&&(i?o.coord=s[1]:e.pop());i&&p(o.coord,s[1])&&e.push({coord:s[1],onBand:!0});function p(t,e){return t=Wr(t),e=Wr(e),c?t>e:t<e}}(this,n,e.get("alignWithLabel"),t.clamp),n},t.prototype.getMinorTicksCoords=function(){if("ordinal"===this.scale.type)return[];var t=this.model.getModel("minorTick").get("splitNumber");return t>0&&t<100||(t=5),N(this.scale.getMinorTicks(t),(function(t){return N(t,(function(t){return{coord:this.dataToCoord(t),tickValue:t}}),this)}),this)},t.prototype.getViewLabels=function(t){return rx(this,t=t||nx(ex)).labels},t.prototype.getLabelModel=function(){return this.model.getModel("axisLabel")},t.prototype.getTickModel=function(){return this.model.getModel("axisTick")},t.prototype.getBandWidth=function(){var t=this._extent,e=this.scale.getExtent(),n=e[1]-e[0]+(this.onBand?1:0);0===n&&(n=1);var i=Math.abs(t[1]-t[0]);return Math.abs(i)/n},t.prototype.calculateCategoryInterval=function(t){return function(t,e){var n=e.kind,i=function(t){var e=t.getLabelModel();return{axisRotate:t.getRotate?t.getRotate():t.isHorizontal&&!t.isHorizontal()?90:0,labelRotate:e.get("rotate")||0,font:e.getFont()}}(t),r=T_(t),o=(i.axisRotate-i.labelRotate)/180*Math.PI,a=t.scale,s=a.getExtent(),l=a.count();if(s[1]-s[0]<1)return 0;var u=1;l>40&&(u=Math.max(1,Math.floor(l/40)));for(var h=s[0],c=t.dataToCoord(h+1)-t.dataToCoord(h),p=Math.abs(c*Math.cos(o)),f=Math.abs(c*Math.sin(o)),d=0,g=0;h<=s[1];h+=u){var v,y,m=lr(r({value:h}),i.font,"center","top");v=1.3*m.width,y=1.3*m.height,d=Math.max(d,v,7),g=Math.max(g,y,7)}var _=d/p,x=g/f;isNaN(_)&&(_=1/0),isNaN(x)&&(x=1/0);var b=Math.max(0,Math.floor(Math.min(_,x)));if(n===tx)return e.out.noPxChangeTryDetermine.push(V(px,null,t,b,l)),b;var w=fx(t,b,l);return null!=w?w:b}(this,t=t||nx(ex))},t}();function mx(t,e){var n=(t[1]-t[0])/e/2;t[0]+=n,t[1]-=n}function _x(t,e,n,i,r,o,a,s){var l=r-t,u=o-e,h=n-t,c=i-e,p=Math.sqrt(h*h+c*c),f=(l*(h/=p)+u*(c/=p))/p;s&&(f=Math.min(Math.max(f,0),1)),f*=p;var d=a[0]=t+f*h,g=a[1]=e+f*c;return Math.sqrt((d-r)*(d-r)+(g-o)*(g-o))}var xx=new _e,bx=new _e,Sx=new _e,Mx=new _e,Tx=new _e,kx=[],Cx=new _e;function Dx(t,e){if(e<=180&&e>0){e=e/180*Math.PI,xx.fromArray(t[0]),bx.fromArray(t[1]),Sx.fromArray(t[2]),_e.sub(Mx,xx,bx),_e.sub(Tx,Sx,bx);var n=Mx.len(),i=Tx.len();if(!(n<.001||i<.001)){Mx.scale(1/n),Tx.scale(1/i);var r=Mx.dot(Tx);if(Math.cos(e)<r){var o=_x(bx.x,bx.y,Sx.x,Sx.y,xx.x,xx.y,kx,!1);Cx.fromArray(kx),Cx.scaleAndAdd(Tx,o/Math.tan(Math.PI-e));var a=Sx.x!==bx.x?(Cx.x-bx.x)/(Sx.x-bx.x):(Cx.y-bx.y)/(Sx.y-bx.y);if(isNaN(a))return;a<0?_e.copy(Cx,bx):a>1&&_e.copy(Cx,Sx),Cx.toArray(t[1])}}}}function Ix(t,e,n){if(n<=180&&n>0){n=n/180*Math.PI,xx.fromArray(t[0]),bx.fromArray(t[1]),Sx.fromArray(t[2]),_e.sub(Mx,bx,xx),_e.sub(Tx,Sx,bx);var i=Mx.len(),r=Tx.len();if(!(i<.001||r<.001))if(Mx.scale(1/i),Tx.scale(1/r),Mx.dot(e)<Math.cos(n)){var o=_x(bx.x,bx.y,Sx.x,Sx.y,xx.x,xx.y,kx,!1);Cx.fromArray(kx);var a=Math.PI/2,s=a+Math.acos(Tx.dot(e))-n;if(s>=a)_e.copy(Cx,Sx);else{Cx.scaleAndAdd(Tx,o/Math.tan(Math.PI/2-s));var l=Sx.x!==bx.x?(Cx.x-bx.x)/(Sx.x-bx.x):(Cx.y-bx.y)/(Sx.y-bx.y);if(isNaN(l))return;l<0?_e.copy(Cx,bx):l>1&&_e.copy(Cx,Sx)}Cx.toArray(t[1])}}}function Ax(t,e,n,i){var r="normal"===n,o=r?t:t.ensureState(n);o.ignore=e;var a=i.get("smooth");a&&!0===a&&(a=.3),o.shape=o.shape||{},a>0&&(o.shape.smooth=a);var s=i.getModel("lineStyle").getLineStyle();r?t.useStyle(s):o.style=s}function Lx(t,e){var n=e.smooth,i=e.points;if(i)if(t.moveTo(i[0][0],i[0][1]),n>0&&i.length>=3){var r=Rt(i[0],i[1]),o=Rt(i[1],i[2]);if(!r||!o)return t.lineTo(i[1][0],i[1][1]),void t.lineTo(i[2][0],i[2][1]);var a=Math.min(r,o)*n,s=Et([],i[1],i[0],a/r),l=Et([],i[1],i[2],a/o),u=Et([],s,l,.5);t.bezierCurveTo(s[0],s[1],s[0],s[1],u[0],u[1]),t.bezierCurveTo(l[0],l[1],l[0],l[1],i[2][0],i[2][1])}else for(var h=1;h<i.length;h++)t.lineTo(i[h][0],i[h][1])}var Px=["label","labelLine","layoutOption","priority","defaultAttr","marginForce","minMarginForce","marginDefault","suggestIgnore"];function Ox(t,e,n){n=n||3,e?t.dirty|=n:t.dirty&=~n}function Rx(t,e){return e=e||3,null==t.dirty||!!(t.dirty&e)}function Nx(t){if(t)return Rx(t)&&Bx(t,t.label,t),t}function Bx(t,e,n){var i=e.getComputedTransform();t.transform=Ah(t.transform,i);var r=t.localRect=Ih(t.localRect,e.getBoundingRect()),o=e.style,a=o.margin,s=n&&n.marginForce,l=n&&n.minMarginForce,u=n&&n.marginDefault,h=o.__marginType;null==h&&u&&(a=u,h=Yh.textMargin);for(var c=0;c<4;c++)Ex[c]=h===Yh.minMargin&&l&&null!=l[c]?l[c]:s&&null!=s[c]?s[c]:a?a[c]:0;h===Yh.textMargin&&bh(r,Ex,!1,!1);var p=t.rect=Ih(t.rect,r);return i&&p.applyTransform(i),h===Yh.minMargin&&bh(p,Ex,!1,!1),t.axisAligned=Ch(i),(t.label=t.label||{}).ignore=e.ignore,Ox(t,!1),Ox(t,!0,2),t}var Ex=[0,0,0,0];function zx(t,e){for(var n=0;n<Px.length;n++){var i=Px[n];null==t[i]&&(t[i]=e[i])}return Nx(t)}function Fx(t){var e=t.obb;return e&&!Rx(t,2)||(t.obb=e=e||new $u,e.fromBoundingRect(t.localRect,t.transform),Ox(t,!1,2)),e}function Vx(t,e,n,i){return!(!t||!e)&&(!(t.label&&t.label.ignore||e.label&&e.label.ignore)&&(!!t.rect.intersect(e.rect,n,i)&&(!(!t.axisAligned||!e.axisAligned)||Fx(t).intersect(Fx(e),n,i))))}function Wx(t,e,n){var i=u.createCanvas(),r=e.getWidth(),o=e.getHeight(),a=i.style;return a&&(a.position="absolute",a.left="0",a.top="0",a.width=r+"px",a.height=o+"px",i.setAttribute("data-zr-dom-id",t)),i.width=r*n,i.height=o*n,i}var Hx=function(t){function e(e,n,i){var r,o=t.call(this)||this;o.motionBlur=!1,o.lastFrameAlpha=.7,o.dpr=1,o.virtual=!1,o.config={},o.incremental=!1,o.zlevel=0,o.maxRepaintRectCount=5,o.__dirty=!0,o.__firstTimePaint=!0,o.__used=!1,o.__drawIndex=0,o.__startIndex=0,o.__endIndex=0,o.__prevStartIndex=null,o.__prevEndIndex=null,i=i||Hi,"string"==typeof e?r=Wx(e,n,i):q(e)&&(e=(r=e).id),o.id=e,o.dom=r;var a=r.style;return a&&(yt(r),r.onselectstart=function(){return!1},a.padding="0",a.margin="0",a.borderWidth="0"),o.painter=n,o.dpr=i,o}return n(e,t),e.prototype.getElementCount=function(){return this.__endIndex-this.__startIndex},e.prototype.afterBrush=function(){this.__prevStartIndex=this.__startIndex,this.__prevEndIndex=this.__endIndex},e.prototype.initContext=function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},e.prototype.setUnpainted=function(){this.__firstTimePaint=!0},e.prototype.createBackBuffer=function(){var t=this.dpr;this.domBack=Wx("back-"+this.id,this.painter,t),this.ctxBack=this.domBack.getContext("2d"),1!==t&&this.ctxBack.scale(t,t)},e.prototype.createRepaintRects=function(t,e,n,i){if(this.__firstTimePaint)return this.__firstTimePaint=!1,null;var r,o=[],a=this.maxRepaintRectCount,s=!1,l=new Oe(0,0,0,0);function u(t){if(t.isFinite()&&!t.isZero())if(0===o.length){(e=new Oe(0,0,0,0)).copy(t),o.push(e)}else{for(var e,n=!1,i=1/0,r=0,u=0;u<o.length;++u){var h=o[u];if(h.intersect(t)){var c=new Oe(0,0,0,0);c.copy(h),c.union(t),o[u]=c,n=!0;break}if(s){l.copy(t),l.union(h);var p=t.width*t.height,f=h.width*h.height,d=l.width*l.height-p-f;d<i&&(i=d,r=u)}}if(s&&(o[r].union(t),n=!0),!n)(e=new Oe(0,0,0,0)).copy(t),o.push(e);s||(s=o.length>=a)}}for(var h=this.__startIndex;h<this.__endIndex;++h){if(f=t[h]){var c=f.shouldBePainted(n,i,!0,!0);(d=f.__isRendered&&(1&f.__dirty||!c)?f.getPrevPaintRect():null)&&u(d);var p=c&&(1&f.__dirty||!f.__isRendered)?f.getPaintRect():null;p&&u(p)}}for(h=this.__prevStartIndex;h<this.__prevEndIndex;++h){var f,d;c=(f=e[h])&&f.shouldBePainted(n,i,!0,!0);if(f&&(!c||!f.__zr)&&f.__isRendered)(d=f.getPrevPaintRect())&&u(d)}do{r=!1;for(h=0;h<o.length;)if(o[h].isZero())o.splice(h,1);else{for(var g=h+1;g<o.length;)o[h].intersect(o[g])?(r=!0,o[h].union(o[g]),o.splice(g,1)):g++;h++}}while(r);return this._paintRects=o,o},e.prototype.debugGetPaintRects=function(){return(this._paintRects||[]).slice()},e.prototype.resize=function(t,e){var n=this.dpr,i=this.dom,r=i.style,o=this.domBack;r&&(r.width=t+"px",r.height=e+"px"),i.width=t*n,i.height=e*n,o&&(o.width=t*n,o.height=e*n,1!==n&&this.ctxBack.scale(n,n))},e.prototype.clear=function(t,e,n){var i=this.dom,r=this.ctx,o=i.width,a=i.height;e=e||this.clearColor;var s=this.motionBlur&&!t,l=this.lastFrameAlpha,u=this.dpr,h=this;s&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(i,0,0,o/u,a/u));var c=this.domBack;function p(t,n,i,o){if(r.clearRect(t,n,i,o),e&&"transparent"!==e){var a=void 0;if($(e))a=(e.global||e.__width===i&&e.__height===o)&&e.__canvasGradient||ev(r,e,{x:0,y:0,width:i,height:o}),e.__canvasGradient=a,e.__width=i,e.__height=o;else Q(e)&&(e.scaleX=e.scaleX||u,e.scaleY=e.scaleY||u,a=cv(r,e,{dirty:function(){h.setUnpainted(),h.painter.refresh()}}));r.save(),r.fillStyle=a||e,r.fillRect(t,n,i,o),r.restore()}s&&(r.save(),r.globalAlpha=l,r.drawImage(c,t,n,i,o),r.restore())}!n||s?p(0,0,o,a):n.length&&R(n,(function(t){p(t.x*u,t.y*u,t.width*u,t.height*u)}))},e}(Ut),Gx=1e5,Ux=314159,Xx=.01;var Yx=function(){function t(t,e,n,i){this.type="canvas",this._zlevelList=[],this._prevDisplayList=[],this._layers={},this._layerConfig={},this._needsManuallyCompositing=!1,this.type="canvas";var r=!t.nodeName||"CANVAS"===t.nodeName.toUpperCase();this._opts=n=C({},n||{}),this.dpr=n.devicePixelRatio||Hi,this._singleCanvas=r,this.root=t,t.style&&(yt(t),t.innerHTML=""),this.storage=e;var o=this._zlevelList;this._prevDisplayList=[];var a=this._layers;if(r){var s=t,l=s.width,u=s.height;null!=n.width&&(l=n.width),null!=n.height&&(u=n.height),this.dpr=n.devicePixelRatio||1,s.width=l*this.dpr,s.height=u*this.dpr,this._width=l,this._height=u;var h=new Hx(s,this,this.dpr);h.__builtin__=!0,h.initContext(),a[314159]=h,h.zlevel=Ux,o.push(Ux),this._domRoot=t}else{this._width=iv(t,0,n),this._height=iv(t,1,n);var c=this._domRoot=function(t,e){var n=document.createElement("div");return n.style.cssText=["position:relative","width:"+t+"px","height:"+e+"px","padding:0","margin:0","border-width:0"].join(";")+";",n}(this._width,this._height);t.appendChild(c)}}return t.prototype.getType=function(){return"canvas"},t.prototype.isSingleCanvas=function(){return this._singleCanvas},t.prototype.getViewportRoot=function(){return this._domRoot},t.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},t.prototype.refresh=function(t){var e=this.storage.getDisplayList(!0),n=this._prevDisplayList,i=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,n,t,this._redrawId);for(var r=0;r<i.length;r++){var o=i[r],a=this._layers[o];if(!a.__builtin__&&a.refresh){var s=0===r?this._backgroundColor:null;a.refresh(s)}}return this._opts.useDirtyRect&&(this._prevDisplayList=e.slice()),this},t.prototype.refreshHover=function(){this._paintHoverList(this.storage.getDisplayList(!1))},t.prototype._paintHoverList=function(t){var e=t.length,n=this._hoverlayer;if(n&&n.clear(),e){for(var i,r={inHover:!0,viewWidth:this._width,viewHeight:this._height},o=0;o<e;o++){var a=t[o];a.__inHover&&(n||(n=this._hoverlayer=this.getLayer(Gx)),i||(i=n.ctx).save(),xv(i,a,r,o===e-1))}i&&i.restore()}},t.prototype.getHoverLayer=function(){return this.getLayer(Gx)},t.prototype.paintOne=function(t,e){_v(t,e)},t.prototype._paintList=function(t,e,n,i){if(this._redrawId===i){n=n||!1,this._updateLayerStatus(t);var r=this._doPaintList(t,e,n),o=r.finished,a=r.needsRefreshHover;if(this._needsManuallyCompositing&&this._compositeManually(),a&&this._paintHoverList(t),o)this.eachLayer((function(t){t.afterBrush&&t.afterBrush()}));else{var s=this;on((function(){s._paintList(t,e,n,i)}))}}},t.prototype._compositeManually=function(){var t=this.getLayer(Ux).ctx,e=this._domRoot.width,n=this._domRoot.height;t.clearRect(0,0,e,n),this.eachBuiltinLayer((function(i){i.virtual&&t.drawImage(i.dom,0,0,e,n)}))},t.prototype._doPaintList=function(t,e,n){for(var i=this,o=[],a=this._opts.useDirtyRect,s=0;s<this._zlevelList.length;s++){var l=this._zlevelList[s],u=this._layers[l];u.__builtin__&&u!==this._hoverlayer&&(u.__dirty||n)&&o.push(u)}for(var h=!0,c=!1,p=function(r){var s,l=o[r],u=l.ctx,p=a&&l.createRepaintRects(t,e,f._width,f._height),d=n?l.__startIndex:l.__drawIndex,g=!n&&l.incremental&&Date.now,v=g&&Date.now(),y=l.zlevel===f._zlevelList[0]?f._backgroundColor:null;if(l.__startIndex===l.__endIndex)l.clear(!1,y,p);else if(d===l.__startIndex){var m=t[d];m.incremental&&m.notClear&&!n||l.clear(!1,y,p)}-1===d&&(console.error("For some unknown reason. drawIndex is -1"),d=l.__startIndex);var _=function(e){var n={inHover:!1,allClipped:!1,prevEl:null,viewWidth:i._width,viewHeight:i._height};for(s=d;s<l.__endIndex;s++){var r=t[s];if(r.__inHover&&(c=!0),i._doPaintEl(r,l,a,e,n,s===l.__endIndex-1),g)if(Date.now()-v>15)break}n.prevElClipPaths&&u.restore()};if(p)if(0===p.length)s=l.__endIndex;else for(var x=f.dpr,b=0;b<p.length;++b){var w=p[b];u.save(),u.beginPath(),u.rect(w.x*x,w.y*x,w.width*x,w.height*x),u.clip(),_(w),u.restore()}else u.save(),_(),u.restore();l.__drawIndex=s,l.__drawIndex<l.__endIndex&&(h=!1)},f=this,d=0;d<o.length;d++)p(d);return r.wxa&&R(this._layers,(function(t){t&&t.ctx&&t.ctx.draw&&t.ctx.draw()})),{finished:h,needsRefreshHover:c}},t.prototype._doPaintEl=function(t,e,n,i,r,o){var a=e.ctx;if(n){var s=t.getPaintRect();(!i||s&&s.intersect(i))&&(xv(a,t,r,o),t.setPrevPaintRect(s))}else xv(a,t,r,o)},t.prototype.getLayer=function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=Ux);var n=this._layers[t];return n||((n=new Hx("zr_"+t,this,this.dpr)).zlevel=t,n.__builtin__=!0,this._layerConfig[t]?k(n,this._layerConfig[t],!0):this._layerConfig[t-Xx]&&k(n,this._layerConfig[t-Xx],!0),e&&(n.virtual=e),this.insertLayer(t,n),n.initContext()),n},t.prototype.insertLayer=function(t,e){var n=this._layers,i=this._zlevelList,r=i.length,o=this._domRoot,a=null,s=-1;if(!n[t]&&function(t){return!!t&&(!!t.__builtin__||"function"==typeof t.resize&&"function"==typeof t.refresh)}(e)){if(r>0&&t>i[0]){for(s=0;s<r-1&&!(i[s]<t&&i[s+1]>t);s++);a=n[i[s]]}if(i.splice(s+1,0,t),n[t]=e,!e.virtual)if(a){var l=a.dom;l.nextSibling?o.insertBefore(e.dom,l.nextSibling):o.appendChild(e.dom)}else o.firstChild?o.insertBefore(e.dom,o.firstChild):o.appendChild(e.dom);e.painter||(e.painter=this)}},t.prototype.eachLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var r=n[i];t.call(e,this._layers[r],r)}},t.prototype.eachBuiltinLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var r=n[i],o=this._layers[r];o.__builtin__&&t.call(e,o,r)}},t.prototype.eachOtherLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var r=n[i],o=this._layers[r];o.__builtin__||t.call(e,o,r)}},t.prototype.getLayers=function(){return this._layers},t.prototype._updateLayerStatus=function(t){function e(t){o&&(o.__endIndex!==t&&(o.__dirty=!0),o.__endIndex=t)}if(this.eachBuiltinLayer((function(t,e){t.__dirty=t.__used=!1})),this._singleCanvas)for(var n=1;n<t.length;n++){if((s=t[n]).zlevel!==t[n-1].zlevel||s.incremental){this._needsManuallyCompositing=!0;break}}var i,r,o=null,a=0;for(r=0;r<t.length;r++){var s,l=(s=t[r]).zlevel,u=void 0;i!==l&&(i=l,a=0),s.incremental?((u=this.getLayer(l+.001,this._needsManuallyCompositing)).incremental=!0,a=1):u=this.getLayer(l+(a>0?Xx:0),this._needsManuallyCompositing),u.__builtin__||M("ZLevel "+l+" has been used by unkown layer "+u.id),u!==o&&(u.__used=!0,u.__startIndex!==r&&(u.__dirty=!0),u.__startIndex=r,u.incremental?u.__drawIndex=-1:u.__drawIndex=r,e(r),o=u),1&s.__dirty&&!s.__inHover&&(u.__dirty=!0,u.incremental&&u.__drawIndex<0&&(u.__drawIndex=r))}e(r),this.eachBuiltinLayer((function(t,e){!t.__used&&t.getElementCount()>0&&(t.__dirty=!0,t.__startIndex=t.__endIndex=t.__drawIndex=0),t.__dirty&&t.__drawIndex<0&&(t.__drawIndex=t.__startIndex)}))},t.prototype.clear=function(){return this.eachBuiltinLayer(this._clearLayer),this},t.prototype._clearLayer=function(t){t.clear()},t.prototype.setBackgroundColor=function(t){this._backgroundColor=t,R(this._layers,(function(t){t.setUnpainted()}))},t.prototype.configLayer=function(t,e){if(e){var n=this._layerConfig;n[t]?k(n[t],e,!0):n[t]=e;for(var i=0;i<this._zlevelList.length;i++){var r=this._zlevelList[i];if(r===t||r===t+Xx)k(this._layers[r],n[t],!0)}}},t.prototype.delLayer=function(t){var e=this._layers,n=this._zlevelList,i=e[t];i&&(i.dom.parentNode.removeChild(i.dom),delete e[t],n.splice(A(n,t),1))},t.prototype.resize=function(t,e){if(this._domRoot.style){var n=this._domRoot;n.style.display="none";var i=this._opts,r=this.root;if(null!=t&&(i.width=t),null!=e&&(i.height=e),t=iv(r,0,i),e=iv(r,1,i),n.style.display="",this._width!==t||e!==this._height){for(var o in n.style.width=t+"px",n.style.height=e+"px",this._layers)this._layers.hasOwnProperty(o)&&this._layers[o].resize(t,e);this.refresh(!0)}this._width=t,this._height=e}else{if(null==t||null==e)return;this._width=t,this._height=e,this.getLayer(Ux).resize(t,e)}return this},t.prototype.clearLayer=function(t){var e=this._layers[t];e&&e.clear()},t.prototype.dispose=function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},t.prototype.getRenderedCanvas=function(t){if(t=t||{},this._singleCanvas&&!this._compositeManually)return this._layers[314159].dom;var e=new Hx("image",this,t.pixelRatio||this.dpr);e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor);var n=e.ctx;if(t.pixelRatio<=this.dpr){this.refresh();var i=e.dom.width,r=e.dom.height;this.eachLayer((function(t){t.__builtin__?n.drawImage(t.dom,0,0,i,r):t.renderToCanvas&&(n.save(),t.renderToCanvas(n),n.restore())}))}else for(var o={inHover:!1,viewWidth:this._width,viewHeight:this._height},a=this.storage.getDisplayList(!0),s=0,l=a.length;s<l;s++){var u=a[s];xv(n,u,o,s===l-1)}return e.dom},t.prototype.getWidth=function(){return this._width},t.prototype.getHeight=function(){return this._height},t}();var qx=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n.hasSymbolVisual=!0,n}return n(e,t),e.prototype.getInitialData=function(t){return Im(null,this,{useEncodeDefaulter:!0})},e.prototype.getLegendIcon=function(t){var e=new Tr,n=Qg("line",0,t.itemHeight/2,t.itemWidth,0,t.lineStyle.stroke,!1);e.add(n),n.setStyle(t.lineStyle);var i=this.getData().getVisual("symbol"),r=this.getData().getVisual("symbolRotate"),o="none"===i?"circle":i,a=.8*t.itemHeight,s=Qg(o,(t.itemWidth-a)/2,(t.itemHeight-a)/2,a,a,t.itemStyle.fill);e.add(s),s.setStyle(t.itemStyle);var l="inherit"===t.iconRotate?r:t.iconRotate||0;return s.rotation=l*Math.PI/180,s.setOrigin([t.itemWidth/2,t.itemHeight/2]),o.indexOf("empty")>-1&&(s.style.stroke=s.style.fill,s.style.fill=xp.color.neutral00,s.style.lineWidth=2),e},e.type="series.line",e.dependencies=["grid","polar"],e.defaultOption={z:3,coordinateSystem:"cartesian2d",legendHoverLink:!0,clip:!0,label:{position:"top"},endLabel:{show:!1,valueAnimation:!0,distance:8},lineStyle:{width:2,type:"solid"},emphasis:{scale:!0},step:!1,smooth:!1,smoothMonotone:null,symbol:"emptyCircle",symbolSize:6,symbolRotate:null,showSymbol:!0,showAllSymbol:"auto",connectNulls:!1,sampling:"none",animationEasing:"linear",progressive:0,hoverLayerThreshold:1/0,universalTransition:{divideShape:"clone"},triggerLineEvent:!1},e}(zd);function Zx(t,e){var n=t.mapDimensionsAll("defaultedLabel"),i=n.length;if(1===i){var r=od(t,e,n[0]);return null!=r?r+"":null}if(i){for(var o=[],a=0;a<n.length;a++)o.push(od(t,e,n[a]));return o.join(" ")}}function jx(t,e){var n=t.mapDimensionsAll("defaultedLabel");if(!H(e))return e+"";for(var i=[],r=0;r<n.length;r++){var o=t.getDimensionIndex(n[r]);o>=0&&i.push(e[o])}return i.join(" ")}var Kx=function(t){function e(e,n,i,r){var o=t.call(this)||this;return o.updateData(e,n,i,r),o}return n(e,t),e.prototype._createSymbol=function(t,e,n,i,r,o){this.removeAll();var a=Qg(t,-1,-1,2,2,null,o);a.attr({z2:nt(r,100),culling:!0,scaleX:i[0]/2,scaleY:i[1]/2}),a.drift=$x,this._symbolType=t,this.add(a)},e.prototype.stopSymbolAnimation=function(t){this.childAt(0).stopAnimation(null,t)},e.prototype.getSymbolType=function(){return this._symbolType},e.prototype.getSymbolPath=function(){return this.childAt(0)},e.prototype.highlight=function(){xl(this.childAt(0))},e.prototype.downplay=function(){bl(this.childAt(0))},e.prototype.setZ=function(t,e){var n=this.childAt(0);n.zlevel=t,n.z=e},e.prototype.setDraggable=function(t,e){var n=this.childAt(0);n.draggable=t,n.cursor=!e&&t?"move":n.cursor},e.prototype.updateData=function(t,n,i,r){this.silent=!1;var o=t.getItemVisual(n,"symbol")||"circle",a=t.hostModel,s=e.getSymbolSize(t,n),l=e.getSymbolZ2(t,n),u=o!==this._symbolType,h=r&&r.disableAnimation;if(u){var c=t.getItemVisual(n,"symbolKeepAspect");this._createSymbol(o,t,n,s,l,c)}else{(f=this.childAt(0)).silent=!1;var p={scaleX:s[0]/2,scaleY:s[1]/2};h?f.attr(p):nh(f,p,a,n),lh(f)}if(this._updateCommon(t,n,s,i,r),u){var f=this.childAt(0);if(!h){p={scaleX:this._sizeX,scaleY:this._sizeY,style:{opacity:f.style.opacity}};f.scaleX=f.scaleY=0,f.style.opacity=0,ih(f,p,a,n)}}h&&this.childAt(0).stopAnimation("leave")},e.prototype._updateCommon=function(t,e,n,i,r){var o,a,s,l,u,h,c,p,f,d=this.childAt(0),g=t.hostModel;if(i&&(o=i.emphasisItemStyle,a=i.blurItemStyle,s=i.selectItemStyle,l=i.focus,u=i.blurScope,c=i.labelStatesModels,p=i.hoverScale,f=i.cursorStyle,h=i.emphasisDisabled),!i||t.hasItemOption){var v=i&&i.itemModel?i.itemModel:t.getItemModel(e),y=v.getModel("emphasis");o=y.getModel("itemStyle").getItemStyle(),s=v.getModel(["select","itemStyle"]).getItemStyle(),a=v.getModel(["blur","itemStyle"]).getItemStyle(),l=y.get("focus"),u=y.get("blurScope"),h=y.get("disabled"),c=Bh(v),p=y.getShallow("scale"),f=v.getShallow("cursor")}var m=t.getItemVisual(e,"symbolRotate");d.attr("rotation",(m||0)*Math.PI/180||0);var _=Jg(t.getItemVisual(e,"symbolOffset"),n);_&&(d.x=_[0],d.y=_[1]),f&&d.attr("cursor",f);var x=t.getItemVisual(e,"style"),b=x.fill;if(d instanceof Ms){var w=d.style;d.useStyle(C({image:w.image,x:w.x,y:w.y,width:w.width,height:w.height},x))}else d.__isEmptyBrush?d.useStyle(C({},x)):d.useStyle(x),d.style.decal=null,d.setColor(b,r&&r.symbolInnerColor),d.style.strokeNoScale=!0;var S=t.getItemVisual(e,"liftZ"),M=this._z2;null!=S?null==M&&(this._z2=d.z2,d.z2+=S):null!=M&&(d.z2=M,this._z2=null);var T=r&&r.useNameLabel;Nh(d,c,{labelFetcher:g,labelDataIndex:e,defaultText:function(e){return T?t.getName(e):Zx(t,e)},inheritColor:b,defaultOpacity:x.opacity}),this._sizeX=n[0]/2,this._sizeY=n[1]/2;var k=d.ensureState("emphasis");k.style=o,d.ensureState("select").style=s,d.ensureState("blur").style=a;var D=null==p||!0===p?Math.max(1.1,3/this._sizeY):isFinite(p)&&p>0?+p:1;k.scaleX=this._sizeX*D,k.scaleY=this._sizeY*D,this.setSymbolScale(1),Ol(this,l,u,h)},e.prototype.setSymbolScale=function(t){this.scaleX=this.scaleY=t},e.prototype.fadeOut=function(t,e,n){var i=this.childAt(0),r=Ys(this).dataIndex,o=n&&n.animation;if(this.silent=i.silent=!0,n&&n.fadeLabel){var a=i.getTextContent();a&&oh(a,{style:{opacity:0}},e,{dataIndex:r,removeOpt:o,cb:function(){i.removeTextContent()}})}else i.removeTextContent();oh(i,{style:{opacity:0},scaleX:0,scaleY:0},e,{dataIndex:r,cb:t,removeOpt:o})},e.getSymbolSize=function(t,e){return H(n=t.getItemVisual(e,"symbolSize"))||(n=[+n,+n]),[n[0]||0,n[1]||0];var n},e.getSymbolZ2=function(t,e){return t.getItemVisual(e,"z2")},e}(Tr);function $x(t,e){this.parent.drift(t,e)}function Qx(t,e,n,i){return e&&!isNaN(e[0])&&!isNaN(e[1])&&!(i.isIgnore&&i.isIgnore(n))&&!(i.clipShape&&!i.clipShape.contain(e[0],e[1]))&&"none"!==t.getItemVisual(n,"symbol")}function Jx(t){return null==t||q(t)||(t={isIgnore:t}),t||{}}function tb(t){var e=t.hostModel,n=e.getModel("emphasis");return{emphasisItemStyle:n.getModel("itemStyle").getItemStyle(),blurItemStyle:e.getModel(["blur","itemStyle"]).getItemStyle(),selectItemStyle:e.getModel(["select","itemStyle"]).getItemStyle(),focus:n.get("focus"),blurScope:n.get("blurScope"),emphasisDisabled:n.get("disabled"),hoverScale:n.get("scale"),labelStatesModels:Bh(e),cursorStyle:e.get("cursor")}}var eb=function(){function t(t){this.group=new Tr,this._SymbolCtor=t||Kx}return t.prototype.updateData=function(t,e){this._progressiveEls=null,e=Jx(e);var n=this.group,i=t.hostModel,r=this._data,o=this._SymbolCtor,a=e.disableAnimation,s=tb(t),l={disableAnimation:a},u=e.getSymbolPoint||function(e){return t.getItemLayout(e)};r||n.removeAll(),t.diff(r).add((function(i){var r=u(i);if(Qx(t,r,i,e)){var a=new o(t,i,s,l);a.setPosition(r),t.setItemGraphicEl(i,a),n.add(a)}})).update((function(h,c){var p=r.getItemGraphicEl(c),f=u(h);if(Qx(t,f,h,e)){var d=t.getItemVisual(h,"symbol")||"circle",g=p&&p.getSymbolType&&p.getSymbolType();if(!p||g&&g!==d)n.remove(p),(p=new o(t,h,s,l)).setPosition(f);else{p.updateData(t,h,s,l);var v={x:f[0],y:f[1]};a?p.attr(v):nh(p,v,i)}n.add(p),t.setItemGraphicEl(h,p)}else n.remove(p)})).remove((function(t){var e=r.getItemGraphicEl(t);e&&e.fadeOut((function(){n.remove(e)}),i)})).execute(),this._getSymbolPoint=u,this._data=t},t.prototype.updateLayout=function(){var t=this,e=this._data;e&&e.eachItemGraphicEl((function(e,n){var i=t._getSymbolPoint(n);e.setPosition(i),e.markRedraw()}))},t.prototype.incrementalPrepareUpdate=function(t){this._seriesScope=tb(t),this._data=null,this.group.removeAll()},t.prototype.incrementalUpdate=function(t,e,n){function i(t){t.isGroup||(t.incremental=!0,t.ensureState("emphasis").hoverLayer=!0)}this._progressiveEls=[],n=Jx(n);for(var r=t.start;r<t.end;r++){var o=e.getItemLayout(r);if(Qx(e,o,r,n)){var a=new this._SymbolCtor(e,r,this._seriesScope);a.traverse(i),a.setPosition(o),this.group.add(a),e.setItemGraphicEl(r,a),this._progressiveEls.push(a)}}},t.prototype.eachRendered=function(t){kh(this._progressiveEls||this.group,t)},t.prototype.remove=function(t){var e=this.group,n=this._data;n&&t?n.eachItemGraphicEl((function(t){t.fadeOut((function(){e.remove(t)}),n.hostModel)})):e.removeAll()},t}();function nb(t,e,n){var i=t.getBaseAxis(),r=t.getOtherAxis(i),o=function(t,e){var n=0,i=t.scale.getExtent();"start"===e?n=i[0]:"end"===e?n=i[1]:Y(e)&&!isNaN(e)?n=e:i[0]>0?n=i[0]:i[1]<0&&(n=i[1]);return n}(r,n),a=i.dim,s=r.dim,l=e.mapDimension(s),u=e.mapDimension(a),h="x"===s||"radius"===s?1:0,c=N(t.dimensions,(function(t){return e.mapDimension(t)})),p=!1,f=e.getCalculationInfo("stackResultDimension");return Cm(e,c[0])&&(p=!0,c[0]=f),Cm(e,c[1])&&(p=!0,c[1]=f),{dataDimsForPoint:c,valueStart:o,valueAxisDim:s,baseAxisDim:a,stacked:!!p,valueDim:l,baseDim:u,baseDataOffset:h,stackedOverDimension:e.getCalculationInfo("stackedOverDimension")}}function ib(t,e,n,i){var r=NaN;t.stacked&&(r=n.get(n.getCalculationInfo("stackedOverDimension"),i)),isNaN(r)&&(r=t.valueStart);var o=t.baseDataOffset,a=[];return a[o]=n.get(t.baseDim,i),a[1-o]=r,e.dataToPoint(a)}var rb=Math.min,ob=Math.max;function ab(t,e){return isNaN(t)||isNaN(e)}function sb(t,e,n,i,r,o,a,s,l){for(var u,h,c,p,f,d,g=n,v=0;v<i;v++){var y=e[2*g],m=e[2*g+1];if(g>=r||g<0)break;if(ab(y,m)){if(l){g+=o;continue}break}if(g===n)t[o>0?"moveTo":"lineTo"](y,m),c=y,p=m;else{var _=y-u,x=m-h;if(_*_+x*x<.5){g+=o;continue}if(a>0){for(var b=g+o,w=e[2*b],S=e[2*b+1];w===y&&S===m&&v<i;)v++,g+=o,w=e[2*(b+=o)],S=e[2*b+1],_=(y=e[2*g])-u,x=(m=e[2*g+1])-h;var M=v+1;if(l)for(;ab(w,S)&&M<i;)M++,w=e[2*(b+=o)],S=e[2*b+1];var T=.5,k=0,C=0,D=void 0,I=void 0;if(M>=i||ab(w,S))f=y,d=m;else{k=w-u,C=S-h;var A=y-u,L=w-y,P=m-h,O=S-m,R=void 0,N=void 0;if("x"===s){var B=k>0?1:-1;f=y-B*(R=Math.abs(A))*a,d=m,D=y+B*(N=Math.abs(L))*a,I=m}else if("y"===s){var E=C>0?1:-1;f=y,d=m-E*(R=Math.abs(P))*a,D=y,I=m+E*(N=Math.abs(O))*a}else R=Math.sqrt(A*A+P*P),f=y-k*a*(1-(T=(N=Math.sqrt(L*L+O*O))/(N+R))),d=m-C*a*(1-T),I=m+C*a*T,D=rb(D=y+k*a*T,ob(w,y)),I=rb(I,ob(S,m)),D=ob(D,rb(w,y)),d=m-(C=(I=ob(I,rb(S,m)))-m)*R/N,f=rb(f=y-(k=D-y)*R/N,ob(u,y)),d=rb(d,ob(h,m)),D=y+(k=y-(f=ob(f,rb(u,y))))*N/R,I=m+(C=m-(d=ob(d,rb(h,m))))*N/R}t.bezierCurveTo(c,p,f,d,y,m),c=D,p=I}else t.lineTo(y,m)}u=y,h=m,g+=o}return v}var lb=function(){this.smooth=0,this.smoothConstraint=!0},ub=function(t){function e(e){var n=t.call(this,e)||this;return n.type="ec-polyline",n}return n(e,t),e.prototype.getDefaultStyle=function(){return{stroke:xp.color.neutral99,fill:null}},e.prototype.getDefaultShape=function(){return new lb},e.prototype.buildPath=function(t,e){var n=e.points,i=0,r=n.length/2;if(e.connectNulls){for(;r>0&&ab(n[2*r-2],n[2*r-1]);r--);for(;i<r&&ab(n[2*i],n[2*i+1]);i++);}for(;i<r;)i+=sb(t,n,i,r,r,1,e.smooth,e.smoothMonotone,e.connectNulls)+1},e.prototype.getPointOn=function(t,e){this.path||(this.createPathProxy(),this.buildPath(this.path,this.shape));for(var n,i,r=this.path.data,o=Ja.CMD,a="x"===e,s=[],l=0;l<r.length;){var u=void 0,h=void 0,c=void 0,p=void 0,f=void 0,d=void 0,g=void 0;switch(r[l++]){case o.M:n=r[l++],i=r[l++];break;case o.L:if(u=r[l++],h=r[l++],(g=a?(t-n)/(u-n):(t-i)/(h-i))<=1&&g>=0){var v=a?(h-i)*g+i:(u-n)*g+n;return a?[t,v]:[v,t]}n=u,i=h;break;case o.C:u=r[l++],h=r[l++],c=r[l++],p=r[l++],f=r[l++],d=r[l++];var y=a?xn(n,u,c,f,t,s):xn(i,h,p,d,t,s);if(y>0)for(var m=0;m<y;m++){var _=s[m];if(_<=1&&_>=0){v=a?mn(i,h,p,d,_):mn(n,u,c,f,_);return a?[t,v]:[v,t]}}n=f,i=d}}},e}(_s),hb=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e}(lb),cb=function(t){function e(e){var n=t.call(this,e)||this;return n.type="ec-polygon",n}return n(e,t),e.prototype.getDefaultShape=function(){return new hb},e.prototype.buildPath=function(t,e){var n=e.points,i=e.stackedOnPoints,r=0,o=n.length/2,a=e.smoothMonotone;if(e.connectNulls){for(;o>0&&ab(n[2*o-2],n[2*o-1]);o--);for(;r<o&&ab(n[2*r],n[2*r+1]);r++);}for(;r<o;){var s=sb(t,n,r,o,o,1,e.smooth,a,e.connectNulls);sb(t,i,r+s-1,s,o,-1,e.stackedOnSmooth,a,e.connectNulls),r+=s+1,t.closePath()}},e}(_s);function pb(t,e,n,i,r){var o=t.getArea(),a=o.x,s=o.y,l=o.width,u=o.height,h=n.get(["lineStyle","width"])||0;a-=h/2,s-=h/2,l+=h,u+=h,l=Math.ceil(l),a!==Math.floor(a)&&(a=Math.floor(a),l++);var c=new As({shape:{x:a,y:s,width:l,height:u}});if(e){var p=t.getBaseAxis(),f=p.isHorizontal(),d=p.inverse;f?(d&&(c.shape.x+=l),c.shape.width=0):(d||(c.shape.y+=u),c.shape.height=0);var g=G(r)?function(t){r(t,c)}:null;ih(c,{shape:{width:l,height:u,x:a,y:s}},n,null,i,g)}return c}function fb(t,e,n){var i=t.getArea(),r=Wr(i.r0,1),o=Wr(i.r,1),a=new wu({shape:{cx:Wr(t.cx,1),cy:Wr(t.cy,1),r0:r,r:o,startAngle:i.startAngle,endAngle:i.endAngle,clockwise:i.clockwise}});e&&("angle"===t.getBaseAxis().dim?a.shape.endAngle=i.startAngle:a.shape.r=r,ih(a,{shape:{endAngle:i.endAngle,r:o}},n));return a}function db(t,e){return t.type===e}function gb(t,e){if(t.length===e.length){for(var n=0;n<t.length;n++)if(t[n]!==e[n])return;return!0}}function vb(t){for(var e=1/0,n=1/0,i=-1/0,r=-1/0,o=0;o<t.length;){var a=t[o++],s=t[o++];isNaN(a)||(e=Math.min(a,e),i=Math.max(a,i)),isNaN(s)||(n=Math.min(s,n),r=Math.max(s,r))}return[[e,n],[i,r]]}function yb(t,e){var n=vb(t),i=n[0],r=n[1],o=vb(e),a=o[0],s=o[1];return Math.max(Math.abs(i[0]-a[0]),Math.abs(i[1]-a[1]),Math.abs(r[0]-s[0]),Math.abs(r[1]-s[1]))}function mb(t){return Y(t)?t:t?.5:0}function _b(t,e,n,i,r){var o=n.getBaseAxis(),a="x"===o.dim||"radius"===o.dim?0:1,s=[],l=0,u=[],h=[],c=[],p=[];if(r){for(l=0;l<t.length;l+=2){var f=e||t;isNaN(f[l])||isNaN(f[l+1])||p.push(t[l],t[l+1])}t=p}for(l=0;l<t.length-2;l+=2)switch(c[0]=t[l+2],c[1]=t[l+3],h[0]=t[l],h[1]=t[l+1],s.push(h[0],h[1]),i){case"end":u[a]=c[a],u[1-a]=h[1-a],s.push(u[0],u[1]);break;case"middle":var d=(h[a]+c[a])/2,g=[];u[a]=g[a]=d,u[1-a]=h[1-a],g[1-a]=c[1-a],s.push(u[0],u[1]),s.push(g[0],g[1]);break;default:u[a]=h[a],u[1-a]=c[1-a],s.push(u[0],u[1])}return s.push(t[l++],t[l++]),s}function xb(t,e,n){var i=t.getVisual("visualMeta");if(i&&i.length&&t.count()&&"cartesian2d"===e.type){for(var r,o,a=i.length-1;a>=0;a--){var s=t.getDimensionInfo(i[a].dimension);if("x"===(r=s&&s.coordDim)||"y"===r){o=i[a];break}}if(o){var l=e.getAxis(r),u=N(o.stops,(function(t){return{coord:l.toGlobalCoord(l.dataToCoord(t.value)),color:t.color}})),h=u.length,c=o.outerColors.slice();h&&u[0].coord>u[h-1].coord&&(u.reverse(),c.reverse());var p=function(t,e){var n,i,r=[],o=t.length;function a(t,e,n){var i=t.coord;return{coord:n,color:Qn((n-i)/(e.coord-i),[t.color,e.color])}}for(var s=0;s<o;s++){var l=t[s],u=l.coord;if(u<0)n=l;else{if(u>e){i?r.push(a(i,l,e)):n&&r.push(a(n,l,0),a(n,l,e));break}n&&(r.push(a(n,l,0)),n=null),r.push(l),i=l}}return r}(u,"x"===r?n.getWidth():n.getHeight()),f=p.length;if(!f&&h)return u[0].coord<0?c[1]?c[1]:u[h-1].color:c[0]?c[0]:u[0].color;var d=p[0].coord-10,g=p[f-1].coord+10,v=g-d;if(v<.001)return"transparent";R(p,(function(t){t.offset=(t.coord-d)/v})),p.push({offset:f?p[f-1].offset:.5,color:c[1]||"transparent"}),p.unshift({offset:f?p[0].offset:.5,color:c[0]||"transparent"});var y=new Wu(0,0,0,0,p,!0);return y[r]=d,y[r+"2"]=g,y}}}function bb(t,e,n){var i=t.get("showAllSymbol"),r="auto"===i;if(!i||r){var o=n.getAxesByScale("ordinal")[0];if(o&&(!r||!function(t,e){var n=t.getExtent(),i=Math.abs(n[1]-n[0])/t.scale.count();isNaN(i)&&(i=0);for(var r=e.count(),o=Math.max(1,Math.round(r/5)),a=0;a<r;a+=o)if(1.5*Kx.getSymbolSize(e,a)[t.isHorizontal()?1:0]>i)return!1;return!0}(o,e))){var a=e.mapDimension(o.dim),s={};return R(o.getViewLabels(),(function(t){var e=o.scale.getRawOrdinalNumber(t.tickValue);s[e]=1})),function(t){return!s.hasOwnProperty(e.get(a,t))}}}}function wb(t,e){return[t[2*e],t[2*e+1]]}function Sb(t){if(t.get(["endLabel","show"]))return!0;for(var e=0;e<$s.length;e++)if(t.get([$s[e],"endLabel","show"]))return!0;return!1}function Mb(t,e,n,i){if(db(e,"cartesian2d")){var r=i.getModel("endLabel"),o=r.get("valueAnimation"),a=i.getData(),s={lastFrameIndex:0},l=Sb(i)?function(n,i){t._endLabelOnDuring(n,i,a,s,o,r,e)}:null,u=e.getBaseAxis().isHorizontal(),h=pb(e,n,i,(function(){var e=t._endLabel;e&&n&&null!=s.originalX&&e.attr({x:s.originalX,y:s.originalY})}),l);if(!i.get("clip",!0)){var c=h.shape,p=Math.max(c.width,c.height);u?(c.y-=p,c.height+=2*p):(c.x-=p,c.width+=2*p)}return l&&l(1,h),h}return fb(e,n,i)}var Tb=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.init=function(){var t=new Tr,e=new eb;this.group.add(e.group),this._symbolDraw=e,this._lineGroup=t,this._changePolyState=V(this._changePolyState,this)},e.prototype.render=function(t,e,n){var i=t.coordinateSystem,r=this.group,o=t.getData(),a=t.getModel("lineStyle"),s=t.getModel("areaStyle"),l=o.getLayout("points")||[],u="polar"===i.type,h=this._coordSys,c=this._symbolDraw,p=this._polyline,f=this._polygon,d=this._lineGroup,g=!e.ssr&&t.get("animation"),v=!s.isEmpty(),y=s.get("origin"),m=nb(i,o,y),_=v&&function(t,e,n){if(!n.valueDim)return[];for(var i=e.count(),r=jm(2*i),o=0;o<i;o++){var a=ib(n,t,e,o);r[2*o]=a[0],r[2*o+1]=a[1]}return r}(i,o,m),x=t.get("showSymbol"),b=t.get("connectNulls"),w=x&&!u&&bb(t,o,i),S=this._data;S&&S.eachItemGraphicEl((function(t,e){t.__temp&&(r.remove(t),S.setItemGraphicEl(e,null))})),x||c.remove(),r.add(d);var M,T=!u&&t.get("step");i&&i.getArea&&t.get("clip",!0)&&(null!=(M=i.getArea()).width?(M.x-=.1,M.y-=.1,M.width+=.2,M.height+=.2):M.r0&&(M.r0-=.5,M.r+=.5)),this._clipShapeForSymbol=M;var k=xb(o,i,n)||o.getVisual("style")[o.getVisual("drawType")];if(p&&h.type===i.type&&T===this._step){v&&!f?f=this._newPolygon(l,_):f&&!v&&(d.remove(f),f=this._polygon=null),u||this._initOrUpdateEndLabel(t,i,Kc(k));var C=d.getClipPath();if(C)ih(C,{shape:Mb(this,i,!1,t).shape},t);else d.setClipPath(Mb(this,i,!0,t));x&&c.updateData(o,{isIgnore:w,clipShape:M,disableAnimation:!0,getSymbolPoint:function(t){return[l[2*t],l[2*t+1]]}}),gb(this._stackedOnPoints,_)&&gb(this._points,l)||(g?this._doUpdateAnimation(o,_,i,n,T,y,b):(T&&(_&&(_=_b(_,l,i,T,b)),l=_b(l,null,i,T,b)),p.setShape({points:l}),f&&f.setShape({points:l,stackedOnPoints:_})))}else x&&c.updateData(o,{isIgnore:w,clipShape:M,disableAnimation:!0,getSymbolPoint:function(t){return[l[2*t],l[2*t+1]]}}),g&&this._initSymbolLabelAnimation(o,i,M),T&&(_&&(_=_b(_,l,i,T,b)),l=_b(l,null,i,T,b)),p=this._newPolyline(l),v?f=this._newPolygon(l,_):f&&(d.remove(f),f=this._polygon=null),u||this._initOrUpdateEndLabel(t,i,Kc(k)),d.setClipPath(Mb(this,i,!0,t));var I=t.getModel("emphasis"),A=I.get("focus"),L=I.get("blurScope"),P=I.get("disabled");(p.useStyle(D(a.getLineStyle(),{fill:"none",stroke:k,lineJoin:"bevel"})),Bl(p,t,"lineStyle"),p.style.lineWidth>0&&"bolder"===t.get(["emphasis","lineStyle","width"]))&&(p.getState("emphasis").style.lineWidth=+p.style.lineWidth+1);Ys(p).seriesIndex=t.seriesIndex,Ol(p,A,L,P);var O=mb(t.get("smooth")),R=t.get("smoothMonotone");if(p.setShape({smooth:O,smoothMonotone:R,connectNulls:b}),f){var N=o.getCalculationInfo("stackedOnSeries"),B=0;f.useStyle(D(s.getAreaStyle(),{fill:k,opacity:.7,lineJoin:"bevel",decal:o.getVisual("style").decal})),N&&(B=mb(N.get("smooth"))),f.setShape({smooth:O,stackedOnSmooth:B,smoothMonotone:R,connectNulls:b}),Bl(f,t,"areaStyle"),Ys(f).seriesIndex=t.seriesIndex,Ol(f,A,L,P)}var E=this._changePolyState;o.eachItemGraphicEl((function(t){t&&(t.onHoverStateChange=E)})),this._polyline.onHoverStateChange=E,this._data=o,this._coordSys=i,this._stackedOnPoints=_,this._points=l,this._step=T,this._valueOrigin=y,t.get("triggerLineEvent")&&(this.packEventData(t,p),f&&this.packEventData(t,f))},e.prototype.packEventData=function(t,e){Ys(e).eventData={componentType:"series",componentSubType:"line",componentIndex:t.componentIndex,seriesIndex:t.seriesIndex,seriesName:t.name,seriesType:"line"}},e.prototype.highlight=function(t,e,n,i){var r=t.getData(),o=Mo(r,i);if(this._changePolyState("emphasis"),!(o instanceof Array)&&null!=o&&o>=0){var a=r.getLayout("points"),s=r.getItemGraphicEl(o);if(!s){var l=a[2*o],u=a[2*o+1];if(isNaN(l)||isNaN(u))return;if(this._clipShapeForSymbol&&!this._clipShapeForSymbol.contain(l,u))return;var h=t.get("zlevel")||0,c=t.get("z")||0;(s=new Kx(r,o)).x=l,s.y=u,s.setZ(h,c);var p=s.getSymbolPath().getTextContent();p&&(p.zlevel=h,p.z=c,p.z2=this._polyline.z2+1),s.__temp=!0,r.setItemGraphicEl(o,s),s.stopSymbolAnimation(!0),this.group.add(s)}s.highlight()}else Kd.prototype.highlight.call(this,t,e,n,i)},e.prototype.downplay=function(t,e,n,i){var r=t.getData(),o=Mo(r,i);if(this._changePolyState("normal"),null!=o&&o>=0){var a=r.getItemGraphicEl(o);a&&(a.__temp?(r.setItemGraphicEl(o,null),this.group.remove(a)):a.downplay())}else Kd.prototype.downplay.call(this,t,e,n,i)},e.prototype._changePolyState=function(t){var e=this._polygon;gl(this._polyline,t),e&&gl(e,t)},e.prototype._newPolyline=function(t){var e=this._polyline;return e&&this._lineGroup.remove(e),e=new ub({shape:{points:t},segmentIgnoreThreshold:2,z2:10}),this._lineGroup.add(e),this._polyline=e,e},e.prototype._newPolygon=function(t,e){var n=this._polygon;return n&&this._lineGroup.remove(n),n=new cb({shape:{points:t,stackedOnPoints:e},segmentIgnoreThreshold:2}),this._lineGroup.add(n),this._polygon=n,n},e.prototype._initSymbolLabelAnimation=function(t,e,n){var i,r,o=e.getBaseAxis(),a=o.inverse;"cartesian2d"===e.type?(i=o.isHorizontal(),r=!1):"polar"===e.type&&(i="angle"===o.dim,r=!0);var s=t.hostModel,l=s.get("animationDuration");G(l)&&(l=l(null));var u=s.get("animationDelay")||0,h=G(u)?u(null):u;t.eachItemGraphicEl((function(t,o){var s=t;if(s){var c=[t.x,t.y],p=void 0,f=void 0,d=void 0;if(n)if(r){var g=n,v=e.pointToCoord(c);i?(p=g.startAngle,f=g.endAngle,d=-v[1]/180*Math.PI):(p=g.r0,f=g.r,d=v[0])}else{var y=n;i?(p=y.x,f=y.x+y.width,d=t.x):(p=y.y+y.height,f=y.y,d=t.y)}var m=f===p?0:(d-p)/(f-p);a&&(m=1-m);var _=G(u)?u(o):l*m+h,x=s.getSymbolPath(),b=x.getTextContent();s.attr({scaleX:0,scaleY:0}),s.animateTo({scaleX:1,scaleY:1},{duration:200,setToFinal:!0,delay:_}),b&&b.animateFrom({style:{opacity:0}},{duration:300,delay:_}),x.disableLabelAnimation=!0}}))},e.prototype._initOrUpdateEndLabel=function(t,e,n){var i=t.getModel("endLabel");if(Sb(t)){var r=t.getData(),o=this._polyline,a=r.getLayout("points");if(!a)return o.removeTextContent(),void(this._endLabel=null);var s=this._endLabel;s||((s=this._endLabel=new Rs({z2:200})).ignoreClip=!0,o.setTextContent(this._endLabel),o.disableLabelAnimation=!0);var l=function(t){for(var e,n,i=t.length/2;i>0&&(e=t[2*i-2],n=t[2*i-1],isNaN(e)||isNaN(n));i--);return i-1}(a);l>=0&&(Nh(o,Bh(t,"endLabel"),{inheritColor:n,labelFetcher:t,labelDataIndex:l,defaultText:function(t,e,n){return null!=n?jx(r,n):Zx(r,t)},enableTextSetter:!0},function(t,e){var n=e.getBaseAxis(),i=n.isHorizontal(),r=n.inverse,o=i?r?"right":"left":"center",a=i?"middle":r?"top":"bottom";return{normal:{align:t.get("align")||o,verticalAlign:t.get("verticalAlign")||a}}}(i,e)),o.textConfig.position=null)}else this._endLabel&&(this._polyline.removeTextContent(),this._endLabel=null)},e.prototype._endLabelOnDuring=function(t,e,n,i,r,o,a){var s=this._endLabel,l=this._polyline;if(s){t<1&&null==i.originalX&&(i.originalX=s.x,i.originalY=s.y);var u=n.getLayout("points"),h=n.hostModel,c=h.get("connectNulls"),p=o.get("precision"),f=o.get("distance")||0,d=a.getBaseAxis(),g=d.isHorizontal(),v=d.inverse,y=e.shape,m=v?g?y.x:y.y+y.height:g?y.x+y.width:y.y,_=(g?f:0)*(v?-1:1),x=(g?0:-f)*(v?-1:1),b=g?"x":"y",w=function(t,e,n){for(var i,r,o=t.length/2,a="x"===n?0:1,s=0,l=-1,u=0;u<o;u++)if(r=t[2*u+a],!isNaN(r)&&!isNaN(t[2*u+1-a]))if(0!==u){if(i<=e&&r>=e||i>=e&&r<=e){l=u;break}s=u,i=r}else i=r;return{range:[s,l],t:(e-i)/(r-i)}}(u,m,b),S=w.range,M=S[1]-S[0],T=void 0;if(M>=1){if(M>1&&!c){var k=wb(u,S[0]);s.attr({x:k[0]+_,y:k[1]+x}),r&&(T=h.getRawValue(S[0]))}else{(k=l.getPointOn(m,b))&&s.attr({x:k[0]+_,y:k[1]+x});var C=h.getRawValue(S[0]),D=h.getRawValue(S[1]);r&&(T=function(t,e,n,i,r){var o=null==e||"auto"===e;if(null==i)return i;if(Y(i))return Wr(d=ho(n||0,i,r),o?Math.max(Hr(n||0),Hr(i)):e);if(U(i))return r<1?n:i;for(var a=[],s=n,l=i,u=Math.max(s?s.length:0,l.length),h=0;h<u;++h){var c=t.getDimensionInfo(h);if(c&&"ordinal"===c.type)a[h]=(r<1&&s?s:l)[h];else{var p=s&&s[h]?s[h]:0,f=l[h],d=ho(p,f,r);a[h]=Wr(d,o?Math.max(Hr(p),Hr(f)):e)}}return a}(n,p,C,D,w.t))}i.lastFrameIndex=S[0]}else{var I=1===t||i.lastFrameIndex>0?S[0]:0;k=wb(u,I);r&&(T=h.getRawValue(I)),s.attr({x:k[0]+_,y:k[1]+x})}if(r){var A=Gh(s);"function"==typeof A.setLabelText&&A.setLabelText(T)}}},e.prototype._doUpdateAnimation=function(t,e,n,i,r,o,a){var s=this._polyline,l=this._polygon,u=t.hostModel,h=function(t,e,n,i,r,o,a,s){for(var l=function(t,e){var n=[];return e.diff(t).add((function(t){n.push({cmd:"+",idx:t})})).update((function(t,e){n.push({cmd:"=",idx:e,idx1:t})})).remove((function(t){n.push({cmd:"-",idx:t})})).execute(),n}(t,e),u=[],h=[],c=[],p=[],f=[],d=[],g=[],v=nb(r,e,a),y=t.getLayout("points")||[],m=e.getLayout("points")||[],_=0;_<l.length;_++){var x=l[_],b=!0,w=void 0,S=void 0;switch(x.cmd){case"=":w=2*x.idx,S=2*x.idx1;var M=y[w],T=y[w+1],k=m[S],C=m[S+1];(isNaN(M)||isNaN(T))&&(M=k,T=C),u.push(M,T),h.push(k,C),c.push(n[w],n[w+1]),p.push(i[S],i[S+1]),g.push(e.getRawIndex(x.idx1));break;case"+":var D=x.idx,I=v.dataDimsForPoint,A=r.dataToPoint([e.get(I[0],D),e.get(I[1],D)]);S=2*D,u.push(A[0],A[1]),h.push(m[S],m[S+1]);var L=ib(v,r,e,D);c.push(L[0],L[1]),p.push(i[S],i[S+1]),g.push(e.getRawIndex(D));break;case"-":b=!1}b&&(f.push(x),d.push(d.length))}d.sort((function(t,e){return g[t]-g[e]}));var P=u.length,O=jm(P),R=jm(P),N=jm(P),B=jm(P),E=[];for(_=0;_<d.length;_++){var z=d[_],F=2*_,V=2*z;O[F]=u[V],O[F+1]=u[V+1],R[F]=h[V],R[F+1]=h[V+1],N[F]=c[V],N[F+1]=c[V+1],B[F]=p[V],B[F+1]=p[V+1],E[_]=f[z]}return{current:O,next:R,stackedOnCurrent:N,stackedOnNext:B,status:E}}(this._data,t,this._stackedOnPoints,e,this._coordSys,0,this._valueOrigin),c=h.current,p=h.stackedOnCurrent,f=h.next,d=h.stackedOnNext;if(r&&(p=_b(h.stackedOnCurrent,h.current,n,r,a),c=_b(h.current,null,n,r,a),d=_b(h.stackedOnNext,h.next,n,r,a),f=_b(h.next,null,n,r,a)),yb(c,f)>3e3||l&&yb(p,d)>3e3)return s.stopAnimation(),s.setShape({points:f}),void(l&&(l.stopAnimation(),l.setShape({points:f,stackedOnPoints:d})));s.shape.__points=h.current,s.shape.points=c;var g={shape:{points:f}};h.current!==c&&(g.shape.__points=h.next),s.stopAnimation(),nh(s,g,u),l&&(l.setShape({points:c,stackedOnPoints:p}),l.stopAnimation(),nh(l,{shape:{stackedOnPoints:d}},u),s.shape.points!==l.shape.points&&(l.shape.points=s.shape.points));for(var v=[],y=h.status,m=0;m<y.length;m++){if("="===y[m].cmd){var _=t.getItemGraphicEl(y[m].idx1);_&&v.push({el:_,ptIdx:m})}}s.animators&&s.animators.length&&s.animators[0].during((function(){l&&l.dirtyShape();for(var t=s.shape.__points,e=0;e<v.length;e++){var n=v[e].el,i=2*v[e].ptIdx;n.x=t[i],n.y=t[i+1],n.markRedraw()}}))},e.prototype.remove=function(t){var e=this.group,n=this._data;this._lineGroup.removeAll(),this._symbolDraw.remove(!0),n&&n.eachItemGraphicEl((function(t,i){t.__temp&&(e.remove(t),n.setItemGraphicEl(i,null))})),this._polyline=this._polygon=this._coordSys=this._points=this._stackedOnPoints=this._endLabel=this._data=null},e.type="line",e}(Kd);var kb={average:function(t){for(var e=0,n=0,i=0;i<t.length;i++)isNaN(t[i])||(e+=t[i],n++);return 0===n?NaN:e/n},sum:function(t){for(var e=0,n=0;n<t.length;n++)e+=t[n]||0;return e},max:function(t){for(var e=-1/0,n=0;n<t.length;n++)t[n]>e&&(e=t[n]);return isFinite(e)?e:NaN},min:function(t){for(var e=1/0,n=0;n<t.length;n++)t[n]<e&&(e=t[n]);return isFinite(e)?e:NaN},nearest:function(t){return t[0]}},Cb=function(t){return Math.round(t.length/2)};function Db(t){return{seriesType:t,reset:function(t,e,n){var i=t.getData(),r=t.get("sampling"),o=t.coordinateSystem,a=i.count();if(a>10&&"cartesian2d"===o.type&&r){var s=o.getBaseAxis(),l=o.getOtherAxis(s),u=s.getExtent(),h=n.getDevicePixelRatio(),c=Math.abs(u[1]-u[0])*(h||1),p=Math.round(a/c);if(isFinite(p)&&p>1){"lttb"===r?t.setData(i.lttbDownSample(i.mapDimension(l.dim),1/p)):"minmax"===r&&t.setData(i.minmaxDownSample(i.mapDimension(l.dim),1/p));var f=void 0;U(r)?f=kb[r]:G(r)&&(f=r),f&&t.setData(i.downSample(i.mapDimension(l.dim),1/p,f,Cb))}}}}}var Ib=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return n(e,t),e.prototype.getInitialData=function(t,e){return Im(null,this,{useEncodeDefaulter:!0})},e.prototype.getMarkerPosition=function(t,e,n){var i=this.coordinateSystem;if(i&&i.clampData){var r=i.clampData(t),o=i.dataToPoint(r);if(n)R(i.getAxes(),(function(t,n){if("category"===t.type&&null!=e){var i=t.getTicksCoords(),a=t.getTickModel().get("alignWithLabel"),s=r[n],l="x1"===e[n]||"y1"===e[n];if(l&&!a&&(s+=1),i.length<2)return;if(2===i.length)return void(o[n]=t.toGlobalCoord(t.getExtent()[l?1:0]));for(var u=void 0,h=void 0,c=1,p=0;p<i.length;p++){var f=i[p].coord,d=p===i.length-1?i[p-1].tickValue+c:i[p].tickValue;if(d===s){h=f;break}if(d<s)u=f;else if(null!=u&&d>s){h=(f+u)/2;break}1===p&&(c=d-i[0].tickValue)}null==h&&(u?u&&(h=i[i.length-1].coord):h=i[0].coord),o[n]=t.toGlobalCoord(h)}}));else{var a=this.getData(),s=a.getLayout("offset"),l=a.getLayout("size"),u=i.getBaseAxis().isHorizontal()?0:1;o[u]+=s+l/2}return o}return[NaN,NaN]},e.type="series.__base_bar__",e.defaultOption={z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,barMinHeight:0,barMinAngle:0,large:!1,largeThreshold:400,progressive:3e3,progressiveChunkMode:"mod",defaultBarGap:"10%"},e}(zd);zd.registerClass(Ib);var Ab=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}var i,r;return n(e,t),e.prototype.getInitialData=function(){return Im(null,this,{useEncodeDefaulter:!0,createInvertedIndices:!!this.get("realtimeSort",!0)||null})},e.prototype.getProgressive=function(){return!!this.get("large")&&this.get("progressive")},e.prototype.getProgressiveThreshold=function(){var t=this.get("progressiveThreshold"),e=this.get("largeThreshold");return e>t&&(t=e),t},e.prototype.brushSelector=function(t,e,n){return n.rect(e.getItemLayout(t))},e.type="series.bar",e.dependencies=["grid","polar"],e.defaultOption=(i=Ib.defaultOption,r={clip:!0,roundCap:!1,showBackground:!1,backgroundStyle:{color:"rgba(180, 180, 180, 0.2)",borderColor:null,borderWidth:0,borderType:"solid",borderRadius:0,shadowBlur:0,shadowColor:null,shadowOffsetX:0,shadowOffsetY:0,opacity:1},select:{itemStyle:{borderColor:xp.color.primary,borderWidth:2}},realtimeSort:!1},k(k({},i,!0),r,!0)),e}(Ib),Lb=function(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0},Pb=function(t){function e(e){var n=t.call(this,e)||this;return n.type="sausage",n}return n(e,t),e.prototype.getDefaultShape=function(){return new Lb},e.prototype.buildPath=function(t,e){var n=e.cx,i=e.cy,r=Math.max(e.r0||0,0),o=Math.max(e.r,0),a=.5*(o-r),s=r+a,l=e.startAngle,u=e.endAngle,h=e.clockwise,c=2*Math.PI,p=h?u-l<c:l-u<c;p||(l=u-(h?c:-c));var f=Math.cos(l),d=Math.sin(l),g=Math.cos(u),v=Math.sin(u);p?(t.moveTo(f*r+n,d*r+i),t.arc(f*s+n,d*s+i,a,-Math.PI+l,l,!h)):t.moveTo(f*o+n,d*o+i),t.arc(n,i,o,l,u,!h),t.arc(g*s+n,v*s+i,a,u-2*Math.PI,u-Math.PI,!h),0!==r&&t.arc(n,i,r,u,l,h)},e}(_s);function Ob(t,e,n){return e*Math.sin(t)*(n?-1:1)}function Rb(t,e,n){return e*Math.cos(t)*(n?1:-1)}function Nb(t,e,n){var i=t.get("borderRadius");if(null==i)return n?{cornerRadius:0}:null;H(i)||(i=[i,i,i,i]);var r=Math.abs(e.r||0-e.r0||0);return{cornerRadius:N(i,(function(t){return pr(t,r)}))}}var Bb=Math.max,Eb=Math.min;var zb=function(t){function e(){var n=t.call(this)||this;return n.type=e.type,n._isFirstFrame=!0,n}return n(e,t),e.prototype.render=function(t,e,n,i){this._model=t,this._removeOnRenderedListener(n),this._updateDrawMode(t);var r=t.get("coordinateSystem");("cartesian2d"===r||"polar"===r)&&(this._progressiveEls=null,this._isLargeDraw?this._renderLarge(t,e,n):this._renderNormal(t,e,n,i))},e.prototype.incrementalPrepareRender=function(t){this._clear(),this._updateDrawMode(t),this._updateLargeClip(t)},e.prototype.incrementalRender=function(t,e){this._progressiveEls=[],this._incrementalRenderLarge(t,e)},e.prototype.eachRendered=function(t){kh(this._progressiveEls||this.group,t)},e.prototype._updateDrawMode=function(t){var e=t.pipelineContext.large;null!=this._isLargeDraw&&e===this._isLargeDraw||(this._isLargeDraw=e,this._clear())},e.prototype._renderNormal=function(t,e,n,i){var r,o=this.group,a=t.getData(),s=this._data,l=t.coordinateSystem,u=l.getBaseAxis();"cartesian2d"===l.type?r=u.isHorizontal():"polar"===l.type&&(r="angle"===u.dim);var h=t.isAnimationEnabled()?t:null,c=function(t,e){var n=t.get("realtimeSort",!0),i=e.getBaseAxis();0;if(n&&"category"===i.type&&"cartesian2d"===e.type)return{baseAxis:i,otherAxis:e.getOtherAxis(i)}}(t,l);c&&this._enableRealtimeSort(c,a,n);var p=t.get("clip",!0)||c,f=function(t,e){var n=t.getArea&&t.getArea();if(db(t,"cartesian2d")){var i=t.getBaseAxis();if("category"!==i.type||!i.onBand){var r=e.getLayout("bandWidth");i.isHorizontal()?(n.x-=r,n.width+=2*r):(n.y-=r,n.height+=2*r)}}return n}(l,a);o.removeClipPath();var d=t.get("roundCap",!0),g=t.get("showBackground",!0),v=t.getModel("backgroundStyle"),y=v.get("borderRadius")||0,m=[],_=this._backgroundEls,x=i&&i.isInitSort,b=i&&"changeAxisOrder"===i.type;function w(t){var e=Yb[l.type](a,t);if(!e)return null;var n=function(t,e,n){var i="polar"===t.type?wu:As;return new i({shape:Jb(e,n,t),silent:!0,z2:0})}(l,r,e);return n.useStyle(v.getItemStyle()),"cartesian2d"===l.type?n.setShape("r",y):n.setShape("cornerRadius",y),m[t]=n,n}a.diff(s).add((function(e){var n=a.getItemModel(e),i=Yb[l.type](a,e,n);if(i&&(g&&w(e),a.hasValue(e)&&Xb[l.type](i))){var s=!1;p&&(s=Fb[l.type](f,i));var v=Vb[l.type](t,a,e,i,r,h,u.model,!1,d);c&&(v.forceLabelAnimation=!0),Zb(v,a,e,n,i,t,r,"polar"===l.type),x?v.attr({shape:i}):c?Wb(c,h,v,i,e,r,!1,!1):ih(v,{shape:i},t,e),a.setItemGraphicEl(e,v),o.add(v),v.ignore=s}})).update((function(e,n){var i=a.getItemModel(e),S=Yb[l.type](a,e,i);if(S){if(g){var M=void 0;0===_.length?M=w(n):((M=_[n]).useStyle(v.getItemStyle()),"cartesian2d"===l.type?M.setShape("r",y):M.setShape("cornerRadius",y),m[e]=M);var T=Yb[l.type](a,e);nh(M,{shape:Jb(r,T,l)},h,e)}var k=s.getItemGraphicEl(n);if(a.hasValue(e)&&Xb[l.type](S)){var C=!1;if(p&&(C=Fb[l.type](f,S))&&o.remove(k),k&&("sector"===k.type&&d||"sausage"===k.type&&!d)&&(k&&sh(k,t,n),k=null),k?lh(k):k=Vb[l.type](t,a,e,S,r,h,u.model,!0,d),c&&(k.forceLabelAnimation=!0),b){var D=k.getTextContent();if(D){var I=Gh(D);null!=I.prevValue&&(I.prevValue=I.value)}}else Zb(k,a,e,i,S,t,r,"polar"===l.type);x?k.attr({shape:S}):c?Wb(c,h,k,S,e,r,!0,b):nh(k,{shape:S},t,e,null),a.setItemGraphicEl(e,k),k.ignore=C,o.add(k)}else o.remove(k)}})).remove((function(e){var n=s.getItemGraphicEl(e);n&&sh(n,t,e)})).execute();var S=this._backgroundGroup||(this._backgroundGroup=new Tr);S.removeAll();for(var M=0;M<m.length;++M)S.add(m[M]);o.add(S),this._backgroundEls=m,this._data=a},e.prototype._renderLarge=function(t,e,n){this._clear(),$b(t,this.group),this._updateLargeClip(t)},e.prototype._incrementalRenderLarge=function(t,e){this._removeBackground(),$b(e,this.group,this._progressiveEls,!0)},e.prototype._updateLargeClip=function(t){var e=t.get("clip",!0)&&function(t,e,n,i,r){return t?"polar"===t.type?fb(t,e,n):"cartesian2d"===t.type?pb(t,e,n,i,r):null:null}(t.coordinateSystem,!1,t),n=this.group;e?n.setClipPath(e):n.removeClipPath()},e.prototype._enableRealtimeSort=function(t,e,n){var i=this;if(e.count()){var r=t.baseAxis;if(this._isFirstFrame)this._dispatchInitSort(e,t,n),this._isFirstFrame=!1;else{var o=function(t){var n=e.getItemGraphicEl(t),i=n&&n.shape;return i&&Math.abs(r.isHorizontal()?i.height:i.width)||0};this._onRendered=function(){i._updateSortWithinSameData(e,o,r,n)},n.getZr().on("rendered",this._onRendered)}}},e.prototype._dataSort=function(t,e,n){var i=[];return t.each(t.mapDimension(e.dim),(function(t,e){var r=n(e);r=null==r?NaN:r,i.push({dataIndex:e,mappedValue:r,ordinalNumber:t})})),i.sort((function(t,e){return e.mappedValue-t.mappedValue})),{ordinalNumbers:N(i,(function(t){return t.ordinalNumber}))}},e.prototype._isOrderChangedWithinSameData=function(t,e,n){for(var i=n.scale,r=t.mapDimension(n.dim),o=Number.MAX_VALUE,a=0,s=i.getOrdinalMeta().categories.length;a<s;++a){var l=t.rawIndexOf(r,i.getRawOrdinalNumber(a)),u=l<0?Number.MIN_VALUE:e(t.indexOfRawIndex(l));if(u>o)return!0;o=u}return!1},e.prototype._isOrderDifferentInView=function(t,e){for(var n=e.scale,i=n.getExtent(),r=Math.max(0,i[0]),o=Math.min(i[1],n.getOrdinalMeta().categories.length-1);r<=o;++r)if(t.ordinalNumbers[r]!==n.getRawOrdinalNumber(r))return!0},e.prototype._updateSortWithinSameData=function(t,e,n,i){if(this._isOrderChangedWithinSameData(t,e,n)){var r=this._dataSort(t,n,e);this._isOrderDifferentInView(r,n)&&(this._removeOnRenderedListener(i),i.dispatchAction({type:"changeAxisOrder",componentType:n.dim+"Axis",axisId:n.index,sortInfo:r}))}},e.prototype._dispatchInitSort=function(t,e,n){var i=e.baseAxis,r=this._dataSort(t,i,(function(n){return t.get(t.mapDimension(e.otherAxis.dim),n)}));n.dispatchAction({type:"changeAxisOrder",componentType:i.dim+"Axis",isInitSort:!0,axisId:i.index,sortInfo:r})},e.prototype.remove=function(t,e){this._clear(this._model),this._removeOnRenderedListener(e)},e.prototype.dispose=function(t,e){this._removeOnRenderedListener(e)},e.prototype._removeOnRenderedListener=function(t){this._onRendered&&(t.getZr().off("rendered",this._onRendered),this._onRendered=null)},e.prototype._clear=function(t){var e=this.group,n=this._data;t&&t.isAnimationEnabled()&&n&&!this._isLargeDraw?(this._removeBackground(),this._backgroundEls=[],n.eachItemGraphicEl((function(e){sh(e,t,Ys(e).dataIndex)}))):e.removeAll(),this._data=null,this._isFirstFrame=!0},e.prototype._removeBackground=function(){this.group.remove(this._backgroundGroup),this._backgroundGroup=null},e.type="bar",e}(Kd),Fb={cartesian2d:function(t,e){var n=e.width<0?-1:1,i=e.height<0?-1:1;n<0&&(e.x+=e.width,e.width=-e.width),i<0&&(e.y+=e.height,e.height=-e.height);var r=t.x+t.width,o=t.y+t.height,a=Bb(e.x,t.x),s=Eb(e.x+e.width,r),l=Bb(e.y,t.y),u=Eb(e.y+e.height,o),h=s<a,c=u<l;return e.x=h&&a>r?s:a,e.y=c&&l>o?u:l,e.width=h?0:s-a,e.height=c?0:u-l,n<0&&(e.x+=e.width,e.width=-e.width),i<0&&(e.y+=e.height,e.height=-e.height),h||c},polar:function(t,e){var n=e.r0<=e.r?1:-1;if(n<0){var i=e.r;e.r=e.r0,e.r0=i}var r=Eb(e.r,t.r),o=Bb(e.r0,t.r0);e.r=r,e.r0=o;var a=r-o<0;if(n<0){i=e.r;e.r=e.r0,e.r0=i}return a}},Vb={cartesian2d:function(t,e,n,i,r,o,a,s,l){var u=new As({shape:C({},i),z2:1});(u.__dataIndex=n,u.name="item",o)&&(u.shape[r?"height":"width"]=0);return u},polar:function(t,e,n,i,r,o,a,s,l){var u=!r&&l?Pb:wu,h=new u({shape:i,z2:1});h.name="item";var c,p,f=qb(r);if(h.calculateTextPosition=(c=f,p=({isRoundCap:u===Pb}||{}).isRoundCap,function(t,e,n){var i=e.position;if(!i||i instanceof Array)return fr(t,e,n);var r=c(i),o=null!=e.distance?e.distance:5,a=this.shape,s=a.cx,l=a.cy,u=a.r,h=a.r0,f=(u+h)/2,d=a.startAngle,g=a.endAngle,v=(d+g)/2,y=p?Math.abs(u-h)/2:0,m=Math.cos,_=Math.sin,x=s+u*m(d),b=l+u*_(d),w="left",S="top";switch(r){case"startArc":x=s+(h-o)*m(v),b=l+(h-o)*_(v),w="center",S="top";break;case"insideStartArc":x=s+(h+o)*m(v),b=l+(h+o)*_(v),w="center",S="bottom";break;case"startAngle":x=s+f*m(d)+Ob(d,o+y,!1),b=l+f*_(d)+Rb(d,o+y,!1),w="right",S="middle";break;case"insideStartAngle":x=s+f*m(d)+Ob(d,-o+y,!1),b=l+f*_(d)+Rb(d,-o+y,!1),w="left",S="middle";break;case"middle":x=s+f*m(v),b=l+f*_(v),w="center",S="middle";break;case"endArc":x=s+(u+o)*m(v),b=l+(u+o)*_(v),w="center",S="bottom";break;case"insideEndArc":x=s+(u-o)*m(v),b=l+(u-o)*_(v),w="center",S="top";break;case"endAngle":x=s+f*m(g)+Ob(g,o+y,!0),b=l+f*_(g)+Rb(g,o+y,!0),w="left",S="middle";break;case"insideEndAngle":x=s+f*m(g)+Ob(g,-o+y,!0),b=l+f*_(g)+Rb(g,-o+y,!0),w="right",S="middle";break;default:return fr(t,e,n)}return(t=t||{}).x=x,t.y=b,t.align=w,t.verticalAlign=S,t}),o){var d=r?"r":"endAngle",g={};h.shape[d]=r?i.r0:i.startAngle,g[d]=i[d],(s?nh:ih)(h,{shape:g},o)}return h}};function Wb(t,e,n,i,r,o,a,s){var l,u;o?(u={x:i.x,width:i.width},l={y:i.y,height:i.height}):(u={y:i.y,height:i.height},l={x:i.x,width:i.width}),s||(a?nh:ih)(n,{shape:l},e,r,null),(a?nh:ih)(n,{shape:u},e?t.baseAxis.model:null,r)}function Hb(t,e){for(var n=0;n<e.length;n++)if(!isFinite(t[e[n]]))return!0;return!1}var Gb=["x","y","width","height"],Ub=["cx","cy","r","startAngle","endAngle"],Xb={cartesian2d:function(t){return!Hb(t,Gb)},polar:function(t){return!Hb(t,Ub)}},Yb={cartesian2d:function(t,e,n){var i=t.getItemLayout(e);if(!i)return null;var r=n?function(t,e){var n=t.get(["itemStyle","borderColor"]);if(!n||"none"===n)return 0;var i=t.get(["itemStyle","borderWidth"])||0,r=isNaN(e.width)?Number.MAX_VALUE:Math.abs(e.width),o=isNaN(e.height)?Number.MAX_VALUE:Math.abs(e.height);return Math.min(i,r,o)}(n,i):0,o=i.width>0?1:-1,a=i.height>0?1:-1;return{x:i.x+o*r/2,y:i.y+a*r/2,width:i.width-o*r,height:i.height-a*r}},polar:function(t,e,n){var i=t.getItemLayout(e);return{cx:i.cx,cy:i.cy,r0:i.r0,r:i.r,startAngle:i.startAngle,endAngle:i.endAngle,clockwise:i.clockwise}}};function qb(t){return function(t){var e=t?"Arc":"Angle";return function(t){switch(t){case"start":case"insideStart":case"end":case"insideEnd":return t+e;default:return t}}}(t)}function Zb(t,e,n,i,r,o,a,s){var l=e.getItemVisual(n,"style");if(s){if(!o.get("roundCap")){var u=t.shape;C(u,Nb(i.getModel("itemStyle"),u,!0)),t.setShape(u)}}else{var h=i.get(["itemStyle","borderRadius"])||0;t.setShape("r",h)}t.useStyle(l);var c=i.getShallow("cursor");c&&t.attr("cursor",c);var p=s?a?r.r>=r.r0?"endArc":"startArc":r.endAngle>=r.startAngle?"endAngle":"startAngle":a?r.height>=0?"bottom":"top":r.width>=0?"right":"left",f=Bh(i);Nh(t,f,{labelFetcher:o,labelDataIndex:n,defaultText:Zx(o.getData(),n),inheritColor:l.fill,defaultOpacity:l.opacity,defaultOutsidePosition:p});var d=t.getTextContent();if(s&&d){var g=i.get(["label","position"]);t.textConfig.inside="middle"===g||null,function(t,e,n,i){if(Y(i))t.setTextConfig({rotation:i});else if(H(e))t.setTextConfig({rotation:0});else{var r,o=t.shape,a=o.clockwise?o.startAngle:o.endAngle,s=o.clockwise?o.endAngle:o.startAngle,l=(a+s)/2,u=n(e);switch(u){case"startArc":case"insideStartArc":case"middle":case"insideEndArc":case"endArc":r=l;break;case"startAngle":case"insideStartAngle":r=a;break;case"endAngle":case"insideEndAngle":r=s;break;default:return void t.setTextConfig({rotation:0})}var h=1.5*Math.PI-r;"middle"===u&&h>Math.PI/2&&h<1.5*Math.PI&&(h-=Math.PI),t.setTextConfig({rotation:h})}}(t,"outside"===g?p:g,qb(a),i.get(["label","rotate"]))}!function(t,e,n,i){if(t){var r=Gh(t);r.prevValue=r.value,r.value=n;var o=e.normal;r.valueAnimation=o.get("valueAnimation"),r.valueAnimation&&(r.precision=o.get("precision"),r.defaultInterpolatedText=i,r.statesModels=e)}}(d,f,o.getRawValue(n),(function(t){return jx(e,t)}));var v=i.getModel(["emphasis"]);Ol(t,v.get("focus"),v.get("blurScope"),v.get("disabled")),Bl(t,i),function(t){return null!=t.startAngle&&null!=t.endAngle&&t.startAngle===t.endAngle}(r)&&(t.style.fill="none",t.style.stroke="none",R(t.states,(function(t){t.style&&(t.style.fill=t.style.stroke="none")})))}var jb=function(){},Kb=function(t){function e(e){var n=t.call(this,e)||this;return n.type="largeBar",n}return n(e,t),e.prototype.getDefaultShape=function(){return new jb},e.prototype.buildPath=function(t,e){for(var n=e.points,i=this.baseDimIdx,r=1-this.baseDimIdx,o=[],a=[],s=this.barWidth,l=0;l<n.length;l+=3)a[i]=s,a[r]=n[l+2],o[i]=n[l+i],o[r]=n[l+r],t.rect(o[0],o[1],a[0],a[1])},e}(_s);function $b(t,e,n,i){var r=t.getData(),o=r.getLayout("valueAxisHorizontal")?1:0,a=r.getLayout("largeDataIndices"),s=r.getLayout("size"),l=t.getModel("backgroundStyle"),u=r.getLayout("largeBackgroundPoints");if(u){var h=new Kb({shape:{points:u},incremental:!!i,silent:!0,z2:0});h.baseDimIdx=o,h.largeDataIndices=a,h.barWidth=s,h.useStyle(l.getItemStyle()),e.add(h),n&&n.push(h)}var c=new Kb({shape:{points:r.getLayout("largePoints")},incremental:!!i,ignoreCoarsePointer:!0,z2:1});c.baseDimIdx=o,c.largeDataIndices=a,c.barWidth=s,e.add(c),c.useStyle(r.getVisual("style")),c.style.stroke=null,Ys(c).seriesIndex=t.seriesIndex,t.get("silent")||(c.on("mousedown",Qb),c.on("mousemove",Qb)),n&&n.push(c)}var Qb=ng((function(t){var e=function(t,e,n){for(var i=t.baseDimIdx,r=1-i,o=t.shape.points,a=t.largeDataIndices,s=[],l=[],u=t.barWidth,h=0,c=o.length/3;h<c;h++){var p=3*h;if(l[i]=u,l[r]=o[p+2],s[i]=o[p+i],s[r]=o[p+r],l[r]<0&&(s[r]+=l[r],l[r]=-l[r]),e>=s[0]&&e<=s[0]+l[0]&&n>=s[1]&&n<=s[1]+l[1])return a[h]}return-1}(this,t.offsetX,t.offsetY);Ys(this).dataIndex=e>=0?e:null}),30,!1);function Jb(t,e,n){if(db(n,"cartesian2d")){var i=e,r=n.getArea();return{x:t?i.x:r.x,y:t?r.y:i.y,width:t?i.width:r.width,height:t?r.height:i.height}}var o=e;return{cx:(r=n.getArea()).cx,cy:r.cy,r0:t?r.r0:o.r0,r:t?r.r:o.r,startAngle:t?o.startAngle:0,endAngle:t?o.endAngle:2*Math.PI}}var tw=2*Math.PI,ew=Math.PI/180;function nw(t,e,n){e.eachSeriesByType(t,(function(t){var e=t.getData(),i=e.mapDimension("value"),r=cp(t,n),o=r.cx,a=r.cy,s=r.r,l=r.r0,u=r.viewRect,h=-t.get("startAngle")*ew,c=t.get("endAngle"),p=t.get("padAngle")*ew;c="auto"===c?h-tw:-c*ew;var f=t.get("minAngle")*ew+p,d=0;e.each(i,(function(t){!isNaN(t)&&d++}));var g=e.getSum(i),v=Math.PI/(g||d)*2,y=t.get("clockwise"),m=t.get("roseType"),_=t.get("stillShowZeroSum"),x=e.getDataExtent(i);x[0]=0;var b=y?1:-1,w=[h,c],S=b*p/2;Qa(w,!y),h=w[0],c=w[1];var M=iw(t);M.startAngle=h,M.endAngle=c,M.clockwise=y,M.cx=o,M.cy=a,M.r=s,M.r0=l;var T=Math.abs(c-h),k=T,C=0,D=h;if(e.setLayout({viewRect:u,r:s}),e.each(i,(function(t,n){var i;if(isNaN(t))e.setItemLayout(n,{angle:NaN,startAngle:NaN,endAngle:NaN,clockwise:y,cx:o,cy:a,r0:l,r:m?NaN:s});else{(i="area"!==m?0===g&&_?v:t*v:T/d)<f?(i=f,k-=f):C+=t;var r=D+b*i,u=0,h=0;p>i?h=u=D+b*i/2:(u=D+S,h=r-S),e.setItemLayout(n,{angle:i,startAngle:u,endAngle:h,clockwise:y,cx:o,cy:a,r0:l,r:m?zr(t,x,[l,s]):s}),D=r}})),k<tw&&d)if(k<=.001){var I=T/d;e.each(i,(function(t,n){if(!isNaN(t)){var i=e.getItemLayout(n);i.angle=I;var r=0,o=0;I<p?o=r=h+b*(n+.5)*I:(r=h+b*n*I+S,o=h+b*(n+1)*I-S),i.startAngle=r,i.endAngle=o}}))}else v=k/C,D=h,e.each(i,(function(t,n){if(!isNaN(t)){var i=e.getItemLayout(n),r=i.angle===f?f:t*v,o=0,a=0;r<p?a=o=D+b*r/2:(o=D+S,a=D+b*r-S),i.startAngle=o,i.endAngle=a,D+=b*r}}))}))}var iw=To();var rw=Math.PI/180;function ow(t,e,n,i,r,o,a,s,l,u){if(!(t.length<2)){for(var h=t.length,c=0;c<h;c++)if("outer"===t[c].position&&"labelLine"===t[c].labelAlignTo){var p=t[c].label.x-u;t[c].linePoints[1][0]+=p,t[c].label.x=u}(function(t,e,n,i,r){var o=t.length,a=hh[e],s=ch[e];if(o<2)return!1;t.sort((function(t,e){return t.rect[a]-e.rect[a]}));for(var l,u=0,h=!1,c=0,p=0;p<o;p++){var f=t[p],d=f.rect;(l=d[a]-u)<0&&(d[a]-=l,f.label[a]-=l,h=!0),c+=Math.max(-l,0),u=d[a]+d[s]}c>0&&r&&b(-c/o,0,o);var g,v,y=t[0],m=t[o-1];function _(){g=y.rect[a]-n,v=i-m.rect[a]-m.rect[s]}function x(t,e,n){if(t<0){var i=Math.min(e,-t);if(i>0){b(i*n,0,o);var r=i+t;r<0&&w(-r*n,1)}else w(-t*n,1)}}function b(e,n,i){0!==e&&(h=!0);for(var r=n;r<i;r++){var o=t[r];o.rect[a]+=e,o.label[a]+=e}}function w(e,n){for(var i=[],r=0,l=1;l<o;l++){var u=t[l-1].rect,h=Math.max(t[l].rect[a]-u[a]-u[s],0);i.push(h),r+=h}if(r){var c=Math.min(Math.abs(e)/r,n);if(e>0)for(l=0;l<o-1;l++)b(i[l]*c,0,l+1);else for(l=o-1;l>0;l--)b(-i[l-1]*c,l,o)}}function S(t){var e=t<0?-1:1;t=Math.abs(t);for(var n=Math.ceil(t/(o-1)),i=0;i<o-1;i++)if(e>0?b(n,0,i+1):b(-n,o-i-1,o),(t-=n)<=0)return}return _(),g<0&&w(-g,.8),v<0&&w(v,.8),_(),x(g,v,1),x(v,g,-1),_(),g<0&&S(-g),v<0&&S(v),h})(t,1,l,l+a)&&function(t){for(var o={list:[],maxY:0},a={list:[],maxY:0},s=0;s<t.length;s++)if("none"===t[s].labelAlignTo){var l=t[s],u=l.label.y>n?a:o,h=Math.abs(l.label.y-n);if(h>=u.maxY){var c=l.label.x-e-l.len2*r,p=i+l.len,d=Math.abs(c)<p?Math.sqrt(h*h/(1-c*c/p/p)):p;u.rB=d,u.maxY=h}u.list.push(l)}f(o),f(a)}(t)}function f(t){for(var o=t.rB,a=o*o,s=0;s<t.list.length;s++){var l=t.list[s],u=Math.abs(l.label.y-n),h=i+l.len,c=h*h,p=Math.sqrt(Math.abs((1-u*u/a)*c)),f=e+(p+l.len2)*r,d=f-l.label.x;aw(l,l.targetTextWidth-d*r,!0),l.label.x=f}}}function aw(t,e,n){if(null==t.labelStyleWidth){var i=t.label,r=i.style,o=t.rect,a=r.backgroundColor,s=r.padding,l=s?s[1]+s[3]:0,u=r.overflow,h=o.width+(a?0:l);if(e<h||n){if(u&&u.match("break")){i.setStyle("backgroundColor",null),i.setStyle("width",e-l);var c=i.getBoundingRect();i.setStyle("width",Math.ceil(c.width)),i.setStyle("backgroundColor",a)}else{var p=e-l,f=e<h?p:n?p>t.unconstrainedWidth?null:p:null;i.setStyle("width",f)}sw(o,i)}}}function sw(t,e){uw.rect=t,Bx(uw,e,lw)}var lw={minMarginForce:[null,0,null,0],marginDefault:[1,0,1,0]},uw={};function hw(t){return"center"===t.position}function cw(t){var e,n,i=t.getData(),r=[],o=!1,a=(t.get("minShowLabelAngle")||0)*rw,s=i.getLayout("viewRect"),l=i.getLayout("r"),u=s.width,h=s.x,c=s.y,p=s.height;function f(t){t.ignore=!0}i.each((function(t){var s=i.getItemGraphicEl(t),c=s.shape,d=s.getTextContent(),g=s.getTextGuideLine(),v=i.getItemModel(t),y=v.getModel("label"),m=y.get("position")||v.get(["emphasis","label","position"]),_=y.get("distanceToLabelLine"),x=y.get("alignTo"),b=Fr(y.get("edgeDistance"),u),w=y.get("bleedMargin");null==w&&(w=Math.min(u,p)>200?10:2);var S=v.getModel("labelLine"),M=S.get("length");M=Fr(M,u);var T=S.get("length2");if(T=Fr(T,u),Math.abs(c.endAngle-c.startAngle)<a)return R(d.states,f),d.ignore=!0,void(g&&(R(g.states,f),g.ignore=!0));if(function(t){if(!t.ignore)return!0;for(var e in t.states)if(!1===t.states[e].ignore)return!0;return!1}(d)){var k,C,D,I,A=(c.startAngle+c.endAngle)/2,L=Math.cos(A),P=Math.sin(A);e=c.cx,n=c.cy;var O="inside"===m||"inner"===m;if("center"===m)k=c.cx,C=c.cy,I="center";else{var N=(O?(c.r+c.r0)/2*L:c.r*L)+e,B=(O?(c.r+c.r0)/2*P:c.r*P)+n;if(k=N+3*L,C=B+3*P,!O){var E=N+L*(M+l-c.r),z=B+P*(M+l-c.r),F=E+(L<0?-1:1)*T;k="edge"===x?L<0?h+b:h+u-b:F+(L<0?-_:_),C=z,D=[[N,B],[E,z],[F,z]]}I=O?"center":"edge"===x?L>0?"right":"left":L>0?"left":"right"}var V=Math.PI,W=0,H=y.get("rotate");if(Y(H))W=H*(V/180);else if("center"===m)W=0;else if("radial"===H||!0===H){W=L<0?-A+V:-A}else if("tangential"===H&&"outside"!==m&&"outer"!==m){var G=Math.atan2(L,P);G<0&&(G=2*V+G),P>0&&(G=V+G),W=G-V}if(o=!!W,d.x=k,d.y=C,d.rotation=W,d.setStyle({verticalAlign:"middle"}),O){d.setStyle({align:I});var U=d.states.select;U&&(U.x+=d.x,U.y+=d.y)}else{var X=new Oe(0,0,0,0);sw(X,d),r.push({label:d,labelLine:g,position:m,len:M,len2:T,minTurnAngle:S.get("minTurnAngle"),maxSurfaceAngle:S.get("maxSurfaceAngle"),surfaceNormal:new _e(L,P),linePoints:D,textAlign:I,labelDistance:_,labelAlignTo:x,edgeDistance:b,bleedMargin:w,rect:X,unconstrainedWidth:X.width,labelStyleWidth:d.style.width})}s.setTextConfig({inside:O})}})),!o&&t.get("avoidLabelOverlap")&&function(t,e,n,i,r,o,a,s){for(var l=[],u=[],h=Number.MAX_VALUE,c=-Number.MAX_VALUE,p=0;p<t.length;p++){var f=t[p].label;hw(t[p])||(f.x<e?(h=Math.min(h,f.x),l.push(t[p])):(c=Math.max(c,f.x),u.push(t[p])))}for(p=0;p<t.length;p++)if(!hw(v=t[p])&&v.linePoints){if(null!=v.labelStyleWidth)continue;f=v.label;var d=v.linePoints,g=void 0;g="edge"===v.labelAlignTo?f.x<e?d[2][0]-v.labelDistance-a-v.edgeDistance:a+r-v.edgeDistance-d[2][0]-v.labelDistance:"labelLine"===v.labelAlignTo?f.x<e?h-a-v.bleedMargin:a+r-c-v.bleedMargin:f.x<e?f.x-a-v.bleedMargin:a+r-f.x-v.bleedMargin,v.targetTextWidth=g,aw(v,g,!1)}for(ow(u,e,n,i,1,0,o,0,s,c),ow(l,e,n,i,-1,0,o,0,s,h),p=0;p<t.length;p++){var v;if(!hw(v=t[p])&&v.linePoints){f=v.label,d=v.linePoints;var y="edge"===v.labelAlignTo,m=f.style.padding,_=m?m[1]+m[3]:0,x=f.style.backgroundColor?0:_,b=v.rect.width+x,w=d[1][0]-d[2][0];y?f.x<e?d[2][0]=a+v.edgeDistance+b+v.labelDistance:d[2][0]=a+r-v.edgeDistance-b-v.labelDistance:(f.x<e?d[2][0]=f.x+v.labelDistance:d[2][0]=f.x-v.labelDistance,d[1][0]=d[2][0]+w),d[1][1]=d[2][1]=f.y}}}(r,e,n,l,u,p,h,c);for(var d=0;d<r.length;d++){var g=r[d],v=g.label,y=g.labelLine,m=isNaN(v.x)||isNaN(v.y);if(v){v.setStyle({align:g.textAlign}),m&&(R(v.states,f),v.ignore=!0);var _=v.states.select;_&&(_.x+=v.x,_.y+=v.y)}if(y){var x=g.linePoints;m||!x?(R(y.states,f),y.ignore=!0):(Dx(x,g.minTurnAngle),Ix(x,g.surfaceNormal,g.maxSurfaceAngle),y.setShape({points:x}),v.__hostTarget.textGuideLineConfig={anchor:new _e(x[0][0],x[0][1])})}}}var pw=function(t){function e(e,n,i){var r=t.call(this)||this;r.z2=2;var o=new Rs;return r.setTextContent(o),r.updateData(e,n,i,!0),r}return n(e,t),e.prototype.updateData=function(t,e,n,i){var r=this,o=t.hostModel,a=t.getItemModel(e),s=a.getModel("emphasis"),l=t.getItemLayout(e),u=C(Nb(a.getModel("itemStyle"),l,!0),l);if(isNaN(u.startAngle))r.setShape(u);else{if(i){r.setShape(u);var h=o.getShallow("animationType");o.ecModel.ssr?(ih(r,{scaleX:0,scaleY:0},o,{dataIndex:e,isFrom:!0}),r.originX=u.cx,r.originY=u.cy):"scale"===h?(r.shape.r=l.r0,ih(r,{shape:{r:l.r}},o,e)):null!=n?(r.setShape({startAngle:n,endAngle:n}),ih(r,{shape:{startAngle:l.startAngle,endAngle:l.endAngle}},o,e)):(r.shape.endAngle=l.startAngle,nh(r,{shape:{endAngle:l.endAngle}},o,e))}else lh(r),nh(r,{shape:u},o,e);r.useStyle(t.getItemVisual(e,"style")),Bl(r,a);var c=(l.startAngle+l.endAngle)/2,p=o.get("selectedOffset"),f=Math.cos(c)*p,d=Math.sin(c)*p,g=a.getShallow("cursor");g&&r.attr("cursor",g),this._updateLabel(o,t,e),r.ensureState("emphasis").shape=C({r:l.r+(s.get("scale")&&s.get("scaleSize")||0)},Nb(s.getModel("itemStyle"),l)),C(r.ensureState("select"),{x:f,y:d,shape:Nb(a.getModel(["select","itemStyle"]),l)}),C(r.ensureState("blur"),{shape:Nb(a.getModel(["blur","itemStyle"]),l)});var v=r.getTextGuideLine(),y=r.getTextContent();v&&C(v.ensureState("select"),{x:f,y:d}),C(y.ensureState("select"),{x:f,y:d}),Ol(this,s.get("focus"),s.get("blurScope"),s.get("disabled"))}},e.prototype._updateLabel=function(t,e,n){var i=this,r=e.getItemModel(n),o=r.getModel("labelLine"),a=e.getItemVisual(n,"style"),s=a&&a.fill,l=a&&a.opacity;Nh(i,Bh(r),{labelFetcher:e.hostModel,labelDataIndex:n,inheritColor:s,defaultOpacity:l,defaultText:t.getFormattedLabel(n,"normal")||e.getName(n)});var u=i.getTextContent();i.setTextConfig({position:null,rotation:null}),u.attr({z2:10});var h=r.get(["label","position"]);if("outside"!==h&&"outer"!==h)i.removeTextGuideLine();else{var c=this.getTextGuideLine();c||(c=new Iu,this.setTextGuideLine(c)),function(t,e,n){var i=t.getTextGuideLine(),r=t.getTextContent();if(r){for(var o=e.normal,a=o.get("show"),s=r.ignore,l=0;l<Qs.length;l++){var u=Qs[l],h=e[u],c="normal"===u;if(h){var p=h.get("show");if((c?s:nt(r.states[u]&&r.states[u].ignore,s))||!nt(p,a)){var f=c?i:i&&i.states[u];f&&(f.ignore=!0),i&&Ax(i,!0,u,h);continue}i||(i=new Iu,t.setTextGuideLine(i),c||!s&&a||Ax(i,!0,"normal",e.normal),t.stateProxy&&(i.stateProxy=t.stateProxy)),Ax(i,!1,u,h)}}if(i){D(i.style,n),i.style.fill=null;var d=o.get("showAbove");(t.textGuideLineConfig=t.textGuideLineConfig||{}).showAbove=d||!1,i.buildPath=Lx}}else i&&t.removeTextGuideLine()}(this,function(t,e){e=e||"labelLine";for(var n={normal:t.getModel(e)},i=0;i<$s.length;i++){var r=$s[i];n[r]=t.getModel([r,e])}return n}(r),{stroke:s,opacity:it(o.get(["lineStyle","opacity"]),l,1)})}},e}(wu),fw=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.ignoreLabelLineUpdate=!0,e}return n(e,t),e.prototype.render=function(t,e,n,i){var r,o=t.getData(),a=this._data,s=this.group;if(!a&&o.count()>0){for(var l=o.getItemLayout(0),u=1;isNaN(l&&l.startAngle)&&u<o.count();++u)l=o.getItemLayout(u);l&&(r=l.startAngle)}if(this._emptyCircleSector&&s.remove(this._emptyCircleSector),0===o.count()&&t.get("showEmptyCircle")){var h=iw(t),c=new wu({shape:T(h)});c.useStyle(t.getModel("emptyCircleStyle").getItemStyle()),this._emptyCircleSector=c,s.add(c)}o.diff(a).add((function(t){var e=new pw(o,t,r);o.setItemGraphicEl(t,e),s.add(e)})).update((function(t,e){var n=a.getItemGraphicEl(e);n.updateData(o,t,r),n.off("click"),s.add(n),o.setItemGraphicEl(t,n)})).remove((function(e){sh(a.getItemGraphicEl(e),t,e)})).execute(),cw(t),"expansion"!==t.get("animationTypeUpdate")&&(this._data=o)},e.prototype.dispose=function(){},e.prototype.containPoint=function(t,e){var n=e.getData().getItemLayout(0);if(n){var i=t[0]-n.cx,r=t[1]-n.cy,o=Math.sqrt(i*i+r*r);return o<=n.r&&o>=n.r0}},e.type="pie",e}(Kd);var dw,gw=function(){function t(t,e){this._getDataWithEncodedVisual=t,this._getRawData=e}return t.prototype.getAllNames=function(){var t=this._getRawData();return t.mapArray(t.getName)},t.prototype.containName=function(t){return this._getRawData().indexOfName(t)>=0},t.prototype.indexOfName=function(t){return this._getDataWithEncodedVisual().indexOfName(t)},t.prototype.getItemVisual=function(t,e){return this._getDataWithEncodedVisual().getItemVisual(t,e)},t}(),vw=To(),yw=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.init=function(e){t.prototype.init.apply(this,arguments),this.legendVisualProvider=new gw(V(this.getData,this),V(this.getRawData,this)),this._defaultLabelLine(e)},e.prototype.mergeOption=function(){t.prototype.mergeOption.apply(this,arguments)},e.prototype.getInitialData=function(){return function(t,e,n){e=H(e)&&{coordDimensions:e}||C({encodeDefine:t.getEncode()},e);var i=t.getSource(),r=bm(i,e).dimensions,o=new xm(r,t);return o.initData(i,n),o}(this,{coordDimensions:["value"],encodeDefaulter:W(Gp,this)})},e.prototype.getDataParams=function(e){var n=this.getData(),i=vw(n),r=i.seats;if(!r){var o=[];n.each(n.mapDimension("value"),(function(t){o.push(t)})),r=i.seats=Xr(o,n.hostModel.get("percentPrecision"))}var a=t.prototype.getDataParams.call(this,e);return a.percent=r[e]||0,a.$vars.push("percent"),a},e.prototype._defaultLabelLine=function(t){fo(t,"labelLine",["show"]);var e=t.labelLine,n=t.emphasis.labelLine;e.show=e.show&&t.label.show,n.show=n.show&&t.emphasis.label.show},e.type="series.pie",e.defaultOption={z:2,legendHoverLink:!0,colorBy:"data",center:["50%","50%"],radius:[0,"50%"],clockwise:!0,startAngle:90,endAngle:"auto",padAngle:0,minAngle:0,minShowLabelAngle:0,selectedOffset:10,percentPrecision:2,stillShowZeroSum:!0,coordinateSystemUsage:"box",left:0,top:0,right:0,bottom:0,width:null,height:null,label:{rotate:0,show:!0,overflow:"truncate",position:"outer",alignTo:"none",edgeDistance:"25%",distanceToLabelLine:5},labelLine:{show:!0,length:15,length2:30,smooth:!1,minTurnAngle:90,maxSurfaceAngle:90,lineStyle:{width:1,type:"solid"}},itemStyle:{borderWidth:1,borderJoin:"round"},showEmptyCircle:!0,emptyCircleStyle:{color:"lightgray",opacity:1},labelLayout:{hideOverlap:!0},emphasis:{scale:!0,scaleSize:5},avoidLabelOverlap:!0,animationType:"expansion",animationDuration:1e3,animationTypeUpdate:"transition",animationEasingUpdate:"cubicInOut",animationDurationUpdate:500,animationEasing:"cubicInOut"},e}(zd);dw={fullType:yw.type,getCoord2:function(t){return t.getShallow("center")}},np.set(dw.fullType,{getCoord2:void 0}).getCoord2=dw.getCoord2;var mw={left:0,right:0,top:0,bottom:0},_w=["25%","25%"],xw=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.mergeDefaultAndTheme=function(e,n){var i=yp(e.outerBounds);t.prototype.mergeDefaultAndTheme.apply(this,arguments),i&&e.outerBounds&&vp(e.outerBounds,i)},e.prototype.mergeOption=function(e,n){t.prototype.mergeOption.apply(this,arguments),this.option.outerBounds&&e.outerBounds&&vp(this.option.outerBounds,e.outerBounds)},e.type="grid",e.dependencies=["xAxis","yAxis"],e.layoutMode="box",e.defaultOption={show:!1,z:0,left:"15%",top:65,right:"10%",bottom:80,containLabel:!1,outerBoundsMode:"auto",outerBounds:mw,outerBoundsContain:"all",outerBoundsClampWidth:_w[0],outerBoundsClampHeight:_w[1],backgroundColor:xp.color.transparent,borderWidth:1,borderColor:xp.color.neutral30},e}(_p),bw=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.getCoordSysModel=function(){return this.getReferringComponents("grid",Io).models[0]},e.type="cartesian2dAxis",e}(_p);P(bw,P_);var ww={show:!0,z:0,inverse:!1,name:"",nameLocation:"end",nameRotate:null,nameTruncate:{maxWidth:null,ellipsis:"...",placeholder:"."},nameTextStyle:{},nameGap:15,silent:!1,triggerEvent:!1,tooltip:{show:!1},axisPointer:{},axisLine:{show:!0,onZero:!0,onZeroAxisIndex:null,lineStyle:{color:xp.color.axisLine,width:1,type:"solid"},symbol:["none","none"],symbolSize:[10,15],breakLine:!0},axisTick:{show:!0,inside:!1,length:5,lineStyle:{width:1}},axisLabel:{show:!0,inside:!1,rotate:0,showMinLabel:null,showMaxLabel:null,margin:8,fontSize:12,color:xp.color.axisLabel,textMargin:[0,3]},splitLine:{show:!0,showMinLine:!0,showMaxLine:!0,lineStyle:{color:xp.color.axisSplitLine,width:1,type:"solid"}},splitArea:{show:!1,areaStyle:{color:[xp.color.backgroundTint,xp.color.backgroundTransparent]}},breakArea:{show:!0,itemStyle:{color:xp.color.neutral00,borderColor:xp.color.border,borderWidth:1,borderType:[3,3],opacity:.6},zigzagAmplitude:4,zigzagMinSpan:4,zigzagMaxSpan:20,zigzagZ:100,expandOnClick:!0},breakLabelLayout:{moveOverlap:"auto"}},Sw=k({boundaryGap:!0,deduplication:null,jitter:0,jitterOverlap:!0,jitterMargin:2,splitLine:{show:!1},axisTick:{alignWithLabel:!1,interval:"auto",show:"auto"},axisLabel:{interval:"auto"}},ww),Mw=k({boundaryGap:[0,0],axisLine:{show:"auto"},axisTick:{show:"auto"},splitNumber:5,minorTick:{show:!1,splitNumber:5,length:3,lineStyle:{}},minorSplitLine:{show:!1,lineStyle:{color:xp.color.axisMinorSplitLine,width:1}}},ww),Tw={category:Sw,value:Mw,time:k({splitNumber:6,axisLabel:{showMinLabel:!1,showMaxLabel:!1,rich:{primary:{fontWeight:"bold"}}},splitLine:{show:!1}},Mw),log:D({logBase:10},Mw)},kw={value:1,category:1,time:1,log:1};function Cw(t,e,i,r){R(kw,(function(o,a){var s=k(k({},Tw[a],!0),r,!0),l=function(t){function i(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e+"Axis."+a,n}return n(i,t),i.prototype.mergeDefaultAndTheme=function(t,e){var n=gp(this),i=n?yp(t):{};k(t,e.getTheme().get(a+"Axis")),k(t,this.getDefaultOption()),t.type=Dw(t),n&&vp(t,i,n)},i.prototype.optionUpdated=function(){"category"===this.option.type&&(this.__ordinalMeta=Hm.createByAxisModel(this))},i.prototype.getCategories=function(t){var e=this.option;if("category"===e.type)return t?e.data:this.__ordinalMeta.categories},i.prototype.getOrdinalMeta=function(){return this.__ordinalMeta},i.prototype.updateAxisBreaks=function(t){return{breaks:[]}},i.type=e+"Axis."+a,i.defaultOption=s,i}(i);t.registerComponentModel(l)})),t.registerSubTypeDefaulter(e+"Axis",Dw)}function Dw(t){return t.type||(t.data?"category":"value")}var Iw=function(){function t(t){this.type="cartesian",this._dimList=[],this._axes={},this.name=t||""}return t.prototype.getAxis=function(t){return this._axes[t]},t.prototype.getAxes=function(){return N(this._dimList,(function(t){return this._axes[t]}),this)},t.prototype.getAxesByScale=function(t){return t=t.toLowerCase(),E(this.getAxes(),(function(e){return e.scale.type===t}))},t.prototype.addAxis=function(t){var e=t.dim;this._axes[e]=t,this._dimList.push(e)},t}(),Aw=["x","y"];function Lw(t){return("interval"===t.type||"time"===t.type)&&!t.hasBreaks()}var Pw=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="cartesian2d",e.dimensions=Aw,e}return n(e,t),e.prototype.calcAffineTransform=function(){this._transform=this._invTransform=null;var t=this.getAxis("x").scale,e=this.getAxis("y").scale;if(Lw(t)&&Lw(e)){var n=t.getExtent(),i=e.getExtent(),r=this.dataToPoint([n[0],i[0]]),o=this.dataToPoint([n[1],i[1]]),a=n[1]-n[0],s=i[1]-i[0];if(a&&s){var l=(o[0]-r[0])/a,u=(o[1]-r[1])/s,h=r[0]-n[0]*l,c=r[1]-i[0]*u,p=this._transform=[l,0,0,u,h,c];this._invTransform=ye([],p)}}},e.prototype.getBaseAxis=function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAxis("x")},e.prototype.containPoint=function(t){var e=this.getAxis("x"),n=this.getAxis("y");return e.contain(e.toLocalCoord(t[0]))&&n.contain(n.toLocalCoord(t[1]))},e.prototype.containData=function(t){return this.getAxis("x").containData(t[0])&&this.getAxis("y").containData(t[1])},e.prototype.containZone=function(t,e){var n=this.dataToPoint(t),i=this.dataToPoint(e),r=this.getArea(),o=new Oe(n[0],n[1],i[0]-n[0],i[1]-n[1]);return r.intersect(o)},e.prototype.dataToPoint=function(t,e,n){n=n||[];var i=t[0],r=t[1];if(this._transform&&null!=i&&isFinite(i)&&null!=r&&isFinite(r))return zt(n,t,this._transform);var o=this.getAxis("x"),a=this.getAxis("y");return n[0]=o.toGlobalCoord(o.dataToCoord(i,e)),n[1]=a.toGlobalCoord(a.dataToCoord(r,e)),n},e.prototype.clampData=function(t,e){var n=this.getAxis("x").scale,i=this.getAxis("y").scale,r=n.getExtent(),o=i.getExtent(),a=n.parse(t[0]),s=i.parse(t[1]);return(e=e||[])[0]=Math.min(Math.max(Math.min(r[0],r[1]),a),Math.max(r[0],r[1])),e[1]=Math.min(Math.max(Math.min(o[0],o[1]),s),Math.max(o[0],o[1])),e},e.prototype.pointToData=function(t,e,n){if(n=n||[],this._invTransform)return zt(n,t,this._invTransform);var i=this.getAxis("x"),r=this.getAxis("y");return n[0]=i.coordToData(i.toLocalCoord(t[0]),e),n[1]=r.coordToData(r.toLocalCoord(t[1]),e),n},e.prototype.getOtherAxis=function(t){return this.getAxis("x"===t.dim?"y":"x")},e.prototype.getArea=function(t){t=t||0;var e=this.getAxis("x").getGlobalExtent(),n=this.getAxis("y").getGlobalExtent(),i=Math.min(e[0],e[1])-t,r=Math.min(n[0],n[1])-t,o=Math.max(e[0],e[1])-i+t,a=Math.max(n[0],n[1])-r+t;return new Oe(i,r,o,a)},e}(Iw),Ow=function(t){function e(e,n,i,r,o){var a=t.call(this,e,n,i)||this;return a.index=0,a.type=r||"value",a.position=o||"bottom",a}return n(e,t),e.prototype.isHorizontal=function(){var t=this.position;return"top"===t||"bottom"===t},e.prototype.getGlobalExtent=function(t){var e=this.getExtent();return e[0]=this.toGlobalCoord(e[0]),e[1]=this.toGlobalCoord(e[1]),t&&e[0]>e[1]&&e.reverse(),e},e.prototype.pointToData=function(t,e){return this.coordToData(this.toLocalCoord(t["x"===this.dim?0:1]),e)},e.prototype.setCategorySortInfo=function(t){if("category"!==this.type)return!1;this.model.option.categorySortInfo=t,this.scale.setSortInfo(t)},e}(yx),Rw="expandAxisBreak",Nw=Math.PI,Bw=[[1,2,1,2],[5,3,5,3],[8,3,8,3]],Ew=[[0,1,0,1],[0,3,0,3],[0,3,0,3]],zw=To(),Fw=To(),Vw=function(){function t(t){this.recordMap={},this.resolveAxisNameOverlap=t}return t.prototype.ensureRecord=function(t){var e=t.axis.dim,n=t.componentIndex,i=this.recordMap,r=i[e]||(i[e]=[]);return r[n]||(r[n]={ready:{}})},t}();var Ww=[1,0,0,1,0,0],Hw=new Oe(0,0,0,0),Gw=function(t,e,n,i,r,o){if(I_(t.nameLocation)){var a=o.stOccupiedRect;a&&Uw(function(t,e,n){return t.transform=Ah(t.transform,n),t.localRect=Ih(t.localRect,e),t.rect=Ih(t.rect,e),n&&t.rect.applyTransform(n),t.axisAligned=Ch(n),t.obb=void 0,(t.label=t.label||{}).ignore=!1,t}({},a,o.transGroup.transform),i,r)}else Xw(o.labelInfoList,o.dirVec,i,r)};function Uw(t,e,n){var i=new _e;Vx(t,e,i,{direction:Math.atan2(n.y,n.x),bidirectional:!1,touchThreshold:.05})&&function(t,e){if(t){t.label.x+=e.x,t.label.y+=e.y,t.label.markRedraw();var n=t.transform;n&&(n[4]+=e.x,n[5]+=e.y);var i=t.rect;i&&(i.x+=e.x,i.y+=e.y);var r=t.obb;r&&r.fromBoundingRect(t.localRect,n)}}(e,i)}function Xw(t,e,n,i){for(var r=_e.dot(i,e)>=0,o=0,a=t.length;o<a;o++){var s=t[r?o:a-1-o];s.label.ignore||Uw(s,n,i)}}var Yw=function(){function t(t,e,n,i){this.group=new Tr,this._axisModel=t,this._api=e,this._local={},this._shared=i||new Vw(Gw),this._resetCfgDetermined(n)}return t.prototype.updateCfg=function(t){var e=this._cfg.raw;e.position=t.position,e.labelOffset=t.labelOffset,this._resetCfgDetermined(e)},t.prototype.__getRawCfg=function(){return this._cfg.raw},t.prototype._resetCfgDetermined=function(t){var e=this._axisModel,n=e.getDefaultOption?e.getDefaultOption():{},i=nt(t.axisName,e.get("name")),r=e.get("nameMoveOverlap");null!=r&&"auto"!==r||(r=nt(t.defaultNameMoveOverlap,!0));var o={raw:t,position:t.position,rotation:t.rotation,nameDirection:nt(t.nameDirection,1),tickDirection:nt(t.tickDirection,1),labelDirection:nt(t.labelDirection,1),labelOffset:nt(t.labelOffset,0),silent:nt(t.silent,!0),axisName:i,nameLocation:it(e.get("nameLocation"),n.nameLocation,"end"),shouldNameMoveOverlap:iS(i)&&r,optionHideOverlap:e.get(["axisLabel","hideOverlap"]),showMinorTicks:e.get(["minorTick","show"])};this._cfg=o;var a=new Tr({x:o.position[0],y:o.position[1],rotation:o.rotation});a.updateTransform(),this._transformGroup=a;var s=this._shared.ensureRecord(e);s.transGroup=this._transformGroup,s.dirVec=new _e(Math.cos(-o.rotation),Math.sin(-o.rotation))},t.prototype.build=function(t,e){var n=this;return t||(t={axisLine:!0,axisTickLabelEstimate:!1,axisTickLabelDetermine:!0,axisName:!0}),R(qw,(function(i){t[i]&&Zw[i](n._cfg,n._local,n._shared,n._axisModel,n.group,n._transformGroup,n._api,e||{})})),this},t.innerTextLayout=function(t,e,n){var i,r,o=qr(e-t);return Zr(o)?(r=n>0?"top":"bottom",i="center"):Zr(o-Nw)?(r=n>0?"bottom":"top",i="center"):(r="middle",i=o>0&&o<Nw?n>0?"right":"left":n>0?"left":"right"),{rotation:o,textAlign:i,textVerticalAlign:r}},t.makeAxisEventDataBase=function(t){var e={componentType:t.mainType,componentIndex:t.componentIndex};return e[t.mainType+"Index"]=t.componentIndex,e},t.isLabelSilent=function(t){var e=t.get("tooltip");return t.get("silent")||!(t.get("triggerEvent")||e&&e.show)},t}(),qw=["axisLine","axisTickLabelEstimate","axisTickLabelDetermine","axisName"],Zw={axisLine:function(t,e,n,i,r,o,a){var s=i.get(["axisLine","show"]);if("auto"===s&&(s=!0,null!=t.raw.axisLineAutoShow&&(s=!!t.raw.axisLineAutoShow)),s){var l=i.axis.getExtent(),u=o.transform,h=[l[0],0],c=[l[1],0],p=h[0]>c[0];u&&(zt(h,h,u),zt(c,c,u));var f=C({lineCap:"round"},i.getModel(["axisLine","lineStyle"]).getLineStyle()),d={strokeContainThreshold:t.raw.strokeContainThreshold||5,silent:!0,z2:1,style:f};if(i.get(["axisLine","breakLine"])&&i.axis.scale.hasBreaks())null.buildAxisBreakLine(i,r,o,d);else{var g=new Pu(C({shape:{x1:h[0],y1:h[1],x2:c[0],y2:c[1]}},d));_h(g.shape,g.style.lineWidth),g.anid="line",r.add(g)}var v=i.get(["axisLine","symbol"]);if(null!=v){var y=i.get(["axisLine","symbolSize"]);U(v)&&(v=[v,v]),(U(y)||Y(y))&&(y=[y,y]);var m=Jg(i.get(["axisLine","symbolOffset"])||0,y),_=y[0],x=y[1];R([{rotate:t.rotation+Math.PI/2,offset:m[0],r:0},{rotate:t.rotation-Math.PI/2,offset:m[1],r:Math.sqrt((h[0]-c[0])*(h[0]-c[0])+(h[1]-c[1])*(h[1]-c[1]))}],(function(e,n){if("none"!==v[n]&&null!=v[n]){var i=Qg(v[n],-_/2,-x/2,_,x,f.stroke,!0),o=e.r+e.offset,a=p?c:h;i.attr({rotation:e.rotate,x:a[0]+o*Math.cos(t.rotation),y:a[1]-o*Math.sin(t.rotation),silent:!0,z2:11}),r.add(i)}}))}}},axisTickLabelEstimate:function(t,e,n,i,r,o,a,s){Qw(e,r,s)&&jw(t,e,n,i,r,o,a,tx)},axisTickLabelDetermine:function(t,e,n,i,r,o,a,s){Qw(e,r,s)&&jw(t,e,n,i,r,o,a,ex);var l=function(t,e,n,i){var r=i.axis,o=i.getModel("axisTick"),a=o.get("show");"auto"===a&&(a=!0,null!=t.raw.axisTickAutoShow&&(a=!!t.raw.axisTickAutoShow));if(!a||r.scale.isBlank())return[];for(var s=o.getModel("lineStyle"),l=t.tickDirection*o.get("length"),u=$w(r.getTicksCoords(),n.transform,l,D(s.getLineStyle(),{stroke:i.get(["axisLine","lineStyle","color"])}),"ticks"),h=0;h<u.length;h++)e.add(u[h]);return u}(t,r,o,i);!function(t,e,n){if(t.showMinorTicks)return;R(e,(function(t){if(t&&t.label.ignore)for(var e=0;e<n.length;e++){var i=n[e],r=Fw(i),o=zw(t.label);if(null!=r.tickValue&&!r.onBand&&r.tickValue===o.tickValue)return void Kw(i)}}))}(t,e.labelLayoutList,l),function(t,e,n,i,r){var o=i.axis,a=i.getModel("minorTick");if(!t.showMinorTicks||o.scale.isBlank())return;var s=o.getMinorTicksCoords();if(!s.length)return;for(var l=a.getModel("lineStyle"),u=r*a.get("length"),h=D(l.getLineStyle(),D(i.getModel("axisTick").getLineStyle(),{stroke:i.get(["axisLine","lineStyle","color"])})),c=0;c<s.length;c++)for(var p=$w(s[c],n.transform,u,h,"minorticks_"+c),f=0;f<p.length;f++)e.add(p[f])}(t,r,o,i,t.tickDirection)},axisName:function(t,e,n,i,r,o,a,s){var l=n.ensureRecord(i);e.nameEl&&(r.remove(e.nameEl),e.nameEl=l.nameLayout=l.nameLocation=null);var u=t.axisName;if(iS(u)){var h=t.nameLocation,c=t.nameDirection,p=i.getModel("nameTextStyle"),f=i.get("nameGap")||0,d=i.axis.getExtent(),g=i.axis.inverse?-1:1,v=new _e(0,0),y=new _e(0,0);"start"===h?(v.x=d[0]-g*f,y.x=-g):"end"===h?(v.x=d[1]+g*f,y.x=g):(v.x=(d[0]+d[1])/2,v.y=t.labelOffset+c*f,y.y=c);var m=[1,0,0,1,0,0];y.transform(ge(m,m,t.rotation));var _,x,b=i.get("nameRotate");null!=b&&(b=b*Nw/180),I_(h)?_=Yw.innerTextLayout(t.rotation,null!=b?b:t.rotation,c):(_=function(t,e,n,i){var r,o,a=qr(n-t),s=i[0]>i[1],l="start"===e&&!s||"start"!==e&&s;Zr(a-Nw/2)?(o=l?"bottom":"top",r="center"):Zr(a-1.5*Nw)?(o=l?"top":"bottom",r="center"):(o="middle",r=a<1.5*Nw&&a>Nw/2?l?"left":"right":l?"right":"left");return{rotation:a,textAlign:r,textVerticalAlign:o}}(t.rotation,h,b||0,d),null!=(x=t.raw.axisNameAvailableWidth)&&(x=Math.abs(x/Math.sin(_.rotation)),!isFinite(x)&&(x=null)));var w=p.getFont(),S=i.get("nameTruncate",!0)||{},M=S.ellipsis,T=et(t.raw.nameTruncateMaxWidth,S.maxWidth,x),k=s.nameMarginLevel||0,C=new Rs({x:v.x,y:v.y,rotation:_.rotation,silent:Yw.isLabelSilent(i),style:Eh(p,{text:u,font:w,overflow:"truncate",width:T,ellipsis:M,fill:p.getTextColor()||i.get(["axisLine","lineStyle","color"]),align:p.get("align")||_.textAlign,verticalAlign:p.get("verticalAlign")||_.textVerticalAlign}),z2:1});if(Mh({el:C,componentModel:i,itemName:u}),C.__fullText=u,C.anid="name",i.get("triggerEvent")){var D=Yw.makeAxisEventDataBase(i);D.targetType="axisName",D.name=u,Ys(C).eventData=D}o.add(C),C.updateTransform(),e.nameEl=C;var I=l.nameLayout=Nx({label:C,priority:C.z2,defaultAttr:{ignore:C.ignore},marginDefault:I_(h)?Bw[k]:Ew[k]});if(l.nameLocation=h,r.add(C),C.decomposeTransform(),t.shouldNameMoveOverlap&&I){var A=n.ensureRecord(i);0,n.resolveAxisNameOverlap(t,n,i,I,y,A)}}}};function jw(t,e,n,i,r,o,a,s){Jw(e)||function(t,e,n,i,r,o){var a=r.axis,s=et(t.raw.axisLabelShow,r.get(["axisLabel","show"])),l=new Tr;n.add(l);var u=nx(i);if(!s||a.scale.isBlank())return void tS(e,[],l,u);var h=r.getModel("axisLabel"),c=a.getViewLabels(u),p=(et(t.raw.labelRotate,h.get("rotate"))||0)*Nw/180,f=Yw.innerTextLayout(t.rotation,p,t.labelDirection),d=r.getCategories&&r.getCategories(!0),g=[],v=r.get("triggerEvent"),y=1/0,m=-1/0;R(c,(function(t,e){var n,i="ordinal"===a.scale.type?a.scale.getRawOrdinalNumber(t.tickValue):t.tickValue,s=t.formattedLabel,u=t.rawLabel,p=h;if(d&&d[i]){var _=d[i];q(_)&&_.textStyle&&(p=new ic(_.textStyle,h,r.ecModel))}var x=p.getTextColor()||r.get(["axisLine","lineStyle","color"]),b=p.getShallow("align",!0)||f.textAlign,w=nt(p.getShallow("alignMinLabel",!0),b),S=nt(p.getShallow("alignMaxLabel",!0),b),M=p.getShallow("verticalAlign",!0)||p.getShallow("baseline",!0)||f.textVerticalAlign,T=nt(p.getShallow("verticalAlignMinLabel",!0),M),k=nt(p.getShallow("verticalAlignMaxLabel",!0),M),C=10+((null===(n=t.time)||void 0===n?void 0:n.level)||0);y=Math.min(y,C),m=Math.max(m,C);var D=new Rs({x:0,y:0,rotation:0,silent:Yw.isLabelSilent(r),z2:C,style:Eh(p,{text:s,align:0===e?w:e===c.length-1?S:b,verticalAlign:0===e?T:e===c.length-1?k:M,fill:G(x)?x("category"===a.type?u:"value"===a.type?i+"":i,e):x})});D.anid="label_"+i;var I=zw(D);if(I.break=t.break,I.tickValue=i,I.layoutRotation=f.rotation,Mh({el:D,componentModel:r,itemName:s,formatterParamsExtra:{isTruncated:function(){return D.isTruncated},value:u,tickIndex:e}}),v){var A=Yw.makeAxisEventDataBase(r);A.targetType="axisLabel",A.value=u,A.tickIndex=e,t.break&&(A.break={start:t.break.parsedBreak.vmin,end:t.break.parsedBreak.vmax}),"category"===a.type&&(A.dataIndex=i),Ys(D).eventData=A,t.break&&function(t,e,n,i){n.on("click",(function(n){var r={type:Rw,breaks:[{start:i.parsedBreak.breakOption.start,end:i.parsedBreak.breakOption.end}]};r[t.axis.dim+"AxisIndex"]=t.componentIndex,e.dispatchAction(r)}))}(r,o,D,t.break)}g.push(D),l.add(D)}));var _=N(g,(function(t){return{label:t,priority:zw(t).break?t.z2+(m-y+1):t.z2,defaultAttr:{ignore:t.ignore}}}));tS(e,_,l,u)}(t,e,r,s,i,a);var l=e.labelLayoutList;!function(t,e,n,i){var r=e.get(["axisLabel","margin"]);R(n,(function(n,o){var a=Nx(n);if(a){var s=a.label,l=zw(s);a.suggestIgnore=s.ignore,s.ignore=!1,er(eS,nS),eS.x=e.axis.dataToCoord(l.tickValue),eS.y=t.labelOffset+t.labelDirection*r,eS.rotation=l.layoutRotation,i.add(eS),eS.updateTransform(),i.remove(eS),eS.decomposeTransform(),er(s,eS),s.markRedraw(),Ox(a,!0),Nx(a)}}))}(t,i,l,o),t.rotation;var u=t.optionHideOverlap;!function(t,e,n){if(D_(t.axis))return;function i(t,i,r){var o=Nx(e[i]),a=Nx(e[r]);if(o&&a)if(!1===t||o.suggestIgnore)Kw(o.label);else if(a.suggestIgnore)Kw(a.label);else{var s=.1;if(!n){var l=[0,0,0,0];o=zx({marginForce:l},o),a=zx({marginForce:l},a)}Vx(o,a,null,{touchThreshold:s})&&Kw(t?a.label:o.label)}}var r=t.get(["axisLabel","showMinLabel"]),o=t.get(["axisLabel","showMaxLabel"]),a=e.length;i(r,0,1),i(o,a-1,a-2)}(i,l,u),u&&function(t){var e=[];function n(t){if(!t.ignore){var e=t.ensureState("emphasis");null==e.ignore&&(e.ignore=!1)}t.ignore=!0}t.sort((function(t,e){return(e.suggestIgnore?1:0)-(t.suggestIgnore?1:0)||e.priority-t.priority}));for(var i=0;i<t.length;i++){var r=Nx(t[i]);if(!r.label.ignore){for(var o=r.label,a=r.labelLine,s=!1,l=0;l<e.length;l++)if(Vx(r,e[l],null,{touchThreshold:.05})){s=!0;break}s?(n(o),a&&n(a)):e.push(r)}}}(E(l,(function(t){return t&&!t.label.ignore}))),function(t,e,n,i){var r,o=n.axis,a=e.ensureRecord(n),s=[],l=iS(t.axisName)&&I_(t.nameLocation);R(i,(function(t){var e=Nx(t);if(e&&!e.label.ignore){s.push(e);var n=a.transGroup;l&&(n.transform?ye(Ww,n.transform):ce(Ww),e.transform&&fe(Ww,Ww,e.transform),Oe.copy(Hw,e.localRect),Hw.applyTransform(Ww),r?r.union(Hw):Oe.copy(r=new Oe(0,0,0,0),Hw))}}));var u=Math.abs(a.dirVec.x)>.1?"x":"y",h=a.transGroup[u];if(s.sort((function(t,e){return Math.abs(t.label[u]-h)-Math.abs(e.label[u]-h)})),l&&r){var c=o.getExtent(),p=Math.min(c[0],c[1]),f=Math.max(c[0],c[1])-p;r.union(new Oe(p,0,f,1))}a.stOccupiedRect=r,a.labelInfoList=s}(t,n,i,l)}function Kw(t){t&&(t.ignore=!0)}function $w(t,e,n,i,r){for(var o=[],a=[],s=[],l=0;l<t.length;l++){var u=t[l].coord;a[0]=u,a[1]=0,s[0]=u,s[1]=n,e&&(zt(a,a,e),zt(s,s,e));var h=new Pu({shape:{x1:a[0],y1:a[1],x2:s[0],y2:s[1]},style:i,z2:2,autoBatch:!0,silent:!0});_h(h.shape,h.style.lineWidth),h.anid=r+"_"+t[l].tickValue,o.push(h);var c=Fw(h);c.onBand=!!t[l].onBand,c.tickValue=t[l].tickValue}return o}function Qw(t,e,n){if(Jw(t)){var i=t.axisLabelsCreationContext;0;var r=i.out.noPxChangeTryDetermine;if(n.noPxChange){for(var o=!0,a=0;a<r.length;a++)o=o&&r[a]();if(o)return!1}r.length&&(e.remove(t.labelGroup),tS(t,null,null,null))}return!0}function Jw(t){return!!t.labelLayoutList}function tS(t,e,n,i){t.labelLayoutList=e,t.labelGroup=n,t.axisLabelsCreationContext=i}var eS=new As,nS=new As;function iS(t){return!!t}function rS(t,e,n){n=n||{};var i=e.axis,r={},o=i.getAxesOnZeroOf()[0],a=i.position,s=o?"onZero":a,l=i.dim,u=[t.x,t.x+t.width,t.y,t.y+t.height],h={left:0,right:1,top:0,bottom:1,onZero:2},c=e.get("offset")||0,p="x"===l?[u[2]-c,u[3]+c]:[u[0]-c,u[1]+c];if(o){var f=o.toGlobalCoord(o.dataToCoord(0));p[h.onZero]=Math.max(Math.min(f,p[1]),p[0])}r.position=["y"===l?p[h[s]]:u[0],"x"===l?p[h[s]]:u[3]],r.rotation=Math.PI/2*("x"===l?0:1);r.labelDirection=r.tickDirection=r.nameDirection={top:-1,bottom:1,left:-1,right:1}[a],r.labelOffset=o?p[h[a]]-p[h.onZero]:0,e.get(["axisTick","inside"])&&(r.tickDirection=-r.tickDirection),et(n.labelInside,e.get(["axisLabel","inside"]))&&(r.labelDirection=-r.labelDirection);var d=e.get(["axisLabel","rotate"]);return r.labelRotate="top"===s?-d:d,r.z2=1,r}function oS(t){var e={xAxisModel:null,yAxisModel:null};return R(e,(function(n,i){var r=i.replace(/Model$/,""),o=t.getReferringComponents(r,Io).models[0];e[i]=o})),e}function aS(t,e,n){var i=Ym.prototype,r=i.getTicks.call(n),o=i.getTicks.call(n,{expandToNicedExtent:!0}),a=r.length-1,s=i.getInterval.call(n),l=w_(t,e),u=l.extent,h=l.fixMin,c=l.fixMax;"log"===t.type&&(u=Fm(t.base,u,!0)),t.setBreaksFromOption(L_(e)),t.setExtent(u[0],u[1]),t.calcNiceExtent({splitNumber:a,fixMin:h,fixMax:c});var p=i.getExtent.call(t);h&&(u[0]=p[0]),c&&(u[1]=p[1]);var f=i.getInterval.call(t),d=u[0],g=u[1];if(h&&c)f=(g-d)/a;else if(h)for(g=u[0]+f*a;g<u[1]&&isFinite(g)&&isFinite(u[1]);)f=Pm(f),g=u[0]+f*a;else if(c)for(d=u[1]-f*a;d>u[0]&&isFinite(d)&&isFinite(u[0]);)f=Pm(f),d=u[1]-f*a;else{t.getTicks().length-1>a&&(f=Pm(f));var v=f*a;(d=Wr((g=Math.ceil(u[1]/f)*f)-v))<0&&u[0]>=0?(d=0,g=Wr(v)):g>0&&u[1]<=0&&(g=0,d=-Wr(v))}var y=(r[0].value-o[0].value)/s,m=(r[a].value-o[a].value)/s;i.setExtent.call(t,d+f*y,g+f*m),i.setInterval.call(t,f),(y||m)&&i.setNiceExtent.call(t,d+f,g-f)}var sS=[[3,1],[0,2]],lS=function(){function t(t,e,n){this.type="grid",this._coordsMap={},this._coordsList=[],this._axesMap={},this._axesList=[],this.axisPointerEnabled=!0,this.dimensions=Aw,this._initCartesian(t,e,n),this.model=t}return t.prototype.getRect=function(){return this._rect},t.prototype.update=function(t,e){var n=this._axesMap;function i(t){var e,n=F(t),i=n.length;if(i){for(var r=[],o=i-1;o>=0;o--){var a=t[+n[o]],s=a.model,l=a.scale;Am(l)&&s.get("alignTicks")&&null==s.get("interval")?r.push(a):(S_(l,s),Am(l)&&(e=a))}r.length&&(e||S_((e=r.pop()).scale,e.model),R(r,(function(t){aS(t.scale,t.model,e.scale)})))}}this._updateScale(t,this.model),i(n.x),i(n.y);var r={};R(n.x,(function(t){hS(n,"y",t,r)})),R(n.y,(function(t){hS(n,"x",t,r)})),this.resize(this.model,e)},t.prototype.resize=function(t,e,n){var i=dp(t,e),r=this._rect=pp(t.getBoxLayoutParams(),i.refContainer),o=this._axesMap,a=this._coordsList,s=t.get("containLabel");if(pS(o,r),!n){var l=function(t,e,n,i,r){var o=new Vw(vS);return R(n,(function(n){return R(n,(function(n){if(A_(n.model)){var a=!i;n.axisBuilder=function(t,e,n,i,r,o){for(var a=rS(t,n),s=!1,l=!1,u=0;u<e.length;u++)Am(e[u].getOtherAxis(n.axis).scale)&&(s=l=!0,"category"===n.axis.type&&n.axis.onBand&&(l=!1));return a.axisLineAutoShow=s,a.axisTickAutoShow=l,a.defaultNameMoveOverlap=o,new Yw(n,i,a,r)}(t,e,n.model,r,o,a)}}))})),o}(r,a,o,s,e),u=void 0;if(s)u=dS(r.clone(),"axisLabel",null,r,o,l,i);else{var h=function(t,e,n){var i,r=t.get("outerBoundsMode",!0);"same"===r?i=e.clone():null!=r&&"auto"!==r||(i=pp(t.get("outerBounds",!0)||mw,n.refContainer));var o,a=t.get("outerBoundsContain",!0);o=null==a||"auto"===a||A(["all","axisLabel"],a)<0?"all":a;var s=[Vr(nt(t.get("outerBoundsClampWidth",!0),_w[0]),e.width),Vr(nt(t.get("outerBoundsClampHeight",!0),_w[1]),e.height)];return{outerBoundsRect:i,parsedOuterBoundsContain:o,outerBoundsClamp:s}}(t,r,i),c=h.outerBoundsRect,p=h.parsedOuterBoundsContain,f=h.outerBoundsClamp;c&&(u=dS(c,p,f,r,o,l,i))}gS(r,o,ex,null,u,i)}R(this._coordsList,(function(t){t.calcAffineTransform()}))},t.prototype.getAxis=function(t,e){var n=this._axesMap[t];if(null!=n)return n[e||0]},t.prototype.getAxes=function(){return this._axesList.slice()},t.prototype.getCartesian=function(t,e){if(null!=t&&null!=e){var n="x"+t+"y"+e;return this._coordsMap[n]}q(t)&&(e=t.yAxisIndex,t=t.xAxisIndex);for(var i=0,r=this._coordsList;i<r.length;i++)if(r[i].getAxis("x").index===t||r[i].getAxis("y").index===e)return r[i]},t.prototype.getCartesians=function(){return this._coordsList.slice()},t.prototype.convertToPixel=function(t,e,n){var i=this._findConvertTarget(e);return i.cartesian?i.cartesian.dataToPoint(n):i.axis?i.axis.toGlobalCoord(i.axis.dataToCoord(n)):null},t.prototype.convertFromPixel=function(t,e,n){var i=this._findConvertTarget(e);return i.cartesian?i.cartesian.pointToData(n):i.axis?i.axis.coordToData(i.axis.toLocalCoord(n)):null},t.prototype._findConvertTarget=function(t){var e,n,i=t.seriesModel,r=t.xAxisModel||i&&i.getReferringComponents("xAxis",Io).models[0],o=t.yAxisModel||i&&i.getReferringComponents("yAxis",Io).models[0],a=t.gridModel,s=this._coordsList;if(i)A(s,e=i.coordinateSystem)<0&&(e=null);else if(r&&o)e=this.getCartesian(r.componentIndex,o.componentIndex);else if(r)n=this.getAxis("x",r.componentIndex);else if(o)n=this.getAxis("y",o.componentIndex);else if(a){a.coordinateSystem===this&&(e=this._coordsList[0])}return{cartesian:e,axis:n}},t.prototype.containPoint=function(t){var e=this._coordsList[0];if(e)return e.containPoint(t)},t.prototype._initCartesian=function(t,e,n){var i=this,r=this,o={left:!1,right:!1,top:!1,bottom:!1},a={x:{},y:{}},s={x:0,y:0};if(e.eachComponent("xAxis",l("x"),this),e.eachComponent("yAxis",l("y"),this),!s.x||!s.y)return this._axesMap={},void(this._axesList=[]);function l(e){return function(n,i){if(uS(n,t)){var l=n.get("position");"x"===e?"top"!==l&&"bottom"!==l&&(l=o.bottom?"top":"bottom"):"left"!==l&&"right"!==l&&(l=o.left?"right":"left"),o[l]=!0;var u=new Ow(e,M_(n),[0,0],n.get("type"),l),h="category"===u.type;u.onBand=h&&n.get("boundaryGap"),u.inverse=n.get("inverse"),n.axis=u,u.model=n,u.grid=r,u.index=i,r._axesList.push(u),a[e][i]=u,s[e]++}}}this._axesMap=a,R(a.x,(function(e,n){R(a.y,(function(r,o){var a="x"+n+"y"+o,s=new Pw(a);s.master=i,s.model=t,i._coordsMap[a]=s,i._coordsList.push(s),s.addAxis(e),s.addAxis(r)}))}))},t.prototype._updateScale=function(t,e){function n(t,e){R(function(t,e){var n={};return R(t.mapDimensionsAll(e),(function(e){n[Dm(t,e)]=!0})),F(n)}(t,e.dim),(function(n){e.scale.unionExtentFromData(t,n)}))}R(this._axesList,(function(t){if(t.scale.setExtent(1/0,-1/0),"category"===t.type){var e=t.model.get("categorySortInfo");t.scale.setSortInfo(e)}})),t.eachSeries((function(t){if(function(t){return t.coordinateSystem&&"cartesian2d"===t.coordinateSystem.type}(t)){var i=oS(t),r=i.xAxisModel,o=i.yAxisModel;if(!uS(r,e)||!uS(o,e))return;var a=this.getCartesian(r.componentIndex,o.componentIndex),s=t.getData(),l=a.getAxis("x"),u=a.getAxis("y");n(s,l),n(s,u)}}),this)},t.prototype.getTooltipAxes=function(t){var e=[],n=[];return R(this.getCartesians(),(function(i){var r=null!=t&&"auto"!==t?i.getAxis(t):i.getBaseAxis(),o=i.getOtherAxis(r);A(e,r)<0&&e.push(r),A(n,o)<0&&n.push(o)})),{baseAxes:e,otherAxes:n}},t.create=function(e,n){var i=[];return e.eachComponent("grid",(function(r,o){var a=new t(r,e,n);a.name="grid_"+o,a.resize(r,n,!0),r.coordinateSystem=a,i.push(a)})),e.eachSeries((function(t){!function(t){var e=t.targetModel,n=t.coordSysType,i=t.coordSysProvider,r=t.isDefaultDataCoordSys;t.allowNotFound;var o=ap(e),a=o.kind,s=o.coordSysType;if(r&&a!==rp&&(a=rp,s=n),a===ip||s!==n)return!1;var l=i(n,e);!!l&&(a===rp?e.coordinateSystem=l:e.boxCoordinateSystem=l)}({targetModel:t,coordSysType:"cartesian2d",coordSysProvider:function(){var e=oS(t),n=e.xAxisModel,i=e.yAxisModel,r=n.getCoordSysModel();0;return r.coordinateSystem.getCartesian(n.componentIndex,i.componentIndex)}})})),i},t.dimensions=Aw,t}();function uS(t,e){return t.getCoordSysModel()===e}function hS(t,e,n,i){n.getAxesOnZeroOf=function(){return r?[r]:[]};var r,o=t[e],a=n.model,s=a.get(["axisLine","onZero"]),l=a.get(["axisLine","onZeroAxisIndex"]);if(s){if(null!=l)cS(o[l])&&(r=o[l]);else for(var u in o)if(o.hasOwnProperty(u)&&cS(o[u])&&!i[h(o[u])]){r=o[u];break}r&&(i[h(r)]=!0)}function h(t){return t.dim+"_"+t.index}}function cS(t){return t&&"category"!==t.type&&"time"!==t.type&&function(t){var e=t.scale.getExtent(),n=e[0],i=e[1];return!(n>0&&i>0||n<0&&i<0)}(t)}function pS(t,e){R(t.x,(function(t){return fS(t,e.x,e.width)})),R(t.y,(function(t){return fS(t,e.y,e.height)}))}function fS(t,e,n){var i=[0,n],r=t.inverse?1:0;t.setExtent(i[r],i[1-r]),function(t,e){var n=t.getExtent(),i=n[0]+n[1];t.toGlobalCoord="x"===t.dim?function(t){return t+e}:function(t){return i-t+e},t.toLocalCoord="x"===t.dim?function(t){return t-e}:function(t){return i-t+e}}(t,e)}function dS(t,e,n,i,r,o,a){gS(i,r,tx,e,!1,a);var s=[0,0,0,0];u(0),u(1),h(i,0,NaN),h(i,1,NaN);var l=null==z(s,(function(t){return t>0}));return bh(i,s,!0,!0,n),pS(r,i),l;function u(t){R(r[hh[t]],(function(e){if(A_(e.model)){var n=o.ensureRecord(e.model),i=n.labelInfoList;if(i)for(var r=0;r<i.length;r++){var a=i[r],s=e.scale.normalize(zw(a.label).tickValue);s=1===t?1-s:s,h(a.rect,t,s),h(a.rect,1-t,NaN)}var l=n.nameLayout;if(l){s=I_(n.nameLocation)?.5:NaN;h(l.rect,t,s),h(l.rect,1-t,NaN)}}}))}function h(e,n,i){var r=t[hh[n]]-e[hh[n]],o=e[ch[n]]+e[hh[n]]-(t[ch[n]]+t[hh[n]]);r=c(r,1-i),o=c(o,i);var a=sS[n][0],l=sS[n][1];s[a]=Br(s[a],r),s[l]=Br(s[l],o)}function c(t,e){return t>0&&!tt(e)&&e>1e-4&&(t/=e),t}}function gS(t,e,n,i,r,o){var a=n===ex;R(e,(function(e){return R(e,(function(e){A_(e.model)&&(!function(t,e,n){var i=rS(e,n);t.updateCfg(i)}(e.axisBuilder,t,e.model),e.axisBuilder.build(a?{axisTickLabelDetermine:!0}:{axisTickLabelEstimate:!0},{noPxChange:r}))}))}));var s={x:0,y:0};function l(e){s[hh[1-e]]=t[ch[e]]<=.5*o.refContainer[ch[e]]?0:1-e==1?2:1}l(0),l(1),R(e,(function(t,e){return R(t,(function(t){A_(t.model)&&(("all"===i||a)&&t.axisBuilder.build({axisName:!0},{nameMarginLevel:s[e]}),a&&t.axisBuilder.build({axisLine:!0}))}))}))}var vS=function(t,e,n,i,r,o){var a="x"===n.axis.dim?"y":"x";Gw(t,0,0,i,r,o),I_(t.nameLocation)||R(e.recordMap[a],(function(t){t&&t.labelInfoList&&t.dirVec&&Xw(t.labelInfoList,t.dirVec,i,r)}))};function yS(t){var e=mS(t);if(e){var n=e.axisPointerModel,i=e.axis.scale,r=n.option,o=n.get("status"),a=n.get("value");null!=a&&(a=i.parse(a));var s=function(t){return!!t.get(["handle","show"])}(n);null==o&&(r.status=s?"show":"hide");var l=i.getExtent().slice();l[0]>l[1]&&l.reverse(),(null==a||a>l[1])&&(a=l[1]),a<l[0]&&(a=l[0]),r.value=a,s&&(r.status=e.axis.scale.isBlank()?"hide":"show")}}function mS(t){var e,n=(t.ecModel.getComponent("axisPointer")||{}).coordSysAxesInfo;return n&&n.axesInfo[(e=t,e.type+"||"+e.id)]}var _S={},xS=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return n(e,t),e.prototype.render=function(e,n,i,r){this.axisPointerClass&&yS(e),t.prototype.render.apply(this,arguments),this._doUpdateAxisPointerClass(e,i,!0)},e.prototype.updateAxisPointer=function(t,e,n,i){this._doUpdateAxisPointerClass(t,n,!1)},e.prototype.remove=function(t,e){var n=this._axisPointer;n&&n.remove(e)},e.prototype.dispose=function(e,n){this._disposeAxisPointer(n),t.prototype.dispose.apply(this,arguments)},e.prototype._doUpdateAxisPointerClass=function(t,n,i){var r=e.getAxisPointerClass(this.axisPointerClass);if(r){var o=function(t){var e=mS(t);return e&&e.axisPointerModel}(t);o?(this._axisPointer||(this._axisPointer=new r)).render(t,o,n,i):this._disposeAxisPointer(n)}},e.prototype._disposeAxisPointer=function(t){this._axisPointer&&this._axisPointer.dispose(t),this._axisPointer=null},e.registerAxisPointerClass=function(t,e){_S[t]=e},e.getAxisPointerClass=function(t){return t&&_S[t]},e.type="axis",e}(Yd),bS=To();var wS=["splitArea","splitLine","minorSplitLine","breakArea"],SS=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n.axisPointerClass="CartesianAxisPointer",n}return n(e,t),e.prototype.render=function(e,n,i,r){this.group.removeAll();var o=this._axisGroup;(this._axisGroup=new Tr,this.group.add(this._axisGroup),A_(e))&&(this._axisGroup.add(e.axis.axisBuilder.group),R(wS,(function(t){e.get([t,"show"])&&MS[t](this,this._axisGroup,e,e.getCoordSysModel(),i)}),this),r&&"changeAxisOrder"===r.type&&r.isInitSort||function(t,e,n){if(t&&e){var i,r=(i={},t.traverse((function(t){xh(t)&&t.anid&&(i[t.anid]=t)})),i);e.traverse((function(t){if(xh(t)&&t.anid){var e=r[t.anid];if(e){var i=o(t);t.attr(o(e)),nh(t,i,n,Ys(t).dataIndex)}}}))}function o(t){var e={x:t.x,y:t.y,rotation:t.rotation};return function(t){return null!=t.shape}(t)&&(e.shape=T(t.shape)),e}}(o,this._axisGroup,e),t.prototype.render.call(this,e,n,i,r))},e.prototype.remove=function(){bS(this).splitAreaColors=null},e.type="cartesianAxis",e}(xS),MS={splitLine:function(t,e,n,i,r){var o=n.axis;if(!o.scale.isBlank()){var a=n.getModel("splitLine"),s=a.getModel("lineStyle"),l=s.get("color"),u=!1!==a.get("showMinLine"),h=!1!==a.get("showMaxLine");l=H(l)?l:[l];for(var c=i.coordinateSystem.getRect(),p=o.isHorizontal(),f=0,d=o.getTicksCoords({tickModel:a,breakTicks:"none",pruneByBreak:"preserve_extent_bound"}),g=[],v=[],y=s.getLineStyle(),m=0;m<d.length;m++){var _=o.toGlobalCoord(d[m].coord);if((0!==m||u)&&(m!==d.length-1||h)){var x=d[m].tickValue;p?(g[0]=_,g[1]=c.y,v[0]=_,v[1]=c.y+c.height):(g[0]=c.x,g[1]=_,v[0]=c.x+c.width,v[1]=_);var b=f++%l.length,w=new Pu({anid:null!=x?"line_"+x:null,autoBatch:!0,shape:{x1:g[0],y1:g[1],x2:v[0],y2:v[1]},style:D({stroke:l[b]},y),silent:!0});_h(w.shape,y.lineWidth),e.add(w)}}}},minorSplitLine:function(t,e,n,i,r){var o=n.axis,a=n.getModel("minorSplitLine").getModel("lineStyle"),s=i.coordinateSystem.getRect(),l=o.isHorizontal(),u=o.getMinorTicksCoords();if(u.length)for(var h=[],c=[],p=a.getLineStyle(),f=0;f<u.length;f++)for(var d=0;d<u[f].length;d++){var g=o.toGlobalCoord(u[f][d].coord);l?(h[0]=g,h[1]=s.y,c[0]=g,c[1]=s.y+s.height):(h[0]=s.x,h[1]=g,c[0]=s.x+s.width,c[1]=g);var v=new Pu({anid:"minor_line_"+u[f][d].tickValue,autoBatch:!0,shape:{x1:h[0],y1:h[1],x2:c[0],y2:c[1]},style:p,silent:!0});_h(v.shape,p.lineWidth),e.add(v)}},splitArea:function(t,e,n,i,r){!function(t,e,n,i){var r=n.axis;if(!r.scale.isBlank()){var o=n.getModel("splitArea"),a=o.getModel("areaStyle"),s=a.get("color"),l=i.coordinateSystem.getRect(),u=r.getTicksCoords({tickModel:o,clamp:!0,breakTicks:"none",pruneByBreak:"preserve_extent_bound"});if(u.length){var h=s.length,c=bS(t).splitAreaColors,p=dt(),f=0;if(c)for(var d=0;d<u.length;d++){var g=c.get(u[d].tickValue);if(null!=g){f=(g+(h-1)*d)%h;break}}var v=r.toGlobalCoord(u[0].coord),y=a.getAreaStyle();for(s=H(s)?s:[s],d=1;d<u.length;d++){var m=r.toGlobalCoord(u[d].coord),_=void 0,x=void 0,b=void 0,w=void 0;r.isHorizontal()?(_=v,x=l.y,b=m-_,w=l.height,v=_+b):(_=l.x,x=v,b=l.width,v=x+(w=m-x));var S=u[d-1].tickValue;null!=S&&p.set(S,f),e.add(new As({anid:null!=S?"area_"+S:null,shape:{x:_,y:x,width:b,height:w},style:D({fill:s[f]},y),autoBatch:!0,silent:!0})),f=(f+1)%h}bS(t).splitAreaColors=p}}}(t,e,n,i)},breakArea:function(t,e,n,i,r){n.axis.scale}},TS=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return n(e,t),e.type="xAxis",e}(SS),kS=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=TS.type,e}return n(e,t),e.type="yAxis",e}(SS),CS=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="grid",e}return n(e,t),e.prototype.render=function(t,e){this.group.removeAll(),t.get("show")&&this.group.add(new As({shape:t.coordinateSystem.getRect(),style:D({fill:t.get("backgroundColor")},t.getItemStyle()),silent:!0,z2:-1}))},e.type="grid",e}(Yd),DS={offset:0};var IS={label:{enabled:!0},decal:{show:!1}},AS=To(),LS={};function PS(t,e){var n=t.getModel("aria");if(n.get("enabled")){var i=T(IS);k(i.label,t.getLocaleModel().get("aria"),!1),k(n.option,i,!1),function(){if(n.getModel("decal").get("show")){var e=dt();t.eachSeries((function(t){if(!t.isColorBySeries()){var n=e.get(t.type);n||(n={},e.set(t.type,n)),AS(t).scope=n}})),t.eachRawSeries((function(e){if(!t.isSeriesFiltered(e))if(G(e.enableAriaDecal))e.enableAriaDecal();else{var n=e.getData();if(e.isColorBySeries()){var i=tf(e.ecModel,e.name,LS,t.getSeriesCount()),r=n.getVisual("decal");n.setVisual("decal",u(r,i))}else{var o=e.getRawData(),a={},s=AS(e).scope;n.each((function(t){var e=n.getRawIndex(t);a[e]=t}));var l=o.count();o.each((function(t){var i=a[t],r=o.getName(t)||t+"",h=tf(e.ecModel,r,s,l),c=n.getItemVisual(i,"decal");n.setItemVisual(i,"decal",u(c,h))}))}}function u(t,e){var n=t?C(C({},e),t):e;return n.dirty=!0,n}}))}}(),function(){var i=e.getZr().dom;if(!i)return;var o=t.getLocaleModel().get("aria"),a=n.getModel("label");if(a.option=D(a.option,o),!a.get("enabled"))return;if(i.setAttribute("role","img"),a.get("description"))return void i.setAttribute("aria-label",a.get("description"));var s,l=t.getSeriesCount(),u=a.get(["data","maxCount"])||10,h=a.get(["series","maxCount"])||10,c=Math.min(l,h);if(l<1)return;var p=function(){var e=t.get("title");e&&e.length&&(e=e[0]);return e&&e.text}();s=p?r(a.get(["general","withTitle"]),{title:p}):a.get(["general","withoutTitle"]);var f=[];s+=r(l>1?a.get(["series","multiple","prefix"]):a.get(["series","single","prefix"]),{seriesCount:l}),t.eachSeries((function(e,n){if(n<c){var i=void 0,o=e.get("name")?"withName":"withoutName";i=r(i=l>1?a.get(["series","multiple",o]):a.get(["series","single",o]),{seriesId:e.seriesIndex,seriesName:e.get("name"),seriesType:(x=e.subType,b=t.getLocaleModel().get(["series","typeNames"]),b[x]||b.chart)});var s=e.getData();if(s.count()>u)i+=r(a.get(["data","partialData"]),{displayCnt:u});else i+=a.get(["data","allData"]);for(var h=a.get(["data","separator","middle"]),p=a.get(["data","separator","end"]),d=a.get(["data","excludeDimensionId"]),g=[],v=0;v<s.count();v++)if(v<u){var y=s.getName(v),m=d?E(s.getValues(v),(function(t,e){return-1===A(d,e)})):s.getValues(v),_=a.get(["data",y?"withName":"withoutName"]);g.push(r(_,{name:y,value:m.join(h)}))}i+=g.join(h)+p,f.push(i)}var x,b}));var d=a.getModel(["series","multiple","separator"]),g=d.get("middle"),v=d.get("end");s+=f.join(g)+v,i.setAttribute("aria-label",s)}()}function r(t,e){if(!U(t))return t;var n=t;return R(e,(function(t,e){n=n.replace(new RegExp("\\{\\s*"+e+"\\s*\\}","g"),t)})),n}}function OS(t){if(t&&t.aria){var e=t.aria;null!=e.show&&(e.enabled=e.show),e.label=e.label||{},R(["description","general","series","data"],(function(t){null!=e[t]&&(e.label[t]=e[t])}))}}var RS=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="dataset",e}return n(e,t),e.prototype.init=function(e,n,i){t.prototype.init.call(this,e,n,i),this._sourceManager=new Ad(this),Ld(this)},e.prototype.mergeOption=function(e,n){t.prototype.mergeOption.call(this,e,n),Ld(this)},e.prototype.optionUpdated=function(){this._sourceManager.dirty()},e.prototype.getSourceManager=function(){return this._sourceManager},e.type="dataset",e.defaultOption={seriesLayoutBy:Bp},e}(_p),NS=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="dataset",e}return n(e,t),e.type="dataset",e}(Yd);Zy([function(t){t.registerPainter("canvas",Yx)}]),Zy([function(t){t.registerChartView(Tb),t.registerSeriesModel(qx),t.registerLayout(function(t,e){return{seriesType:t,plan:qd(),reset:function(t){var n=t.getData(),i=t.coordinateSystem,r=t.pipelineContext,o=e||r.large;if(i){var a=N(i.dimensions,(function(t){return n.mapDimension(t)})).slice(0,2),s=a.length,l=n.getCalculationInfo("stackResultDimension");Cm(n,a[0])&&(a[0]=l),Cm(n,a[1])&&(a[1]=l);var u=n.getStore(),h=n.getDimensionIndex(a[0]),c=n.getDimensionIndex(a[1]);return s&&{progress:function(t,e){for(var n=t.end-t.start,r=o&&jm(n*s),a=[],l=[],p=t.start,f=0;p<t.end;p++){var d=void 0;if(1===s){var g=u.get(h,p);d=i.dataToPoint(g,null,l)}else a[0]=u.get(h,p),a[1]=u.get(c,p),d=i.dataToPoint(a,null,l);o?(r[f++]=d[0],r[f++]=d[1]):e.setItemLayout(p,d.slice())}o&&e.setLayout("points",r)}}}}}}("line",!0)),t.registerVisual({seriesType:"line",reset:function(t){var e=t.getData(),n=t.getModel("lineStyle").getLineStyle();n&&!n.stroke&&(n.stroke=e.getVisual("style").fill),e.setVisual("legendLineStyle",n)}}),t.registerProcessor(t.PRIORITY.PROCESSOR.STATISTIC,Db("line"))},function(t){t.registerChartView(zb),t.registerSeriesModel(Ab),t.registerLayout(t.PRIORITY.VISUAL.LAYOUT,W(t_,"bar")),t.registerLayout(t.PRIORITY.VISUAL.PROGRESSIVE_LAYOUT,function(t){return{seriesType:t,plan:qd(),reset:function(t){if(e_(t)){var e=t.getData(),n=t.coordinateSystem,i=n.getBaseAxis(),r=n.getOtherAxis(i),o=e.getDimensionIndex(e.mapDimension(r.dim)),a=e.getDimensionIndex(e.mapDimension(i.dim)),s=t.get("showBackground",!0),l=e.mapDimension(r.dim),u=e.getCalculationInfo("stackResultDimension"),h=Cm(e,l)&&!!e.getCalculationInfo("stackedOnSeries"),c=r.isHorizontal(),p=function(t,e){var n=e.model.get("startValue");return n||(n=0),e.toGlobalCoord(e.dataToCoord("log"===e.type?n>0?n:1:n))}(0,r),f=n_(t),d=t.get("barMinHeight")||0,g=u&&e.getDimensionIndex(u),v=e.getLayout("size"),y=e.getLayout("offset");return{progress:function(t,e){for(var i,r=t.count,l=f&&jm(3*r),u=f&&s&&jm(3*r),m=f&&jm(r),_=n.master.getRect(),x=c?_.width:_.height,b=e.getStore(),w=0;null!=(i=t.next());){var S=b.get(h?g:o,i),M=b.get(a,i),T=p,k=void 0;h&&(k=+S-b.get(o,i));var C=void 0,D=void 0,I=void 0,A=void 0;if(c){var L=n.dataToPoint([S,M]);h&&(T=n.dataToPoint([k,M])[0]),C=T,D=L[1]+y,I=L[0]-T,A=v,Math.abs(I)<d&&(I=(I<0?-1:1)*d)}else L=n.dataToPoint([M,S]),h&&(T=n.dataToPoint([M,k])[1]),C=L[0]+y,D=T,I=v,A=L[1]-T,Math.abs(A)<d&&(A=(A<=0?-1:1)*d);f?(l[w]=C,l[w+1]=D,l[w+2]=c?I:A,u&&(u[w]=c?_.x:C,u[w+1]=c?D:_.y,u[w+2]=x),m[i]=i):e.setItemLayout(i,{x:C,y:D,width:I,height:A}),w+=3}f&&e.setLayout({largePoints:l,largeDataIndices:m,largeBackgroundPoints:u,valueAxisHorizontal:c})}}}}}}("bar")),t.registerProcessor(t.PRIORITY.PROCESSOR.STATISTIC,Db("bar")),t.registerAction({type:"changeAxisOrder",event:"changeAxisOrder",update:"update"},(function(t,e){var n=t.componentType||"series";e.eachComponent({mainType:n,query:t},(function(e){t.sortInfo&&e.axis.setCategorySortInfo(t.sortInfo)}))}))},function(t){t.registerChartView(fw),t.registerSeriesModel(yw),function(t,e){function n(e,n){var i=[];return e.eachComponent({mainType:"series",subType:t,query:n},(function(t){i.push(t.seriesIndex)})),i}R([[t+"ToggleSelect","toggleSelect"],[t+"Select","select"],[t+"UnSelect","unselect"]],(function(t){e(t[0],(function(e,i,r){e=C({},e),r.dispatchAction(C(e,{type:t[1],seriesIndex:n(i,e)}))}))}))}("pie",t.registerAction),t.registerLayout(W(nw,"pie")),t.registerProcessor(function(t){return{seriesType:t,reset:function(t,e){var n=e.findComponents({mainType:"legend"});if(n&&n.length){var i=t.getData();i.filterSelf((function(t){for(var e=i.getName(t),r=0;r<n.length;r++)if(!n[r].isSelected(e))return!1;return!0}))}}}}("pie")),t.registerProcessor(function(t){return{seriesType:t,reset:function(t,e){var n=t.getData();n.filterSelf((function(t){var e=n.mapDimension("value"),i=n.get(e,t);return!(Y(i)&&!isNaN(i)&&i<0)}))}}}("pie"))}]),Zy([function(t){t.registerComponentView(CS),t.registerComponentModel(xw),t.registerCoordinateSystem("cartesian2d",lS),Cw(t,"x",bw,DS),Cw(t,"y",bw,DS),t.registerComponentView(TS),t.registerComponentView(kS),t.registerPreprocessor((function(t){t.xAxis&&t.yAxis&&!t.grid&&(t.grid={})}))},function(t){t.registerPreprocessor(OS),t.registerVisual(t.PRIORITY.VISUAL.ARIA,PS)},function(t){t.registerComponentModel(RS),t.registerComponentView(NS)}]),t.Axis=yx,t.ChartView=Kd,t.ComponentModel=_p,t.ComponentView=Yd,t.List=xm,t.Model=ic,t.PRIORITY=Ov,t.SeriesModel=zd,t.color=oi,t.connect=function(t){if(H(t)){var e=t;t=null,R(e,(function(e){null!=e.group&&(t=e.group)})),t=t||"g_"+Ty++,R(e,(function(e){e.group=t}))}return Sy[t]=!0,t},t.dataTool={},t.dependencies={zrender:"6.0.0"},t.disConnect=Dy,t.disconnect=Cy,t.dispose=function(t){U(t)?t=wy[t]:t instanceof hy||(t=Iy(t)),t instanceof hy&&!t.isDisposed()&&t.dispose()},t.env=r,t.extendChartView=function(t){var e=Kd.extend(t);return Kd.registerClass(e),e},t.extendComponentModel=function(t){var e=_p.extend(t);return _p.registerClass(e),e},t.extendComponentView=function(t){var e=Yd.extend(t);return Yd.registerClass(e),e},t.extendSeriesModel=function(t){var e=zd.extend(t);return zd.registerClass(e),e},t.format=K_,t.getCoordinateSystemDimensions=function(t){var e=Jc.get(t);if(e)return e.getDimensionsInfo?e.getDimensionsInfo():e.dimensions.slice()},t.getInstanceByDom=Iy,t.getInstanceById=function(t){return wy[t]},t.getMap=function(t){var e=Av("getMap");return e&&e(t)},t.graphic=j_,t.helper=R_,t.init=function(t,e,n){var i=!(n&&n.ssr);if(i){0;var r=Iy(t);if(r)return r;0}var o=new hy(t,e,n);return o.id="ec_"+My++,wy[o.id]=o,i&&Lo(t,ky,o.id),ry(o),Dv.trigger("afterinit",o),o},t.innerDrawElementOnCanvas=_v,t.matrix=me,t.number=q_,t.parseGeoJSON=Y_,t.parseGeoJson=Y_,t.registerAction=By,t.registerCoordinateSystem=Ey,t.registerCustomSeries=function(t,e){},t.registerLayout=zy,t.registerLoading=Hy,t.registerLocale=pc,t.registerMap=Gy,t.registerPostInit=Oy,t.registerPostUpdate=Ry,t.registerPreprocessor=Ly,t.registerProcessor=Py,t.registerTheme=Ay,t.registerTransform=Uy,t.registerUpdateLifecycle=Ny,t.registerVisual=Fy,t.setCanvasCreator=function(t){h({createCanvas:t})},t.setPlatformAPI=h,t.throttle=ng,t.time=Z_,t.use=Zy,t.util=$_,t.vector=Wt,t.version="6.0.0",t.zrUtil=wt,t.zrender=Or,Object.defineProperty(t,"__esModule",{value:!0})}));
