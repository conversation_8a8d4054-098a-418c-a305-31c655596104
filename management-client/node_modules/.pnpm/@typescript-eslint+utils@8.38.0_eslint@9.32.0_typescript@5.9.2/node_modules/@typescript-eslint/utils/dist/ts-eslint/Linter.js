"use strict";
/* eslint-disable @typescript-eslint/no-namespace, no-restricted-syntax */
Object.defineProperty(exports, "__esModule", { value: true });
exports.Linter = void 0;
const eslint_1 = require("eslint");
/**
 * The Linter object does the actual evaluation of the JavaScript code. It doesn't do any filesystem operations, it
 * simply parses and reports on the code. In particular, the Linter object does not process configuration objects
 * or files.
 */
class Linter extends eslint_1.Linter {
}
exports.Linter = Linter;
