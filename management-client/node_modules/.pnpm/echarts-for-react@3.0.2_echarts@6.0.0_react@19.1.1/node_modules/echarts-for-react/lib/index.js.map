{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;AAAA,4DAAmC;AAEnC,6DAAsC;AAItC,2CAA2C;AAC3C;IAA0C,6CAAgB;IACxD,sBAAY,KAAwB;QAApC,YACE,kBAAM,KAAK,CAAC,SAIb;QAFC,mBAAmB;QACnB,KAAI,CAAC,OAAO,GAAG,OAAO,CAAC;;IACzB,CAAC;IACH,mBAAC;AAAD,CAAC,AAPD,CAA0C,cAAgB,GAOzD", "sourcesContent": ["import * as echarts from 'echarts';\nimport { EChartsReactProps, EChartsOption, EChartsInstance } from './types';\nimport EChartsReactCore from './core';\n\nexport { EChartsReactProps, EChartsOption, EChartsInstance };\n\n// export the Component the echarts Object.\nexport default class EChartsReact extends EChartsReactCore {\n  constructor(props: EChartsReactProps) {\n    super(props);\n\n    // 初始化为 echarts 整个包\n    this.echarts = echarts;\n  }\n}\n"]}