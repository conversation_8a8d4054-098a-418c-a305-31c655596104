{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";AAAA,OAAO,KAAK,OAAO,MAAM,SAAS,CAAC;AAEnC,OAAO,gBAAgB,MAAM,QAAQ,CAAC;AAItC,2CAA2C;AAC3C;IAA0C,gCAAgB;IACxD,sBAAY,KAAwB;QAApC,YACE,kBAAM,KAAK,CAAC,SAIb;QAFC,mBAAmB;QACnB,KAAI,CAAC,OAAO,GAAG,OAAO,CAAC;;IACzB,CAAC;IACH,mBAAC;AAAD,CAAC,AAPD,CAA0C,gBAAgB,GAOzD", "sourcesContent": ["import * as echarts from 'echarts';\nimport { EChartsReactProps, EChartsOption, EChartsInstance } from './types';\nimport EChartsReactCore from './core';\n\nexport { EChartsReactProps, EChartsOption, EChartsInstance };\n\n// export the Component the echarts Object.\nexport default class EChartsReact extends EChartsReactCore {\n  constructor(props: EChartsReactProps) {\n    super(props);\n\n    // 初始化为 echarts 整个包\n    this.echarts = echarts;\n  }\n}\n"]}