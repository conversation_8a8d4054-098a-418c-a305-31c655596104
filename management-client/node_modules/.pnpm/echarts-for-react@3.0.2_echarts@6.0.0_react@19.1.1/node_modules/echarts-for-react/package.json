{"name": "echarts-for-react", "version": "3.0.2", "description": " Apache Echarts components for React.", "main": "lib/index.js", "module": "esm/index.js", "types": "lib/index.d.ts", "files": ["lib", "esm", "src"], "scripts": {"docs:dev": "NODE_ENV=development dumi dev", "docs:build": "NODE_ENV=production dumi build", "docs:deploy": "rimraf dist && npm run docs:build && gh-pages -d dist", "start": "npm run docs:dev", "lint": "eslint src __tests__ && prettier src __tests__ --check", "fix": "eslint src __tests__ --fix && prettier src __tests__ --write", "clean": "<PERSON><PERSON>f lib esm dist", "test": "jest", "lib": "run-p lib:*", "lib:cjs": "tsc -p tsconfig.json --target ES5 --module commonjs --outDir lib", "lib:esm": "tsc -p tsconfig.json --target ES5 --module ESNext --outDir esm", "build": "npm run clean && npm run test && npm run lib"}, "repository": {"type": "git", "url": "https://github.com/hustcc/echarts-for-react.git"}, "keywords": ["react", "component", "echarts-react", "echarts", "react-echarts", "chart", "charts", "graph", "react-component"], "author": "hustcc (http://github.com/hustcc)", "license": "MIT", "bugs": {"url": "https://github.com/hustcc/echarts-for-react/issues"}, "homepage": "https://github.com/hustcc/echarts-for-react", "devDependencies": {"@commitlint/cli": "^12.0.0", "@commitlint/config-angular": "^12.0.0", "@types/jest": "^26.0.20", "@types/node": "^14.14.31", "@types/react": "^17.0.2", "@typescript-eslint/eslint-plugin": "^4.15.2", "@typescript-eslint/parser": "^4.15.2", "cross-env": "^7.0.3", "dumi": "^1.1.6", "dumi-theme-default": "^1.0.6", "echarts": "^5.0.2", "echarts-gl": "^2.0.2", "eslint": "^7.20.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.3.1", "gh-pages": "^3.1.0", "husky": "^5.1.1", "jest": "^26.6.3", "jest-canvas-mock": "^2.3.1", "jest-electron": "^0.1.11", "lint-md-cli": "^0.1.2", "lint-staged": "^10.5.4", "lodash.clonedeep": "^4.5.0", "miz": "^1.0.1", "npm-run-all": "^4.1.5", "prettier": "^2.2.1", "rimraf": "^3.0.2", "ts-jest": "^26.5.2", "ts-loader": "^8.0.17", "typescript": "^4.2.2"}, "dependencies": {"size-sensor": "^1.0.1", "fast-deep-equal": "^3.1.3"}, "peerDependencies": {"react": "^15.0.0 || >=16.0.0", "echarts": "^3.0.0 || ^4.0.0 || ^5.0.0"}, "jest": {"runner": "jest-electron/runner", "testEnvironment": "jest-electron/environment", "preset": "ts-jest", "collectCoverage": true, "collectCoverageFrom": ["src/**/*.ts", "!**/node_modules/**", "!**/vendor/**"], "testRegex": "/__tests__/.*-spec\\.tsx?$"}, "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "lint-staged": {"*.ts": ["eslint --fix", "prettier --write"], "*.md": ["lint-md --fix"]}}