# `@typescript-eslint/type-utils`

> Type utilities for working with TypeScript within ESLint rules.

[![NPM Version](https://img.shields.io/npm/v/@typescript-eslint/utils.svg?style=flat-square)](https://www.npmjs.com/package/@typescript-eslint/utils)
[![NPM Downloads](https://img.shields.io/npm/dm/@typescript-eslint/utils.svg?style=flat-square)](https://www.npmjs.com/package/@typescript-eslint/utils)

The utilities in this package are separated from `@typescript-eslint/utils` so that that package does not require a dependency on `typescript`.

> See https://typescript-eslint.io for general documentation on typescript-eslint, the tooling that allows you to run ESLint and Prettier on TypeScript code.

<!-- Local path for docs: docs/packages/Type_Utils.mdx -->
