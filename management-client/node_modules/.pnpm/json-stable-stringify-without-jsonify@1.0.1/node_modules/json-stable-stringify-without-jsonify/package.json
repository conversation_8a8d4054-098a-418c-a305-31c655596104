{"name": "json-stable-stringify-without-jsonify", "version": "1.0.1", "description": "deterministic JSON.stringify() with custom sorting to get deterministic hashes from stringified results, with no public domain dependencies", "main": "index.js", "dependencies": {}, "devDependencies": {"tape": "~1.0.4"}, "scripts": {"test": "tape test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "ff/5", "ff/latest", "chrome/15", "chrome/latest", "safari/latest", "opera/latest"]}, "repository": {"type": "git", "url": "git://github.com/samn/json-stable-stringify.git"}, "homepage": "https://github.com/samn/json-stable-stringify", "keywords": ["json", "stringify", "deterministic", "hash", "sort", "stable"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT"}