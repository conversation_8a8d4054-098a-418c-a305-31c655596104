/**
 * @license lucide-react v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M14 2v4a2 2 0 0 0 2 2h4", key: "tnqrlb" }],
  ["path", { d: "M16 22h2a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v3", key: "37hlfg" }],
  ["path", { d: "M8 14v2.2l1.6 1", key: "6m4bie" }],
  ["circle", { cx: "8", cy: "16", r: "6", key: "10v15b" }]
];
const FileClock = createLucideIcon("file-clock", __iconNode);

export { __iconNode, FileClock as default };
//# sourceMappingURL=file-clock.js.map
