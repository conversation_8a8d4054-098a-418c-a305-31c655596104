/**
 * @license lucide-react v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M12 6v6l2 4", key: "1287s9" }],
  ["circle", { cx: "12", cy: "12", r: "10", key: "1mglay" }]
];
const Clock5 = createLucideIcon("clock-5", __iconNode);

export { __iconNode, Clock5 as default };
//# sourceMappingURL=clock-5.js.map
