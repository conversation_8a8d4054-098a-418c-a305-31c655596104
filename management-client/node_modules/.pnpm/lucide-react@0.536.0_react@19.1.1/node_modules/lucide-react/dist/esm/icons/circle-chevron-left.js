/**
 * @license lucide-react v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["circle", { cx: "12", cy: "12", r: "10", key: "1mglay" }],
  ["path", { d: "m14 16-4-4 4-4", key: "ojs7w8" }]
];
const CircleChevronLeft = createLucideIcon("circle-chevron-left", __iconNode);

export { __iconNode, CircleChevronLeft as default };
//# sourceMappingURL=circle-chevron-left.js.map
