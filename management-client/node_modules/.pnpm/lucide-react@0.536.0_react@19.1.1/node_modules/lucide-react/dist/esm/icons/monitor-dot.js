/**
 * @license lucide-react v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M12 17v4", key: "1riwvh" }],
  [
    "path",
    { d: "M22 12.307V15a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h8.693", key: "1dx6ho" }
  ],
  ["path", { d: "M8 21h8", key: "1ev6f3" }],
  ["circle", { cx: "19", cy: "6", r: "3", key: "108a5v" }]
];
const MonitorDot = createLucideIcon("monitor-dot", __iconNode);

export { __iconNode, MonitorDot as default };
//# sourceMappingURL=monitor-dot.js.map
