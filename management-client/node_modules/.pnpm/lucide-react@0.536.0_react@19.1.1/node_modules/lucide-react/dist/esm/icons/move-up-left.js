/**
 * @license lucide-react v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M5 11V5H11", key: "3q78g9" }],
  ["path", { d: "M5 5L19 19", key: "5zm2fv" }]
];
const MoveUpLeft = createLucideIcon("move-up-left", __iconNode);

export { __iconNode, MoveUpLeft as default };
//# sourceMappingURL=move-up-left.js.map
