/**
 * @license lucide-react v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M14 12h2v8", key: "c1fccl" }],
  ["path", { d: "M14 20h4", key: "lzx1xo" }],
  ["path", { d: "M6 12h4", key: "a4o3ry" }],
  ["path", { d: "M6 20h4", key: "1i6q5t" }],
  ["path", { d: "M8 20V8a4 4 0 0 1 7.464-2", key: "wk9t6r" }]
];
const Ligature = createLucideIcon("ligature", __iconNode);

export { __iconNode, Ligature as default };
//# sourceMappingURL=ligature.js.map
