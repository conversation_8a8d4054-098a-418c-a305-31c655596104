/**
 * Pure function - doesn't mutate either parameter!
 * Uses the default options and overrides with the options provided by the user
 * @param defaultOptions the defaults
 * @param userOptions the user opts
 * @returns the options with defaults
 */
export declare function applyDefault<User extends readonly unknown[], <PERSON><PERSON><PERSON> extends User>(defaultOptions: Readonly<Default>, userOptions: Readonly<User> | null): Default;
//# sourceMappingURL=applyDefault.d.ts.map