#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/home/<USER>/ideas/designs/weibo/management-client/node_modules/.pnpm/mkdirp@3.0.1/node_modules/mkdirp/dist/cjs/src/node_modules:/home/<USER>/ideas/designs/weibo/management-client/node_modules/.pnpm/mkdirp@3.0.1/node_modules/mkdirp/dist/cjs/node_modules:/home/<USER>/ideas/designs/weibo/management-client/node_modules/.pnpm/mkdirp@3.0.1/node_modules/mkdirp/dist/node_modules:/home/<USER>/ideas/designs/weibo/management-client/node_modules/.pnpm/mkdirp@3.0.1/node_modules/mkdirp/node_modules:/home/<USER>/ideas/designs/weibo/management-client/node_modules/.pnpm/mkdirp@3.0.1/node_modules:/home/<USER>/ideas/designs/weibo/management-client/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/home/<USER>/ideas/designs/weibo/management-client/node_modules/.pnpm/mkdirp@3.0.1/node_modules/mkdirp/dist/cjs/src/node_modules:/home/<USER>/ideas/designs/weibo/management-client/node_modules/.pnpm/mkdirp@3.0.1/node_modules/mkdirp/dist/cjs/node_modules:/home/<USER>/ideas/designs/weibo/management-client/node_modules/.pnpm/mkdirp@3.0.1/node_modules/mkdirp/dist/node_modules:/home/<USER>/ideas/designs/weibo/management-client/node_modules/.pnpm/mkdirp@3.0.1/node_modules/mkdirp/node_modules:/home/<USER>/ideas/designs/weibo/management-client/node_modules/.pnpm/mkdirp@3.0.1/node_modules:/home/<USER>/ideas/designs/weibo/management-client/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../dist/cjs/src/bin.js" "$@"
else
  exec node  "$basedir/../../dist/cjs/src/bin.js" "$@"
fi
