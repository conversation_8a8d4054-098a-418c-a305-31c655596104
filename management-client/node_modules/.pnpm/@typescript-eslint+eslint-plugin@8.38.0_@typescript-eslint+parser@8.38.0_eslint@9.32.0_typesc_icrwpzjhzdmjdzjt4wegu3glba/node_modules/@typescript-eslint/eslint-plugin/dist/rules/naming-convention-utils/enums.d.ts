export declare enum PredefinedFormats {
    camelCase = 1,
    strictCamelCase = 2,
    PascalCase = 3,
    StrictPascalCase = 4,
    snake_case = 5,
    UPPER_CASE = 6
}
export type PredefinedFormatsString = keyof typeof PredefinedFormats;
export declare enum UnderscoreOptions {
    forbid = 1,
    allow = 2,
    require = 3,
    requireDouble = 4,
    allowDouble = 5,
    allowSingleOrDouble = 6
}
export type UnderscoreOptionsString = keyof typeof UnderscoreOptions;
export declare enum Selectors {
    variable = 1,
    function = 2,
    parameter = 4,
    parameterProperty = 8,
    classicAccessor = 16,
    enumMember = 32,
    classMethod = 64,
    objectLiteralMethod = 128,
    typeMethod = 256,
    classProperty = 512,
    objectLiteralProperty = 1024,
    typeProperty = 2048,
    autoAccessor = 4096,
    class = 8192,
    interface = 16384,
    typeAlias = 32768,
    enum = 65536,
    typeParameter = 131072,
    import = 262144
}
export type SelectorsString = keyof typeof Selectors;
export declare enum MetaSelectors {
    default = -1,
    variableLike = 7,
    memberLike = 8184,
    typeLike = 253952,
    method = 448,
    property = 3584,
    accessor = 4112
}
export type MetaSelectorsString = keyof typeof MetaSelectors;
export type IndividualAndMetaSelectorsString = MetaSelectorsString | SelectorsString;
export declare enum Modifiers {
    const = 1,
    readonly = 2,
    static = 4,
    public = 8,
    protected = 16,
    private = 32,
    '#private' = 64,
    abstract = 128,
    destructured = 256,
    global = 512,
    exported = 1024,
    unused = 2048,
    requiresQuotes = 4096,
    override = 8192,
    async = 16384,
    default = 32768,
    namespace = 65536
}
export type ModifiersString = keyof typeof Modifiers;
export declare enum TypeModifiers {
    boolean = 131072,
    string = 262144,
    number = 524288,
    function = 1048576,
    array = 2097152
}
export type TypeModifiersString = keyof typeof TypeModifiers;
//# sourceMappingURL=enums.d.ts.map