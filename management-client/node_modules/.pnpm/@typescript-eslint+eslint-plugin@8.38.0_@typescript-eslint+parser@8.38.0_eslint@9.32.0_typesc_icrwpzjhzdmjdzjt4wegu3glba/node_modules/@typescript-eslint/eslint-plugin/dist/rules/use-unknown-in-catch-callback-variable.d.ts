import type { TSESLint } from '@typescript-eslint/utils';
export type MessageIds = 'addUnknownRestTypeAnnotationSuggestion' | 'addUnknownTypeAnnotationSuggestion' | 'useUnknown' | 'useUnknownArrayDestructuringPattern' | 'useUnknownObjectDestructuringPattern' | 'wrongRestTypeAnnotationSuggestion' | 'wrongTypeAnnotationSuggestion';
declare const _default: TSESLint.RuleModule<MessageIds, [], import("../../rules").ESLintPluginDocs, TSESLint.RuleListener>;
export default _default;
//# sourceMappingURL=use-unknown-in-catch-callback-variable.d.ts.map