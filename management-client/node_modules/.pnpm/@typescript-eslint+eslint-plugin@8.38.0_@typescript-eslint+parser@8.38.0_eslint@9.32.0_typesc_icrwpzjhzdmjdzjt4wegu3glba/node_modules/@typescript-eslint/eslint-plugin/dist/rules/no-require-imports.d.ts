export type Options = [
    {
        allow?: string[];
        allowAsImport?: boolean;
    }
];
export type MessageIds = 'noRequireImports';
declare const _default: import("@typescript-eslint/utils/ts-eslint").RuleModule<"noRequireImports", Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
export default _default;
//# sourceMappingURL=no-require-imports.d.ts.map