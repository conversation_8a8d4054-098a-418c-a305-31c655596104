export declare const enum OptionKeys {
    ArrayDestructuring = "arrayDestructuring",
    ArrowParameter = "arrowParameter",
    MemberVariableDeclaration = "memberVariableDeclaration",
    ObjectDestructuring = "objectDestructuring",
    Parameter = "parameter",
    PropertyDeclaration = "propertyDeclaration",
    VariableDeclaration = "variableDeclaration",
    VariableDeclarationIgnoreFunction = "variableDeclarationIgnoreFunction"
}
export type Options = [Partial<Record<OptionKeys, boolean>>];
export type MessageIds = 'expectedTypedef' | 'expectedTypedefNamed';
declare const _default: import("@typescript-eslint/utils/ts-eslint").RuleModule<MessageIds, Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
export default _default;
//# sourceMappingURL=typedef.d.ts.map