export type Options = [
    {
        allowArgumentsExplicitlyTypedAsAny?: boolean;
        allowDirectConstAssertionInArrowFunctions?: boolean;
        allowedNames?: string[];
        allowHigherOrderFunctions?: boolean;
        allowTypedFunctionExpressions?: boolean;
        allowOverloadFunctions?: boolean;
    }
];
export type MessageIds = 'anyTypedArg' | 'anyTypedArgUnnamed' | 'missingArgType' | 'missingArgTypeUnnamed' | 'missingReturnType';
declare const _default: import("@typescript-eslint/utils/ts-eslint").RuleModule<MessageIds, Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
export default _default;
//# sourceMappingURL=explicit-module-boundary-types.d.ts.map