module.exports={A:{D:{"1":"0 pB qB rB sB tB uB vB wB xB yB zB QC 0B RC 1B 2B 3B 4B 5B 6B 7B 8B 9B AC BC CC DC EC FC GC HC Q H R S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x y z AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB I TC IC UC","2":"1 2 3 4 5 6 7 8 9 J UB K D E F A B C L M G N O P VB WB XB YB ZB aB bB cB dB eB fB gB hB iB jB kB lB mB nB oB"},L:{"1":"I"},B:{"1":"0 Q H R S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x y z AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB I","2":"C L M G N O P"},C:{"1":"0 rB sB tB uB vB wB xB yB zB QC 0B RC 1B 2B 3B 4B 5B 6B 7B 8B 9B AC BC CC DC EC FC GC HC Q H R SC S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x y z AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB I TC IC UC tC uC vC","2":"sC PC J UB K D E F A B C L M G N wC xC","33":"1 2 3 4 5 6 7 8 9 O P VB WB XB YB ZB aB bB cB dB eB fB gB hB iB jB kB lB mB nB oB pB qB"},M:{"1":"IC"},A:{"2":"K D E F A B rC"},F:{"1":"0 cB dB eB fB gB hB iB jB kB lB mB nB oB pB qB rB sB tB uB vB wB xB yB zB 0B 1B 2B 3B 4B 5B 6B 7B 8B 9B AC BC CC DC EC FC GC HC Q H R SC S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x y z","2":"1 2 3 4 5 6 7 8 9 F B C G N O P VB WB XB YB ZB aB bB AD BD CD DD JC pC ED KC"},K:{"1":"H","2":"A B C JC pC KC"},E:{"1":"B C L M G JC KC 3C 4C 5C XC YC LC 6C MC ZC aC bC cC dC 7C NC eC fC gC hC iC 8C OC jC kC lC mC nC oC","2":"J UB K yC VC zC 0C 9C","33":"D E F A 1C 2C WC"},G:{"1":"OD PD QD RD SD TD UD VD WD XD YD XC YC LC ZD MC ZC aC bC cC dC aD NC eC fC gC hC iC bD OC jC kC lC mC nC oC","2":"VC FD qC GD HD","33":"E ID JD KD LD MD ND"},P:{"1":"1 2 3 4 5 6 7 8 9 jD kD lD mD nD WC oD pD qD rD sD MC NC OC tD","2":"J"},I:{"1":"I","2":"PC J dD eD fD gD qC hD iD"}},B:6,C:"isolate-override from unicode-bidi",D:undefined};
