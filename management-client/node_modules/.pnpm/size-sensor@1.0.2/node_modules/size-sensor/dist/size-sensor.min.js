!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e=e||self).sizeSensor={})}(this,function(e){"use strict";function u(o){var r=1<arguments.length&&void 0!==arguments[1]?arguments[1]:60,u=null;return function(){for(var e=this,t=arguments.length,n=new Array(t),i=0;i<t;i++)n[i]=arguments[i];clearTimeout(u),u=setTimeout(function(){o.apply(e,n)},r)}}var t=1,i=function(){return"".concat(t++)},s="size-sensor-id",o="undefined"!=typeof ResizeObserver?function(n,e){function t(){i.disconnect(),o=[],i=void 0,n.removeAttribute(s),e&&e()}var i=void 0,o=[],r=u(function(){o.forEach(function(e){e(n)})});return{element:n,bind:function(e){var t;i||((t=new ResizeObserver(r)).observe(n),r(),i=t),-1===o.indexOf(e)&&o.push(e)},destroy:t,unbind:function(e){e=o.indexOf(e);-1!==e&&o.splice(e,1),0===o.length&&i&&t()}}}:function(n,e){function t(){i&&i.parentNode&&(i.contentDocument&&i.contentDocument.defaultView.removeEventListener("resize",r),i.parentNode.removeChild(i),n.removeAttribute(s),i=void 0,o=[],e)&&e()}var i=void 0,o=[],r=u(function(){o.forEach(function(e){e(n)})});return{element:n,bind:function(e){var t;i||("static"===getComputedStyle(n).position&&(n.style.position="relative"),(t=document.createElement("object")).onload=function(){t.contentDocument.defaultView.addEventListener("resize",r),r()},t.style.display="block",t.style.position="absolute",t.style.top="0",t.style.left="0",t.style.height="100%",t.style.width="100%",t.style.overflow="hidden",t.style.pointerEvents="none",t.style.zIndex="-1",t.style.opacity="0",t.setAttribute("class","size-sensor-object"),t.setAttribute("tabindex","-1"),t.type="text/html",n.appendChild(t),t.data="about:blank",i=t),-1===o.indexOf(e)&&o.push(e)},destroy:t,unbind:function(e){e=o.indexOf(e);-1!==e&&o.splice(e,1),0===o.length&&i&&t()}}},r={};function c(e){e&&r[e]&&delete r[e]}function d(e){var t,n=e.getAttribute(s);return n&&r[n]?r[n]:(t=i(),e.setAttribute(s,t),n=o(e,function(){return c(t)}),r[t]=n)}e.bind=function(e,t){var n=d(e);return n.bind(t),function(){n.unbind(t)}},e.clear=function(e){var t,e=d(e);t=(e=e).element.getAttribute(s),e.destroy(),c(t)},e.ver="1.0.2",Object.defineProperty(e,"__esModule",{value:!0})});
