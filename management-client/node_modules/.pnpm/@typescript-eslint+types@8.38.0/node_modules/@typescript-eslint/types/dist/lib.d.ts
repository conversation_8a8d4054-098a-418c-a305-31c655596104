export type Lib = 'decorators' | 'decorators.legacy' | 'dom' | 'dom.asynciterable' | 'dom.iterable' | 'es5' | 'es6' | 'es7' | 'es2015' | 'es2015.collection' | 'es2015.core' | 'es2015.generator' | 'es2015.iterable' | 'es2015.promise' | 'es2015.proxy' | 'es2015.reflect' | 'es2015.symbol' | 'es2015.symbol.wellknown' | 'es2016' | 'es2016.array.include' | 'es2016.full' | 'es2016.intl' | 'es2017' | 'es2017.arraybuffer' | 'es2017.date' | 'es2017.full' | 'es2017.intl' | 'es2017.object' | 'es2017.sharedmemory' | 'es2017.string' | 'es2017.typedarrays' | 'es2018' | 'es2018.asyncgenerator' | 'es2018.asynciterable' | 'es2018.full' | 'es2018.intl' | 'es2018.promise' | 'es2018.regexp' | 'es2019' | 'es2019.array' | 'es2019.full' | 'es2019.intl' | 'es2019.object' | 'es2019.string' | 'es2019.symbol' | 'es2020' | 'es2020.bigint' | 'es2020.date' | 'es2020.full' | 'es2020.intl' | 'es2020.number' | 'es2020.promise' | 'es2020.sharedmemory' | 'es2020.string' | 'es2020.symbol.wellknown' | 'es2021' | 'es2021.full' | 'es2021.intl' | 'es2021.promise' | 'es2021.string' | 'es2021.weakref' | 'es2022' | 'es2022.array' | 'es2022.error' | 'es2022.full' | 'es2022.intl' | 'es2022.object' | 'es2022.regexp' | 'es2022.string' | 'es2023' | 'es2023.array' | 'es2023.collection' | 'es2023.full' | 'es2023.intl' | 'es2024' | 'es2024.arraybuffer' | 'es2024.collection' | 'es2024.full' | 'es2024.object' | 'es2024.promise' | 'es2024.regexp' | 'es2024.sharedmemory' | 'es2024.string' | 'esnext' | 'esnext.array' | 'esnext.asynciterable' | 'esnext.bigint' | 'esnext.collection' | 'esnext.decorators' | 'esnext.disposable' | 'esnext.float16' | 'esnext.full' | 'esnext.intl' | 'esnext.iterator' | 'esnext.object' | 'esnext.promise' | 'esnext.regexp' | 'esnext.string' | 'esnext.symbol' | 'esnext.weakref' | 'lib' | 'scripthost' | 'webworker' | 'webworker.asynciterable' | 'webworker.importscripts' | 'webworker.iterable';
//# sourceMappingURL=lib.d.ts.map