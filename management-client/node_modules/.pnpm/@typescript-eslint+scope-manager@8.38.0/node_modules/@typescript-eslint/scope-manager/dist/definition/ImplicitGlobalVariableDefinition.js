"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ImplicitGlobalVariableDefinition = void 0;
const DefinitionBase_1 = require("./DefinitionBase");
const DefinitionType_1 = require("./DefinitionType");
class ImplicitGlobalVariableDefinition extends DefinitionBase_1.DefinitionBase {
    isTypeDefinition = false;
    isVariableDefinition = true;
    constructor(name, node) {
        super(DefinitionType_1.DefinitionType.ImplicitGlobalVariable, name, node, null);
    }
}
exports.ImplicitGlobalVariableDefinition = ImplicitGlobalVariableDefinition;
