import type { ESLintScopeVariable } from './ESLintScopeVariable';
import type { Variable } from './Variable';
export { ESLintScopeVariable } from './ESLintScopeVariable';
export { ImplicitLibVariable, type ImplicitLibVariableOptions, type LibDefinition, } from './ImplicitLibVariable';
export { Variable } from './Variable';
export type ScopeVariable = ESLintScopeVariable | Variable;
//# sourceMappingURL=index.d.ts.map