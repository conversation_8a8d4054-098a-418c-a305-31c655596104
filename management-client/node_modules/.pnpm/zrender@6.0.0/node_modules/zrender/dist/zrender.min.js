!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t=t||self).zrender={})}(this,function(t){"use strict";var e=function(){this.firefox=!1,this.ie=!1,this.edge=!1,this.newEdge=!1,this.weChat=!1},c=new function(){this.browser=new e,this.node=!1,this.wxa=!1,this.worker=!1,this.svgSupported=!1,this.touchEventsSupported=!1,this.pointerEventsSupported=!1,this.domSupported=!1,this.transformSupported=!1,this.transform3dSupported=!1,this.hasGlobalWindow="undefined"!=typeof window};"object"==typeof wx&&"function"==typeof wx.getSystemInfoSync?(c.wxa=!0,c.touchEventsSupported=!0):"undefined"==typeof document&&"undefined"!=typeof self?c.worker=!0:!c.hasGlobalWindow||"Deno"in window||"undefined"!=typeof navigator&&"string"==typeof navigator.userAgent&&-1<navigator.userAgent.indexOf("Node.js")?(c.node=!0,c.svgSupported=!0):function(t,e){var r=e.browser,i=t.match(/Firefox\/([\d.]+)/),n=t.match(/MSIE\s([\d.]+)/)||t.match(/Trident\/.+?rv:(([\d.]+))/),o=t.match(/Edge?\/([\d.]+)/),a=/micromessenger/i.test(t);i&&(r.firefox=!0,r.version=i[1]);n&&(r.ie=!0,r.version=n[1]);o&&(r.edge=!0,r.version=o[1],r.newEdge=18<+o[1].split(".")[0]);a&&(r.weChat=!0);{var s;e.svgSupported="undefined"!=typeof SVGRect,e.touchEventsSupported="ontouchstart"in window&&!r.ie&&!r.edge,e.pointerEventsSupported="onpointerdown"in window&&(r.edge||r.ie&&11<=+r.version),(e.domSupported="undefined"!=typeof document)&&(s=document.documentElement.style,e.transform3dSupported=(r.ie&&"transition"in s||r.edge||"WebKitCSSMatrix"in window&&"m11"in new WebKitCSSMatrix||"MozPerspective"in s)&&!("OTransition"in s),e.transformSupported=e.transform3dSupported||r.ie&&9<=+r.version)}}(navigator.userAgent,c);var h=12,v="sans-serif",W=h+"px "+v;var l,u,p=function(t){var e={};if("undefined"==typeof JSON)return e;for(var r=0;r<t.length;r++){var i=String.fromCharCode(r+32),n=(t.charCodeAt(r)-20)/100;e[i]=n}return e}("007LLmW'55;N0500LLLLLLLLLL00NNNLzWW\\\\WQb\\0FWLg\\bWb\\WQ\\WrWWQ000CL5LLFLL0LL**F*gLLLL5F0LF\\FFF5.5N"),f={createCanvas:function(){return"undefined"!=typeof document&&document.createElement("canvas")},measureText:function(t,e){var r;if(l||(r=f.createCanvas(),l=r&&r.getContext("2d")),l)return u!==e&&(u=l.font=e||W),l.measureText(t);t=t||"";var i=/((?:\d+)?\.?\d*)px/.exec(e=e||W),n=i&&+i[1]||h,o=0;if(0<=e.indexOf("mono"))o=n*t.length;else for(var a=0;a<t.length;a++){var s=p[t[a]];o+=null==s?n:s*n}return{width:o}},loadImage:function(t,e,r){var i=new Image;return i.onload=e,i.onerror=r,i.src=t,i}};var s=L(["Function","RegExp","Date","Error","CanvasGradient","CanvasPattern","Image","Canvas"],function(t,e){return t["[object "+e+"]"]=!0,t},{}),d=L(["Int8","Uint8","Uint8Clamped","Int16","Uint16","Int32","Uint32","Float32","Float64"],function(t,e){return t["[object "+e+"Array]"]=!0,t},{}),y=Object.prototype.toString,r=Array.prototype,a=r.forEach,g=r.filter,n=r.slice,_=r.map,i=function(){}.constructor,o=i?i.prototype:null,m="__proto__",x=2311;function w(){return x++}function b(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];"undefined"!=typeof console&&console.error.apply(console,t)}function C(t){if(null==t||"object"!=typeof t)return t;var e=t,r=y.call(t);if("[object Array]"===r){if(!rt(t)){e=[];for(var i=0,n=t.length;i<n;i++)e[i]=C(t[i])}}else if(d[r]){if(!rt(t)){var o=t.constructor;if(o.from)e=o.from(t);else{e=new o(t.length);for(i=0,n=t.length;i<n;i++)e[i]=t[i]}}}else if(!s[r]&&!rt(t)&&!q(t))for(var a in e={},t)t.hasOwnProperty(a)&&a!==m&&(e[a]=C(t[a]));return e}function S(t,e,r){if(!E(e)||!E(t))return r?C(e):t;for(var i in e){var n,o;e.hasOwnProperty(i)&&i!==m&&(n=t[i],!E(o=e[i])||!E(n)||G(o)||G(n)||q(o)||q(n)||X(o)||X(n)||rt(o)||rt(n)?!r&&i in t||(t[i]=C(e[i])):S(n,o,r))}return t}function I(t,e){if(Object.assign)Object.assign(t,e);else for(var r in e)e.hasOwnProperty(r)&&r!==m&&(t[r]=e[r]);return t}function T(t,e,r){for(var i=F(e),n=0,o=i.length;n<o;n++){var a=i[n];(r?null!=e[a]:null==t[a])&&(t[a]=e[a])}return t}var k=f.createCanvas;function P(t,e){if(t){if(t.indexOf)return t.indexOf(e);for(var r=0,i=t.length;r<i;r++)if(t[r]===e)return r}return-1}function M(t,e,r){if(t="prototype"in t?t.prototype:t,e="prototype"in e?e.prototype:e,Object.getOwnPropertyNames)for(var i=Object.getOwnPropertyNames(e),n=0;n<i.length;n++){var o=i[n];"constructor"!==o&&(r?null!=e[o]:null==t[o])&&(t[o]=e[o])}else T(t,e,r)}function A(t){return!!t&&("string"!=typeof t&&"number"==typeof t.length)}function R(t,e,r){if(t&&e)if(t.forEach&&t.forEach===a)t.forEach(e,r);else if(t.length===+t.length)for(var i=0,n=t.length;i<n;i++)e.call(r,t[i],i,t);else for(var o in t)t.hasOwnProperty(o)&&e.call(r,t[o],o,t)}function D(t,e,r){if(!t)return[];if(!e)return Q(t);if(t.map&&t.map===_)return t.map(e,r);for(var i=[],n=0,o=t.length;n<o;n++)i.push(e.call(r,t[n],n,t));return i}function L(t,e,r,i){if(t&&e){for(var n=0,o=t.length;n<o;n++)r=e.call(i,r,t[n],n,t);return r}}function O(t,e,r){if(!t)return[];if(!e)return Q(t);if(t.filter&&t.filter===g)return t.filter(e,r);for(var i=[],n=0,o=t.length;n<o;n++)e.call(r,t[n],n,t)&&i.push(t[n]);return i}function F(t){if(!t)return[];if(Object.keys)return Object.keys(t);var e=[];for(var r in t)t.hasOwnProperty(r)&&e.push(r);return e}var z=o&&B(o.bind)?o.call.bind(o.bind):function(t,e){for(var r=[],i=2;i<arguments.length;i++)r[i-2]=arguments[i];return function(){return t.apply(e,r.concat(n.call(arguments)))}};function G(t){return Array.isArray?Array.isArray(t):"[object Array]"===y.call(t)}function B(t){return"function"==typeof t}function N(t){return"string"==typeof t}function H(t){return"number"==typeof t}function E(t){var e=typeof t;return"function"==e||!!t&&"object"==e}function X(t){return!!s[y.call(t)]}function Y(t){return!!d[y.call(t)]}function q(t){return"object"==typeof t&&"number"==typeof t.nodeType&&"object"==typeof t.ownerDocument}function j(t){return null!=t.colorStops}function V(t){return null!=t.image}function U(t){return t!=t}function Z(t,e){return null!=t?t:e}function K(t,e,r){return null!=t?t:null!=e?e:r}function Q(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];return n.apply(t,e)}function $(t){if("number"==typeof t)return[t,t,t,t];var e=t.length;return 2===e?[t[0],t[1],t[0],t[1]]:3===e?[t[0],t[1],t[2],t[1]]:t}function J(t,e){if(!t)throw new Error(e)}function tt(t){return null==t?null:"function"==typeof t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}var et="__ec_primitive__";function rt(t){return t[et]}var it=(nt.prototype.delete=function(t){var e=this.has(t);return e&&delete this.data[t],e},nt.prototype.has=function(t){return this.data.hasOwnProperty(t)},nt.prototype.get=function(t){return this.data[t]},nt.prototype.set=function(t,e){return this.data[t]=e,this},nt.prototype.keys=function(){return F(this.data)},nt.prototype.forEach=function(t){var e=this.data;for(var r in e)e.hasOwnProperty(r)&&t(e[r],r)},nt);function nt(){this.data={}}var ot="function"==typeof Map;var at=(st.prototype.hasKey=function(t){return this.data.has(t)},st.prototype.get=function(t){return this.data.get(t)},st.prototype.set=function(t,e){return this.data.set(t,e),e},st.prototype.each=function(r,i){this.data.forEach(function(t,e){r.call(i,t,e)})},st.prototype.keys=function(){var t=this.data.keys();return ot?Array.from(t):t},st.prototype.removeKey=function(t){this.data.delete(t)},st);function st(t){var r=G(t);this.data=new(ot?Map:it);var i=this;function e(t,e){r?i.set(t,e):i.set(e,t)}t instanceof st?t.each(e):t&&R(t,e)}function ht(t,e){var r,i=Object.create?Object.create(t):((r=function(){}).prototype=t,new r);return e&&I(i,e),i}function lt(t){var e=t.style;e.webkitUserSelect="none",e.userSelect="none",e.webkitTapHighlightColor="rgba(0,0,0,0)",e["-webkit-touch-callout"]="none"}function ut(t,e){return t.hasOwnProperty(e)}function ct(){}var pt=180/Math.PI,ft=Number.EPSILON||Math.pow(2,-52),dt=Object.freeze({__proto__:null,guid:w,logError:b,clone:C,merge:S,mergeAll:function(t,e){for(var r=t[0],i=1,n=t.length;i<n;i++)r=S(r,t[i],e);return r},extend:I,defaults:T,createCanvas:k,indexOf:P,inherits:function(t,e){var r=t.prototype;function i(){}for(var n in i.prototype=e.prototype,t.prototype=new i,r)r.hasOwnProperty(n)&&(t.prototype[n]=r[n]);(t.prototype.constructor=t).superClass=e},mixin:M,isArrayLike:A,each:R,map:D,reduce:L,filter:O,find:function(t,e,r){if(t&&e)for(var i=0,n=t.length;i<n;i++)if(e.call(r,t[i],i,t))return t[i]},keys:F,bind:z,curry:function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];return function(){return t.apply(this,e.concat(n.call(arguments)))}},isArray:G,isFunction:B,isString:N,isStringSafe:function(t){return"[object String]"===y.call(t)},isNumber:H,isObject:E,isBuiltInObject:X,isTypedArray:Y,isDom:q,isGradientObject:j,isImagePatternObject:V,isRegExp:function(t){return"[object RegExp]"===y.call(t)},eqNaN:U,retrieve:function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var r=0,i=t.length;r<i;r++)if(null!=t[r])return t[r]},retrieve2:Z,retrieve3:K,slice:Q,normalizeCssArray:$,assert:J,trim:tt,setAsPrimitive:function(t){t[et]=!0},isPrimitive:rt,HashMap:at,createHashMap:function(t){return new at(t)},concatArray:function(t,e){for(var r=new t.constructor(t.length+e.length),i=0;i<t.length;i++)r[i]=t[i];for(var n=t.length,i=0;i<e.length;i++)r[i+n]=e[i];return r},createObject:ht,disableUserSelect:lt,hasOwn:ut,noop:ct,RADIAN_TO_DEGREE:pt,EPSILON:ft}),yt=function(t,e){return(yt=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)};function vt(t,e){function r(){this.constructor=t}yt(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}function gt(t,e){return null==t&&(t=0),null==e&&(e=0),[t,e]}function _t(t){return[t[0],t[1]]}function mt(t,e,r){return t[0]=e[0]+r[0],t[1]=e[1]+r[1],t}function xt(t,e,r){return t[0]=e[0]-r[0],t[1]=e[1]-r[1],t}function wt(t){return Math.sqrt(bt(t))}function bt(t){return t[0]*t[0]+t[1]*t[1]}function St(t,e,r){return t[0]=e[0]*r,t[1]=e[1]*r,t}function Tt(t,e){var r=wt(e);return 0===r?(t[0]=0,t[1]=0):(t[0]=e[0]/r,t[1]=e[1]/r),t}function kt(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))}var Ct=kt;function Pt(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])}var Mt=Pt;function At(t,e,r,i){return t[0]=e[0]+i*(r[0]-e[0]),t[1]=e[1]+i*(r[1]-e[1]),t}function Dt(t,e,r){var i=e[0],n=e[1];return t[0]=r[0]*i+r[2]*n+r[4],t[1]=r[1]*i+r[3]*n+r[5],t}function Lt(t,e,r){return t[0]=Math.min(e[0],r[0]),t[1]=Math.min(e[1],r[1]),t}function zt(t,e,r){return t[0]=Math.max(e[0],r[0]),t[1]=Math.max(e[1],r[1]),t}var It=Object.freeze({__proto__:null,create:gt,copy:function(t,e){return t[0]=e[0],t[1]=e[1],t},clone:_t,set:function(t,e,r){return t[0]=e,t[1]=r,t},add:mt,scaleAndAdd:function(t,e,r,i){return t[0]=e[0]+r[0]*i,t[1]=e[1]+r[1]*i,t},sub:xt,len:wt,length:wt,lenSquare:bt,lengthSquare:bt,mul:function(t,e,r){return t[0]=e[0]*r[0],t[1]=e[1]*r[1],t},div:function(t,e,r){return t[0]=e[0]/r[0],t[1]=e[1]/r[1],t},dot:function(t,e){return t[0]*e[0]+t[1]*e[1]},scale:St,normalize:Tt,distance:kt,dist:Ct,distanceSquare:Pt,distSquare:Mt,negate:function(t,e){return t[0]=-e[0],t[1]=-e[1],t},lerp:At,applyTransform:Dt,min:Lt,max:zt}),Rt=function(t,e){this.target=t,this.topTarget=e&&e.topTarget},Ot=(Ft.prototype._dragStart=function(t){for(var e=t.target;e&&!e.draggable;)e=e.parent||e.__hostTarget;e&&((this._draggingTarget=e).dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.handler.dispatchToElement(new Rt(e,t),"dragstart",t.event))},Ft.prototype._drag=function(t){var e,r,i,n,o,a,s=this._draggingTarget;s&&(e=t.offsetX,r=t.offsetY,i=e-this._x,n=r-this._y,this._x=e,this._y=r,s.drift(i,n,t),this.handler.dispatchToElement(new Rt(s,t),"drag",t.event),o=this.handler.findHover(e,r,s).target,a=this._dropTarget,s!==(this._dropTarget=o)&&(a&&o!==a&&this.handler.dispatchToElement(new Rt(a,t),"dragleave",t.event),o&&o!==a&&this.handler.dispatchToElement(new Rt(o,t),"dragenter",t.event)))},Ft.prototype._dragEnd=function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.handler.dispatchToElement(new Rt(e,t),"dragend",t.event),this._dropTarget&&this.handler.dispatchToElement(new Rt(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null},Ft);function Ft(t){(this.handler=t).on("mousedown",this._dragStart,this),t.on("mousemove",this._drag,this),t.on("mouseup",this._dragEnd,this)}var Bt=(Nt.prototype.on=function(t,e,r,i){this._$handlers||(this._$handlers={});var n=this._$handlers;if("function"==typeof e&&(i=r,r=e,e=null),!r||!t)return this;var o=this._$eventProcessor;null!=e&&o&&o.normalizeQuery&&(e=o.normalizeQuery(e)),n[t]||(n[t]=[]);for(var a=0;a<n[t].length;a++)if(n[t][a].h===r)return this;var s={h:r,query:e,ctx:i||this,callAtLast:r.zrEventfulCallAtLast},h=n[t].length-1,l=n[t][h];return l&&l.callAtLast?n[t].splice(h,0,s):n[t].push(s),this},Nt.prototype.isSilent=function(t){var e=this._$handlers;return!e||!e[t]||!e[t].length},Nt.prototype.off=function(t,e){var r=this._$handlers;if(!r)return this;if(!t)return this._$handlers={},this;if(e){if(r[t]){for(var i=[],n=0,o=r[t].length;n<o;n++)r[t][n].h!==e&&i.push(r[t][n]);r[t]=i}r[t]&&0===r[t].length&&delete r[t]}else delete r[t];return this},Nt.prototype.trigger=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];if(!this._$handlers)return this;var i=this._$handlers[t],n=this._$eventProcessor;if(i)for(var o=e.length,a=i.length,s=0;s<a;s++){var h=i[s];if(!n||!n.filter||null==h.query||n.filter(t,h.query))switch(o){case 0:h.h.call(h.ctx);break;case 1:h.h.call(h.ctx,e[0]);break;case 2:h.h.call(h.ctx,e[0],e[1]);break;default:h.h.apply(h.ctx,e)}}return n&&n.afterTrigger&&n.afterTrigger(t),this},Nt.prototype.triggerWithContext=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];if(!this._$handlers)return this;var i=this._$handlers[t],n=this._$eventProcessor;if(i)for(var o=e.length,a=e[o-1],s=i.length,h=0;h<s;h++){var l=i[h];if(!n||!n.filter||null==l.query||n.filter(t,l.query))switch(o){case 0:l.h.call(a);break;case 1:l.h.call(a,e[0]);break;case 2:l.h.call(a,e[0],e[1]);break;default:l.h.apply(a,e.slice(1,o-1))}}return n&&n.afterTrigger&&n.afterTrigger(t),this},Nt);function Nt(t){t&&(this._$eventProcessor=t)}var Ht=Math.log(2);function Wt(t,e,r,i,n,o){var a=i+"-"+n,s=t.length;if(o.hasOwnProperty(a))return o[a];if(1===e){var h=Math.round(Math.log((1<<s)-1&~n)/Ht);return t[r][h]}for(var l=i|1<<r,u=r+1;i&1<<u;)u++;for(var c=0,p=0,f=0;p<s;p++){var d=1<<p;d&n||(c+=(f%2?-1:1)*t[r][p]*Wt(t,e-1,u,l,n|d,o),f++)}return o[a]=c}function Et(t,e){var r=[[t[0],t[1],1,0,0,0,-e[0]*t[0],-e[0]*t[1]],[0,0,0,t[0],t[1],1,-e[1]*t[0],-e[1]*t[1]],[t[2],t[3],1,0,0,0,-e[2]*t[2],-e[2]*t[3]],[0,0,0,t[2],t[3],1,-e[3]*t[2],-e[3]*t[3]],[t[4],t[5],1,0,0,0,-e[4]*t[4],-e[4]*t[5]],[0,0,0,t[4],t[5],1,-e[5]*t[4],-e[5]*t[5]],[t[6],t[7],1,0,0,0,-e[6]*t[6],-e[6]*t[7]],[0,0,0,t[6],t[7],1,-e[7]*t[6],-e[7]*t[7]]],i={},n=Wt(r,8,0,0,0,i);if(0!==n){for(var o=[],a=0;a<8;a++)for(var s=0;s<8;s++)null==o[s]&&(o[s]=0),o[s]+=((a+s)%2?-1:1)*Wt(r,7,0===a?1:0,1<<a,1<<s,i)/n*e[a];return function(t,e,r){var i=e*o[6]+r*o[7]+1;t[0]=(e*o[0]+r*o[1]+o[2])/i,t[1]=(e*o[3]+r*o[4]+o[5])/i}}}var Xt="___zrEVENTSAVED";function Yt(t,e,r,i,n){if(e.getBoundingClientRect&&c.domSupported&&!qt(e)){var o=e[Xt]||(e[Xt]={}),a=function(t,e,r){for(var i=r?"invTrans":"trans",n=e[i],o=e.srcCoords,a=[],s=[],h=!0,l=0;l<4;l++){var u=t[l].getBoundingClientRect(),c=2*l,p=u.left,f=u.top;a.push(p,f),h=h&&o&&p===o[c]&&f===o[1+c],s.push(t[l].offsetLeft,t[l].offsetTop)}return h&&n?n:(e.srcCoords=a,e[i]=r?Et(s,a):Et(a,s))}(function(t,e){var r=e.markers;if(r)return r;r=e.markers=[];for(var i=["left","right"],n=["top","bottom"],o=0;o<4;o++){var a=document.createElement("div"),s=a.style,h=o%2,l=(o>>1)%2;s.cssText=["position: absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","user-select: none","width:0","height:0",i[h]+":0",n[l]+":0",i[1-h]+":auto",n[1-l]+":auto",""].join("!important;"),t.appendChild(a),r.push(a)}return e.clearMarkers=function(){R(r,function(t){t.parentNode&&t.parentNode.removeChild(t)})},r}(e,o),o,n);if(a)return a(t,r,i),1}}function qt(t){return"CANVAS"===t.nodeName.toUpperCase()}var jt=/([&<>"'])/g,Vt={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};var Ut=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,Gt=[],Zt=c.browser.firefox&&+c.browser.version.split(".")[0]<39;function Kt(t,e,r,i){return r=r||{},i?Qt(t,e,r):Zt&&null!=e.layerX&&e.layerX!==e.offsetX?(r.zrX=e.layerX,r.zrY=e.layerY):null!=e.offsetX?(r.zrX=e.offsetX,r.zrY=e.offsetY):Qt(t,e,r),r}function Qt(t,e,r){if(c.domSupported&&t.getBoundingClientRect){var i=e.clientX,n=e.clientY;if(qt(t)){var o=t.getBoundingClientRect();return r.zrX=i-o.left,void(r.zrY=n-o.top)}if(Yt(Gt,t,i,n))return r.zrX=Gt[0],void(r.zrY=Gt[1])}r.zrX=r.zrY=0}function $t(t){return t||window.event}function Jt(t,e,r){if(null!=(e=$t(e)).zrX)return e;var i,n,o=e.type;o&&0<=o.indexOf("touch")?(i="touchend"!==o?e.targetTouches[0]:e.changedTouches[0])&&Kt(t,i,e,r):(Kt(t,e,e,r),n=function(t){var e=t.wheelDelta;if(e)return e;var r=t.deltaX,i=t.deltaY;return null!=r&&null!=i?3*(0!==i?Math.abs(i):Math.abs(r))*(0<i||!(i<0)&&0<r?-1:1):e}(e),e.zrDelta=n?n/120:-(e.detail||0)/3);var a=e.button;return null==e.which&&void 0!==a&&Ut.test(e.type)&&(e.which=1&a?1:2&a?3:4&a?2:0),e}var te=function(t){t.preventDefault(),t.stopPropagation(),t.cancelBubble=!0},ee=(re.prototype.recognize=function(t,e,r){return this._doTrack(t,e,r),this._recognize(t)},re.prototype.clear=function(){return this._track.length=0,this},re.prototype._doTrack=function(t,e,r){var i=t.touches;if(i){for(var n={points:[],touches:[],target:e,event:t},o=0,a=i.length;o<a;o++){var s=i[o],h=Kt(r,s,{});n.points.push([h.zrX,h.zrY]),n.touches.push(s)}this._track.push(n)}},re.prototype._recognize=function(t){for(var e in ne)if(ne.hasOwnProperty(e)){var r=ne[e](this._track,t);if(r)return r}},re);function re(){this._track=[]}function ie(t){var e=t[1][0]-t[0][0],r=t[1][1]-t[0][1];return Math.sqrt(e*e+r*r)}var ne={pinch:function(t,e){var r=t.length;if(r){var i,n=(t[r-1]||{}).points,o=(t[r-2]||{}).points||n;if(o&&1<o.length&&n&&1<n.length){var a=ie(n)/ie(o);isFinite(a)||(a=1),e.pinchScale=a;var s=[((i=n)[0][0]+i[1][0])/2,(i[0][1]+i[1][1])/2];return e.pinchX=s[0],e.pinchY=s[1],{type:"pinch",target:t[0].target,event:e}}}}};function oe(){return[1,0,0,1,0,0]}function ae(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t}function se(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t}function he(t,e,r){var i=e[0]*r[0]+e[2]*r[1],n=e[1]*r[0]+e[3]*r[1],o=e[0]*r[2]+e[2]*r[3],a=e[1]*r[2]+e[3]*r[3],s=e[0]*r[4]+e[2]*r[5]+e[4],h=e[1]*r[4]+e[3]*r[5]+e[5];return t[0]=i,t[1]=n,t[2]=o,t[3]=a,t[4]=s,t[5]=h,t}function le(t,e,r){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+r[0],t[5]=e[5]+r[1],t}function ue(t,e,r,i){void 0===i&&(i=[0,0]);var n=e[0],o=e[2],a=e[4],s=e[1],h=e[3],l=e[5],u=Math.sin(r),c=Math.cos(r);return t[0]=n*c+s*u,t[1]=-n*u+s*c,t[2]=o*c+h*u,t[3]=-o*u+c*h,t[4]=c*(a-i[0])+u*(l-i[1])+i[0],t[5]=c*(l-i[1])-u*(a-i[0])+i[1],t}function ce(t,e,r){var i=r[0],n=r[1];return t[0]=e[0]*i,t[1]=e[1]*n,t[2]=e[2]*i,t[3]=e[3]*n,t[4]=e[4]*i,t[5]=e[5]*n,t}function pe(t,e){var r=e[0],i=e[2],n=e[4],o=e[1],a=e[3],s=e[5],h=r*a-o*i;return h?(h=1/h,t[0]=a*h,t[1]=-o*h,t[2]=-i*h,t[3]=r*h,t[4]=(i*s-a*n)*h,t[5]=(o*n-r*s)*h,t):null}var fe=Object.freeze({__proto__:null,create:oe,identity:ae,copy:se,mul:he,translate:le,rotate:ue,scale:ce,invert:pe,clone:function(t){var e=oe();return se(e,t),e}}),de=(ye.prototype.copy=function(t){return this.x=t.x,this.y=t.y,this},ye.prototype.clone=function(){return new ye(this.x,this.y)},ye.prototype.set=function(t,e){return this.x=t,this.y=e,this},ye.prototype.equal=function(t){return t.x===this.x&&t.y===this.y},ye.prototype.add=function(t){return this.x+=t.x,this.y+=t.y,this},ye.prototype.scale=function(t){this.x*=t,this.y*=t},ye.prototype.scaleAndAdd=function(t,e){this.x+=t.x*e,this.y+=t.y*e},ye.prototype.sub=function(t){return this.x-=t.x,this.y-=t.y,this},ye.prototype.dot=function(t){return this.x*t.x+this.y*t.y},ye.prototype.len=function(){return Math.sqrt(this.x*this.x+this.y*this.y)},ye.prototype.lenSquare=function(){return this.x*this.x+this.y*this.y},ye.prototype.normalize=function(){var t=this.len();return this.x/=t,this.y/=t,this},ye.prototype.distance=function(t){var e=this.x-t.x,r=this.y-t.y;return Math.sqrt(e*e+r*r)},ye.prototype.distanceSquare=function(t){var e=this.x-t.x,r=this.y-t.y;return e*e+r*r},ye.prototype.negate=function(){return this.x=-this.x,this.y=-this.y,this},ye.prototype.transform=function(t){if(t){var e=this.x,r=this.y;return this.x=t[0]*e+t[2]*r+t[4],this.y=t[1]*e+t[3]*r+t[5],this}},ye.prototype.toArray=function(t){return t[0]=this.x,t[1]=this.y,t},ye.prototype.fromArray=function(t){this.x=t[0],this.y=t[1]},ye.set=function(t,e,r){t.x=e,t.y=r},ye.copy=function(t,e){t.x=e.x,t.y=e.y},ye.len=function(t){return Math.sqrt(t.x*t.x+t.y*t.y)},ye.lenSquare=function(t){return t.x*t.x+t.y*t.y},ye.dot=function(t,e){return t.x*e.x+t.y*e.y},ye.add=function(t,e,r){t.x=e.x+r.x,t.y=e.y+r.y},ye.sub=function(t,e,r){t.x=e.x-r.x,t.y=e.y-r.y},ye.scale=function(t,e,r){t.x=e.x*r,t.y=e.y*r},ye.scaleAndAdd=function(t,e,r,i){t.x=e.x+r.x*i,t.y=e.y+r.y*i},ye.lerp=function(t,e,r,i){var n=1-i;t.x=n*e.x+i*r.x,t.y=n*e.y+i*r.y},ye);function ye(t,e){this.x=t||0,this.y=e||0}var ve=Math.min,ge=Math.max,_e=Math.abs,me=["x","y"],xe=["width","height"],we=new de,be=new de,Se=new de,Te=new de,ke=Re(),Ce=ke.minTv,Pe=ke.maxTv,Me=[0,0],Ae=(De.set=function(t,e,r,i,n){return i<0&&(e+=i,i=-i),n<0&&(r+=n,n=-n),t.x=e,t.y=r,t.width=i,t.height=n,t},De.prototype.union=function(t){var e=ve(t.x,this.x),r=ve(t.y,this.y);isFinite(this.x)&&isFinite(this.width)?this.width=ge(t.x+t.width,this.x+this.width)-e:this.width=t.width,isFinite(this.y)&&isFinite(this.height)?this.height=ge(t.y+t.height,this.y+this.height)-r:this.height=t.height,this.x=e,this.y=r},De.prototype.applyTransform=function(t){De.applyTransform(this,this,t)},De.prototype.calculateTransform=function(t){var e=t.width/this.width,r=t.height/this.height,i=oe();return le(i,i,[-this.x,-this.y]),ce(i,i,[e,r]),le(i,i,[t.x,t.y]),i},De.prototype.intersect=function(t,e,r){return De.intersect(this,t,e,r)},De.intersect=function(t,e,r,i){r&&de.set(r,0,0);var n=i&&i.outIntersectRect||null,o=i&&i.clamp;if(n&&(n.x=n.y=n.width=n.height=NaN),!t||!e)return!1;t instanceof De||(t=De.set(Le,t.x,t.y,t.width,t.height)),e instanceof De||(e=De.set(ze,e.x,e.y,e.width,e.height));var a=!!r;ke.reset(i,a);var s=ke.touchThreshold,h=t.x+s,l=t.x+t.width-s,u=t.y+s,c=t.y+t.height-s,p=e.x+s,f=e.x+e.width-s,d=e.y+s,y=e.y+e.height-s;if(l<h||c<u||f<p||y<d)return!1;var v=!(l<p||f<h||c<d||y<u);return(a||n)&&(Me[0]=1/0,Ie(h,l,p,f,Me[1]=0,a,n,o),Ie(u,c,d,y,1,a,n,o),a&&de.copy(r,v?ke.useDir?ke.dirMinTv:Ce:Pe)),v},De.contain=function(t,e,r){return e>=t.x&&e<=t.x+t.width&&r>=t.y&&r<=t.y+t.height},De.prototype.contain=function(t,e){return De.contain(this,t,e)},De.prototype.clone=function(){return new De(this.x,this.y,this.width,this.height)},De.prototype.copy=function(t){De.copy(this,t)},De.prototype.plain=function(){return{x:this.x,y:this.y,width:this.width,height:this.height}},De.prototype.isFinite=function(){return isFinite(this.x)&&isFinite(this.y)&&isFinite(this.width)&&isFinite(this.height)},De.prototype.isZero=function(){return 0===this.width||0===this.height},De.create=function(t){return new De(t.x,t.y,t.width,t.height)},De.copy=function(t,e){return t.x=e.x,t.y=e.y,t.width=e.width,t.height=e.height,t},De.applyTransform=function(t,e,r){if(r){if(r[1]<1e-5&&-1e-5<r[1]&&r[2]<1e-5&&-1e-5<r[2]){var i=r[0],n=r[3],o=r[4],a=r[5];return t.x=e.x*i+o,t.y=e.y*n+a,t.width=e.width*i,t.height=e.height*n,t.width<0&&(t.x+=t.width,t.width=-t.width),void(t.height<0&&(t.y+=t.height,t.height=-t.height))}we.x=Se.x=e.x,we.y=Te.y=e.y,be.x=Te.x=e.x+e.width,be.y=Se.y=e.y+e.height,we.transform(r),Te.transform(r),be.transform(r),Se.transform(r),t.x=ve(we.x,be.x,Se.x,Te.x),t.y=ve(we.y,be.y,Se.y,Te.y);var s=ge(we.x,be.x,Se.x,Te.x),h=ge(we.y,be.y,Se.y,Te.y);t.width=s-t.x,t.height=h-t.y}else t!==e&&De.copy(t,e)},De);function De(t,e,r,i){De.set(this,t,e,r,i)}var Le=new Ae(0,0,0,0),ze=new Ae(0,0,0,0);function Ie(t,e,r,i,n,o,a,s){var h=_e(e-r),l=_e(i-t),u=ve(h,l),c=me[n],p=me[1-n],f=xe[n];e<r||i<t?h<l?(o&&(Pe[c]=-h),s&&(a[c]=e,a[f]=0)):(o&&(Pe[c]=l),s&&(a[c]=t,a[f]=0)):(a&&(a[c]=ge(t,r),a[f]=ve(e,i)-a[c]),o&&(u<Me[0]||ke.useDir)&&(Me[0]=ve(u,Me[0]),(h<l||!ke.bidirectional)&&(Ce[c]=h,Ce[p]=0,ke.useDir&&ke.calcDirMTV()),(l<=h||!ke.bidirectional)&&(Ce[c]=-l,Ce[p]=0,ke.useDir&&ke.calcDirMTV())))}function Re(){var a=0,s=new de,h=new de,l={minTv:new de,maxTv:new de,useDir:!1,dirMinTv:new de,touchThreshold:0,bidirectional:!0,negativeSize:!1,reset:function(t,e){l.touchThreshold=0,t&&null!=t.touchThreshold&&(l.touchThreshold=ge(0,t.touchThreshold)),l.negativeSize=!1,e&&(l.minTv.set(1/0,1/0),l.maxTv.set(0,0),l.useDir=!1,t&&null!=t.direction&&(l.useDir=!0,l.dirMinTv.copy(l.minTv),h.copy(l.minTv),a=t.direction,l.bidirectional=null==t.bidirectional||!!t.bidirectional,l.bidirectional||s.set(Math.cos(a),Math.sin(a))))},calcDirMTV:function(){var t=l.minTv,e=l.dirMinTv,r=t.y*t.y+t.x*t.x,i=Math.sin(a),n=Math.cos(a),o=i*t.y+n*t.x;u(o)?u(t.x)&&u(t.y)&&e.set(0,0):(h.x=r*n/o,h.y=r*i/o,u(h.x)&&u(h.y)?e.set(0,0):(l.bidirectional||0<s.dot(h))&&h.len()<e.len()&&e.copy(h))}};function u(t){return _e(t)<1e-10}return l}var Oe="silent";function Fe(){te(this.event)}var Be,Ne=(vt(He,Be=Bt),He.prototype.dispose=function(){},He.prototype.setCursor=function(){},He);function He(){var t=null!==Be&&Be.apply(this,arguments)||this;return t.handler=null,t}var We,Ee=function(t,e){this.x=t,this.y=e},Xe=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],Ye=new Ae(0,0,0,0),qe=(vt(je,We=Bt),je.prototype.setHandlerProxy=function(e){this.proxy&&this.proxy.dispose(),e&&(R(Xe,function(t){e.on&&e.on(t,this[t],this)},this),e.handler=this),this.proxy=e},je.prototype.mousemove=function(t){var e=t.zrX,r=t.zrY,i=Ue(this,e,r),n=this._hovered,o=n.target;o&&!o.__zr&&(o=(n=this.findHover(n.x,n.y)).target);var a=this._hovered=i?new Ee(e,r):this.findHover(e,r),s=a.target,h=this.proxy;h.setCursor&&h.setCursor(s?s.cursor:"default"),o&&s!==o&&this.dispatchToElement(n,"mouseout",t),this.dispatchToElement(a,"mousemove",t),s&&s!==o&&this.dispatchToElement(a,"mouseover",t)},je.prototype.mouseout=function(t){var e=t.zrEventControl;"only_globalout"!==e&&this.dispatchToElement(this._hovered,"mouseout",t),"no_globalout"!==e&&this.trigger("globalout",{type:"globalout",event:t})},je.prototype.resize=function(){this._hovered=new Ee(0,0)},je.prototype.dispatch=function(t,e){var r=this[t];r&&r.call(this,e)},je.prototype.dispose=function(){this.proxy.dispose(),this.storage=null,this.proxy=null,this.painter=null},je.prototype.setCursorStyle=function(t){var e=this.proxy;e.setCursor&&e.setCursor(t)},je.prototype.dispatchToElement=function(t,e,r){var i=(t=t||{}).target;if(!i||!i.silent){for(var n,o,a="on"+e,s={type:e,event:o=r,target:(n=t).target,topTarget:n.topTarget,cancelBubble:!1,offsetX:o.zrX,offsetY:o.zrY,gestureEvent:o.gestureEvent,pinchX:o.pinchX,pinchY:o.pinchY,pinchScale:o.pinchScale,wheelDelta:o.zrDelta,zrByTouch:o.zrByTouch,which:o.which,stop:Fe};i&&(i[a]&&(s.cancelBubble=!!i[a].call(i,s)),i.trigger(e,s),i=i.__hostTarget?i.__hostTarget:i.parent,!s.cancelBubble););s.cancelBubble||(this.trigger(e,s),this.painter&&this.painter.eachOtherLayer&&this.painter.eachOtherLayer(function(t){"function"==typeof t[a]&&t[a].call(t,s),t.trigger&&t.trigger(e,s)}))}},je.prototype.findHover=function(t,e,r){var i=this.storage.getDisplayList(),n=new Ee(t,e);if(Ve(i,n,t,e,r),this._pointerSize&&!n.target){for(var o=[],a=this._pointerSize,s=a/2,h=new Ae(t-s,e-s,a,a),l=i.length-1;0<=l;l--){var u=i[l];u===r||u.ignore||u.ignoreCoarsePointer||u.parent&&u.parent.ignoreCoarsePointer||(Ye.copy(u.getBoundingRect()),u.transform&&Ye.applyTransform(u.transform),Ye.intersect(h)&&o.push(u))}if(o.length)for(var c=Math.PI/12,p=2*Math.PI,f=0;f<s;f+=4)for(var d=0;d<p;d+=c)if(Ve(o,n,t+f*Math.cos(d),e+f*Math.sin(d),r),n.target)return n}return n},je.prototype.processGesture=function(t,e){this._gestureMgr||(this._gestureMgr=new ee);var r=this._gestureMgr;"start"===e&&r.clear();var i,n,o=r.recognize(t,this.findHover(t.zrX,t.zrY,null).target,this.proxy.dom);"end"===e&&r.clear(),o&&(i=o.type,t.gestureEvent=i,(n=new Ee).target=o.target,this.dispatchToElement(n,i,o.event))},je);function je(t,e,r,i,n){var o=We.call(this)||this;return o._hovered=new Ee(0,0),o.storage=t,o.painter=e,o.painterRoot=i,o._pointerSize=n,r=r||new Ne,o.proxy=null,o.setHandlerProxy(r),o._draggingMgr=new Ot(o),o}function Ve(t,e,r,i,n){for(var o=t.length-1;0<=o;o--){var a=t[o],s=void 0;if(a!==n&&!a.ignore&&(s=function(t,e,r){if(t[t.rectHover?"rectContain":"contain"](e,r)){for(var i=t,n=void 0,o=!1;i;){if(i.ignoreClip&&(o=!0),!o){var a=i.getClipPath();if(a&&!a.contain(e,r))return!1}i.silent&&(n=!0);var s=i.__hostTarget,i=s?i.ignoreHostSilent?null:s:i.parent}return!n||Oe}return!1}(a,r,i))&&(e.topTarget||(e.topTarget=a),s!==Oe)){e.target=a;break}}}function Ue(t,e,r){var i=t.painter;return e<0||e>i.getWidth()||r<0||r>i.getHeight()}R(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(a){qe.prototype[a]=function(t){var e,r,i=t.zrX,n=t.zrY,o=Ue(this,i,n);if("mouseup"===a&&o||(r=(e=this.findHover(i,n)).target),"mousedown"===a)this._downEl=r,this._downPoint=[t.zrX,t.zrY],this._upEl=r;else if("mouseup"===a)this._upEl=r;else if("click"===a){if(this._downEl!==this._upEl||!this._downPoint||4<Ct(this._downPoint,[t.zrX,t.zrY]))return;this._downPoint=null}this.dispatchToElement(e,a,t)}});var Ge=32,Ze=7;function Ke(t,e,r,i){var n=e+1;if(n===r)return 1;if(i(t[n++],t[e])<0){for(;n<r&&i(t[n],t[n-1])<0;)n++;!function(t,e,r){r--;for(;e<r;){var i=t[e];t[e++]=t[r],t[r--]=i}}(t,e,n)}else for(;n<r&&0<=i(t[n],t[n-1]);)n++;return n-e}function Qe(t,e,r,i,n){for(i===e&&i++;i<r;i++){for(var o,a=t[i],s=e,h=i;s<h;)n(a,t[o=s+h>>>1])<0?h=o:s=1+o;var l=i-s;switch(l){case 3:t[s+3]=t[s+2];case 2:t[s+2]=t[s+1];case 1:t[s+1]=t[s];break;default:for(;0<l;)t[s+l]=t[s+l-1],l--}t[s]=a}}function $e(t,e,r,i,n,o){var a=0,s=0,h=1;if(0<o(t,e[r+n])){for(s=i-n;h<s&&0<o(t,e[r+n+h]);)(h=1+((a=h)<<1))<=0&&(h=s);s<h&&(h=s),a+=n,h+=n}else{for(s=n+1;h<s&&o(t,e[r+n-h])<=0;)(h=1+((a=h)<<1))<=0&&(h=s);s<h&&(h=s);var l=a,a=n-h,h=n-l}for(a++;a<h;){var u=a+(h-a>>>1);0<o(t,e[r+u])?a=u+1:h=u}return h}function Je(t,e,r,i,n,o){var a=0,s=0,h=1;if(o(t,e[r+n])<0){for(s=n+1;h<s&&o(t,e[r+n-h])<0;)(h=1+((a=h)<<1))<=0&&(h=s);s<h&&(h=s);var l=a,a=n-h,h=n-l}else{for(s=i-n;h<s&&0<=o(t,e[r+n+h]);)(h=1+((a=h)<<1))<=0&&(h=s);s<h&&(h=s),a+=n,h+=n}for(a++;a<h;){var u=a+(h-a>>>1);o(t,e[r+u])<0?h=u:a=u+1}return h}function tr(d,y){var a,s,v=Ze,h=0,g=[];function e(t){var e=a[t],r=s[t],i=a[t+1],n=s[t+1];s[t]=r+n,t===h-3&&(a[t+1]=a[t+2],s[t+1]=s[t+2]),h--;var o=Je(d[i],d,e,r,0,y);e+=o,0!=(r-=o)&&0!==(n=$e(d[e+r-1],d,i,n,n-1,y))&&(r<=n?function(t,e,r,i){var n=0;for(n=0;n<e;n++)g[n]=d[t+n];var o=0,a=r,s=t;if(d[s++]=d[a++],0==--i){for(n=0;n<e;n++)d[s+n]=g[o+n];return}if(1===e){for(n=0;n<i;n++)d[s+n]=d[a+n];return d[s+i]=g[o]}var h,l,u,c=v;for(;;){l=h=0,u=!1;do{if(y(d[a],g[o])<0){if(d[s++]=d[a++],l++,(h=0)==--i){u=!0;break}}else if(d[s++]=g[o++],h++,l=0,1==--e){u=!0;break}}while((h|l)<c);if(u)break;do{if(0!==(h=Je(d[a],g,o,e,0,y))){for(n=0;n<h;n++)d[s+n]=g[o+n];if(s+=h,o+=h,(e-=h)<=1){u=!0;break}}if(d[s++]=d[a++],0==--i){u=!0;break}if(0!==(l=$e(g[o],d,a,i,0,y))){for(n=0;n<l;n++)d[s+n]=d[a+n];if(s+=l,a+=l,0===(i-=l)){u=!0;break}}if(d[s++]=g[o++],1==--e){u=!0;break}c--}while(Ze<=h||Ze<=l);if(u)break;c<0&&(c=0),c+=2}if((v=c)<1&&(v=1),1===e){for(n=0;n<i;n++)d[s+n]=d[a+n];d[s+i]=g[o]}else{if(0===e)throw new Error;for(n=0;n<e;n++)d[s+n]=g[o+n]}}:function(t,e,r,i){var n=0;for(n=0;n<i;n++)g[n]=d[r+n];var o=t+e-1,a=i-1,s=r+i-1,h=0,l=0;if(d[s--]=d[o--],0==--e){for(h=s-(i-1),n=0;n<i;n++)d[h+n]=g[n];return}if(1===i){for(l=(s-=e)+1,h=(o-=e)+1,n=e-1;0<=n;n--)d[l+n]=d[h+n];return d[s]=g[a]}var u=v;for(;;){var c=0,p=0,f=!1;do{if(y(g[a],d[o])<0){if(d[s--]=d[o--],c++,(p=0)==--e){f=!0;break}}else if(d[s--]=g[a--],p++,c=0,1==--i){f=!0;break}}while((c|p)<u);if(f)break;do{if(0!==(c=e-Je(g[a],d,t,e,e-1,y))){for(e-=c,l=(s-=c)+1,h=(o-=c)+1,n=c-1;0<=n;n--)d[l+n]=d[h+n];if(0===e){f=!0;break}}if(d[s--]=g[a--],1==--i){f=!0;break}if(0!==(p=i-$e(d[o],g,0,i,i-1,y))){for(i-=p,l=(s-=p)+1,h=(a-=p)+1,n=0;n<p;n++)d[l+n]=g[h+n];if(i<=1){f=!0;break}}if(d[s--]=d[o--],0==--e){f=!0;break}u--}while(Ze<=c||Ze<=p);if(f)break;u<0&&(u=0),u+=2}(v=u)<1&&(v=1);if(1===i){for(l=(s-=e)+1,h=(o-=e)+1,n=e-1;0<=n;n--)d[l+n]=d[h+n];d[s]=g[a]}else{if(0===i)throw new Error;for(h=s-(i-1),n=0;n<i;n++)d[h+n]=g[n]}})(e,r,i,n)}return a=[],s=[],{mergeRuns:function(){for(;1<h;){var t=h-2;if(1<=t&&s[t-1]<=s[t]+s[t+1]||2<=t&&s[t-2]<=s[t]+s[t-1])s[t-1]<s[t+1]&&t--;else if(s[t]>s[t+1])break;e(t)}},forceMergeRuns:function(){for(;1<h;){var t=h-2;0<t&&s[t-1]<s[t+1]&&t--,e(t)}},pushRun:function(t,e){a[h]=t,s[h]=e,h+=1}}}function er(t,e,r,i){r=r||0;var n=(i=i||t.length)-r;if(!(n<2)){var o=0;if(n<Ge)Qe(t,r,i,r+(o=Ke(t,r,i,e)),e);else{var a,s=tr(t,e),h=function(t){for(var e=0;Ge<=t;)e|=1&t,t>>=1;return t+e}(n);do{(o=Ke(t,r,i,e))<h&&(h<(a=n)&&(a=h),Qe(t,r,r+a,r+o,e),o=a),s.pushRun(r,o),s.mergeRuns(),n-=o,r+=o}while(0!==n);s.forceMergeRuns()}}}var rr=1,ir=4,nr=!1;function or(){nr||(nr=!0,console.warn("z / z2 / zlevel of displayable is invalid, which may cause unexpected errors"))}function ar(t,e){return t.zlevel===e.zlevel?t.z===e.z?t.z2-e.z2:t.z-e.z:t.zlevel-e.zlevel}var sr=(hr.prototype.traverse=function(t,e){for(var r=0;r<this._roots.length;r++)this._roots[r].traverse(t,e)},hr.prototype.getDisplayList=function(t,e){e=e||!1;var r=this._displayList;return!t&&r.length||this.updateDisplayList(e),r},hr.prototype.updateDisplayList=function(t){this._displayListLen=0;for(var e=this._roots,r=this._displayList,i=0,n=e.length;i<n;i++)this._updateAndAddDisplayable(e[i],null,t);r.length=this._displayListLen,er(r,ar)},hr.prototype._updateAndAddDisplayable=function(t,e,r){if(!t.ignore||r){t.beforeUpdate(),t.update(),t.afterUpdate();var i=t.getClipPath(),n=e&&e.length,o=0,a=t.__clipPaths;if(!t.ignoreClip&&(n||i)){if(a=a||(t.__clipPaths=[]),n)for(var s=0;s<e.length;s++)a[o++]=e[s];for(var h=i,l=t;h;)h.parent=l,h.updateTransform(),h=(l=a[o++]=h).getClipPath()}if(a&&(a.length=o),t.childrenRef){for(var u=t.childrenRef(),c=0;c<u.length;c++){var p=u[c];t.__dirty&&(p.__dirty|=rr),this._updateAndAddDisplayable(p,a,r)}t.__dirty=0}else{var f=t;isNaN(f.z)&&(or(),f.z=0),isNaN(f.z2)&&(or(),f.z2=0),isNaN(f.zlevel)&&(or(),f.zlevel=0),this._displayList[this._displayListLen++]=f}var d=t.getDecalElement&&t.getDecalElement();d&&this._updateAndAddDisplayable(d,a,r);var y=t.getTextGuideLine();y&&this._updateAndAddDisplayable(y,a,r);var v=t.getTextContent();v&&this._updateAndAddDisplayable(v,a,r)}},hr.prototype.addRoot=function(t){t.__zr&&t.__zr.storage===this||this._roots.push(t)},hr.prototype.delRoot=function(t){if(t instanceof Array)for(var e=0,r=t.length;e<r;e++)this.delRoot(t[e]);else{var i=P(this._roots,t);0<=i&&this._roots.splice(i,1)}},hr.prototype.delAllRoots=function(){this._roots=[],this._displayList=[],this._displayListLen=0},hr.prototype.getRoots=function(){return this._roots},hr.prototype.dispose=function(){this._displayList=null,this._roots=null},hr);function hr(){this._roots=[],this._displayList=[],this._displayListLen=0,this.displayableSortFunc=ar}var lr=c.hasGlobalWindow&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(t){return setTimeout(t,16)},ur={linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return.5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(2-Math.pow(2,-10*(t-1)))},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,r=.1;return 0===t?0:1===t?1:(e=!r||r<1?(r=1,.1):.4*Math.asin(1/r)/(2*Math.PI),-(r*Math.pow(2,10*--t)*Math.sin((t-e)*(2*Math.PI)/.4)))},elasticOut:function(t){var e,r=.1;return 0===t?0:1===t?1:(e=!r||r<1?(r=1,.1):.4*Math.asin(1/r)/(2*Math.PI),r*Math.pow(2,-10*t)*Math.sin((t-e)*(2*Math.PI)/.4)+1)},elasticInOut:function(t){var e,r=.1;return 0===t?0:1===t?1:(e=!r||r<1?(r=1,.1):.4*Math.asin(1/r)/(2*Math.PI),(t*=2)<1?r*Math.pow(2,10*--t)*Math.sin((t-e)*(2*Math.PI)/.4)*-.5:r*Math.pow(2,-10*--t)*Math.sin((t-e)*(2*Math.PI)/.4)*.5+1)},backIn:function(t){return t*t*(2.70158*t-1.70158)},backOut:function(t){return--t*t*(2.70158*t+1.70158)+1},backInOut:function(t){var e=2.5949095;return(t*=2)<1?t*t*((1+e)*t-e)*.5:.5*((t-=2)*t*((1+e)*t+e)+2)},bounceIn:function(t){return 1-ur.bounceOut(1-t)},bounceOut:function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(t){return t<.5?.5*ur.bounceIn(2*t):.5*ur.bounceOut(2*t-1)+.5}},cr=Math.pow,pr=Math.sqrt,fr=1e-8,dr=1e-4,yr=pr(3),vr=1/3,gr=gt(),_r=gt(),mr=gt();function xr(t){return-fr<t&&t<fr}function wr(t){return fr<t||t<-fr}function br(t,e,r,i,n){var o=1-n;return o*o*(o*t+3*n*e)+n*n*(n*i+3*o*r)}function Sr(t,e,r,i,n){var o=1-n;return 3*(((e-t)*o+2*(r-e)*n)*o+(i-r)*n*n)}function Tr(t,e,r,i,n,o){var a,s,h,l,u,c,p,f,d,y,v,g,_=i+3*(e-r)-t,m=3*(r-2*e+t),x=3*(e-t),w=t-n,b=m*m-3*_*x,S=m*x-9*_*w,T=x*x-3*m*w,k=0;return xr(b)&&xr(S)?xr(m)?o[0]=0:0<=(y=-x/m)&&y<=1&&(o[k++]=y):xr(a=S*S-4*b*T)?(v=-(s=S/b)/2,0<=(y=-m/_+s)&&y<=1&&(o[k++]=y),0<=v&&v<=1&&(o[k++]=v)):0<a?(u=b*m+1.5*_*(-S-(h=pr(a))),0<=(y=(-m-((l=(l=b*m+1.5*_*(-S+h))<0?-cr(-l,vr):cr(l,vr))+(u=u<0?-cr(-u,vr):cr(u,vr))))/(3*_))&&y<=1&&(o[k++]=y)):(c=(2*b*m-3*_*S)/(2*pr(b*b*b)),p=Math.acos(c)/3,y=(-m-2*(f=pr(b))*(d=Math.cos(p)))/(3*_),v=(-m+f*(d+yr*Math.sin(p)))/(3*_),g=(-m+f*(d-yr*Math.sin(p)))/(3*_),0<=y&&y<=1&&(o[k++]=y),0<=v&&v<=1&&(o[k++]=v),0<=g&&g<=1&&(o[k++]=g)),k}function kr(t,e,r,i,n){var o,a,s,h,l=6*r-12*e+6*t,u=9*e+3*i-3*t-9*r,c=3*e-3*t,p=0;return xr(u)?wr(l)&&0<=(s=-c/l)&&s<=1&&(n[p++]=s):xr(o=l*l-4*u*c)?n[0]=-l/(2*u):0<o&&(h=(-l-(a=pr(o)))/(2*u),0<=(s=(-l+a)/(2*u))&&s<=1&&(n[p++]=s),0<=h&&h<=1&&(n[p++]=h)),p}function Cr(t,e,r,i,n,o){var a=(e-t)*n+t,s=(r-e)*n+e,h=(i-r)*n+r,l=(s-a)*n+a,u=(h-s)*n+s,c=(u-l)*n+l;o[0]=t,o[1]=a,o[2]=l,o[3]=c,o[4]=c,o[5]=u,o[6]=h,o[7]=i}function Pr(t,e,r,i){var n=1-i;return n*(n*t+2*i*e)+i*i*r}function Mr(t,e,r,i){return 2*((1-i)*(e-t)+i*(r-e))}function Ar(t,e,r){var i=t+r-2*e;return 0==i?.5:(t-e)/i}function Dr(t,e,r,i,n){var o=(e-t)*i+t,a=(r-e)*i+e,s=(a-o)*i+o;n[0]=t,n[1]=o,n[2]=s,n[3]=s,n[4]=a,n[5]=r}var Lr=/cubic-bezier\(([0-9,\.e ]+)\)/;function zr(t){var e=t&&Lr.exec(t);if(e){var r=e[1].split(","),i=+tt(r[0]),n=+tt(r[1]),o=+tt(r[2]),a=+tt(r[3]);if(isNaN(i+n+o+a))return;var s=[];return function(t){return t<=0?0:1<=t?1:Tr(0,i,o,1,t,s)&&br(0,n,a,1,s[0])}}}var Ir=(Rr.prototype.step=function(t,e){if(this._inited||(this._startTime=t+this._delay,this._inited=!0),!this._paused){var r=this._life,i=t-this._startTime-this._pausedTime,n=i/r;n<0&&(n=0),n=Math.min(n,1);var o=this.easingFunc,a=o?o(n):n;if(this.onframe(a),1===n){if(!this.loop)return!0;var s=i%r;this._startTime=t-s,this._pausedTime=0,this.onrestart()}return!1}this._pausedTime+=e},Rr.prototype.pause=function(){this._paused=!0},Rr.prototype.resume=function(){this._paused=!1},Rr.prototype.setEasing=function(t){this.easing=t,this.easingFunc=B(t)?t:ur[t]||zr(t)},Rr);function Rr(t){this._inited=!1,this._startTime=0,this._pausedTime=0,this._paused=!1,this._life=t.life||1e3,this._delay=t.delay||0,this.loop=t.loop||!1,this.onframe=t.onframe||ct,this.ondestroy=t.ondestroy||ct,this.onrestart=t.onrestart||ct,t.easing&&this.setEasing(t.easing)}var Or=function(t){this.value=t},Fr=(Br.prototype.insert=function(t){var e=new Or(t);return this.insertEntry(e),e},Br.prototype.insertEntry=function(t){this.head?((this.tail.next=t).prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},Br.prototype.remove=function(t){var e=t.prev,r=t.next;e?e.next=r:this.head=r,r?r.prev=e:this.tail=e,t.next=t.prev=null,this._len--},Br.prototype.len=function(){return this._len},Br.prototype.clear=function(){this.head=this.tail=null,this._len=0},Br);function Br(){this._len=0}var Nr=(Hr.prototype.put=function(t,e){var r,i,n,o=this._list,a=this._map,s=null;return null==a[t]&&(r=o.len(),i=this._lastRemovedEntry,r>=this._maxSize&&0<r&&(n=o.head,o.remove(n),delete a[n.key],s=n.value,this._lastRemovedEntry=n),i?i.value=e:i=new Or(e),i.key=t,o.insertEntry(i),a[t]=i),s},Hr.prototype.get=function(t){var e=this._map[t],r=this._list;if(null!=e)return e!==r.tail&&(r.remove(e),r.insertEntry(e)),e.value},Hr.prototype.clear=function(){this._list.clear(),this._map={}},Hr.prototype.len=function(){return this._list.len()},Hr);function Hr(t){this._list=new Fr,this._maxSize=10,this._map={},this._maxSize=t}var Wr={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function Er(t){return(t=Math.round(t))<0?0:255<t?255:t}function Xr(t){return t<0?0:1<t?1:t}function Yr(t){var e=t;return e.length&&"%"===e.charAt(e.length-1)?Er(parseFloat(e)/100*255):Er(parseInt(e,10))}function qr(t){var e=t;return e.length&&"%"===e.charAt(e.length-1)?Xr(parseFloat(e)/100):Xr(parseFloat(e))}function jr(t,e,r){return r<0?r+=1:1<r&&--r,6*r<1?t+(e-t)*r*6:2*r<1?e:3*r<2?t+(e-t)*(2/3-r)*6:t}function Vr(t,e,r){return t+(e-t)*r}function Ur(t,e,r,i,n){return t[0]=e,t[1]=r,t[2]=i,t[3]=n,t}function Gr(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}var Zr=new Nr(20),Kr=null;function Qr(t,e){Kr&&Gr(Kr,e),Kr=Zr.put(t,Kr||e.slice())}function $r(t,e){if(t){e=e||[];var r=Zr.get(t);if(r)return Gr(e,r);var i=(t+="").replace(/ /g,"").toLowerCase();if(i in Wr)return Gr(e,Wr[i]),Qr(t,e),e;var n,o=i.length;if("#"===i.charAt(0))return 4===o||5===o?0<=(n=parseInt(i.slice(1,4),16))&&n<=4095?(Ur(e,(3840&n)>>4|(3840&n)>>8,240&n|(240&n)>>4,15&n|(15&n)<<4,5===o?parseInt(i.slice(4),16)/15:1),Qr(t,e),e):void Ur(e,0,0,0,1):7===o||9===o?0<=(n=parseInt(i.slice(1,7),16))&&n<=16777215?(Ur(e,(16711680&n)>>16,(65280&n)>>8,255&n,9===o?parseInt(i.slice(7),16)/255:1),Qr(t,e),e):void Ur(e,0,0,0,1):void 0;var a=i.indexOf("("),s=i.indexOf(")");if(-1!==a&&s+1===o){var h=i.substr(0,a),l=i.substr(a+1,s-(a+1)).split(","),u=1;switch(h){case"rgba":if(4!==l.length)return 3===l.length?Ur(e,+l[0],+l[1],+l[2],1):Ur(e,0,0,0,1);u=qr(l.pop());case"rgb":return 3<=l.length?(Ur(e,Yr(l[0]),Yr(l[1]),Yr(l[2]),3===l.length?u:qr(l[3])),Qr(t,e),e):void Ur(e,0,0,0,1);case"hsla":return 4!==l.length?void Ur(e,0,0,0,1):(l[3]=qr(l[3]),Jr(l,e),Qr(t,e),e);case"hsl":return 3!==l.length?void Ur(e,0,0,0,1):(Jr(l,e),Qr(t,e),e);default:return}}Ur(e,0,0,0,1)}}function Jr(t,e){var r=(parseFloat(t[0])%360+360)%360/360,i=qr(t[1]),n=qr(t[2]),o=n<=.5?n*(i+1):n+i-n*i,a=2*n-o;return Ur(e=e||[],Er(255*jr(a,o,r+1/3)),Er(255*jr(a,o,r)),Er(255*jr(a,o,r-1/3)),1),4===t.length&&(e[3]=t[3]),e}function ti(t,e){var r=$r(t);if(r){for(var i=0;i<3;i++)r[i]=e<0?r[i]*(1-e)|0:(255-r[i])*e+r[i]|0,255<r[i]?r[i]=255:r[i]<0&&(r[i]=0);return oi(r,4===r.length?"rgba":"rgb")}}function ei(t,e,r){if(e&&e.length&&0<=t&&t<=1){r=r||[];var i=t*(e.length-1),n=Math.floor(i),o=Math.ceil(i),a=e[n],s=e[o],h=i-n;return r[0]=Er(Vr(a[0],s[0],h)),r[1]=Er(Vr(a[1],s[1],h)),r[2]=Er(Vr(a[2],s[2],h)),r[3]=Xr(Vr(a[3],s[3],h)),r}}var ri=ei;function ii(t,e,r){if(e&&e.length&&0<=t&&t<=1){var i=t*(e.length-1),n=Math.floor(i),o=Math.ceil(i),a=$r(e[n]),s=$r(e[o]),h=i-n,l=oi([Er(Vr(a[0],s[0],h)),Er(Vr(a[1],s[1],h)),Er(Vr(a[2],s[2],h)),Xr(Vr(a[3],s[3],h))],"rgba");return r?{color:l,leftIndex:n,rightIndex:o,value:i}:l}}var ni=ii;function oi(t,e){if(t&&t.length){var r=t[0]+","+t[1]+","+t[2];return"rgba"!==e&&"hsva"!==e&&"hsla"!==e||(r+=","+t[3]),e+"("+r+")"}}function ai(t,e){var r=$r(t);return r?(.299*r[0]+.587*r[1]+.114*r[2])*r[3]/255+(1-r[3])*e:0}var si=new Nr(100);function hi(t){if(N(t)){var e=si.get(t);return e||(e=ti(t,-.1),si.put(t,e)),e}if(j(t)){var r=I({},t);return r.colorStops=D(t.colorStops,function(t){return{offset:t.offset,color:ti(t.color,-.1)}}),r}return t}var li=Object.freeze({__proto__:null,parseCssInt:Yr,parseCssFloat:qr,parse:$r,lift:ti,toHex:function(t){var e=$r(t);if(e)return((1<<24)+(e[0]<<16)+(e[1]<<8)+ +e[2]).toString(16).slice(1)},fastLerp:ei,fastMapToColor:ri,lerp:ii,mapToColor:ni,modifyHSL:function(t,e,r,i){var n,o=$r(t);if(t)return o=function(t){if(t){var e,r,i,n,o,a=t[0]/255,s=t[1]/255,h=t[2]/255,l=Math.min(a,s,h),u=Math.max(a,s,h),c=u-l,p=(u+l)/2;0==c?r=e=0:(r=p<.5?c/(u+l):c/(2-u-l),i=((u-a)/6+c/2)/c,n=((u-s)/6+c/2)/c,o=((u-h)/6+c/2)/c,a===u?e=o-n:s===u?e=1/3+i-o:h===u&&(e=2/3+n-i),e<0&&(e+=1),1<e&&--e);var f=[360*e,r,p];return null!=t[3]&&f.push(t[3]),f}}(o),null!=e&&(o[0]=(n=B(e)?e(o[0]):e,(n=Math.round(n))<0?0:360<n?360:n)),null!=r&&(o[1]=qr(B(r)?r(o[1]):r)),null!=i&&(o[2]=qr(B(i)?i(o[2]):i)),oi(Jr(o),"rgba")},modifyAlpha:function(t,e){var r=$r(t);if(r&&null!=e)return r[3]=Xr(e),oi(r,"rgba")},stringify:oi,lum:ai,random:function(){return oi([Math.round(255*Math.random()),Math.round(255*Math.random()),Math.round(255*Math.random())],"rgb")},liftColor:hi}),ui=Math.round;function ci(t){var e,r;return t&&"transparent"!==t?"string"==typeof t&&-1<t.indexOf("rgba")&&((r=$r(t))&&(t="rgb("+r[0]+","+r[1]+","+r[2]+")",e=r[3])):t="none",{color:t,opacity:null==e?1:e}}var pi=1e-4;function fi(t){return t<pi&&-pi<t}function di(t){return ui(1e3*t)/1e3}function yi(t){return ui(1e4*t)/1e4}var vi={left:"start",right:"end",center:"middle",middle:"middle"};function gi(t){return t&&!!t.image}function _i(t){return gi(t)||(e=t)&&e.svgElement;var e}function mi(t){return"linear"===t.type}function xi(t){return"radial"===t.type}function wi(t){return t&&("linear"===t.type||"radial"===t.type)}function bi(t){return"url(#"+t+")"}function Si(t){var e=t.getGlobalScale(),r=Math.max(e[0],e[1]);return Math.max(Math.ceil(Math.log(r)/Math.log(10)),1)}function Ti(t){var e=t.x||0,r=t.y||0,i=(t.rotation||0)*pt,n=Z(t.scaleX,1),o=Z(t.scaleY,1),a=t.skewX||0,s=t.skewY||0,h=[];return(e||r)&&h.push("translate("+e+"px,"+r+"px)"),i&&h.push("rotate("+i+")"),1===n&&1===o||h.push("scale("+n+","+o+")"),(a||s)&&h.push("skew("+ui(a*pt)+"deg, "+ui(s*pt)+"deg)"),h.join(" ")}var ki=c.hasGlobalWindow&&B(window.btoa)?function(t){return window.btoa(unescape(encodeURIComponent(t)))}:"undefined"!=typeof Buffer?function(t){return Buffer.from(t).toString("base64")}:function(t){return null},Ci=Array.prototype.slice;function Pi(t,e,r){return(e-t)*r+t}function Mi(t,e,r,i){for(var n=e.length,o=0;o<n;o++)t[o]=Pi(e[o],r[o],i);return t}function Ai(t,e,r,i){for(var n=e.length,o=0;o<n;o++)t[o]=e[o]+r[o]*i;return t}function Di(t,e,r,i){for(var n=e.length,o=n&&e[0].length,a=0;a<n;a++){t[a]||(t[a]=[]);for(var s=0;s<o;s++)t[a][s]=e[a][s]+r[a][s]*i}return t}function Li(t){if(A(t)){var e=t.length;if(A(t[0])){for(var r=[],i=0;i<e;i++)r.push(Ci.call(t[i]));return r}return Ci.call(t)}return t}function zi(t){return t[0]=Math.floor(t[0])||0,t[1]=Math.floor(t[1])||0,t[2]=Math.floor(t[2])||0,t[3]=null==t[3]?1:t[3],"rgba("+t.join(",")+")"}function Ii(t){return 4===t||5===t}function Ri(t){return 1===t||2===t}var Oi=[0,0,0,0],Fi=(Bi.prototype.isFinished=function(){return this._finished},Bi.prototype.setFinished=function(){this._finished=!0,this._additiveTrack&&this._additiveTrack.setFinished()},Bi.prototype.needsAnimate=function(){return 1<=this.keyframes.length},Bi.prototype.getAdditiveTrack=function(){return this._additiveTrack},Bi.prototype.addKeyframe=function(t,e,r){this._needsSort=!0;var i,n,o,a,s=this.keyframes,h=s.length,l=!1,u=6,c=e;A(e)?(1==(u=i=A((a=e)&&a[0])?2:1)&&!H(e[0])||2==i&&!H(e[0][0]))&&(l=!0):H(e)&&!U(e)?u=0:N(e)?isNaN(+e)?(n=$r(e))&&(c=n,u=3):u=0:j(e)&&((o=I({},c)).colorStops=D(e.colorStops,function(t){return{offset:t.offset,color:$r(t.color)}}),mi(e)?u=4:xi(e)&&(u=5),c=o),0===h?this.valType=u:u===this.valType&&6!==u||(l=!0),this.discrete=this.discrete||l;var p={time:t,value:c,rawValue:e,percent:0};return r&&(p.easing=r,p.easingFunc=B(r)?r:ur[r]||zr(r)),s.push(p),p},Bi.prototype.prepare=function(t,e){var r=this.keyframes;this._needsSort&&r.sort(function(t,e){return t.time-e.time});for(var i=this.valType,n=r.length,o=r[n-1],a=this.discrete,s=Ri(i),h=Ii(i),l=0;l<n;l++){var u=r[l],c=u.value,p=o.value;u.percent=u.time/t,a||(s&&l!==n-1?function(t,e,r){var i=t,n=e;if(i.push&&n.push){var o=i.length,a=n.length;if(o!==a)if(a<o)i.length=a;else for(var s=o;s<a;s++)i.push(1===r?n[s]:Ci.call(n[s]));for(var h=i[0]&&i[0].length,s=0;s<i.length;s++)if(1===r)isNaN(i[s])&&(i[s]=n[s]);else for(var l=0;l<h;l++)isNaN(i[s][l])&&(i[s][l]=n[s][l])}}(c,p,i):h&&function(t,e){for(var r=t.length,i=e.length,n=i<r?e:t,o=Math.min(r,i),a=n[o-1]||{color:[0,0,0,0],offset:0},s=o;s<Math.max(r,i);s++)n.push({offset:a.offset,color:a.color.slice()})}(c.colorStops,p.colorStops))}if(!a&&5!==i&&e&&this.needsAnimate()&&e.needsAnimate()&&i===e.valType&&!e._finished){this._additiveTrack=e;for(var f=r[0].value,l=0;l<n;l++)0===i?r[l].additiveValue=r[l].value-f:3===i?r[l].additiveValue=Ai([],r[l].value,f,-1):Ri(i)&&(r[l].additiveValue=(1===i?Ai:Di)([],r[l].value,f,-1))}},Bi.prototype.step=function(t,e){if(!this._finished){this._additiveTrack&&this._additiveTrack._finished&&(this._additiveTrack=null);var r,i,n,o,a,s,h,l,u,c=null!=this._additiveTrack,p=c?"additiveValue":"value",f=this.valType,d=this.keyframes,y=d.length,v=this.propName,g=3===f,_=this._lastFr,m=Math.min;if(1===y)r=i=d[0];else{if(e<0)x=0;else if(e<this._lastFrP){for(var x=m(_+1,y-1);0<=x&&!(d[x].percent<=e);x--);x=m(x,y-2)}else{for(x=_;x<y&&!(d[x].percent>e);x++);x=m(x-1,y-2)}i=d[x+1],r=d[x]}r&&i&&(this._lastFr=x,this._lastFrP=e,n=i.percent-r.percent,o=0==n?1:m((e-r.percent)/n,1),i.easingFunc&&(o=i.easingFunc(o)),a=c?this._additiveValue:g?Oi:t[v],!Ri(f)&&!g||a||(a=this._additiveValue=[]),this.discrete?t[v]=o<1?r.rawValue:i.rawValue:Ri(f)?(1===f?Mi:function(t,e,r,i){for(var n=e.length,o=n&&e[0].length,a=0;a<n;a++){t[a]||(t[a]=[]);for(var s=0;s<o;s++)t[a][s]=Pi(e[a][s],r[a][s],i)}return t})(a,r[p],i[p],o):Ii(f)?(s=r[p],h=i[p],l=4===f,t[v]={type:l?"linear":"radial",x:Pi(s.x,h.x,o),y:Pi(s.y,h.y,o),colorStops:D(s.colorStops,function(t,e){var r=h.colorStops[e];return{offset:Pi(t.offset,r.offset,o),color:zi(Mi([],t.color,r.color,o))}}),global:h.global},l?(t[v].x2=Pi(s.x2,h.x2,o),t[v].y2=Pi(s.y2,h.y2,o)):t[v].r=Pi(s.r,h.r,o)):g?(Mi(a,r[p],i[p],o),c||(t[v]=zi(a))):(u=Pi(r[p],i[p],o),c?this._additiveValue=u:t[v]=u),c&&this._addToTarget(t))}},Bi.prototype._addToTarget=function(t){var e=this.valType,r=this.propName,i=this._additiveValue;0===e?t[r]=t[r]+i:3===e?($r(t[r],Oi),Ai(Oi,Oi,i,1),t[r]=zi(Oi)):1===e?Ai(t[r],t[r],i,1):2===e&&Di(t[r],t[r],i,1)},Bi);function Bi(t){this.keyframes=[],this.discrete=!1,this._invalid=!1,this._needsSort=!1,this._lastFr=0,this._lastFrP=0,this.propName=t}var Ni=(Hi.prototype.getMaxTime=function(){return this._maxTime},Hi.prototype.getDelay=function(){return this._delay},Hi.prototype.getLoop=function(){return this._loop},Hi.prototype.getTarget=function(){return this._target},Hi.prototype.changeTarget=function(t){this._target=t},Hi.prototype.when=function(t,e,r){return this.whenWithKeys(t,e,F(e),r)},Hi.prototype.whenWithKeys=function(t,e,r,i){for(var n=this._tracks,o=0;o<r.length;o++){var a=r[o],s=n[a];if(!s){s=n[a]=new Fi(a);var h,l,u=void 0,c=this._getAdditiveTrack(a);if(c?(u=(l=(h=c.keyframes)[h.length-1])&&l.value,3===c.valType&&u&&(u=zi(u))):u=this._target[a],null==u)continue;0<t&&s.addKeyframe(0,Li(u),i),this._trackKeys.push(a)}s.addKeyframe(t,Li(e[a]),i)}return this._maxTime=Math.max(this._maxTime,t),this},Hi.prototype.pause=function(){this._clip.pause(),this._paused=!0},Hi.prototype.resume=function(){this._clip.resume(),this._paused=!1},Hi.prototype.isPaused=function(){return!!this._paused},Hi.prototype.duration=function(t){return this._maxTime=t,this._force=!0,this},Hi.prototype._doneCallback=function(){this._setTracksFinished(),this._clip=null;var t=this._doneCbs;if(t)for(var e=t.length,r=0;r<e;r++)t[r].call(this)},Hi.prototype._abortedCallback=function(){this._setTracksFinished();var t=this.animation,e=this._abortedCbs;if(t&&t.removeClip(this._clip),this._clip=null,e)for(var r=0;r<e.length;r++)e[r].call(this)},Hi.prototype._setTracksFinished=function(){for(var t=this._tracks,e=this._trackKeys,r=0;r<e.length;r++)t[e[r]].setFinished()},Hi.prototype._getAdditiveTrack=function(t){var e,r=this._additiveAnimators;if(r)for(var i=0;i<r.length;i++){var n=r[i].getTrack(t);n&&(e=n)}return e},Hi.prototype.start=function(t){if(!(0<this._started)){this._started=1;for(var e,o=this,a=[],r=this._maxTime||0,i=0;i<this._trackKeys.length;i++){var n,s=this._trackKeys[i],h=this._tracks[s],l=this._getAdditiveTrack(s),u=h.keyframes,c=u.length;h.prepare(r,l),h.needsAnimate()&&(!this._allowDiscrete&&h.discrete?((n=u[c-1])&&(o._target[h.propName]=n.rawValue),h.setFinished()):a.push(h))}return a.length||this._force?(e=new Ir({life:r,loop:this._loop,delay:this._delay||0,onframe:function(t){o._started=2;var e=o._additiveAnimators;if(e){for(var r=!1,i=0;i<e.length;i++)if(e[i]._clip){r=!0;break}r||(o._additiveAnimators=null)}for(i=0;i<a.length;i++)a[i].step(o._target,t);var n=o._onframeCbs;if(n)for(i=0;i<n.length;i++)n[i](o._target,t)},ondestroy:function(){o._doneCallback()}}),this._clip=e,this.animation&&this.animation.addClip(e),t&&e.setEasing(t)):this._doneCallback(),this}},Hi.prototype.stop=function(t){var e;this._clip&&(e=this._clip,t&&e.onframe(1),this._abortedCallback())},Hi.prototype.delay=function(t){return this._delay=t,this},Hi.prototype.during=function(t){return t&&(this._onframeCbs||(this._onframeCbs=[]),this._onframeCbs.push(t)),this},Hi.prototype.done=function(t){return t&&(this._doneCbs||(this._doneCbs=[]),this._doneCbs.push(t)),this},Hi.prototype.aborted=function(t){return t&&(this._abortedCbs||(this._abortedCbs=[]),this._abortedCbs.push(t)),this},Hi.prototype.getClip=function(){return this._clip},Hi.prototype.getTrack=function(t){return this._tracks[t]},Hi.prototype.getTracks=function(){var e=this;return D(this._trackKeys,function(t){return e._tracks[t]})},Hi.prototype.stopTracks=function(t,e){if(!t.length||!this._clip)return!0;for(var r=this._tracks,i=this._trackKeys,n=0;n<t.length;n++){var o=r[t[n]];o&&!o.isFinished()&&(e?o.step(this._target,1):1===this._started&&o.step(this._target,0),o.setFinished())}for(var a=!0,n=0;n<i.length;n++)if(!r[i[n]].isFinished()){a=!1;break}return a&&this._abortedCallback(),a},Hi.prototype.saveTo=function(t,e,r){if(t){e=e||this._trackKeys;for(var i=0;i<e.length;i++){var n,o,a=e[i],s=this._tracks[a];s&&!s.isFinished()&&(o=(n=s.keyframes)[r?0:n.length-1])&&(t[a]=Li(o.rawValue))}}},Hi.prototype.__changeFinalValue=function(t,e){e=e||F(t);for(var r=0;r<e.length;r++){var i,n,o=e[r],a=this._tracks[o];a&&1<(i=a.keyframes).length&&(n=i.pop(),a.addKeyframe(n.time,t[o]),a.prepare(this._maxTime,a.getAdditiveTrack()))}},Hi);function Hi(t,e,r,i){this._tracks={},this._trackKeys=[],this._maxTime=0,this._started=0,this._clip=null,this._target=t,(this._loop=e)&&i?b("Can' use additive animation on looped animation."):(this._additiveAnimators=i,this._allowDiscrete=r)}function Wi(){return(new Date).getTime()}var Ei,Xi=(vt(Yi,Ei=Bt),Yi.prototype.addClip=function(t){t.animation&&this.removeClip(t),this._head?((this._tail.next=t).prev=this._tail,t.next=null,this._tail=t):this._head=this._tail=t,t.animation=this},Yi.prototype.addAnimator=function(t){t.animation=this;var e=t.getClip();e&&this.addClip(e)},Yi.prototype.removeClip=function(t){var e,r;t.animation&&(e=t.prev,r=t.next,e?e.next=r:this._head=r,r?r.prev=e:this._tail=e,t.next=t.prev=t.animation=null)},Yi.prototype.removeAnimator=function(t){var e=t.getClip();e&&this.removeClip(e),t.animation=null},Yi.prototype.update=function(t){for(var e=Wi()-this._pausedTime,r=e-this._time,i=this._head;i;)var n=i.next,i=(i.step(e,r)&&(i.ondestroy(),this.removeClip(i)),n);this._time=e,t||(this.trigger("frame",r),this.stage.update&&this.stage.update())},Yi.prototype._startLoop=function(){var e=this;this._running=!0,lr(function t(){e._running&&(lr(t),e._paused||e.update())})},Yi.prototype.start=function(){this._running||(this._time=Wi(),this._pausedTime=0,this._startLoop())},Yi.prototype.stop=function(){this._running=!1},Yi.prototype.pause=function(){this._paused||(this._pauseStart=Wi(),this._paused=!0)},Yi.prototype.resume=function(){this._paused&&(this._pausedTime+=Wi()-this._pauseStart,this._paused=!1)},Yi.prototype.clear=function(){for(var t=this._head;t;){var e=t.next;t.prev=t.next=t.animation=null,t=e}this._head=this._tail=null},Yi.prototype.isFinished=function(){return null==this._head},Yi.prototype.animate=function(t,e){e=e||{},this.start();var r=new Ni(t,e.loop);return this.addAnimator(r),r},Yi);function Yi(t){var e=Ei.call(this)||this;return e._running=!1,e._time=0,e._pausedTime=0,e._pauseStart=0,e._paused=!1,t=t||{},e.stage=t.stage||{},e}var qi,ji,Vi=c.domSupported,Ui=(ji={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},{mouse:qi=["click","dblclick","mousewheel","wheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],touch:["touchstart","touchend","touchmove"],pointer:D(qi,function(t){var e=t.replace("mouse","pointer");return ji.hasOwnProperty(e)?e:t})}),Gi=["mousemove","mouseup"],Zi=["pointermove","pointerup"],Ki=!1;function Qi(t){var e=t.pointerType;return"pen"===e||"touch"===e}function $i(t){t&&(t.zrByTouch=!0)}function Ji(t,e){for(var r=e,i=!1;r&&9!==r.nodeType&&!(i=r.domBelongToZr||r!==e&&r===t.painterRoot);)r=r.parentNode;return i}var tn=function(t,e){this.stopPropagation=ct,this.stopImmediatePropagation=ct,this.preventDefault=ct,this.type=e.type,this.target=this.currentTarget=t.dom,this.pointerType=e.pointerType,this.clientX=e.clientX,this.clientY=e.clientY},en={mousedown:function(t){t=Jt(this.dom,t),this.__mayPointerCapture=[t.zrX,t.zrY],this.trigger("mousedown",t)},mousemove:function(t){t=Jt(this.dom,t);var e=this.__mayPointerCapture;!e||t.zrX===e[0]&&t.zrY===e[1]||this.__togglePointerCapture(!0),this.trigger("mousemove",t)},mouseup:function(t){t=Jt(this.dom,t),this.__togglePointerCapture(!1),this.trigger("mouseup",t)},mouseout:function(t){Ji(this,(t=Jt(this.dom,t)).toElement||t.relatedTarget)||(this.__pointerCapturing&&(t.zrEventControl="no_globalout"),this.trigger("mouseout",t))},wheel:function(t){Ki=!0,t=Jt(this.dom,t),this.trigger("mousewheel",t)},mousewheel:function(t){Ki||(t=Jt(this.dom,t),this.trigger("mousewheel",t))},touchstart:function(t){$i(t=Jt(this.dom,t)),this.__lastTouchMoment=new Date,this.handler.processGesture(t,"start"),en.mousemove.call(this,t),en.mousedown.call(this,t)},touchmove:function(t){$i(t=Jt(this.dom,t)),this.handler.processGesture(t,"change"),en.mousemove.call(this,t)},touchend:function(t){$i(t=Jt(this.dom,t)),this.handler.processGesture(t,"end"),en.mouseup.call(this,t),new Date-this.__lastTouchMoment<300&&en.click.call(this,t)},pointerdown:function(t){en.mousedown.call(this,t)},pointermove:function(t){Qi(t)||en.mousemove.call(this,t)},pointerup:function(t){en.mouseup.call(this,t)},pointerout:function(t){Qi(t)||en.mouseout.call(this,t)}};R(["click","dblclick","contextmenu"],function(e){en[e]=function(t){t=Jt(this.dom,t),this.trigger(e,t)}});var rn={pointermove:function(t){Qi(t)||rn.mousemove.call(this,t)},pointerup:function(t){rn.mouseup.call(this,t)},mousemove:function(t){this.trigger("mousemove",t)},mouseup:function(t){var e=this.__pointerCapturing;this.__togglePointerCapture(!1),this.trigger("mouseup",t),e&&(t.zrEventControl="only_globalout",this.trigger("mouseout",t))}};function nn(i,n){var o=n.domHandlers;c.pointerEventsSupported?R(Ui.pointer,function(e){an(n,e,function(t){o[e].call(i,t)})}):(c.touchEventsSupported&&R(Ui.touch,function(r){an(n,r,function(t){var e;o[r].call(i,t),(e=n).touching=!0,null!=e.touchTimer&&(clearTimeout(e.touchTimer),e.touchTimer=null),e.touchTimer=setTimeout(function(){e.touching=!1,e.touchTimer=null},700)})}),R(Ui.mouse,function(e){an(n,e,function(t){t=$t(t),n.touching||o[e].call(i,t)})}))}function on(n,o){function t(i){an(o,i,function(t){var e,r;t=$t(t),Ji(n,t.target)||(r=t,t=Jt((e=n).dom,new tn(e,r),!0),o.domHandlers[i].call(n,t))},{capture:!0})}c.pointerEventsSupported?R(Zi,t):c.touchEventsSupported||R(Gi,t)}function an(t,e,r,i){var n,o,a,s;t.mounted[e]=r,t.listenerOpts[e]=i,n=t.domTarget,o=e,a=r,s=i,n.addEventListener(o,a,s)}function sn(t){var e,r,i,n,o=t.mounted;for(var a in o)o.hasOwnProperty(a)&&(e=t.domTarget,i=o[r=a],n=t.listenerOpts[a],e.removeEventListener(r,i,n));t.mounted={}}var hn,ln=function(t,e){this.mounted={},this.listenerOpts={},this.touching=!1,this.domTarget=t,this.domHandlers=e},un=(vt(cn,hn=Bt),cn.prototype.dispose=function(){sn(this._localHandlerScope),Vi&&sn(this._globalHandlerScope)},cn.prototype.setCursor=function(t){this.dom.style&&(this.dom.style.cursor=t||"default")},cn.prototype.__togglePointerCapture=function(t){var e;this.__mayPointerCapture=null,Vi&&+this.__pointerCapturing^+t&&(this.__pointerCapturing=t,e=this._globalHandlerScope,t?on(this,e):sn(e))},cn);function cn(t,e){var r=hn.call(this)||this;return r.__pointerCapturing=!1,r.dom=t,r.painterRoot=e,r._localHandlerScope=new ln(t,en),Vi&&(r._globalHandlerScope=new ln(document,rn)),nn(r,r._localHandlerScope),r}var pn=1;c.hasGlobalWindow&&(pn=Math.max(window.devicePixelRatio||window.screen&&window.screen.deviceXDPI/window.screen.logicalXDPI||1,1));var fn=pn,dn="#333",yn="#ccc",vn=ae;function gn(t){return 5e-5<t||t<-5e-5}var _n,mn=[],xn=[],wn=oe(),bn=Math.abs,Sn=(Tn.prototype.getLocalTransform=function(t){return Tn.getLocalTransform(this,t)},Tn.prototype.setPosition=function(t){this.x=t[0],this.y=t[1]},Tn.prototype.setScale=function(t){this.scaleX=t[0],this.scaleY=t[1]},Tn.prototype.setSkew=function(t){this.skewX=t[0],this.skewY=t[1]},Tn.prototype.setOrigin=function(t){this.originX=t[0],this.originY=t[1]},Tn.prototype.needLocalTransform=function(){return gn(this.rotation)||gn(this.x)||gn(this.y)||gn(this.scaleX-1)||gn(this.scaleY-1)||gn(this.skewX)||gn(this.skewY)},Tn.prototype.updateTransform=function(){var t=this.parent&&this.parent.transform,e=this.needLocalTransform(),r=this.transform;e||t?(r=r||oe(),e?this.getLocalTransform(r):vn(r),t&&(e?he(r,t,r):se(r,t)),this.transform=r,this._resolveGlobalScaleRatio(r)):r&&(vn(r),this.invTransform=null)},Tn.prototype._resolveGlobalScaleRatio=function(t){var e,r,i,n,o=this.globalScaleRatio;null!=o&&1!==o&&(this.getGlobalScale(mn),i=((mn[0]-(e=mn[0]<0?-1:1))*o+e)/mn[0]||0,n=((mn[1]-(r=mn[1]<0?-1:1))*o+r)/mn[1]||0,t[0]*=i,t[1]*=i,t[2]*=n,t[3]*=n),this.invTransform=this.invTransform||oe(),pe(this.invTransform,t)},Tn.prototype.getComputedTransform=function(){for(var t=this,e=[];t;)e.push(t),t=t.parent;for(;t=e.pop();)t.updateTransform();return this.transform},Tn.prototype.setLocalTransform=function(t){var e,r,i,n;t&&(n=t[0]*t[0]+t[1]*t[1],i=t[2]*t[2]+t[3]*t[3],e=Math.atan2(t[1],t[0]),r=Math.PI/2+e-Math.atan2(t[3],t[2]),i=Math.sqrt(i)*Math.cos(r),n=Math.sqrt(n),this.skewX=r,this.skewY=0,this.rotation=-e,this.x=+t[4],this.y=+t[5],this.scaleX=n,this.scaleY=i,this.originX=0,this.originY=0)},Tn.prototype.decomposeTransform=function(){var t,e,r,i;this.transform&&(t=this.parent,e=this.transform,t&&t.transform&&(t.invTransform=t.invTransform||oe(),he(xn,t.invTransform,e),e=xn),r=this.originX,i=this.originY,(r||i)&&(wn[4]=r,wn[5]=i,he(xn,e,wn),xn[4]-=r,xn[5]-=i,e=xn),this.setLocalTransform(e))},Tn.prototype.getGlobalScale=function(t){var e=this.transform;return t=t||[],e?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1])):(t[0]=1,t[1]=1),t},Tn.prototype.transformCoordToLocal=function(t,e){var r=[t,e],i=this.invTransform;return i&&Dt(r,r,i),r},Tn.prototype.transformCoordToGlobal=function(t,e){var r=[t,e],i=this.transform;return i&&Dt(r,r,i),r},Tn.prototype.getLineScale=function(){var t=this.transform;return t&&1e-10<bn(t[0]-1)&&1e-10<bn(t[3]-1)?Math.sqrt(bn(t[0]*t[3]-t[2]*t[1])):1},Tn.prototype.copyTransform=function(t){Pn(this,t)},Tn.getLocalTransform=function(t,e){e=e||[];var r,i,n=t.originX||0,o=t.originY||0,a=t.scaleX,s=t.scaleY,h=t.anchorX,l=t.anchorY,u=t.rotation||0,c=t.x,p=t.y,f=t.skewX?Math.tan(t.skewX):0,d=t.skewY?Math.tan(-t.skewY):0;return n||o||h||l?(r=n+h,i=o+l,e[4]=-r*a-f*i*s,e[5]=-i*s-d*r*a):e[4]=e[5]=0,e[0]=a,e[3]=s,e[1]=d*a,e[2]=f*s,u&&ue(e,e,u),e[4]+=n+c,e[5]+=o+p,e},Tn.initDefaultProps=((_n=Tn.prototype).scaleX=_n.scaleY=_n.globalScaleRatio=1,void(_n.x=_n.y=_n.originX=_n.originY=_n.skewX=_n.skewY=_n.rotation=_n.anchorX=_n.anchorY=0)),Tn);function Tn(){}var kn,Cn=["x","y","originX","originY","anchorX","anchorY","rotation","scaleX","scaleY","skewX","skewY"];function Pn(t,e){for(var r=0;r<Cn.length;r++){var i=Cn[r];t[i]=e[i]}}function Mn(t){kn=kn||new Nr(100),t=t||W;var e=kn.get(t);return e||(e={font:t,strWidthCache:new Nr(500),asciiWidthMap:null,asciiWidthMapTried:!1,stWideCharWidth:f.measureText("国",t).width,asciiCharWidth:f.measureText("a",t).width},kn.put(t,e)),e}var An=0,Dn=5;function Ln(t,e){return t.asciiWidthMapTried||(t.asciiWidthMap=function(t){if(!(Dn<=An)){t=t||W;for(var e=[],r=+new Date,i=0;i<=127;i++)e[i]=f.measureText(String.fromCharCode(i),t).width;var n=new Date-r;return 16<n?An=Dn:2<n&&An++,e}}(t.font),t.asciiWidthMapTried=!0),0<=e&&e<=127?null!=t.asciiWidthMap?t.asciiWidthMap[e]:t.asciiCharWidth:t.stWideCharWidth}function zn(t,e){var r=t.strWidthCache,i=r.get(e);return null==i&&(i=f.measureText(e,t.font).width,r.put(e,i)),i}function In(t,e,r,i){return"right"===r?i?t+=e:t-=e:"center"===r&&(i?t+=e/2:t-=e/2),t}function Rn(t,e,r,i){return"middle"===r?i?t+=e/2:t-=e/2:"bottom"===r&&(i?t+=e:t-=e),t}function On(t){return Mn(t).stWideCharWidth}function Fn(t,e){return"string"==typeof t?0<=t.lastIndexOf("%")?parseFloat(t)/100*e:parseFloat(t):t}var Bn,Nn="__zr_normal__",Hn=Cn.concat(["ignore"]),Wn=L(Cn,function(t,e){return t[e]=!0,t},{ignore:!1}),En={},Xn=new Ae(0,0,0,0),Yn=[],qn=(jn.prototype._init=function(t){this.attr(t)},jn.prototype.drift=function(t,e,r){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0}var i=this.transform;(i=i||(this.transform=[1,0,0,1,0,0]))[4]+=t,i[5]+=e,this.decomposeTransform(),this.markRedraw()},jn.prototype.beforeUpdate=function(){},jn.prototype.afterUpdate=function(){},jn.prototype.update=function(){this.updateTransform(),this.__dirty&&this.updateInnerText()},jn.prototype.updateInnerText=function(t){var e,r,i,n,o,a,s,h,l,u,c,p,f,d,y,v,g,_,m,x=this._textContent;!x||x.ignore&&!t||(this.textConfig||(this.textConfig={}),r=(e=this.textConfig).local,o=n=void 0,a=!1,(i=x.innerTransformable).parent=r?this:null,f=!1,i.copyTransform(x),s=null!=e.position,l=void 0,((h=e.autoOverflowArea)||s)&&(l=Xn,e.layoutRect?l.copy(e.layoutRect):l.copy(this.getBoundingRect()),r||l.applyTransform(this.transform)),s&&(this.calculateTextPosition?this.calculateTextPosition(En,e,l):function(t,e,r){var i=e.position||"inside",n=null!=e.distance?e.distance:5,o=r.height,a=r.width,s=o/2,h=r.x,l=r.y,u="left",c="top";if(i instanceof Array)h+=Fn(i[0],r.width),l+=Fn(i[1],r.height),c=u=null;else switch(i){case"left":h-=n,l+=s,u="right",c="middle";break;case"right":h+=n+a,l+=s,c="middle";break;case"top":h+=a/2,l-=n,u="center",c="bottom";break;case"bottom":h+=a/2,l+=o+n,u="center";break;case"inside":h+=a/2,l+=s,u="center",c="middle";break;case"insideLeft":h+=n,l+=s,c="middle";break;case"insideRight":h+=a-n,l+=s,u="right",c="middle";break;case"insideTop":h+=a/2,l+=n,u="center";break;case"insideBottom":h+=a/2,l+=o-n,u="center",c="bottom";break;case"insideTopLeft":h+=n,l+=n;break;case"insideTopRight":h+=a-n,l+=n,u="right";break;case"insideBottomLeft":h+=n,l+=o-n,c="bottom";break;case"insideBottomRight":h+=a-n,l+=o-n,u="right",c="bottom"}(t=t||{}).x=h,t.y=l,t.align=u,t.verticalAlign=c}(En,e,l),i.x=En.x,i.y=En.y,n=En.align,o=En.verticalAlign,(u=e.origin)&&null!=e.rotation&&(p=c=void 0,p="center"===u?(c=.5*l.width,.5*l.height):(c=Fn(u[0],l.width),Fn(u[1],l.height)),f=!0,i.originX=-i.x+c+(r?0:l.x),i.originY=-i.y+p+(r?0:l.y))),null!=e.rotation&&(i.rotation=e.rotation),(d=e.offset)&&(i.x+=d[0],i.y+=d[1],f||(i.originX=-d[0],i.originY=-d[1])),y=this._innerTextDefaultStyle||(this._innerTextDefaultStyle={}),h?(v=y.overflowRect=y.overflowRect||new Ae(0,0,0,0),i.getLocalTransform(Yn),pe(Yn,Yn),Ae.copy(v,l),v.applyTransform(Yn)):y.overflowRect=null,m=_=g=void 0,(null==e.inside?"string"==typeof e.position&&0<=e.position.indexOf("inside"):e.inside)&&this.canBeInsideText()?(g=e.insideFill,_=e.insideStroke,null!=g&&"auto"!==g||(g=this.getInsideTextFill()),null!=_&&"auto"!==_||(_=this.getInsideTextStroke(g),m=!0)):(g=e.outsideFill,_=e.outsideStroke,null!=g&&"auto"!==g||(g=this.getOutsideFill()),null!=_&&"auto"!==_||(_=this.getOutsideStroke(g),m=!0)),(g=g||"#000")===y.fill&&_===y.stroke&&m===y.autoStroke&&n===y.align&&o===y.verticalAlign||(a=!0,y.fill=g,y.stroke=_,y.autoStroke=m,y.align=n,y.verticalAlign=o,x.setDefaultTextStyle(y)),x.__dirty|=rr,a&&x.dirtyStyle(!0))},jn.prototype.canBeInsideText=function(){return!0},jn.prototype.getInsideTextFill=function(){return"#fff"},jn.prototype.getInsideTextStroke=function(t){return"#000"},jn.prototype.getOutsideFill=function(){return this.__zr&&this.__zr.isDarkMode()?yn:dn},jn.prototype.getOutsideStroke=function(t){for(var e=this.__zr&&this.__zr.getBackgroundColor(),r="string"==typeof e&&$r(e),i=(r=r||[255,255,255,1])[3],n=this.__zr.isDarkMode(),o=0;o<3;o++)r[o]=r[o]*i+(n?0:255)*(1-i);return r[3]=1,oi(r,"rgba")},jn.prototype.traverse=function(t,e){},jn.prototype.attrKV=function(t,e){"textConfig"===t?this.setTextConfig(e):"textContent"===t?this.setTextContent(e):"clipPath"===t?this.setClipPath(e):"extra"===t?(this.extra=this.extra||{},I(this.extra,e)):this[t]=e},jn.prototype.hide=function(){this.ignore=!0,this.markRedraw()},jn.prototype.show=function(){this.ignore=!1,this.markRedraw()},jn.prototype.attr=function(t,e){if("string"==typeof t)this.attrKV(t,e);else if(E(t))for(var r=F(t),i=0;i<r.length;i++){var n=r[i];this.attrKV(n,t[n])}return this.markRedraw(),this},jn.prototype.saveCurrentToNormalState=function(t){this._innerSaveToNormal(t);for(var e=this._normalState,r=0;r<this.animators.length;r++){var i,n,o=this.animators[r],a=o.__fromStateTransition;o.getLoop()||a&&a!==Nn||(n=(i=o.targetName)?e[i]:e,o.saveTo(n))}},jn.prototype._innerSaveToNormal=function(t){var e=(e=this._normalState)||(this._normalState={});t.textConfig&&!e.textConfig&&(e.textConfig=this.textConfig),this._savePrimaryToNormal(t,e,Hn)},jn.prototype._savePrimaryToNormal=function(t,e,r){for(var i=0;i<r.length;i++){var n=r[i];null==t[n]||n in e||(e[n]=this[n])}},jn.prototype.hasState=function(){return 0<this.currentStates.length},jn.prototype.getState=function(t){return this.states[t]},jn.prototype.ensureState=function(t){var e=this.states;return e[t]||(e[t]={}),e[t]},jn.prototype.clearStates=function(t){this.useState(Nn,!1,t)},jn.prototype.useState=function(t,e,r,i){var n=t===Nn;if(this.hasState()||!n){var o,a=this.currentStates,s=this.stateTransition;if(!(0<=P(a,t))||!e&&1!==a.length){if(this.stateProxy&&!n&&(o=this.stateProxy(t)),(o=o||this.states&&this.states[t])||n){n||this.saveCurrentToNormalState(o);var h=!!(o&&o.hoverLayer||i);h&&this._toggleHoverLayerFlag(!0),this._applyStateObj(t,o,this._normalState,e,!r&&!this.__inHover&&s&&0<s.duration,s);var l=this._textContent,u=this._textGuide;return l&&l.useState(t,e,r,h),u&&u.useState(t,e,r,h),n?(this.currentStates=[],this._normalState={}):e?this.currentStates.push(t):this.currentStates=[t],this._updateAnimationTargets(),this.markRedraw(),!h&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~rr),o}b("State "+t+" not exists.")}}},jn.prototype.useStates=function(t,e,r){if(t.length){var i=[],n=this.currentStates,o=t.length,a=o===n.length;if(a)for(var s=0;s<o;s++)if(t[s]!==n[s]){a=!1;break}if(a)return;for(s=0;s<o;s++){var h=t[s],l=void 0;this.stateProxy&&(l=this.stateProxy(h,t)),(l=l||this.states[h])&&i.push(l)}var u=i[o-1],c=!!(u&&u.hoverLayer||r);c&&this._toggleHoverLayerFlag(!0);var p=this._mergeStates(i),f=this.stateTransition;this.saveCurrentToNormalState(p),this._applyStateObj(t.join(","),p,this._normalState,!1,!e&&!this.__inHover&&f&&0<f.duration,f);var d=this._textContent,y=this._textGuide;d&&d.useStates(t,e,c),y&&y.useStates(t,e,c),this._updateAnimationTargets(),this.currentStates=t.slice(),this.markRedraw(),!c&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~rr)}else this.clearStates()},jn.prototype.isSilent=function(){for(var t=this;t;){if(t.silent)return!0;var e=t.__hostTarget,t=e?t.ignoreHostSilent?null:e:t.parent}return!1},jn.prototype._updateAnimationTargets=function(){for(var t=0;t<this.animators.length;t++){var e=this.animators[t];e.targetName&&e.changeTarget(this[e.targetName])}},jn.prototype.removeState=function(t){var e,r=P(this.currentStates,t);0<=r&&((e=this.currentStates.slice()).splice(r,1),this.useStates(e))},jn.prototype.replaceState=function(t,e,r){var i=this.currentStates.slice(),n=P(i,t),o=0<=P(i,e);0<=n?o?i.splice(n,1):i[n]=e:r&&!o&&i.push(e),this.useStates(i)},jn.prototype.toggleState=function(t,e){e?this.useState(t,!0):this.removeState(t)},jn.prototype._mergeStates=function(t){for(var e,r={},i=0;i<t.length;i++){var n=t[i];I(r,n),n.textConfig&&I(e=e||{},n.textConfig)}return e&&(r.textConfig=e),r},jn.prototype._applyStateObj=function(t,e,r,i,n,o){var a=!(e&&i);e&&e.textConfig?(this.textConfig=I({},i?this.textConfig:r.textConfig),I(this.textConfig,e.textConfig)):a&&r.textConfig&&(this.textConfig=r.textConfig);for(var s={},h=!1,l=0;l<Hn.length;l++){var u=Hn[l],c=n&&Wn[u];e&&null!=e[u]?c?(h=!0,s[u]=e[u]):this[u]=e[u]:a&&null!=r[u]&&(c?(h=!0,s[u]=r[u]):this[u]=r[u])}if(!n)for(l=0;l<this.animators.length;l++){var p=this.animators[l],f=p.targetName;p.getLoop()||p.__changeFinalValue(f?(e||r)[f]:e||r)}h&&this._transitionState(t,s,o)},jn.prototype._attachComponent=function(t){var e;t.__zr&&!t.__hostTarget||t!==this&&((e=this.__zr)&&t.addSelfToZr(e),t.__zr=e,t.__hostTarget=this)},jn.prototype._detachComponent=function(t){t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__hostTarget=null},jn.prototype.getClipPath=function(){return this._clipPath},jn.prototype.setClipPath=function(t){this._clipPath&&this._clipPath!==t&&this.removeClipPath(),this._attachComponent(t),this._clipPath=t,this.markRedraw()},jn.prototype.removeClipPath=function(){var t=this._clipPath;t&&(this._detachComponent(t),this._clipPath=null,this.markRedraw())},jn.prototype.getTextContent=function(){return this._textContent},jn.prototype.setTextContent=function(t){var e=this._textContent;e!==t&&(e&&e!==t&&this.removeTextContent(),t.innerTransformable=new Sn,this._attachComponent(t),this._textContent=t,this.markRedraw())},jn.prototype.setTextConfig=function(t){this.textConfig||(this.textConfig={}),I(this.textConfig,t),this.markRedraw()},jn.prototype.removeTextConfig=function(){this.textConfig=null,this.markRedraw()},jn.prototype.removeTextContent=function(){var t=this._textContent;t&&(t.innerTransformable=null,this._detachComponent(t),this._textContent=null,this._innerTextDefaultStyle=null,this.markRedraw())},jn.prototype.getTextGuideLine=function(){return this._textGuide},jn.prototype.setTextGuideLine=function(t){this._textGuide&&this._textGuide!==t&&this.removeTextGuideLine(),this._attachComponent(t),this._textGuide=t,this.markRedraw()},jn.prototype.removeTextGuideLine=function(){var t=this._textGuide;t&&(this._detachComponent(t),this._textGuide=null,this.markRedraw())},jn.prototype.markRedraw=function(){this.__dirty|=rr;var t=this.__zr;t&&(this.__inHover?t.refreshHover():t.refresh()),this.__hostTarget&&this.__hostTarget.markRedraw()},jn.prototype.dirty=function(){this.markRedraw()},jn.prototype._toggleHoverLayerFlag=function(t){this.__inHover=t;var e=this._textContent,r=this._textGuide;e&&(e.__inHover=t),r&&(r.__inHover=t)},jn.prototype.addSelfToZr=function(t){if(this.__zr!==t){this.__zr=t;var e=this.animators;if(e)for(var r=0;r<e.length;r++)t.animation.addAnimator(e[r]);this._clipPath&&this._clipPath.addSelfToZr(t),this._textContent&&this._textContent.addSelfToZr(t),this._textGuide&&this._textGuide.addSelfToZr(t)}},jn.prototype.removeSelfFromZr=function(t){if(this.__zr){this.__zr=null;var e=this.animators;if(e)for(var r=0;r<e.length;r++)t.animation.removeAnimator(e[r]);this._clipPath&&this._clipPath.removeSelfFromZr(t),this._textContent&&this._textContent.removeSelfFromZr(t),this._textGuide&&this._textGuide.removeSelfFromZr(t)}},jn.prototype.animate=function(t,e,r){var i=t?this[t]:this,n=new Ni(i,e,r);return t&&(n.targetName=t),this.addAnimator(n,t),n},jn.prototype.addAnimator=function(r,t){var e=this.__zr,i=this;r.during(function(){i.updateDuringAnimation(t)}).done(function(){var t=i.animators,e=P(t,r);0<=e&&t.splice(e,1)}),this.animators.push(r),e&&e.animation.addAnimator(r),e&&e.wakeUp()},jn.prototype.updateDuringAnimation=function(t){this.markRedraw()},jn.prototype.stopAnimation=function(t,e){for(var r=this.animators,i=r.length,n=[],o=0;o<i;o++){var a=r[o];t&&t!==a.scope?n.push(a):a.stop(e)}return this.animators=n,this},jn.prototype.animateTo=function(t,e,r){Un(this,t,e,r)},jn.prototype.animateFrom=function(t,e,r){Un(this,t,e,r,!0)},jn.prototype._transitionState=function(t,e,r,i){for(var n=Un(this,e,r,i),o=0;o<n.length;o++)n[o].__fromStateTransition=t},jn.prototype.getBoundingRect=function(){return null},jn.prototype.getPaintRect=function(){return null},jn.initDefaultProps=((Bn=jn.prototype).type="element",Bn.name="",Bn.ignore=Bn.silent=Bn.ignoreHostSilent=Bn.isGroup=Bn.draggable=Bn.dragging=Bn.ignoreClip=Bn.__inHover=!1,Bn.__dirty=rr,void(Object.defineProperty&&(Vn("position","_legacyPos","x","y"),Vn("scale","_legacyScale","scaleX","scaleY"),Vn("origin","_legacyOrigin","originX","originY")))),jn);function jn(t){this.id=w(),this.animators=[],this.currentStates=[],this.states={},this._init(t)}function Vn(t,e,r,i){function n(e,t){Object.defineProperty(t,0,{get:function(){return e[r]},set:function(t){e[r]=t}}),Object.defineProperty(t,1,{get:function(){return e[i]},set:function(t){e[i]=t}})}Object.defineProperty(Bn,t,{get:function(){var t;return this[e]||(t=this[e]=[],n(this,t)),this[e]},set:function(t){this[r]=t[0],this[i]=t[1],this[e]=t,n(this,t)}})}function Un(t,e,r,i,n){var o=[];!function t(e,r,i,n,o,a,s,h){var l=F(n);var u=o.duration;var c=o.delay;var p=o.additive;var f=o.setToFinal;var d=!E(a);var y=e.animators;var v=[];for(var g=0;g<l.length;g++){var _=l[g],m=n[_];if(null!=m&&null!=i[_]&&(d||a[_]))if(!E(m)||A(m)||j(m))v.push(_);else{if(r){h||(i[_]=m,e.updateDuringAnimation(r));continue}t(e,_,i[_],m,o,a&&a[_],s,h)}else h||(i[_]=m,e.updateDuringAnimation(r),v.push(_))}var x=v.length;if(!p&&x)for(var w=0;w<y.length;w++){var b,S=y[w];S.targetName!==r||S.stopTracks(v)&&(b=P(y,S),y.splice(b,1))}o.force||(v=O(v,function(t){return e=n[t],r=i[t],!(e===r||A(e)&&A(r)&&function(t,e){var r=t.length;if(r!==e.length)return;for(var i=0;i<r;i++)if(t[i]!==e[i])return;return 1}(e,r));var e,r}),x=v.length);if(0<x||o.force&&!s.length){var T=void 0,k=void 0,C=void 0;if(h){k={},f&&(T={});for(var w=0;w<x;w++){var _=v[w];k[_]=i[_],f?T[_]=n[_]:i[_]=n[_]}}else if(f){C={};for(var w=0;w<x;w++){var _=v[w];C[_]=Li(i[_]),Zn(i,n,_)}}var S=new Ni(i,!1,!1,p?O(y,function(t){return t.targetName===r}):null);S.targetName=r,o.scope&&(S.scope=o.scope),f&&T&&S.whenWithKeys(0,T,v),C&&S.whenWithKeys(0,C,v),S.whenWithKeys(null==u?500:u,h?k:n,v).delay(c||0),e.addAnimator(S,r),s.push(S)}}(t,"",t,e,r=r||{},i,o,n);function a(){l=!0,--h<=0&&(l?u&&u():c&&c())}function s(){--h<=0&&(l?u&&u():c&&c())}var h=o.length,l=!1,u=r.done,c=r.aborted;h||u&&u(),0<o.length&&r.during&&o[0].during(function(t,e){r.during(e)});for(var p=0;p<o.length;p++){var f=o[p];f.done(a),f.aborted(s),r.force&&f.duration(r.duration),f.start(r.easing)}return o}function Gn(t,e,r){for(var i=0;i<r;i++)t[i]=e[i]}function Zn(t,e,r){if(A(e[r]))if(A(t[r])||(t[r]=[]),Y(e[r])){var i=e[r].length;t[r].length!==i&&(t[r]=new e[r].constructor(i),Gn(t[r],e[r],i))}else{var n=e[r],o=t[r],a=n.length;if(A(n[0]))for(var s=n[0].length,h=0;h<a;h++)o[h]?Gn(o[h],n[h],s):o[h]=Array.prototype.slice.call(n[h]);else Gn(o,n,a);o.length=n.length}else t[r]=e[r]}M(qn,Bt),M(qn,Sn);var Kn,Qn=(vt($n,Kn=qn),$n.prototype.childrenRef=function(){return this._children},$n.prototype.children=function(){return this._children.slice()},$n.prototype.childAt=function(t){return this._children[t]},$n.prototype.childOfName=function(t){for(var e=this._children,r=0;r<e.length;r++)if(e[r].name===t)return e[r]},$n.prototype.childCount=function(){return this._children.length},$n.prototype.add=function(t){return t&&t!==this&&t.parent!==this&&(this._children.push(t),this._doAdd(t)),this},$n.prototype.addBefore=function(t,e){var r,i;return t&&t!==this&&t.parent!==this&&e&&e.parent===this&&0<=(i=(r=this._children).indexOf(e))&&(r.splice(i,0,t),this._doAdd(t)),this},$n.prototype.replace=function(t,e){var r=P(this._children,t);return 0<=r&&this.replaceAt(e,r),this},$n.prototype.replaceAt=function(t,e){var r,i=this._children,n=i[e];return t&&t!==this&&t.parent!==this&&t!==n&&(i[e]=t,n.parent=null,(r=this.__zr)&&n.removeSelfFromZr(r),this._doAdd(t)),this},$n.prototype._doAdd=function(t){t.parent&&t.parent.remove(t);var e=(t.parent=this).__zr;e&&e!==t.__zr&&t.addSelfToZr(e),e&&e.refresh()},$n.prototype.remove=function(t){var e=this.__zr,r=this._children,i=P(r,t);return i<0||(r.splice(i,1),t.parent=null,e&&t.removeSelfFromZr(e),e&&e.refresh()),this},$n.prototype.removeAll=function(){for(var t=this._children,e=this.__zr,r=0;r<t.length;r++){var i=t[r];e&&i.removeSelfFromZr(e),i.parent=null}return t.length=0,this},$n.prototype.eachChild=function(t,e){for(var r=this._children,i=0;i<r.length;i++){var n=r[i];t.call(e,n,i)}return this},$n.prototype.traverse=function(t,e){for(var r=0;r<this._children.length;r++){var i=this._children[r],n=t.call(e,i);i.isGroup&&!n&&i.traverse(t,e)}return this},$n.prototype.addSelfToZr=function(t){Kn.prototype.addSelfToZr.call(this,t);for(var e=0;e<this._children.length;e++)this._children[e].addSelfToZr(t)},$n.prototype.removeSelfFromZr=function(t){Kn.prototype.removeSelfFromZr.call(this,t);for(var e=0;e<this._children.length;e++)this._children[e].removeSelfFromZr(t)},$n.prototype.getBoundingRect=function(t){for(var e=new Ae(0,0,0,0),r=t||this._children,i=[],n=null,o=0;o<r.length;o++){var a,s,h=r[o];h.ignore||h.invisible||(a=h.getBoundingRect(),(s=h.getLocalTransform(i))?(Ae.applyTransform(e,a,s),(n=n||e.clone()).union(e)):(n=n||a.clone()).union(a))}return n||e},$n);function $n(t){var e=Kn.call(this)||this;return e.isGroup=!0,e._children=[],e.attr(t),e}Qn.prototype.type="group";var Jn={},to={};var eo,ro=(io.prototype.add=function(t){!this._disposed&&t&&(this.storage.addRoot(t),t.addSelfToZr(this),this.refresh())},io.prototype.remove=function(t){!this._disposed&&t&&(this.storage.delRoot(t),t.removeSelfFromZr(this),this.refresh())},io.prototype.configLayer=function(t,e){this._disposed||(this.painter.configLayer&&this.painter.configLayer(t,e),this.refresh())},io.prototype.setBackgroundColor=function(t){this._disposed||(this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this.refresh(),this._backgroundColor=t,this._darkMode=function(t){if(!t)return!1;if("string"==typeof t)return ai(t,1)<.4;if(t.colorStops){for(var e=t.colorStops,r=0,i=e.length,n=0;n<i;n++)r+=ai(e[n].color,1);return(r/=i)<.4}return!1}(t))},io.prototype.getBackgroundColor=function(){return this._backgroundColor},io.prototype.setDarkMode=function(t){this._darkMode=t},io.prototype.isDarkMode=function(){return this._darkMode},io.prototype.refreshImmediately=function(t){this._disposed||(t||this.animation.update(!0),this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1)},io.prototype.refresh=function(){this._disposed||(this._needsRefresh=!0,this.animation.start())},io.prototype.flush=function(){this._disposed||this._flush(!1)},io.prototype._flush=function(t){var e,r=Wi();this._needsRefresh&&(e=!0,this.refreshImmediately(t)),this._needsRefreshHover&&(e=!0,this.refreshHoverImmediately());var i=Wi();e?(this._stillFrameAccum=0,this.trigger("rendered",{elapsedTime:i-r})):0<this._sleepAfterStill&&(this._stillFrameAccum++,this._stillFrameAccum>this._sleepAfterStill&&this.animation.stop())},io.prototype.setSleepAfterStill=function(t){this._sleepAfterStill=t},io.prototype.wakeUp=function(){this._disposed||(this.animation.start(),this._stillFrameAccum=0)},io.prototype.refreshHover=function(){this._needsRefreshHover=!0},io.prototype.refreshHoverImmediately=function(){this._disposed||(this._needsRefreshHover=!1,this.painter.refreshHover&&"canvas"===this.painter.getType()&&this.painter.refreshHover())},io.prototype.resize=function(t){this._disposed||(t=t||{},this.painter.resize(t.width,t.height),this.handler.resize())},io.prototype.clearAnimation=function(){this._disposed||this.animation.clear()},io.prototype.getWidth=function(){if(!this._disposed)return this.painter.getWidth()},io.prototype.getHeight=function(){if(!this._disposed)return this.painter.getHeight()},io.prototype.setCursorStyle=function(t){this._disposed||this.handler.setCursorStyle(t)},io.prototype.findHover=function(t,e){if(!this._disposed)return this.handler.findHover(t,e)},io.prototype.on=function(t,e,r){return this._disposed||this.handler.on(t,e,r),this},io.prototype.off=function(t,e){this._disposed||this.handler.off(t,e)},io.prototype.trigger=function(t,e){this._disposed||this.handler.trigger(t,e)},io.prototype.clear=function(){if(!this._disposed){for(var t=this.storage.getRoots(),e=0;e<t.length;e++)t[e]instanceof Qn&&t[e].removeSelfFromZr(this);this.storage.delAllRoots(),this.painter.clear()}},io.prototype.dispose=function(){var t;this._disposed||(this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,this._disposed=!0,t=this.id,delete to[t])},io);function io(t,e,r){var i=this;this._sleepAfterStill=10,this._stillFrameAccum=0,this._needsRefresh=!0,this._needsRefreshHover=!0,this._darkMode=!1,r=r||{},this.dom=e,this.id=t;var n=new sr,o=r.renderer||"canvas";Jn[o]||(o=F(Jn)[0]),r.useDirtyRect=null!=r.useDirtyRect&&r.useDirtyRect;var a=new Jn[o](e,n,r,t),s=r.ssr||a.ssrOnly;this.storage=n,this.painter=a;var h,l=c.node||c.worker||s?null:new un(a.getViewportRoot(),a.root),u=r.useCoarsePointer;(null==u||"auto"===u?c.touchEventsSupported:!!u)&&(h=Z(r.pointerSize,44)),this.handler=new qe(n,a,l,a.root,h),this.animation=new Xi({stage:{update:s?null:function(){return i._flush(!0)}}}),s||this.animation.start()}function no(t,e){Jn[t]=e}function oo(t){if("function"==typeof eo)return eo(t)}var ao="__zr_style_"+Math.round(10*Math.random()),so={shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,shadowColor:"#000",opacity:1,blend:"source-over"},ho={style:{shadowBlur:!0,shadowOffsetX:!0,shadowOffsetY:!0,shadowColor:!0,opacity:!0}};so[ao]=!0;var lo,uo,co=["z","z2","invisible"],po=["invisible"],fo=(vt(yo,lo=qn),yo.prototype._init=function(t){for(var e=F(t),r=0;r<e.length;r++){var i=e[r];"style"===i?this.useStyle(t[i]):lo.prototype.attrKV.call(this,i,t[i])}this.style||this.useStyle({})},yo.prototype.beforeBrush=function(){},yo.prototype.afterBrush=function(){},yo.prototype.innerBeforeBrush=function(){},yo.prototype.innerAfterBrush=function(){},yo.prototype.shouldBePainted=function(t,e,r,i){var n=this.transform;if(this.ignore||this.invisible||0===this.style.opacity||this.culling&&function(t,e,r){return vo.copy(t.getBoundingRect()),t.transform&&vo.applyTransform(t.transform),go.width=e,go.height=r,!vo.intersect(go)}(this,t,e)||n&&!n[0]&&!n[3])return!1;if(r&&this.__clipPaths&&this.__clipPaths.length)for(var o=0;o<this.__clipPaths.length;++o)if(this.__clipPaths[o].isZeroArea())return!1;if(i&&this.parent)for(var a=this.parent;a;){if(a.ignore)return!1;a=a.parent}return!0},yo.prototype.contain=function(t,e){return this.rectContain(t,e)},yo.prototype.traverse=function(t,e){t.call(e,this)},yo.prototype.rectContain=function(t,e){var r=this.transformCoordToLocal(t,e);return this.getBoundingRect().contain(r[0],r[1])},yo.prototype.getPaintRect=function(){var t,e,r,i,n,o,a,s=this._paintRect;return this._paintRect&&!this.__dirty||(t=this.transform,e=this.getBoundingRect(),i=(r=this.style).shadowBlur||0,n=r.shadowOffsetX||0,o=r.shadowOffsetY||0,s=this._paintRect||(this._paintRect=new Ae(0,0,0,0)),t?Ae.applyTransform(s,e,t):s.copy(e),(i||n||o)&&(s.width+=2*i+Math.abs(n),s.height+=2*i+Math.abs(o),s.x=Math.min(s.x,s.x+n-i),s.y=Math.min(s.y,s.y+o-i)),a=this.dirtyRectTolerance,s.isZero()||(s.x=Math.floor(s.x-a),s.y=Math.floor(s.y-a),s.width=Math.ceil(s.width+1+2*a),s.height=Math.ceil(s.height+1+2*a))),s},yo.prototype.setPrevPaintRect=function(t){t?(this._prevPaintRect=this._prevPaintRect||new Ae(0,0,0,0),this._prevPaintRect.copy(t)):this._prevPaintRect=null},yo.prototype.getPrevPaintRect=function(){return this._prevPaintRect},yo.prototype.animateStyle=function(t){return this.animate("style",t)},yo.prototype.updateDuringAnimation=function(t){"style"===t?this.dirtyStyle():this.markRedraw()},yo.prototype.attrKV=function(t,e){"style"!==t?lo.prototype.attrKV.call(this,t,e):this.style?this.setStyle(e):this.useStyle(e)},yo.prototype.setStyle=function(t,e){return"string"==typeof t?this.style[t]=e:I(this.style,t),this.dirtyStyle(),this},yo.prototype.dirtyStyle=function(t){t||this.markRedraw(),this.__dirty|=2,this._rect&&(this._rect=null)},yo.prototype.dirty=function(){this.dirtyStyle()},yo.prototype.styleChanged=function(){return!!(2&this.__dirty)},yo.prototype.styleUpdated=function(){this.__dirty&=-3},yo.prototype.createStyle=function(t){return ht(so,t)},yo.prototype.useStyle=function(t){t[ao]||(t=this.createStyle(t)),this.__inHover?this.__hoverStyle=t:this.style=t,this.dirtyStyle()},yo.prototype.isStyleObject=function(t){return t[ao]},yo.prototype._innerSaveToNormal=function(t){lo.prototype._innerSaveToNormal.call(this,t);var e=this._normalState;t.style&&!e.style&&(e.style=this._mergeStyle(this.createStyle(),this.style)),this._savePrimaryToNormal(t,e,co)},yo.prototype._applyStateObj=function(t,e,r,i,n,o){lo.prototype._applyStateObj.call(this,t,e,r,i,n,o);var a,s=!(e&&i);if(e&&e.style?n?i?a=e.style:(a=this._mergeStyle(this.createStyle(),r.style),this._mergeStyle(a,e.style)):(a=this._mergeStyle(this.createStyle(),i?this.style:r.style),this._mergeStyle(a,e.style)):s&&(a=r.style),a)if(n){var h=this.style;if(this.style=this.createStyle(s?{}:h),s)for(var l=F(h),u=0;u<l.length;u++)(p=l[u])in a&&(a[p]=a[p],this.style[p]=h[p]);for(var c=F(a),u=0;u<c.length;u++){var p=c[u];this.style[p]=this.style[p]}this._transitionState(t,{style:a},o,this.getAnimationStyleProps())}else this.useStyle(a);for(var f=this.__inHover?po:co,u=0;u<f.length;u++)p=f[u],e&&null!=e[p]?this[p]=e[p]:s&&null!=r[p]&&(this[p]=r[p])},yo.prototype._mergeStates=function(t){for(var e,r=lo.prototype._mergeStates.call(this,t),i=0;i<t.length;i++){var n=t[i];n.style&&(e=e||{},this._mergeStyle(e,n.style))}return e&&(r.style=e),r},yo.prototype._mergeStyle=function(t,e){return I(t,e),t},yo.prototype.getAnimationStyleProps=function(){return ho},yo.initDefaultProps=((uo=yo.prototype).type="displayable",uo.invisible=!1,uo.z=0,uo.z2=0,uo.zlevel=0,uo.culling=!1,uo.cursor="pointer",uo.rectHover=!1,uo.incremental=!1,uo._rect=null,uo.dirtyRectTolerance=0,void(uo.__dirty=2|rr)),yo);function yo(t){return lo.call(this,t)||this}var vo=new Ae(0,0,0,0),go=new Ae(0,0,0,0);var _o=Math.min,mo=Math.max,xo=Math.sin,wo=Math.cos,bo=2*Math.PI,So=gt(),To=gt(),ko=gt();function Co(t,e,r){if(0!==t.length){for(var i=t[0],n=i[0],o=i[0],a=i[1],s=i[1],h=1;h<t.length;h++)i=t[h],n=_o(n,i[0]),o=mo(o,i[0]),a=_o(a,i[1]),s=mo(s,i[1]);e[0]=n,e[1]=a,r[0]=o,r[1]=s}}function Po(t,e,r,i,n,o){n[0]=_o(t,r),n[1]=_o(e,i),o[0]=mo(t,r),o[1]=mo(e,i)}var Mo=[],Ao=[];var Do={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},Lo=[],zo=[],Io=[],Ro=[],Oo=[],Fo=[],Bo=Math.min,No=Math.max,Ho=Math.cos,Wo=Math.sin,Eo=Math.abs,Xo=Math.PI,Yo=2*Xo,qo="undefined"!=typeof Float32Array,jo=[];function Vo(t){return Math.round(t/Xo*1e8)/1e8%2*Xo}var Uo,Go=(Zo.prototype.increaseVersion=function(){this._version++},Zo.prototype.getVersion=function(){return this._version},Zo.prototype.setScale=function(t,e,r){0<(r=r||0)&&(this._ux=Eo(r/fn/t)||0,this._uy=Eo(r/fn/e)||0)},Zo.prototype.setDPR=function(t){this.dpr=t},Zo.prototype.setContext=function(t){this._ctx=t},Zo.prototype.getContext=function(){return this._ctx},Zo.prototype.beginPath=function(){return this._ctx&&this._ctx.beginPath(),this.reset(),this},Zo.prototype.reset=function(){this._saveData&&(this._len=0),this._pathSegLen&&(this._pathSegLen=null,this._pathLen=0),this._version++},Zo.prototype.moveTo=function(t,e){return this._drawPendingPt(),this.addData(Do.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},Zo.prototype.lineTo=function(t,e){var r,i=Eo(t-this._xi),n=Eo(e-this._yi),o=i>this._ux||n>this._uy;return this.addData(Do.L,t,e),this._ctx&&o&&this._ctx.lineTo(t,e),o?(this._xi=t,this._yi=e,this._pendingPtDist=0):(r=i*i+n*n)>this._pendingPtDist&&(this._pendingPtX=t,this._pendingPtY=e,this._pendingPtDist=r),this},Zo.prototype.bezierCurveTo=function(t,e,r,i,n,o){return this._drawPendingPt(),this.addData(Do.C,t,e,r,i,n,o),this._ctx&&this._ctx.bezierCurveTo(t,e,r,i,n,o),this._xi=n,this._yi=o,this},Zo.prototype.quadraticCurveTo=function(t,e,r,i){return this._drawPendingPt(),this.addData(Do.Q,t,e,r,i),this._ctx&&this._ctx.quadraticCurveTo(t,e,r,i),this._xi=r,this._yi=i,this},Zo.prototype.arc=function(t,e,r,i,n,o){this._drawPendingPt(),jo[0]=i,jo[1]=n,function(t,e){var r=Vo(t[0]);r<0&&(r+=Yo);var i=r-t[0],n=t[1];n+=i,!e&&Yo<=n-r?n=r+Yo:e&&Yo<=r-n?n=r-Yo:!e&&n<r?n=r+(Yo-Vo(r-n)):e&&r<n&&(n=r-(Yo-Vo(n-r))),t[0]=r,t[1]=n}(jo,o),i=jo[0];var a=(n=jo[1])-i;return this.addData(Do.A,t,e,r,r,i,a,0,o?0:1),this._ctx&&this._ctx.arc(t,e,r,i,n,o),this._xi=Ho(n)*r+t,this._yi=Wo(n)*r+e,this},Zo.prototype.arcTo=function(t,e,r,i,n){return this._drawPendingPt(),this._ctx&&this._ctx.arcTo(t,e,r,i,n),this},Zo.prototype.rect=function(t,e,r,i){return this._drawPendingPt(),this._ctx&&this._ctx.rect(t,e,r,i),this.addData(Do.R,t,e,r,i),this},Zo.prototype.closePath=function(){this._drawPendingPt(),this.addData(Do.Z);var t=this._ctx,e=this._x0,r=this._y0;return t&&t.closePath(),this._xi=e,this._yi=r,this},Zo.prototype.fill=function(t){t&&t.fill(),this.toStatic()},Zo.prototype.stroke=function(t){t&&t.stroke(),this.toStatic()},Zo.prototype.len=function(){return this._len},Zo.prototype.setData=function(t){if(this._saveData){var e=t.length;this.data&&this.data.length===e||!qo||(this.data=new Float32Array(e));for(var r=0;r<e;r++)this.data[r]=t[r];this._len=e}},Zo.prototype.appendPath=function(t){if(this._saveData){t instanceof Array||(t=[t]);for(var e=t.length,r=0,i=this._len,n=0;n<e;n++)r+=t[n].len();var o=this.data;if(qo&&(o instanceof Float32Array||!o)&&(this.data=new Float32Array(i+r),0<i&&o))for(var a=0;a<i;a++)this.data[a]=o[a];for(n=0;n<e;n++)for(var s=t[n].data,a=0;a<s.length;a++)this.data[i++]=s[a];this._len=i}},Zo.prototype.addData=function(t,e,r,i,n,o,a,s,h){if(this._saveData){var l=this.data;this._len+arguments.length>l.length&&(this._expandData(),l=this.data);for(var u=0;u<arguments.length;u++)l[this._len++]=arguments[u]}},Zo.prototype._drawPendingPt=function(){0<this._pendingPtDist&&(this._ctx&&this._ctx.lineTo(this._pendingPtX,this._pendingPtY),this._pendingPtDist=0)},Zo.prototype._expandData=function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},Zo.prototype.toStatic=function(){var t;this._saveData&&(this._drawPendingPt(),(t=this.data)instanceof Array&&(t.length=this._len,qo&&11<this._len&&(this.data=new Float32Array(t))))},Zo.prototype.getBoundingRect=function(){Io[0]=Io[1]=Oo[0]=Oo[1]=Number.MAX_VALUE,Ro[0]=Ro[1]=Fo[0]=Fo[1]=-Number.MAX_VALUE;for(var t,e,r,i,n,o,a,s,h,l,u,c,p,f,d=this.data,y=0,v=0,g=0,_=0,m=0;m<this._len;){var x=d[m++],w=1===m;switch(w&&(g=y=d[m],_=v=d[m+1]),x){case Do.M:y=g=d[m++],v=_=d[m++],Oo[0]=g,Oo[1]=_,Fo[0]=g,Fo[1]=_;break;case Do.L:Po(y,v,d[m],d[m+1],Oo,Fo),y=d[m++],v=d[m++];break;case Do.C:!function(t,e,r,i,n,o,a,s,h,l){var u=kr,c=br,p=u(t,r,n,a,Mo);h[0]=1/0,h[1]=1/0,l[0]=-1/0,l[1]=-1/0;for(var f=0;f<p;f++){var d=c(t,r,n,a,Mo[f]);h[0]=_o(d,h[0]),l[0]=mo(d,l[0])}for(p=u(e,i,o,s,Ao),f=0;f<p;f++){var y=c(e,i,o,s,Ao[f]);h[1]=_o(y,h[1]),l[1]=mo(y,l[1])}h[0]=_o(t,h[0]),l[0]=mo(t,l[0]),h[0]=_o(a,h[0]),l[0]=mo(a,l[0]),h[1]=_o(e,h[1]),l[1]=mo(e,l[1]),h[1]=_o(s,h[1]),l[1]=mo(s,l[1])}(y,v,d[m++],d[m++],d[m++],d[m++],d[m],d[m+1],Oo,Fo),y=d[m++],v=d[m++];break;case Do.Q:t=y,e=v,r=d[m++],i=d[m++],n=d[m],o=d[m+1],a=Oo,s=Fo,f=p=c=u=l=h=void 0,l=Pr,u=mo(_o((h=Ar)(t,r,n),1),0),c=mo(_o(h(e,i,o),1),0),p=l(t,r,n,u),f=l(e,i,o,c),a[0]=_o(t,n,p),a[1]=_o(e,o,f),s[0]=mo(t,n,p),s[1]=mo(e,o,f),y=d[m++],v=d[m++];break;case Do.A:var b=d[m++],S=d[m++],T=d[m++],k=d[m++],C=d[m++],P=d[m++]+C;m+=1;var M=!d[m++];w&&(g=Ho(C)*T+b,_=Wo(C)*k+S),function(t,e,r,i,n,o,a,s,h){var l,u=Lt,c=zt,p=Math.abs(n-o);if(p%bo<1e-4&&1e-4<p)return s[0]=t-r,s[1]=e-i,h[0]=t+r,h[1]=e+i;So[0]=wo(n)*r+t,So[1]=xo(n)*i+e,To[0]=wo(o)*r+t,To[1]=xo(o)*i+e,u(s,So,To),c(h,So,To),(n%=bo)<0&&(n+=bo),(o%=bo)<0&&(o+=bo),o<n&&!a?o+=bo:n<o&&a&&(n+=bo),a&&(l=o,o=n,n=l);for(var f=0;f<o;f+=Math.PI/2)n<f&&(ko[0]=wo(f)*r+t,ko[1]=xo(f)*i+e,u(s,ko,s),c(h,ko,h))}(b,S,T,k,C,P,M,Oo,Fo),y=Ho(P)*T+b,v=Wo(P)*k+S;break;case Do.R:Po(g=y=d[m++],_=v=d[m++],g+d[m++],_+d[m++],Oo,Fo);break;case Do.Z:y=g,v=_}Lt(Io,Io,Oo),zt(Ro,Ro,Fo)}return 0===m&&(Io[0]=Io[1]=Ro[0]=Ro[1]=0),new Ae(Io[0],Io[1],Ro[0]-Io[0],Ro[1]-Io[1])},Zo.prototype._calculateLength=function(){var t=this.data,e=this._len,r=this._ux,i=this._uy,n=0,o=0,a=0,s=0;this._pathSegLen||(this._pathSegLen=[]);for(var h=this._pathSegLen,l=0,u=0,c=0;c<e;){var p=t[c++],f=1===c;f&&(a=n=t[c],s=o=t[c+1]);var d=-1;switch(p){case Do.M:n=a=t[c++],o=s=t[c++];break;case Do.L:var y=t[c++],v=(m=t[c++])-o;(Eo(A=y-n)>r||Eo(v)>i||c===e-1)&&(d=Math.sqrt(A*A+v*v),n=y,o=m);break;case Do.C:var g=t[c++],_=t[c++],y=t[c++],m=t[c++],x=t[c++],w=t[c++],d=function(t,e,r,i,n,o,a,s,h){for(var l=t,u=e,c=0,p=1/h,f=1;f<=h;f++){var d=f*p,y=br(t,r,n,a,d),v=br(e,i,o,s,d),g=y-l,_=v-u;c+=Math.sqrt(g*g+_*_),l=y,u=v}return c}(n,o,g,_,y,m,x,w,10),n=x,o=w;break;case Do.Q:d=function(t,e,r,i,n,o,a){for(var s=t,h=e,l=0,u=1/a,c=1;c<=a;c++){var p=c*u,f=Pr(t,r,n,p),d=Pr(e,i,o,p),y=f-s,v=d-h;l+=Math.sqrt(y*y+v*v),s=f,h=d}return l}(n,o,g=t[c++],_=t[c++],y=t[c++],m=t[c++],10),n=y,o=m;break;case Do.A:var b=t[c++],S=t[c++],T=t[c++],k=t[c++],C=t[c++],P=t[c++],M=P+C;c+=1,f&&(a=Ho(C)*T+b,s=Wo(C)*k+S),d=No(T,k)*Bo(Yo,Math.abs(P)),n=Ho(M)*T+b,o=Wo(M)*k+S;break;case Do.R:a=n=t[c++],s=o=t[c++],d=2*t[c++]+2*t[c++];break;case Do.Z:var A=a-n,v=s-o;d=Math.sqrt(A*A+v*v),n=a,o=s}0<=d&&(l+=h[u++]=d)}return this._pathLen=l},Zo.prototype.rebuildPath=function(t,e){var r,i,n,o,a,s,h,l,u,c,p=this.data,f=this._ux,d=this._uy,y=this._len,v=e<1,g=0,_=0,m=0;if(!v||(this._pathSegLen||this._calculateLength(),h=this._pathSegLen,l=e*this._pathLen))t:for(var x=0;x<y;){var w=p[x++],b=1===x;switch(b&&(r=n=p[x],i=o=p[x+1]),w!==Do.L&&0<m&&(t.lineTo(u,c),m=0),w){case Do.M:r=n=p[x++],i=o=p[x++],t.moveTo(n,o);break;case Do.L:a=p[x++],s=p[x++];var S=Eo(a-n),T=Eo(s-o);if(f<S||d<T){if(v){if(l<g+(j=h[_++])){var k=(l-g)/j;t.lineTo(n*(1-k)+a*k,o*(1-k)+s*k);break t}g+=j}t.lineTo(a,s),n=a,o=s,m=0}else{var C=S*S+T*T;m<C&&(u=a,c=s,m=C)}break;case Do.C:var P=p[x++],M=p[x++],A=p[x++],D=p[x++],L=p[x++],z=p[x++];if(v){if(l<g+(j=h[_++])){Cr(n,P,A,L,k=(l-g)/j,Lo),Cr(o,M,D,z,k,zo),t.bezierCurveTo(Lo[1],zo[1],Lo[2],zo[2],Lo[3],zo[3]);break t}g+=j}t.bezierCurveTo(P,M,A,D,L,z),n=L,o=z;break;case Do.Q:if(P=p[x++],M=p[x++],A=p[x++],D=p[x++],v){if(l<g+(j=h[_++])){Dr(n,P,A,k=(l-g)/j,Lo),Dr(o,M,D,k,zo),t.quadraticCurveTo(Lo[1],zo[1],Lo[2],zo[2]);break t}g+=j}t.quadraticCurveTo(P,M,A,D),n=A,o=D;break;case Do.A:var I=p[x++],R=p[x++],O=p[x++],F=p[x++],B=p[x++],N=p[x++],H=p[x++],W=!p[x++],E=F<O?O:F,X=.001<Eo(O-F),Y=B+N,q=!1;if(v&&(l<g+(j=h[_++])&&(Y=B+N*(l-g)/j,q=!0),g+=j),X&&t.ellipse?t.ellipse(I,R,O,F,H,B,Y,W):t.arc(I,R,E,B,Y,W),q)break t;b&&(r=Ho(B)*O+I,i=Wo(B)*F+R),n=Ho(Y)*O+I,o=Wo(Y)*F+R;break;case Do.R:r=n=p[x],i=o=p[x+1],a=p[x++],s=p[x++];var j,V=p[x++],U=p[x++];if(v){if(l<g+(j=h[_++])){var G=l-g;t.moveTo(a,s),t.lineTo(a+Bo(G,V),s),0<(G-=V)&&t.lineTo(a+V,s+Bo(G,U)),0<(G-=U)&&t.lineTo(a+No(V-G,0),s+U),0<(G-=V)&&t.lineTo(a,s+No(U-G,0));break t}g+=j}t.rect(a,s,V,U);break;case Do.Z:if(v){if(l<g+(j=h[_++])){k=(l-g)/j,t.lineTo(n*(1-k)+r*k,o*(1-k)+i*k);break t}g+=j}t.closePath(),n=r,o=i}}},Zo.prototype.clone=function(){var t=new Zo,e=this.data;return t.data=e.slice?e.slice():Array.prototype.slice.call(e),t._len=this._len,t},Zo.prototype.canSave=function(){return!!this._saveData},Zo.CMD=Do,Zo.initDefaultProps=((Uo=Zo.prototype)._saveData=!0,Uo._ux=0,Uo._uy=0,Uo._pendingPtDist=0,void(Uo._version=0)),Zo);function Zo(t){this.dpr=1,this._xi=0,this._yi=0,this._x0=0,this._y0=0,this._len=0,t&&(this._saveData=!1),this._saveData&&(this.data=[])}function Ko(t,e,r,i,n,o,a){if(0!==n){var s=n,h=0;if(!(e+s<a&&i+s<a||a<e-s&&a<i-s||t+s<o&&r+s<o||o<t-s&&o<r-s)){if(t===r)return Math.abs(o-t)<=s/2;var l=(h=(e-i)/(t-r))*o-a+(t*i-r*e)/(t-r);return l*l/(h*h+1)<=s/2*s/2}}}function Qo(t,e,r,i,n,o,a,s,h,l,u){if(0!==h){var c=h;if(!(e+c<u&&i+c<u&&o+c<u&&s+c<u||u<e-c&&u<i-c&&u<o-c&&u<s-c||t+c<l&&r+c<l&&n+c<l&&a+c<l||l<t-c&&l<r-c&&l<n-c&&l<a-c))return function(t,e,r,i,n,o,a,s,h,l,u){var c,p,f,d,y,v=.005,g=1/0;gr[0]=h,gr[1]=l;for(var _=0;_<1;_+=.05)_r[0]=br(t,r,n,a,_),_r[1]=br(e,i,o,s,_),(d=Mt(gr,_r))<g&&(c=_,g=d);g=1/0;for(var m=0;m<32&&!(v<dr);m++)p=c-v,f=c+v,_r[0]=br(t,r,n,a,p),_r[1]=br(e,i,o,s,p),d=Mt(_r,gr),0<=p&&d<g?(c=p,g=d):(mr[0]=br(t,r,n,a,f),mr[1]=br(e,i,o,s,f),y=Mt(mr,gr),f<=1&&y<g?(c=f,g=y):v*=.5);return u&&(u[0]=br(t,r,n,a,c),u[1]=br(e,i,o,s,c)),pr(g)}(t,e,r,i,n,o,a,s,l,u,null)<=c/2}}function $o(t,e,r,i,n,o,a,s,h){if(0!==a){var l=a;if(!(e+l<h&&i+l<h&&o+l<h||h<e-l&&h<i-l&&h<o-l||t+l<s&&r+l<s&&n+l<s||s<t-l&&s<r-l&&s<n-l))return function(t,e,r,i,n,o,a,s,h){var l,u=.005,c=1/0;gr[0]=a,gr[1]=s;for(var p=0;p<1;p+=.05){_r[0]=Pr(t,r,n,p),_r[1]=Pr(e,i,o,p),(g=Mt(gr,_r))<c&&(l=p,c=g)}c=1/0;for(var f=0;f<32&&!(u<dr);f++){var d=l-u,y=l+u;_r[0]=Pr(t,r,n,d),_r[1]=Pr(e,i,o,d);var v,g=Mt(_r,gr);0<=d&&g<c?(l=d,c=g):(mr[0]=Pr(t,r,n,y),mr[1]=Pr(e,i,o,y),v=Mt(mr,gr),y<=1&&v<c?(l=y,c=v):u*=.5)}return h&&(h[0]=Pr(t,r,n,l),h[1]=Pr(e,i,o,l)),pr(c)}(t,e,r,i,n,o,s,h,null)<=l/2}}var Jo=2*Math.PI;function ta(t){return(t%=Jo)<0&&(t+=Jo),t}var ea=2*Math.PI;function ra(t,e,r,i,n,o){if(e<o&&i<o||o<e&&o<i)return 0;if(i===e)return 0;var a=(o-e)/(i-e),s=i<e?1:-1;1!=a&&0!=a||(s=i<e?.5:-.5);var h=a*(r-t)+t;return h===n?1/0:n<h?s:0}var ia=Go.CMD,na=2*Math.PI,oa=1e-4;var aa=[-1,-1,-1],sa=[-1,-1];function ha(t,e,r,i,n,o,a,s,h,l){if(e<l&&i<l&&o<l&&s<l||l<e&&l<i&&l<o&&l<s)return 0;var u=Tr(e,i,o,s,l,aa);if(0===u)return 0;for(var c,p=0,f=-1,d=void 0,y=void 0,v=0;v<u;v++){var g=aa[v],_=0===g||1===g?.5:1;br(t,r,n,a,g)<h||(f<0&&(f=kr(e,i,o,s,sa),sa[1]<sa[0]&&1<f&&(c=void 0,c=sa[0],sa[0]=sa[1],sa[1]=c),d=br(e,i,o,s,sa[0]),1<f&&(y=br(e,i,o,s,sa[1]))),2===f?g<sa[0]?p+=d<e?_:-_:g<sa[1]?p+=y<d?_:-_:p+=s<y?_:-_:g<sa[0]?p+=d<e?_:-_:p+=s<d?_:-_)}return p}function la(t,e,r,i,n,o,a,s){if(e<s&&i<s&&o<s||s<e&&s<i&&s<o)return 0;var h,l,u,c,p,f,d,y,v,g,_,m=(u=aa,v=2*((l=i)-(h=e)),g=h-s,_=0,xr(y=h-2*l+o)?wr(v)&&0<=(f=-g/v)&&f<=1&&(u[_++]=f):xr(c=v*v-4*y*g)?0<=(f=-v/(2*y))&&f<=1&&(u[_++]=f):0<c&&(d=(-v-(p=pr(c)))/(2*y),0<=(f=(-v+p)/(2*y))&&f<=1&&(u[_++]=f),0<=d&&d<=1&&(u[_++]=d)),_);if(0===m)return 0;var x=Ar(e,i,o);if(0<=x&&x<=1){for(var w=0,b=Pr(e,i,o,x),S=0;S<m;S++){var T=0===aa[S]||1===aa[S]?.5:1;Pr(t,r,n,aa[S])<a||(aa[S]<x?w+=b<e?T:-T:w+=o<b?T:-T)}return w}T=0===aa[0]||1===aa[0]?.5:1;return Pr(t,r,n,aa[0])<a?0:o<e?T:-T}function ua(t,e,r,i,n){for(var o,a,s=t.data,h=t.len(),l=0,u=0,c=0,p=0,f=0,d=0;d<h;){var y=s[d++],v=1===d;switch(y===ia.M&&1<d&&(r||(l+=ra(u,c,p,f,i,n))),v&&(p=u=s[d],f=c=s[d+1]),y){case ia.M:u=p=s[d++],c=f=s[d++];break;case ia.L:if(r){if(Ko(u,c,s[d],s[d+1],e,i,n))return!0}else l+=ra(u,c,s[d],s[d+1],i,n)||0;u=s[d++],c=s[d++];break;case ia.C:if(r){if(Qo(u,c,s[d++],s[d++],s[d++],s[d++],s[d],s[d+1],e,i,n))return!0}else l+=ha(u,c,s[d++],s[d++],s[d++],s[d++],s[d],s[d+1],i,n)||0;u=s[d++],c=s[d++];break;case ia.Q:if(r){if($o(u,c,s[d++],s[d++],s[d],s[d+1],e,i,n))return!0}else l+=la(u,c,s[d++],s[d++],s[d],s[d+1],i,n)||0;u=s[d++],c=s[d++];break;case ia.A:var g=s[d++],_=s[d++],m=s[d++],x=s[d++],w=s[d++],b=s[d++];d+=1;var S=!!(1-s[d++]),T=Math.cos(w)*m+g,k=Math.sin(w)*x+_;v?(p=T,f=k):l+=ra(u,c,T,k,i,n);var C=(i-g)*x/m+g;if(r){if(function(t,e,r,i,n,o,a,s,h){if(0!==a){var l=a;s-=t,h-=e;var u,c=Math.sqrt(s*s+h*h);if(!(r<c-l||c+l<r)){if(Math.abs(i-n)%ea<1e-4)return 1;(n=o?(u=i,i=ta(n),ta(u)):(i=ta(i),ta(n)))<i&&(n+=ea);var p=Math.atan2(h,s);return p<0&&(p+=ea),i<=p&&p<=n||i<=p+ea&&p+ea<=n}}}(g,_,x,w,w+b,S,e,C,n))return!0}else l+=function(t,e,r,i,n,o,a,s){if(r<(s-=e)||s<-r)return 0;var h=Math.sqrt(r*r-s*s);aa[0]=-h,aa[1]=h;var l,u=Math.abs(i-n);if(u<1e-4)return 0;if(na-1e-4<=u){n=na;var c=o?1:-1;return a>=aa[i=0]+t&&a<=aa[1]+t?c:0}n<i&&(l=i,i=n,n=l),i<0&&(i+=na,n+=na);for(var p=0,f=0;f<2;f++){var d,y=aa[f];a<y+t&&(c=o?1:-1,(d=Math.atan2(s,y))<0&&(d=na+d),(i<=d&&d<=n||i<=d+na&&d+na<=n)&&(d>Math.PI/2&&d<1.5*Math.PI&&(c=-c),p+=c))}return p}(g,_,x,w,w+b,S,C,n);u=Math.cos(w+b)*m+g,c=Math.sin(w+b)*x+_;break;case ia.R:p=u=s[d++],f=c=s[d++];if(T=p+s[d++],k=f+s[d++],r){if(Ko(p,f,T,f,e,i,n)||Ko(T,f,T,k,e,i,n)||Ko(T,k,p,k,e,i,n)||Ko(p,k,p,f,e,i,n))return!0}else l+=ra(T,f,T,k,i,n),l+=ra(p,k,p,f,i,n);break;case ia.Z:if(r){if(Ko(u,c,p,f,e,i,n))return!0}else l+=ra(u,c,p,f,i,n);u=p,c=f}}return r||(o=c,a=f,Math.abs(o-a)<oa)||(l+=ra(u,c,p,f,i,n)||0),0!==l}var ca,pa,fa=T({fill:"#000",stroke:null,strokePercent:1,fillOpacity:1,strokeOpacity:1,lineDashOffset:0,lineWidth:1,lineCap:"butt",miterLimit:10,strokeNoScale:!1,strokeFirst:!1},so),da={style:T({fill:!0,stroke:!0,strokePercent:!0,fillOpacity:!0,strokeOpacity:!0,lineDashOffset:!0,lineWidth:!0,miterLimit:!0},ho.style)},ya=Cn.concat(["invisible","culling","z","z2","zlevel","parent"]),va=(vt(ga,ca=fo),ga.prototype.update=function(){var e=this;ca.prototype.update.call(this);var t=this.style;if(t.decal){var r=this._decalEl=this._decalEl||new ga;r.buildPath===ga.prototype.buildPath&&(r.buildPath=function(t){e.buildPath(t,e.shape)}),r.silent=!0;var i=r.style;for(var n in t)i[n]!==t[n]&&(i[n]=t[n]);i.fill=t.fill?t.decal:null,i.decal=null,i.shadowColor=null,t.strokeFirst&&(i.stroke=null);for(var o=0;o<ya.length;++o)r[ya[o]]=this[ya[o]];r.__dirty|=rr}else this._decalEl&&(this._decalEl=null)},ga.prototype.getDecalElement=function(){return this._decalEl},ga.prototype._init=function(t){var e=F(t);this.shape=this.getDefaultShape();var r=this.getDefaultStyle();r&&this.useStyle(r);for(var i=0;i<e.length;i++){var n=e[i],o=t[n];"style"===n?this.style?I(this.style,o):this.useStyle(o):"shape"===n?I(this.shape,o):ca.prototype.attrKV.call(this,n,o)}this.style||this.useStyle({})},ga.prototype.getDefaultStyle=function(){return null},ga.prototype.getDefaultShape=function(){return{}},ga.prototype.canBeInsideText=function(){return this.hasFill()},ga.prototype.getInsideTextFill=function(){var t=this.style.fill;if("none"!==t){if(N(t)){var e=ai(t,0);return.5<e?dn:.2<e?"#eee":yn}if(t)return yn}return dn},ga.prototype.getInsideTextStroke=function(t){var e=this.style.fill;if(N(e)){var r=this.__zr;if(!(!r||!r.isDarkMode())==ai(t,0)<.4)return e}},ga.prototype.buildPath=function(t,e,r){},ga.prototype.pathUpdated=function(){this.__dirty&=~ir},ga.prototype.getUpdatedPathProxy=function(t){return this.path||this.createPathProxy(),this.path.beginPath(),this.buildPath(this.path,this.shape,t),this.path},ga.prototype.createPathProxy=function(){this.path=new Go(!1)},ga.prototype.hasStroke=function(){var t=this.style,e=t.stroke;return!(null==e||"none"===e||!(0<t.lineWidth))},ga.prototype.hasFill=function(){var t=this.style.fill;return null!=t&&"none"!==t},ga.prototype.getBoundingRect=function(){var t,e,r=this._rect,i=this.style,n=!r;if(n&&(t=!1,this.path||(t=!0,this.createPathProxy()),e=this.path,(t||this.__dirty&ir)&&(e.beginPath(),this.buildPath(e,this.shape,!1),this.pathUpdated()),r=e.getBoundingRect()),this._rect=r,this.hasStroke()&&this.path&&0<this.path.len()){var o,a,s,h=this._rectStroke||(this._rectStroke=r.clone());return(this.__dirty||n)&&(h.copy(r),o=i.strokeNoScale?this.getLineScale():1,s=i.lineWidth,this.hasFill()||(a=this.strokeContainThreshold,s=Math.max(s,null==a?4:a)),1e-10<o&&(h.width+=s/o,h.height+=s/o,h.x-=s/o/2,h.y-=s/o/2)),h}return r},ga.prototype.contain=function(t,e){var r=this.transformCoordToLocal(t,e),i=this.getBoundingRect(),n=this.style;if(t=r[0],e=r[1],i.contain(t,e)){var o=this.path;if(this.hasStroke()){var a=n.lineWidth,s=n.strokeNoScale?this.getLineScale():1;if(1e-10<s&&(this.hasFill()||(a=Math.max(a,this.strokeContainThreshold)),ua(o,a/s,!0,t,e)))return!0}if(this.hasFill())return ua(o,0,!1,t,e)}return!1},ga.prototype.dirtyShape=function(){this.__dirty|=ir,this._rect&&(this._rect=null),this._decalEl&&this._decalEl.dirtyShape(),this.markRedraw()},ga.prototype.dirty=function(){this.dirtyStyle(),this.dirtyShape()},ga.prototype.animateShape=function(t){return this.animate("shape",t)},ga.prototype.updateDuringAnimation=function(t){"style"===t?this.dirtyStyle():"shape"===t?this.dirtyShape():this.markRedraw()},ga.prototype.attrKV=function(t,e){"shape"===t?this.setShape(e):ca.prototype.attrKV.call(this,t,e)},ga.prototype.setShape=function(t,e){var r=(r=this.shape)||(this.shape={});return"string"==typeof t?r[t]=e:I(r,t),this.dirtyShape(),this},ga.prototype.shapeChanged=function(){return!!(this.__dirty&ir)},ga.prototype.createStyle=function(t){return ht(fa,t)},ga.prototype._innerSaveToNormal=function(t){ca.prototype._innerSaveToNormal.call(this,t);var e=this._normalState;t.shape&&!e.shape&&(e.shape=I({},this.shape))},ga.prototype._applyStateObj=function(t,e,r,i,n,o){ca.prototype._applyStateObj.call(this,t,e,r,i,n,o);var a,s=!(e&&i);if(e&&e.shape?n?i?a=e.shape:(a=I({},r.shape),I(a,e.shape)):(a=I({},i?this.shape:r.shape),I(a,e.shape)):s&&(a=r.shape),a)if(n){this.shape=I({},this.shape);for(var h={},l=F(a),u=0;u<l.length;u++){var c=l[u];"object"==typeof a[c]?this.shape[c]=a[c]:h[c]=a[c]}this._transitionState(t,{shape:h},o)}else this.shape=a,this.dirtyShape()},ga.prototype._mergeStates=function(t){for(var e,r=ca.prototype._mergeStates.call(this,t),i=0;i<t.length;i++){var n=t[i];n.shape&&(e=e||{},this._mergeStyle(e,n.shape))}return e&&(r.shape=e),r},ga.prototype.getAnimationStyleProps=function(){return da},ga.prototype.isZeroArea=function(){return!1},ga.extend=function(r){var i,t=(vt(e,i=ga),e.prototype.getDefaultStyle=function(){return C(r.style)},e.prototype.getDefaultShape=function(){return C(r.shape)},e);function e(t){var e=i.call(this,t)||this;return r.init&&r.init.call(e,t),e}for(var n in r)"function"==typeof r[n]&&(t.prototype[n]=r[n]);return t},ga.initDefaultProps=((pa=ga.prototype).type="path",pa.strokeContainThreshold=5,pa.segmentIgnoreThreshold=0,pa.subPixelOptimize=!1,pa.autoBatch=!1,void(pa.__dirty=2|rr|ir)),ga);function ga(t){return ca.call(this,t)||this}var _a=Go.CMD,ma=[[],[],[]],xa=Math.sqrt,wa=Math.atan2;function ba(t,e){if(e){for(var r,i,n,o,a=t.data,s=t.len(),h=_a.M,l=_a.C,u=_a.L,c=_a.R,p=_a.A,f=_a.Q,d=0,y=0;d<s;){switch(r=a[d++],y=d,i=0,r){case h:case u:i=1;break;case l:i=3;break;case f:i=2;break;case p:var v=e[4],g=e[5],_=xa(e[0]*e[0]+e[1]*e[1]),m=xa(e[2]*e[2]+e[3]*e[3]),x=wa(-e[1]/m,e[0]/_);a[d]*=_,a[d++]+=v,a[d]*=m,a[d++]+=g,a[d++]*=_,a[d++]*=m,a[d++]+=x,a[d++]+=x,y=d+=2;break;case c:o[0]=a[d++],o[1]=a[d++],Dt(o,o,e),a[y++]=o[0],a[y++]=o[1],o[0]+=a[d++],o[1]+=a[d++],Dt(o,o,e),a[y++]=o[0],a[y++]=o[1]}for(n=0;n<i;n++){var w=ma[n];w[0]=a[d++],w[1]=a[d++],Dt(w,w,e),a[y++]=w[0],a[y++]=w[1]}}t.increaseVersion()}}var Sa=Math.sqrt,Ta=Math.sin,ka=Math.cos,Ca=Math.PI;function Pa(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])}function Ma(t,e){return(t[0]*e[0]+t[1]*e[1])/(Pa(t)*Pa(e))}function Aa(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(Ma(t,e))}function Da(t,e,r,i,n,o,a,s,h,l,u){var c=h*(Ca/180),p=ka(c)*(t-r)/2+Ta(c)*(e-i)/2,f=-1*Ta(c)*(t-r)/2+ka(c)*(e-i)/2,d=p*p/(a*a)+f*f/(s*s);1<d&&(a*=Sa(d),s*=Sa(d));var y,v=(n===o?-1:1)*Sa((a*a*(s*s)-a*a*(f*f)-s*s*(p*p))/(a*a*(f*f)+s*s*(p*p)))||0,g=v*a*f/s,_=v*-s*p/a,m=(t+r)/2+ka(c)*g-Ta(c)*_,x=(e+i)/2+Ta(c)*g+ka(c)*_,w=Aa([1,0],[(p-g)/a,(f-_)/s]),b=[(p-g)/a,(f-_)/s],S=[(-1*p-g)/a,(-1*f-_)/s],T=Aa(b,S);Ma(b,S)<=-1&&(T=Ca),1<=Ma(b,S)&&(T=0),T<0&&(y=Math.round(T/Ca*1e6)/1e6,T=2*Ca+y%2*Ca),u.addData(l,m,x,a,s,w,T,c,o)}var La=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/gi,za=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;var Ia,Ra=(vt(Oa,Ia=va),Oa.prototype.applyTransform=function(t){},Oa);function Oa(){return null!==Ia&&Ia.apply(this,arguments)||this}function Fa(t){return null!=t.setData}function Ba(t,e){var i=function(t){var e=new Go;if(!t)return e;var r,i=0,n=0,o=i,a=n,s=Go.CMD,h=t.match(La);if(!h)return e;for(var l=0;l<h.length;l++){for(var u=h[l],c=u.charAt(0),p=void 0,f=u.match(za)||[],d=f.length,y=0;y<d;y++)f[y]=parseFloat(f[y]);for(var v=0;v<d;){var g=void 0,_=void 0,m=void 0,x=void 0,w=void 0,b=void 0,S=void 0,T=i,k=n,C=void 0,P=void 0;switch(c){case"l":i+=f[v++],n+=f[v++],p=s.L,e.addData(p,i,n);break;case"L":i=f[v++],n=f[v++],p=s.L,e.addData(p,i,n);break;case"m":i+=f[v++],n+=f[v++],p=s.M,e.addData(p,i,n),o=i,a=n,c="l";break;case"M":i=f[v++],n=f[v++],p=s.M,e.addData(p,i,n),o=i,a=n,c="L";break;case"h":i+=f[v++],p=s.L,e.addData(p,i,n);break;case"H":i=f[v++],p=s.L,e.addData(p,i,n);break;case"v":n+=f[v++],p=s.L,e.addData(p,i,n);break;case"V":n=f[v++],p=s.L,e.addData(p,i,n);break;case"C":p=s.C,e.addData(p,f[v++],f[v++],f[v++],f[v++],f[v++],f[v++]),i=f[v-2],n=f[v-1];break;case"c":p=s.C,e.addData(p,f[v++]+i,f[v++]+n,f[v++]+i,f[v++]+n,f[v++]+i,f[v++]+n),i+=f[v-2],n+=f[v-1];break;case"S":g=i,_=n,C=e.len(),P=e.data,r===s.C&&(g+=i-P[C-4],_+=n-P[C-3]),p=s.C,T=f[v++],k=f[v++],i=f[v++],n=f[v++],e.addData(p,g,_,T,k,i,n);break;case"s":g=i,_=n,C=e.len(),P=e.data,r===s.C&&(g+=i-P[C-4],_+=n-P[C-3]),p=s.C,T=i+f[v++],k=n+f[v++],i+=f[v++],n+=f[v++],e.addData(p,g,_,T,k,i,n);break;case"Q":T=f[v++],k=f[v++],i=f[v++],n=f[v++],p=s.Q,e.addData(p,T,k,i,n);break;case"q":T=f[v++]+i,k=f[v++]+n,i+=f[v++],n+=f[v++],p=s.Q,e.addData(p,T,k,i,n);break;case"T":g=i,_=n,C=e.len(),P=e.data,r===s.Q&&(g+=i-P[C-4],_+=n-P[C-3]),i=f[v++],n=f[v++],p=s.Q,e.addData(p,g,_,i,n);break;case"t":g=i,_=n,C=e.len(),P=e.data,r===s.Q&&(g+=i-P[C-4],_+=n-P[C-3]),i+=f[v++],n+=f[v++],p=s.Q,e.addData(p,g,_,i,n);break;case"A":m=f[v++],x=f[v++],w=f[v++],b=f[v++],S=f[v++],Da(T=i,k=n,i=f[v++],n=f[v++],b,S,m,x,w,p=s.A,e);break;case"a":m=f[v++],x=f[v++],w=f[v++],b=f[v++],S=f[v++],Da(T=i,k=n,i+=f[v++],n+=f[v++],b,S,m,x,w,p=s.A,e)}}"z"!==c&&"Z"!==c||(p=s.Z,e.addData(p),i=o,n=a),r=p}return e.toStatic(),e}(t),r=I({},e);return r.buildPath=function(t){var e,r=Fa(t);r&&t.canSave()?(t.appendPath(i),(e=t.getContext())&&t.rebuildPath(e,1)):(e=r?t.getContext():t)&&i.rebuildPath(e,1)},r.applyTransform=function(t){ba(i,t),this.dirtyShape()},r}function Na(t,e){return new Ra(Ba(t,e))}function Ha(t,e){e=e||{};var r=new va;return t.shape&&r.setShape(t.shape),r.setStyle(t.style),e.bakeTransform?ba(r.path,t.getComputedTransform()):e.toLocal?r.setLocalTransform(t.getComputedTransform()):r.copyTransform(t),r.buildPath=t.buildPath,r.applyTransform=r.applyTransform,r.z=t.z,r.z2=t.z2,r.zlevel=t.zlevel,r}var Wa=Object.freeze({__proto__:null,createFromString:Na,extendFromString:function(t,e){var r,i=Ba(t,e);function n(t){var e=r.call(this,t)||this;return e.applyTransform=i.applyTransform,e.buildPath=i.buildPath,e}return vt(n,r=Ra),n},mergePath:function(t,e){for(var r=[],i=t.length,n=0;n<i;n++){var o=t[n];r.push(o.getUpdatedPathProxy(!0))}var a=new va(e);return a.createPathProxy(),a.buildPath=function(t){var e;Fa(t)&&(t.appendPath(r),(e=t.getContext())&&t.rebuildPath(e,1))},a},clonePath:Ha}),Ea=T({x:0,y:0},so),Xa={style:T({x:!0,y:!0,width:!0,height:!0,sx:!0,sy:!0,sWidth:!0,sHeight:!0},ho.style)};var Ya,qa=(vt(ja,Ya=fo),ja.prototype.createStyle=function(t){return ht(Ea,t)},ja.prototype._getSize=function(t){var e=this.style,r=e[t];if(null!=r)return r;var i,n=(i=e.image)&&"string"!=typeof i&&i.width&&i.height?e.image:this.__image;if(!n)return 0;var o="width"===t?"height":"width",a=e[o];return null==a?n[t]:n[t]/n[o]*a},ja.prototype.getWidth=function(){return this._getSize("width")},ja.prototype.getHeight=function(){return this._getSize("height")},ja.prototype.getAnimationStyleProps=function(){return Xa},ja.prototype.getBoundingRect=function(){var t=this.style;return this._rect||(this._rect=new Ae(t.x||0,t.y||0,this.getWidth(),this.getHeight())),this._rect},ja);function ja(){return null!==Ya&&Ya.apply(this,arguments)||this}qa.prototype.type="image";var Va,Ua=function(){this.cx=0,this.cy=0,this.r=0},Ga=(vt(Za,Va=va),Za.prototype.getDefaultShape=function(){return new Ua},Za.prototype.buildPath=function(t,e){t.moveTo(e.cx+e.r,e.cy),t.arc(e.cx,e.cy,e.r,0,2*Math.PI)},Za);function Za(t){return Va.call(this,t)||this}Ga.prototype.type="circle";var Ka=Math.round;function Qa(t,e,r){if(!e)return t;var i=Ka(2*t);return(i+Ka(e))%2==0?i/2:(i+(r?1:-1))/2}var $a,Ja=function(){this.x=0,this.y=0,this.width=0,this.height=0},ts={},es=(vt(rs,$a=va),rs.prototype.getDefaultShape=function(){return new Ja},rs.prototype.buildPath=function(t,e){var r,i,n,o,a,s,h,l,u,c,p,f,d,y,v,g,_;this.subPixelOptimize?(i=(r=function(t,e,r){if(e){var i=e.x,n=e.y,o=e.width,a=e.height;t.x=i,t.y=n,t.width=o,t.height=a;var s=r&&r.lineWidth;return s&&(t.x=Qa(i,s,!0),t.y=Qa(n,s,!0),t.width=Math.max(Qa(i+o,s,!1)-t.x,0===o?0:1),t.height=Math.max(Qa(n+a,s,!1)-t.y,0===a?0:1)),t}}(ts,e,this.style)).x,n=r.y,o=r.width,a=r.height,r.r=e.r,e=r):(i=e.x,n=e.y,o=e.width,a=e.height),e.r?(s=t,d=(h=e).x,y=h.y,v=h.width,g=h.height,_=h.r,v<0&&(d+=v,v=-v),g<0&&(y+=g,g=-g),"number"==typeof _?l=u=c=p=_:_ instanceof Array?1===_.length?l=u=c=p=_[0]:2===_.length?(l=c=_[0],u=p=_[1]):3===_.length?(l=_[0],u=p=_[1],c=_[2]):(l=_[0],u=_[1],c=_[2],p=_[3]):l=u=c=p=0,v<l+u&&(l*=v/(f=l+u),u*=v/f),v<c+p&&(c*=v/(f=c+p),p*=v/f),g<u+c&&(u*=g/(f=u+c),c*=g/f),g<l+p&&(l*=g/(f=l+p),p*=g/f),s.moveTo(d+l,y),s.lineTo(d+v-u,y),0!==u&&s.arc(d+v-u,y+u,u,-Math.PI/2,0),s.lineTo(d+v,y+g-c),0!==c&&s.arc(d+v-c,y+g-c,c,0,Math.PI/2),s.lineTo(d+p,y+g),0!==p&&s.arc(d+p,y+g-p,p,Math.PI/2,Math.PI),s.lineTo(d,y+l),0!==l&&s.arc(d+l,y+l,l,Math.PI,1.5*Math.PI)):t.rect(i,n,o,a)},rs.prototype.isZeroArea=function(){return!this.shape.width||!this.shape.height},rs);function rs(t){return $a.call(this,t)||this}es.prototype.type="rect";var is,ns=function(){this.cx=0,this.cy=0,this.rx=0,this.ry=0},os=(vt(as,is=va),as.prototype.getDefaultShape=function(){return new ns},as.prototype.buildPath=function(t,e){var r=e.cx,i=e.cy,n=e.rx,o=e.ry,a=.5522848*n,s=.5522848*o;t.moveTo(r-n,i),t.bezierCurveTo(r-n,i-s,r-a,i-o,r,i-o),t.bezierCurveTo(r+a,i-o,r+n,i-s,r+n,i),t.bezierCurveTo(r+n,i+s,r+a,i+o,r,i+o),t.bezierCurveTo(r-a,i+o,r-n,i+s,r-n,i),t.closePath()},as);function as(t){return is.call(this,t)||this}os.prototype.type="ellipse";var ss,hs={},ls=function(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1},us=(vt(cs,ss=va),cs.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},cs.prototype.getDefaultShape=function(){return new ls},cs.prototype.buildPath=function(t,e){var r,i,n,o,a;a=this.subPixelOptimize?(i=(r=function(t,e,r){if(e){var i=e.x1,n=e.x2,o=e.y1,a=e.y2;t.x1=i,t.x2=n,t.y1=o,t.y2=a;var s=r&&r.lineWidth;return s&&(Ka(2*i)===Ka(2*n)&&(t.x1=t.x2=Qa(i,s,!0)),Ka(2*o)===Ka(2*a)&&(t.y1=t.y2=Qa(o,s,!0))),t}}(hs,e,this.style)).x1,n=r.y1,o=r.x2,r.y2):(i=e.x1,n=e.y1,o=e.x2,e.y2);var s=e.percent;0!==s&&(t.moveTo(i,n),s<1&&(o=i*(1-s)+o*s,a=n*(1-s)+a*s),t.lineTo(o,a))},cs.prototype.pointAt=function(t){var e=this.shape;return[e.x1*(1-t)+e.x2*t,e.y1*(1-t)+e.y2*t]},cs);function cs(t){return ss.call(this,t)||this}function ps(t,e,r){var i=e.smooth,n=e.points;if(n&&2<=n.length){if(i){var o=function(t,e,r,i){var n,o,a,s,h=[],l=[],u=[],c=[];if(i){a=[1/0,1/0],s=[-1/0,-1/0];for(var p=0,f=t.length;p<f;p++)Lt(a,a,t[p]),zt(s,s,t[p]);Lt(a,a,i[0]),zt(s,s,i[1])}for(p=0,f=t.length;p<f;p++){var d=t[p];if(r)n=t[p?p-1:f-1],o=t[(p+1)%f];else{if(0===p||p===f-1){h.push(_t(t[p]));continue}n=t[p-1],o=t[p+1]}xt(l,o,n),St(l,l,e);var y=kt(d,n),v=kt(d,o),g=y+v;0!==g&&(y/=g,v/=g),St(u,l,-y),St(c,l,v);var _=mt([],d,u),m=mt([],d,c);i&&(zt(_,_,a),Lt(_,_,s),zt(m,m,a),Lt(m,m,s)),h.push(_),h.push(m)}return r&&h.push(h.shift()),h}(n,i,r,e.smoothConstraint);t.moveTo(n[0][0],n[0][1]);for(var a=n.length,s=0;s<(r?a:a-1);s++){var h=o[2*s],l=o[2*s+1],u=n[(s+1)%a];t.bezierCurveTo(h[0],h[1],l[0],l[1],u[0],u[1])}}else{t.moveTo(n[0][0],n[0][1]);for(var s=1,c=n.length;s<c;s++)t.lineTo(n[s][0],n[s][1])}r&&t.closePath()}}us.prototype.type="line";var fs,ds=function(){this.points=null,this.smooth=0,this.smoothConstraint=null},ys=(vt(vs,fs=va),vs.prototype.getDefaultShape=function(){return new ds},vs.prototype.buildPath=function(t,e){ps(t,e,!0)},vs);function vs(t){return fs.call(this,t)||this}ys.prototype.type="polygon";var gs,_s=function(){this.points=null,this.percent=1,this.smooth=0,this.smoothConstraint=null},ms=(vt(xs,gs=va),xs.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},xs.prototype.getDefaultShape=function(){return new _s},xs.prototype.buildPath=function(t,e){ps(t,e,!1)},xs);function xs(t){return gs.call(this,t)||this}ms.prototype.type="polyline";var ws=(bs.prototype.addColorStop=function(t,e){this.colorStops.push({offset:t,color:e})},bs);function bs(t){this.colorStops=t||[]}var Ss,Ts=(vt(ks,Ss=ws),ks);function ks(t,e,r,i,n,o){var a=Ss.call(this,n)||this;return a.x=null==t?0:t,a.y=null==e?0:e,a.x2=null==r?1:r,a.y2=null==i?0:i,a.type="linear",a.global=o||!1,a}var Cs,Ps=(vt(Ms,Cs=ws),Ms);function Ms(t,e,r,i,n){var o=Cs.call(this,i)||this;return o.x=null==t?.5:t,o.y=null==e?.5:e,o.r=null==r?.5:r,o.type="radial",o.global=n||!1,o}var As=new Nr(50);function Ds(t,e,r,i,n){if(t){if("string"!=typeof t)return t;if(e&&e.__zrImageSrc===t||!r)return e;var o=As.get(t),a={hostEl:r,cb:i,cbPayload:n};return o?zs(e=o.image)||o.pending.push(a):((e=f.loadImage(t,Ls,Ls)).__zrImageSrc=t,As.put(t,e.__cachedImgObj={image:e,pending:[a]})),e}return e}function Ls(){var t=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var e=0;e<t.pending.length;e++){var r=t.pending[e],i=r.cb;i&&i(this,r.cbPayload),r.hostEl.dirty()}t.pending.length=0}function zs(t){return t&&t.width&&t.height}var Is=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g;function Rs(t,e,r,i){var n=I({},i=i||{});r=Z(r,"..."),n.maxIterations=Z(i.maxIterations,2);var o=n.minChar=Z(i.minChar,0),a=n.fontMeasureInfo=Mn(e),s=a.asciiCharWidth;n.placeholder=Z(i.placeholder,"");for(var h=t=Math.max(0,t-1),l=0;l<o&&s<=h;l++)h-=s;var u=zn(a,r);return h<u&&(r="",u=0),h=t-u,n.ellipsis=r,n.ellipsisWidth=u,n.contentWidth=h,n.containerWidth=t,n}function Os(t,e,r){var i=r.containerWidth,n=r.contentWidth,o=r.fontMeasureInfo;if(!i)return t.textLine="",void(t.isTruncated=!1);if((h=zn(o,e))<=i)return t.textLine=e,void(t.isTruncated=!1);for(var a=0;;a++){if(h<=n||a>=r.maxIterations){e+=r.ellipsis;break}var s=0===a?function(t,e,r){for(var i=0,n=0,o=t.length;n<o&&i<e;n++)i+=Ln(r,t.charCodeAt(n));return n}(e,n,o):0<h?Math.floor(e.length*n/h):0,h=zn(o,e=e.substr(0,s))}""===e&&(e=r.placeholder),t.textLine=e,t.isTruncated=!0}var Fs=function(){},Bs=function(t){this.tokens=[],t&&(this.tokens=t)},Ns=function(){this.width=0,this.height=0,this.contentWidth=0,this.contentHeight=0,this.outerWidth=0,this.outerHeight=0,this.lines=[],this.isTruncated=!1};function Hs(t,e,r,i,n){var o=new Ns,a=Vs(t);if(!a)return o;var s=e.padding,h=s?s[1]+s[3]:0,l=s?s[0]+s[2]:0,u=e.width;null==u&&null!=r&&(u=r-h);var c=e.height;null==c&&null!=i&&(c=i-l);for(var p,f=e.overflow,d="break"!==f&&"breakAll"!==f||null==u?null:{width:u,accumWidth:0,breakAll:"breakAll"===f},y=Is.lastIndex=0;null!=(p=Is.exec(a));){var v=p.index;y<v&&Ws(o,a.substring(y,v),e,d),Ws(o,p[2],e,d,p[1]),y=Is.lastIndex}y<a.length&&Ws(o,a.substring(y,a.length),e,d);var g=[],_=0,m=0,x="truncate"===f,w="truncate"===e.lineOverflow,b={};function S(t,e,r){t.width=e,t.lineHeight=r,_+=r,m=Math.max(m,e)}t:for(var T=0;T<o.lines.length;T++){for(var k=o.lines[T],C=0,P=0,M=0;M<k.tokens.length;M++){var A=(W=k.tokens[M]).styleName&&e.rich[W.styleName]||{},D=W.textPadding=A.padding,L=D?D[1]+D[3]:0,z=W.font=A.font||e.font;W.contentHeight=On(z);var I=Z(A.height,W.contentHeight);if(W.innerHeight=I,D&&(I+=D[0]+D[2]),W.height=I,W.lineHeight=K(A.lineHeight,e.lineHeight,I),W.align=A&&A.align||n,W.verticalAlign=A&&A.verticalAlign||"middle",w&&null!=c&&_+W.lineHeight>c){var R=o.lines.length;0<M?(k.tokens=k.tokens.slice(0,M),S(k,P,C),o.lines=o.lines.slice(0,T+1)):o.lines=o.lines.slice(0,T),o.isTruncated=o.isTruncated||o.lines.length<R;break t}var O,F,B,N=A.width,H=null==N||"auto"===N;"string"==typeof N&&"%"===N.charAt(N.length-1)?(W.percentWidth=N,g.push(W),W.contentWidth=zn(Mn(z),W.text)):(!H||(F=(O=A.backgroundColor)&&O.image)&&zs(F=function(t){if("string"!=typeof t)return t;var e=As.get(t);return e&&e.image}(F))&&(W.width=Math.max(W.width,F.width*I/F.height)),null!=(B=x&&null!=u?u-P:null)&&B<W.width?!H||B<L?(W.text="",W.width=W.contentWidth=0):(function(t,e,r,i,n,o){if(!r)return t.text="",t.isTruncated=!1;var a=(e+"").split("\n");o=Rs(r,i,n,o);for(var s=!1,h={},l=0,u=a.length;l<u;l++)Os(h,a[l],o),a[l]=h.textLine,s=s||h.isTruncated;t.text=a.join("\n"),t.isTruncated=s}(b,W.text,B-L,z,e.ellipsis,{minChar:e.truncateMinChar}),W.text=b.text,o.isTruncated=o.isTruncated||b.isTruncated,W.width=W.contentWidth=zn(Mn(z),W.text)):W.contentWidth=zn(Mn(z),W.text)),W.width+=L,P+=W.width,A&&(C=Math.max(C,W.lineHeight))}S(k,P,C)}o.outerWidth=o.width=Z(u,m),o.outerHeight=o.height=Z(c,_),o.contentHeight=_,o.contentWidth=m,o.outerWidth+=h,o.outerHeight+=l;for(T=0;T<g.length;T++){var W,E=(W=g[T]).percentWidth;W.width=parseInt(E,10)/100*o.width}return o}function Ws(t,e,r,i,n){var o,a,s,h,l,u,c=""===e,p=n&&r.rich[n]||{},f=t.lines,d=p.font||r.font,y=!1;i&&(h=(s=p.padding)?s[1]+s[3]:0,null!=p.width&&"auto"!==p.width?(l=Fn(p.width,i.width)+h,0<f.length&&l+i.accumWidth>i.width&&(o=e.split("\n"),y=!0),i.accumWidth=l):(u=Xs(e,d,i.width,i.breakAll,i.accumWidth),i.accumWidth=u.accumWidth+h,a=u.linesWidths,o=u.lines)),o=o||e.split("\n");for(var v=Mn(d),g=0;g<o.length;g++){var _,m,x=o[g],w=new Fs;w.styleName=n,w.text=x,w.isLineHolder=!x&&!c,w.width="number"==typeof p.width?p.width:a?a[g]:zn(v,x),g||y?f.push(new Bs([w])):1===(m=(_=(f[f.length-1]||(f[0]=new Bs)).tokens).length)&&_[0].isLineHolder?_[0]=w:!x&&m&&!c||_.push(w)}}var Es=L(",&?/;] ".split(""),function(t,e){return t[e]=!0,t},{});function Xs(t,e,r,i,n){for(var o,a,s=[],h=[],l="",u="",c=0,p=0,f=Mn(e),d=0;d<t.length;d++){var y,v,g=t.charAt(d);"\n"!==g?(y=Ln(f,g.charCodeAt(0)),v=!i&&(a=void 0,!(!(32<=(a=(o=g).charCodeAt(0))&&a<=591||880<=a&&a<=4351||4608<=a&&a<=5119||7680<=a&&a<=8303)||Es[o])),(s.length?r<p+y:r<n+p+y)?p?(l||u)&&(p=v?(l||(l=u,u="",p=c=0),s.push(l),h.push(p-c),u+=g,l="",c+=y):(u&&(l+=u,u="",c=0),s.push(l),h.push(p),l=g,y)):v?(s.push(u),h.push(c),u=g,c=y):(s.push(g),h.push(y)):(p+=y,v?(u+=g,c+=y):(u&&(l+=u,u="",c=0),l+=g))):(u&&(l+=u,p+=c),s.push(l),h.push(p),u=l="",p=c=0)}return u&&(l+=u),l&&(s.push(l),h.push(p)),1===s.length&&(p+=n),{accumWidth:p,lines:s,linesWidths:h}}function Ys(t,e,r,i,n,o){var a,s,h;t.baseX=r,t.baseY=i,t.outerWidth=t.outerHeight=null,e&&(a=2*e.width,s=2*e.height,Ae.set(qs,In(r,a,n),Rn(i,s,o),a,s),Ae.intersect(e,qs,null,js),h=js.outIntersectRect,t.outerWidth=h.width,t.outerHeight=h.height,t.baseX=In(h.x,h.width,n,!0),t.baseY=Rn(h.y,h.height,o,!0))}var qs=new Ae(0,0,0,0),js={outIntersectRect:{},clamp:!0};function Vs(t){return null!=t?t+="":t=""}function Us(t,e,r,i){var n=new Ae(In(t.x||0,e,t.textAlign),Rn(t.y||0,r,t.textBaseline),e,r),o=null!=i?i:Gs(t)?t.lineWidth:0;return 0<o&&(n.x-=o/2,n.y-=o/2,n.width+=o,n.height+=o),n}function Gs(t){var e=t.stroke;return null!=e&&"none"!==e&&0<t.lineWidth}var Zs,Ks,Qs=T({strokeFirst:!0,font:W,x:0,y:0,textAlign:"left",textBaseline:"top",miterLimit:2},fa),$s=(vt(Js,Zs=fo),Js.prototype.hasStroke=function(){return Gs(this.style)},Js.prototype.hasFill=function(){var t=this.style.fill;return null!=t&&"none"!==t},Js.prototype.createStyle=function(t){return ht(Qs,t)},Js.prototype.setBoundingRect=function(t){this._rect=t},Js.prototype.getBoundingRect=function(){var t,e,r;return this._rect||(this._rect=(t=this.style,e=Vs(t.text),r=t.font,Us(t,zn(Mn(r),e),On(r),null))),this._rect},Js.initDefaultProps=void(Js.prototype.dirtyRectTolerance=10),Js);function Js(){return null!==Zs&&Zs.apply(this,arguments)||this}$s.prototype.type="tspan";var th={fill:"fill",stroke:"stroke","stroke-width":"lineWidth",opacity:"opacity","fill-opacity":"fillOpacity","stroke-opacity":"strokeOpacity","stroke-dasharray":"lineDash","stroke-dashoffset":"lineDashOffset","stroke-linecap":"lineCap","stroke-linejoin":"lineJoin","stroke-miterlimit":"miterLimit","font-family":"fontFamily","font-size":"fontSize","font-style":"fontStyle","font-weight":"fontWeight","text-anchor":"textAlign",visibility:"visibility",display:"display"},eh=F(th),rh={"alignment-baseline":"textBaseline","stop-color":"stopColor"},ih=F(rh),nh=(oh.prototype.parse=function(t,e){e=e||{};var r=function(t){N(t)&&(t=(new DOMParser).parseFromString(t,"text/xml"));var e=t;for(9===e.nodeType&&(e=e.firstChild);"svg"!==e.nodeName.toLowerCase()||1!==e.nodeType;)e=e.nextSibling;return e}(t);this._defsUsePending=[];var i=new Qn;this._root=i;var n=[],o=r.getAttribute("viewBox")||"",a=parseFloat(r.getAttribute("width")||e.width),s=parseFloat(r.getAttribute("height")||e.height);isNaN(a)&&(a=null),isNaN(s)&&(s=null),ch(r,i,null,!0,!1);for(var h,l,u,c,p,f,d,y,v,g=r.firstChild;g;)this._parseNode(g,i,n,null,!1,!1),g=g.nextSibling;return function(t,e){for(var r=0;r<e.length;r++){var i=e[r];i[0].style[i[1]]=t[i[2]]}}(this._defs,this._defsUsePending),this._defsUsePending=[],!o||4<=(u=yh(o)).length&&(h={x:parseFloat(u[0]||0),y:parseFloat(u[1]||0),width:parseFloat(u[2]),height:parseFloat(u[3])}),h&&null!=a&&null!=s&&(d=(f={x:0,y:0,width:a,height:s}).width/(p=h).width,y=f.height/p.height,l={scale:v=Math.min(d,y),x:-(p.x+p.width/2)*v+(f.x+f.width/2),y:-(p.y+p.height/2)*v+(f.y+f.height/2)},e.ignoreViewBox||(c=i,(i=new Qn).add(c),c.scaleX=c.scaleY=l.scale,c.x=l.x,c.y=l.y)),e.ignoreRootClip||null==a||null==s||i.setClipPath(new es({shape:{x:0,y:0,width:a,height:s}})),{root:i,width:a,height:s,viewBoxRect:h,viewBoxTransform:l,named:n}},oh.prototype._parseNode=function(t,e,r,i,n,o){var a,s,h,l,u,c,p,f=t.nodeName.toLowerCase(),d=i;if("defs"===f&&(n=!0),"text"===f&&(o=!0),"defs"===f||"switch"===f?a=e:(n||(s=Ks[f])&&ut(Ks,f)&&(a=s.call(this,t,e),(h=t.getAttribute("name"))?(l={name:h,namedFrom:null,svgNodeTagLower:f,el:a},r.push(l),"g"===f&&(d=l)):i&&r.push({name:i.name,namedFrom:i,svgNodeTagLower:f,el:a}),e.add(a)),(u=ah[f])&&ut(ah,f)&&(c=u.call(this,t),(p=t.getAttribute("id"))&&(this._defs[p]=c))),a&&a.isGroup)for(var y=t.firstChild;y;)1===y.nodeType?this._parseNode(y,a,r,d,n,o):3===y.nodeType&&o&&this._parseText(y,a),y=y.nextSibling},oh.prototype._parseText=function(t,e){var r=new $s({style:{text:t.textContent},silent:!0,x:this._textX||0,y:this._textY||0});lh(e,r),ch(t,r,this._defsUsePending,!1,!1),function(t,e){var r,i,n=e.__selfStyle;n&&(r=n.textBaseline,(i=r)&&"auto"!==r&&"baseline"!==r?"before-edge"===r||"text-before-edge"===r?i="top":"after-edge"===r||"text-after-edge"===r?i="bottom":"central"!==r&&"mathematical"!==r||(i="middle"):i="alphabetic",t.style.textBaseline=i);var o,a,s=e.__inheritedStyle;s&&(o=s.textAlign,(a=o)&&("middle"===o&&(a="center"),t.style.textAlign=a))}(r,e);var i=r.style,n=i.fontSize;n&&n<9&&(i.fontSize=9,r.scaleX*=n/9,r.scaleY*=n/9);var o=(i.fontSize||i.fontFamily)&&[i.fontStyle,i.fontWeight,(i.fontSize||12)+"px",i.fontFamily||"sans-serif"].join(" ");i.font=o;var a=r.getBoundingRect();return this._textX+=a.width,e.add(r),r},oh.internalField=void(Ks={g:function(t,e){var r=new Qn;return lh(e,r),ch(t,r,this._defsUsePending,!1,!1),r},rect:function(t,e){var r=new es;return lh(e,r),ch(t,r,this._defsUsePending,!1,!1),r.setShape({x:parseFloat(t.getAttribute("x")||"0"),y:parseFloat(t.getAttribute("y")||"0"),width:parseFloat(t.getAttribute("width")||"0"),height:parseFloat(t.getAttribute("height")||"0")}),r.silent=!0,r},circle:function(t,e){var r=new Ga;return lh(e,r),ch(t,r,this._defsUsePending,!1,!1),r.setShape({cx:parseFloat(t.getAttribute("cx")||"0"),cy:parseFloat(t.getAttribute("cy")||"0"),r:parseFloat(t.getAttribute("r")||"0")}),r.silent=!0,r},line:function(t,e){var r=new us;return lh(e,r),ch(t,r,this._defsUsePending,!1,!1),r.setShape({x1:parseFloat(t.getAttribute("x1")||"0"),y1:parseFloat(t.getAttribute("y1")||"0"),x2:parseFloat(t.getAttribute("x2")||"0"),y2:parseFloat(t.getAttribute("y2")||"0")}),r.silent=!0,r},ellipse:function(t,e){var r=new os;return lh(e,r),ch(t,r,this._defsUsePending,!1,!1),r.setShape({cx:parseFloat(t.getAttribute("cx")||"0"),cy:parseFloat(t.getAttribute("cy")||"0"),rx:parseFloat(t.getAttribute("rx")||"0"),ry:parseFloat(t.getAttribute("ry")||"0")}),r.silent=!0,r},polygon:function(t,e){var r,i=t.getAttribute("points");i&&(r=uh(i));var n=new ys({shape:{points:r||[]},silent:!0});return lh(e,n),ch(t,n,this._defsUsePending,!1,!1),n},polyline:function(t,e){var r,i=t.getAttribute("points");i&&(r=uh(i));var n=new ms({shape:{points:r||[]},silent:!0});return lh(e,n),ch(t,n,this._defsUsePending,!1,!1),n},image:function(t,e){var r=new qa;return lh(e,r),ch(t,r,this._defsUsePending,!1,!1),r.setStyle({image:t.getAttribute("xlink:href")||t.getAttribute("href"),x:+t.getAttribute("x"),y:+t.getAttribute("y"),width:+t.getAttribute("width"),height:+t.getAttribute("height")}),r.silent=!0,r},text:function(t,e){var r=t.getAttribute("x")||"0",i=t.getAttribute("y")||"0",n=t.getAttribute("dx")||"0",o=t.getAttribute("dy")||"0";this._textX=parseFloat(r)+parseFloat(n),this._textY=parseFloat(i)+parseFloat(o);var a=new Qn;return lh(e,a),ch(t,a,this._defsUsePending,!1,!0),a},tspan:function(t,e){var r=t.getAttribute("x"),i=t.getAttribute("y");null!=r&&(this._textX=parseFloat(r)),null!=i&&(this._textY=parseFloat(i));var n=t.getAttribute("dx")||"0",o=t.getAttribute("dy")||"0",a=new Qn;return lh(e,a),ch(t,a,this._defsUsePending,!1,!0),this._textX+=parseFloat(n),this._textY+=parseFloat(o),a},path:function(t,e){var r=Na(t.getAttribute("d")||"");return lh(e,r),ch(t,r,this._defsUsePending,!1,!1),r.silent=!0,r}}),oh);function oh(){this._defs={},this._root=null}var ah={lineargradient:function(t){var e=parseInt(t.getAttribute("x1")||"0",10),r=parseInt(t.getAttribute("y1")||"0",10),i=parseInt(t.getAttribute("x2")||"10",10),n=parseInt(t.getAttribute("y2")||"0",10),o=new Ts(e,r,i,n);return sh(t,o),hh(t,o),o},radialgradient:function(t){var e=parseInt(t.getAttribute("cx")||"0",10),r=parseInt(t.getAttribute("cy")||"0",10),i=parseInt(t.getAttribute("r")||"0",10),n=new Ps(e,r,i);return sh(t,n),hh(t,n),n}};function sh(t,e){"userSpaceOnUse"===t.getAttribute("gradientUnits")&&(e.global=!0)}function hh(t,e){for(var r,i,n,o,a,s,h=t.firstChild;h;){1===h.nodeType&&"stop"===h.nodeName.toLocaleLowerCase()&&(i=void 0,i=(r=h.getAttribute("offset"))&&0<r.indexOf("%")?parseInt(r,10)/100:r?parseFloat(r):0,mh(h,n={},n),o=n.stopColor||h.getAttribute("stop-color")||"#000000",!(a=n.stopOpacity||h.getAttribute("stop-opacity"))||(s=$r(o))&&s[3]&&(s[3]*=qr(a),o=oi(s,"rgba")),e.colorStops.push({offset:i,color:o})),h=h.nextSibling}}function lh(t,e){t&&t.__inheritedStyle&&(e.__inheritedStyle||(e.__inheritedStyle={}),T(e.__inheritedStyle,t.__inheritedStyle))}function uh(t){for(var e=yh(t),r=[],i=0;i<e.length;i+=2){var n=parseFloat(e[i]),o=parseFloat(e[i+1]);r.push([n,o])}return r}function ch(t,e,r,i,n){var o=e,a=o.__inheritedStyle=o.__inheritedStyle||{},s={};1===t.nodeType&&(function(t,e){var r=t.getAttribute("transform");if(r){r=r.replace(/,/g," ");var i=[],n=null;r.replace(vh,function(t,e,r){return i.push(e,r),""});for(var o=i.length-1;0<o;o-=2){var a=i[o],s=i[o-1],h=yh(a);switch(n=n||oe(),s){case"translate":le(n,n,[parseFloat(h[0]),parseFloat(h[1]||"0")]);break;case"scale":ce(n,n,[parseFloat(h[0]),parseFloat(h[1]||h[0])]);break;case"rotate":ue(n,n,-parseFloat(h[0])*gh,[parseFloat(h[1]||"0"),parseFloat(h[2]||"0")]);break;case"skewX":var l=Math.tan(parseFloat(h[0])*gh);he(n,[1,0,l,1,0,0],n);break;case"skewY":var u=Math.tan(parseFloat(h[0])*gh);he(n,[1,u,0,1,0,0],n);break;case"matrix":n[0]=parseFloat(h[0]),n[1]=parseFloat(h[1]),n[2]=parseFloat(h[2]),n[3]=parseFloat(h[3]),n[4]=parseFloat(h[4]),n[5]=parseFloat(h[5])}}e.setLocalTransform(n)}}(t,e),mh(t,a,s),i||function(t,e,r){for(var i=0;i<eh.length;i++){var n=eh[i];null!=(o=t.getAttribute(n))&&(e[th[n]]=o)}for(i=0;i<ih.length;i++){var o,n=ih[i];null!=(o=t.getAttribute(n))&&(r[rh[n]]=o)}}(t,a,s)),o.style=o.style||{},null!=a.fill&&(o.style.fill=fh(o,"fill",a.fill,r)),null!=a.stroke&&(o.style.stroke=fh(o,"stroke",a.stroke,r)),R(["lineWidth","opacity","fillOpacity","strokeOpacity","miterLimit","fontSize"],function(t){null!=a[t]&&(o.style[t]=parseFloat(a[t]))}),R(["lineDashOffset","lineCap","lineJoin","fontWeight","fontFamily","fontStyle","textAlign"],function(t){null!=a[t]&&(o.style[t]=a[t])}),n&&(o.__selfStyle=s),a.lineDash&&(o.style.lineDash=D(yh(a.lineDash),function(t){return parseFloat(t)})),"hidden"!==a.visibility&&"collapse"!==a.visibility||(o.invisible=!0),"none"===a.display&&(o.ignore=!0)}var ph=/^url\(\s*#(.*?)\)/;function fh(t,e,r,i){var n=r&&r.match(ph);if(!n)return"none"===r&&(r=null),r;var o=tt(n[1]);i.push([t,e,o])}var dh=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;function yh(t){return t.match(dh)||[]}var vh=/(translate|scale|rotate|skewX|skewY|matrix)\(([\-\s0-9\.eE,]*)\)/g,gh=Math.PI/180;var _h=/([^\s:;]+)\s*:\s*([^:;]+)/g;function mh(t,e,r){var i,n=t.getAttribute("style");if(n)for(_h.lastIndex=0;null!=(i=_h.exec(n));){var o=i[1],a=ut(th,o)?th[o]:null;a&&(e[a]=i[2]);var s=ut(rh,o)?rh[o]:null;s&&(r[s]=i[2])}}var xh=Math.PI,wh=2*xh,bh=Math.sin,Sh=Math.cos,Th=Math.acos,kh=Math.atan2,Ch=Math.abs,Ph=Math.sqrt,Mh=Math.max,Ah=Math.min,Dh=1e-4;function Lh(t,e,r,i,n,o,a){var s=t-r,h=e-i,l=(a?o:-o)/Ph(s*s+h*h),u=l*h,c=-l*s,p=t+u,f=e+c,d=r+u,y=i+c,v=(p+d)/2,g=(f+y)/2,_=d-p,m=y-f,x=_*_+m*m,w=n-o,b=p*y-d*f,S=(m<0?-1:1)*Ph(Mh(0,w*w*x-b*b)),T=(b*m-_*S)/x,k=(-b*_-m*S)/x,C=(b*m+_*S)/x,P=(-b*_+m*S)/x,M=T-v,A=k-g,D=C-v,L=P-g;return D*D+L*L<M*M+A*A&&(T=C,k=P),{cx:T,cy:k,x0:-u,y0:-c,x1:T*(n/w-1),y1:k*(n/w-1)}}function zh(t,e){var r,i,n,o,a,s,h,l,u,c,p,f,d,y,v,g,_,m,x,w,b,S,T,k,C,P,M,A,D,L,z,I,R,O,F,B,N,H,W,E,X,Y,q,j=Mh(e.r,0),V=Mh(e.r0||0,0),U=0<j;(U||0<V)&&(U||(j=V,V=0),j<V&&(i=j,j=V,V=i),n=e.startAngle,o=e.endAngle,isNaN(n)||isNaN(o)||(a=e.cx,s=e.cy,h=!!e.clockwise,l=Ch(o-n),Dh<(u=wh<l&&l%wh)&&(l=u),Dh<j?wh-Dh<l?(t.moveTo(a+j*Sh(n),s+j*bh(n)),t.arc(a,s,j,n,o,!h),Dh<V&&(t.moveTo(a+V*Sh(o),s+V*bh(o)),t.arc(a,s,V,o,n,h))):(F=O=R=I=z=L=v=y=W=H=N=B=d=f=p=c=void 0,g=j*Sh(n),_=j*bh(n),m=V*Sh(o),x=V*bh(o),(w=Dh<l)&&((b=e.cornerRadius)&&(c=(r=function(t){var e;if(G(t)){var r=t.length;if(!r)return t;e=1===r?[t[0],t[0],0,0]:2===r?[t[0],t[0],t[1],t[1]]:3===r?t.concat(t[2]):t}else e=[t,t,t,t];return e}(b))[0],p=r[1],f=r[2],d=r[3]),S=Ch(j-V)/2,B=Ah(S,f),N=Ah(S,d),H=Ah(S,c),W=Ah(S,p),L=y=Mh(B,N),z=v=Mh(H,W),(Dh<y||Dh<v)&&(I=j*Sh(o),R=j*bh(o),O=V*Sh(n),F=V*bh(n),l<xh&&((T=function(t,e,r,i,n,o,a,s){var h=r-t,l=i-e,u=a-n,c=s-o,p=c*h-u*l;if(!(p*p<Dh))return[t+(p=(u*(e-o)-c*(t-n))/p)*h,e+p*l]}(g,_,O,F,I,R,m,x))&&(k=g-T[0],C=_-T[1],P=I-T[0],M=R-T[1],A=1/bh(Th((k*P+C*M)/(Ph(k*k+C*C)*Ph(P*P+M*M)))/2),D=Ph(T[0]*T[0]+T[1]*T[1]),L=Ah(y,(j-D)/(1+A)),z=Ah(v,(V-D)/(A-1)))))),w?Dh<L?(E=Ah(f,L),X=Ah(d,L),Y=Lh(O,F,g,_,j,E,h),q=Lh(I,R,m,x,j,X,h),t.moveTo(a+Y.cx+Y.x0,s+Y.cy+Y.y0),L<y&&E===X?t.arc(a+Y.cx,s+Y.cy,L,kh(Y.y0,Y.x0),kh(q.y0,q.x0),!h):(0<E&&t.arc(a+Y.cx,s+Y.cy,E,kh(Y.y0,Y.x0),kh(Y.y1,Y.x1),!h),t.arc(a,s,j,kh(Y.cy+Y.y1,Y.cx+Y.x1),kh(q.cy+q.y1,q.cx+q.x1),!h),0<X&&t.arc(a+q.cx,s+q.cy,X,kh(q.y1,q.x1),kh(q.y0,q.x0),!h))):(t.moveTo(a+g,s+_),t.arc(a,s,j,n,o,!h)):t.moveTo(a+g,s+_),Dh<V&&w?Dh<z?(E=Ah(c,z),Y=Lh(m,x,I,R,V,-(X=Ah(p,z)),h),q=Lh(g,_,O,F,V,-E,h),t.lineTo(a+Y.cx+Y.x0,s+Y.cy+Y.y0),z<v&&E===X?t.arc(a+Y.cx,s+Y.cy,z,kh(Y.y0,Y.x0),kh(q.y0,q.x0),!h):(0<X&&t.arc(a+Y.cx,s+Y.cy,X,kh(Y.y0,Y.x0),kh(Y.y1,Y.x1),!h),t.arc(a,s,V,kh(Y.cy+Y.y1,Y.cx+Y.x1),kh(q.cy+q.y1,q.cx+q.x1),h),0<E&&t.arc(a+q.cx,s+q.cy,E,kh(q.y1,q.x1),kh(q.y0,q.x0),!h))):(t.lineTo(a+m,s+x),t.arc(a,s,V,o,n,h)):t.lineTo(a+m,s+x)):t.moveTo(a,s),t.closePath()))}var Ih,Rh=function(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0,this.cornerRadius=0},Oh=(vt(Fh,Ih=va),Fh.prototype.getDefaultShape=function(){return new Rh},Fh.prototype.buildPath=function(t,e){zh(t,e)},Fh.prototype.isZeroArea=function(){return this.shape.startAngle===this.shape.endAngle||this.shape.r===this.shape.r0},Fh);function Fh(t){return Ih.call(this,t)||this}Oh.prototype.type="sector";var Bh=Go.CMD;function Nh(t,e){return Math.abs(t-e)<1e-5}function Hh(t){var n,e,r,i=t.data,o=t.len(),a=[],s=0,h=0,l=0,u=0;function c(t,e){n&&2<n.length&&a.push(n),n=[t,e]}function p(t,e,r,i){Nh(t,r)&&Nh(e,i)||n.push(t,e,r,i,r,i)}for(var f,d,y,v,g,_,m,x,w,b,S,T,k,C,P,M,A,D,L,z=0;z<o;){var I=i[z++],R=1===z;switch(R&&(l=s=i[z],u=h=i[z+1],I!==Bh.L&&I!==Bh.C&&I!==Bh.Q||(n=[l,u])),I){case Bh.M:s=l=i[z++],h=u=i[z++],c(l,u);break;case Bh.L:p(s,h,X=i[z++],Y=i[z++]),s=X,h=Y;break;case Bh.C:n.push(i[z++],i[z++],i[z++],i[z++],s=i[z++],h=i[z++]);break;case Bh.Q:X=i[z++],Y=i[z++],e=i[z++],r=i[z++],n.push(s+2/3*(X-s),h+2/3*(Y-h),e+2/3*(X-e),r+2/3*(Y-r),e,r),s=e,h=r;break;case Bh.A:var O=i[z++],F=i[z++],B=i[z++],N=i[z++],H=i[z++],W=i[z++]+H;z+=1;var E=!i[z++],X=Math.cos(H)*B+O,Y=Math.sin(H)*N+F;R?c(l=X,u=Y):p(s,h,X,Y),s=Math.cos(W)*B+O,h=Math.sin(W)*N+F;for(var q=(E?-1:1)*Math.PI/2,j=H;E?W<j:j<W;j+=q){var V=E?Math.max(j+q,W):Math.min(j+q,W);f=j,d=V,y=O,v=F,g=B,_=N,L=D=A=M=P=C=k=T=S=b=w=x=m=void 0,m=Math.abs(d-f),x=4*Math.tan(m/4)/3,w=d<f?-1:1,b=Math.cos(f),S=Math.sin(f),T=Math.cos(d),k=Math.sin(d),C=b*g+y,P=S*_+v,M=T*g+y,A=k*_+v,D=g*x*w,L=_*x*w,n.push(C-D*S,P+L*b,M+D*k,A-L*T,M,A)}break;case Bh.R:l=s=i[z++],u=h=i[z++],X=l+i[z++],Y=u+i[z++],c(X,u),p(X,u,X,Y),p(X,Y,l,Y),p(l,Y,l,u),p(l,u,X,u);break;case Bh.Z:n&&p(s,h,l,u),s=l,h=u}}return n&&2<n.length&&a.push(n),a}function Wh(t,e){var r=Hh(t),i=[];e=e||1;for(var n=0;n<r.length;n++){var o=r[n],a=[],s=o[0],h=o[1];a.push(s,h);for(var l=2;l<o.length;){var u=o[l++],c=o[l++],p=o[l++],f=o[l++],d=o[l++],y=o[l++];!function t(e,r,i,n,o,a,s,h,l,u){var c,p,f,d,y,v,g,_,m,x,w,b,S,T,k;Nh(e,i)&&Nh(r,n)&&Nh(o,s)&&Nh(a,h)?l.push(s,h):(p=(c=2/u)*c,f=s-e,d=h-r,f/=y=Math.sqrt(f*f+d*d),d/=y,w=(_=o-s)*_+(m=a-h)*m,(x=(v=i-e)*v+(g=n-r)*g)<p&&w<p?l.push(s,h):(S=-f*_-d*m,x-(b=f*v+d*g)*b<p&&0<=b&&w-S*S<p&&0<=S?l.push(s,h):(k=[],Cr(e,i,o,s,.5,T=[]),Cr(r,n,a,h,.5,k),t(T[0],k[0],T[1],k[1],T[2],k[2],T[3],k[3],l,u),t(T[4],k[4],T[5],k[5],T[6],k[6],T[7],k[7],l,u))))}(s,h,u,c,p,f,d,y,a,e),s=d,h=y}i.push(a)}return i}function Eh(t,e,r){var i=t[e],n=t[1-e],o=Math.abs(i/n),a=Math.ceil(Math.sqrt(o*r)),s=Math.floor(r/a);0===s&&(s=1,a=r);for(var h=[],l=0;l<a;l++)h.push(s);var u=r-a*s;if(0<u)for(l=0;l<u;l++)h[l%a]+=1;return h}function Xh(t,e,r){for(var i=t.r0,n=t.r,o=t.startAngle,a=t.endAngle,s=Math.abs(a-o),h=s*n,l=n-i,u=h>Math.abs(l),c=Eh([h,l],u?0:1,e),p=(u?s:l)/c.length,f=0;f<c.length;f++)for(var d=(u?l:s)/c[f],y=0;y<c[f];y++){var v={};u?(v.startAngle=o+p*f,v.endAngle=o+p*(f+1),v.r0=i+d*y,v.r=i+d*(y+1)):(v.startAngle=o+d*y,v.endAngle=o+d*(y+1),v.r0=i+p*f,v.r=i+p*(f+1)),v.clockwise=t.clockwise,v.cx=t.cx,v.cy=t.cy,r.push(v)}}function Yh(t,e){var r=t[t.length-1];r&&r[0]===e[0]&&r[1]===e[1]||t.push(e)}function qh(t,e,r){for(var i=t.length,n=[],o=0;o<i;o++){var a=t[o],s=t[(o+1)%i],h=function(t,e,r,i,n,o,a,s){var h=r-t,l=i-e,u=a-n,c=s-o,p=u*l-h*c;if(Math.abs(p)<1e-6)return null;var f=((t-n)*c-u*(e-o))/p;return f<0||1<f?null:new de(f*h+t,f*l+e)}(a[0],a[1],s[0],s[1],e.x,e.y,r.x,r.y);h&&n.push({projPt:function(t,e,r){var i=new de;de.sub(i,r,e),i.normalize();var n=new de;return de.sub(n,t,e),n.dot(i)}(h,e,r),pt:h,idx:o})}if(n.length<2)return[{points:t},{points:t}];n.sort(function(t,e){return t.projPt-e.projPt});var l,u=n[0],c=n[n.length-1];c.idx<u.idx&&(l=u,u=c,c=l);for(var p=[u.pt.x,u.pt.y],f=[c.pt.x,c.pt.y],d=[p],y=[f],o=u.idx+1;o<=c.idx;o++)Yh(d,t[o].slice());Yh(d,f),Yh(d,p);for(o=c.idx+1;o<=u.idx+i;o++)Yh(y,t[o%i].slice());return Yh(y,p),Yh(y,f),[{points:d},{points:y}]}function jh(t){var e=t.points,r=[],i=[];Co(e,r,i);var n=new Ae(r[0],r[1],i[0]-r[0],i[1]-r[1]),o=n.width,a=n.height,s=n.x,h=n.y,l=new de,u=new de;return a<o?(l.x=u.x=s+o/2,l.y=h,u.y=h+a):(l.y=u.y=h+a/2,l.x=s,u.x=s+o),qh(e,l,u)}function Vh(t,e,r,i){var n,o;return 1===r?i.push(e):(n=Math.floor(r/2),o=t(e),Vh(t,o[0],n,i),Vh(t,o[1],r-n,i)),i}function Uh(t,e){var r,i=[],n=t.shape;switch(t.type){case"rect":!function(t,e,r){for(var i=t.width,n=t.height,o=n<i,a=Eh([i,n],o?0:1,e),s=o?"width":"height",h=o?"height":"width",l=o?"x":"y",u=o?"y":"x",c=t[s]/a.length,p=0;p<a.length;p++)for(var f=t[h]/a[p],d=0;d<a[p];d++){var y={};y[l]=p*c,y[u]=d*f,y[s]=c,y[h]=f,y.x+=t.x,y.y+=t.y,r.push(y)}}(n,e,i),r=es;break;case"sector":Xh(n,e,i),r=Oh;break;case"circle":Xh({r0:0,r:n.r,startAngle:0,endAngle:2*Math.PI,cx:n.cx,cy:n.cy},e,i),r=Oh;break;default:var o=t.getComputedTransform(),a=o?Math.sqrt(Math.max(o[0]*o[0]+o[1]*o[1],o[2]*o[2]+o[3]*o[3])):1,s=D(Wh(t.getUpdatedPathProxy(),a),function(t){for(var e=[],r=0;r<t.length;)e.push([t[r++],t[r++]]);return e}),h=s.length;if(0===h)Vh(jh,{points:s[0]},e,i);else if(h===e)for(var l=0;l<h;l++)i.push({points:s[l]});else{var u=0,c=D(s,function(t){var e=[],r=[];Co(t,e,r);var i=(r[1]-e[1])*(r[0]-e[0]);return u+=i,{poly:t,area:i}});c.sort(function(t,e){return e.area-t.area});for(var p=e,l=0;l<h;l++){var f=c[l];if(p<=0)break;var d=l===h-1?p:Math.ceil(f.area/u*e);d<0||(Vh(jh,{points:f.poly},d,i),p-=d)}}r=ys}if(!r)return function(t,e){for(var r=[],i=0;i<e;i++)r.push(Ha(t));return r}(t,e);for(var y,v,g=[],l=0;l<i.length;l++){var _=new r;_.setShape(i[l]),y=t,(v=_).setStyle(y.style),v.z=y.z,v.z2=y.z2,v.zlevel=y.zlevel,g.push(_)}return g}function Gh(t,e){for(var r=t.length,i=t[r-2],n=t[r-1],o=[],a=0;a<e.length;)o[a++]=i,o[a++]=n;return o}function Zh(t,e){for(var r,i,n,o=[],a=[],s=0;s<Math.max(t.length,e.length);s++){var h=t[s],l=e[s],u=void 0,c=void 0;h?l?(i=u=(r=function(t,e){var r=t.length,i=e.length;if(r===i)return[t,e];for(var n=[],o=[],a=r<i?t:e,s=Math.min(r,i),h=Math.abs(i-r)/6,l=(s-2)/6,u=Math.ceil(h/l)+1,c=[a[0],a[1]],p=h,f=2;f<s;){var d=a[f-2],y=a[f-1],v=a[f++],g=a[f++],_=a[f++],m=a[f++],x=a[f++],w=a[f++];if(p<=0)c.push(v,g,_,m,x,w);else{for(var b=Math.min(p,u-1)+1,S=1;S<=b;S++){var T=S/b;Cr(d,v,_,x,T,n),Cr(y,g,m,w,T,o),d=n[3],y=o[3],c.push(n[1],o[1],n[2],o[2],d,y),v=n[5],g=o[5],_=n[6],m=o[6]}p-=b-1}}return a===t?[c,e]:[t,c]}(h,l))[0],n=c=r[1]):(c=Gh(n||h,h),u=h):(u=Gh(i||l,l),c=l),o.push(u),a.push(c)}return[o,a]}function Kh(t){for(var e=0,r=0,i=0,n=t.length,o=0,a=n-2;o<n;a=o,o+=2){var s=t[a],h=t[a+1],l=t[o],u=t[o+1],c=s*u-l*h;e+=c,r+=(s+l)*c,i+=(h+u)*c}return 0===e?[t[0]||0,t[1]||0]:[r/e/3,i/e/3,e]}function Qh(t,e,r,i){for(var n,o=[],a=0;a<t.length;a++){var s=t[a],h=e[a],l=Kh(s),u=Kh(h);null==n&&(n=l[2]<0!=u[2]<0);var c=[],p=[],f=0,d=1/0,y=[],v=s.length;n&&(s=function(t){for(var e=[],r=t.length,i=0;i<r;i+=2)e[i]=t[r-i-2],e[i+1]=t[r-i-1];return e}(s));for(var g=6*function(t,e,r,i){for(var n=(t.length-2)/6,o=1/0,a=0,s=t.length,h=s-2,l=0;l<n;l++){for(var u=6*l,c=0,p=0;p<s;p+=2){var f=0===p?u:(u+p-2)%h+2,d=t[f]-r[0],y=t[1+f]-r[1],v=e[p]-i[0]-d,g=e[p+1]-i[1]-y;c+=v*v+g*g}c<o&&(o=c,a=l)}return a}(s,h,l,u),_=v-2,m=0;m<_;m+=2){var x=(g+m)%_+2;c[m+2]=s[x]-l[0],c[m+3]=s[1+x]-l[1]}if(c[0]=s[g]-l[0],c[1]=s[1+g]-l[1],0<r)for(var w=i/r,b=-i/2;b<=i/2;b+=w){for(var S=Math.sin(b),T=Math.cos(b),k=0,m=0;m<s.length;m+=2){var C=c[m],P=c[m+1],M=h[m]-u[0],A=h[m+1]-u[1],D=M*T-A*S,L=M*S+A*T,z=(y[m]=D)-C,I=(y[m+1]=L)-P;k+=z*z+I*I}if(k<d){d=k,f=b;for(var R=0;R<y.length;R++)p[R]=y[R]}}else for(var O=0;O<v;O+=2)p[O]=h[O]-u[0],p[O+1]=h[O+1]-u[1];o.push({from:c,to:p,fromCp:l,toCp:u,rotation:-f})}return o}function $h(t){return t.__isCombineMorphing}var Jh="__mOriginal_";function tl(t,e,r){var i=Jh+e,n=t[i]||t[e];t[i]||(t[i]=t[e]);var o=r.replace,a=r.after,s=r.before;t[e]=function(){var t,e=arguments;return s&&s.apply(this,e),t=o?o.apply(this,e):n.apply(this,e),a&&a.apply(this,e),t}}function el(t,e){var r=Jh+e;t[r]&&(t[e]=t[r],t[r]=null)}function rl(t,e){for(var r=0;r<t.length;r++)for(var i=t[r],n=0;n<i.length;){var o=i[n],a=i[n+1];i[n++]=e[0]*o+e[2]*a+e[4],i[n++]=e[1]*o+e[3]*a+e[5]}}function il(t,C){var e=t.getUpdatedPathProxy(),r=C.getUpdatedPathProxy(),i=Zh(Hh(e),Hh(r)),n=i[0],o=i[1],a=t.getComputedTransform(),s=C.getComputedTransform();a&&rl(n,a),s&&rl(o,s),tl(C,"updateTransform",{replace:function(){this.transform=null}}),C.transform=null;var P=Qh(n,o,10,Math.PI),M=[];tl(C,"buildPath",{replace:function(t){for(var e=C.__morphT,r=1-e,i=[],n=0;n<P.length;n++){var o=P[n],a=o.from,s=o.to,h=o.rotation*e,l=o.fromCp,u=o.toCp,c=Math.sin(h),p=Math.cos(h);At(i,l,u,e);for(var f=0;f<a.length;f+=2){var d=a[f],y=a[f+1],v=d*r+(x=s[f])*e,g=y*r+(w=s[f+1])*e;M[f]=v*p-g*c+i[0],M[f+1]=v*c+g*p+i[1]}var _=M[0],m=M[1];t.moveTo(_,m);for(f=2;f<a.length;){var x=M[f++],w=M[f++],b=M[f++],S=M[f++],T=M[f++],k=M[f++];_===x&&m===w&&b===T&&S===k?t.lineTo(T,k):t.bezierCurveTo(x,w,b,S,T,k),_=T,m=k}}}})}function nl(t,e,r){if(!t||!e)return e;var i=r.done,n=r.during;return il(t,e),e.__morphT=0,e.animateTo({__morphT:1},T({during:function(t){e.dirtyShape(),n&&n(t)},done:function(){el(e,"buildPath"),el(e,"updateTransform"),e.__morphT=-1,e.createPathProxy(),e.dirtyShape(),i&&i()}},r)),e}function ol(r){var o=1/0,a=1/0,s=-1/0,h=-1/0,t=D(r,function(t){var e=t.getBoundingRect(),r=t.getComputedTransform(),i=e.x+e.width/2+(r?r[4]:0),n=e.y+e.height/2+(r?r[5]:0);return o=Math.min(i,o),a=Math.min(n,a),s=Math.max(i,s),h=Math.max(n,h),[i,n]});return D(t,function(t,e){return{cp:t,z:function(t,e,r,i,n,o){t=n===r?0:Math.round(32767*(t-r)/(n-r)),e=o===i?0:Math.round(32767*(e-i)/(o-i));for(var a,s=0,h=32768;0<h;h/=2){var l=0,u=0;0<(t&h)&&(l=1),0<(e&h)&&(u=1),s+=h*h*(3*l^u),0===u&&(1===l&&(t=h-1-t,e=h-1-e),a=t,t=e,e=a)}return s}(t[0],t[1],o,a,s,h),path:r[e]}}).sort(function(t,e){return t.z-e.z}).map(function(t){return t.path})}function al(t){return Uh(t.path,t.count)}function sl(){return{fromIndividuals:[],toIndividuals:[],count:0}}var hl,ll=Object.freeze({__proto__:null,alignBezierCurves:Zh,centroid:Kh,isCombineMorphing:$h,isMorphing:function(t){return 0<=t.__morphT},morphPath:nl,combineMorph:function(e,i,t){var n=[];!function t(e){for(var r=0;r<e.length;r++){var i=e[r];$h(i)?t(i.childrenRef()):i instanceof va&&n.push(i)}}(e);var r=n.length;if(!r)return sl();var o=(t.dividePath||al)({path:i,count:r});if(o.length!==r)return console.error("Invalid morphing: unmatched splitted path"),sl();n=ol(n),o=ol(o);for(var a=t.done,s=t.during,h=t.individualDelay,l=new Sn,u=0;u<r;u++){var c=n[u],p=o[u];p.parent=i,p.copyTransform(l),h||il(c,p)}function f(t){for(var e=0;e<o.length;e++)o[e].addSelfToZr(t)}function d(){i.__isCombineMorphing=!1,i.__morphT=-1,i.childrenRef=null,el(i,"addSelfToZr"),el(i,"removeSelfFromZr")}i.__isCombineMorphing=!0,i.childrenRef=function(){return o},tl(i,"addSelfToZr",{after:function(t){f(t)}}),tl(i,"removeSelfFromZr",{after:function(t){for(var e=0;e<o.length;e++)o[e].removeSelfFromZr(t)}});var y=o.length;if(h)for(var v=y,g=function(){0===--v&&(d(),a&&a())},u=0;u<y;u++){var _=h?T({delay:(t.delay||0)+h(u,y,n[u],o[u]),done:g},t):t;nl(n[u],o[u],_)}else i.__morphT=0,i.animateTo({__morphT:1},T({during:function(t){for(var e=0;e<y;e++){var r=o[e];r.__morphT=i.__morphT,r.dirtyShape()}s&&s(t)},done:function(){d();for(var t=0;t<e.length;t++)el(e[t],"updateTransform");a&&a()}},t));return i.__zr&&f(i.__zr),{fromIndividuals:n,toIndividuals:o,count:y}},separateMorph:function(t,e,r){var i=e.length,n=[],o=r.dividePath||al;if($h(t)){!function t(e){for(var r=0;r<e.length;r++){var i=e[r];$h(i)?t(i.childrenRef()):i instanceof va&&n.push(i)}}(t.childrenRef());var a=n.length;if(a<i)for(var s=0,h=a;h<i;h++)n.push(Ha(n[s++%a]));n.length=i}else{n=o({path:t,count:i});for(var l=t.getComputedTransform(),h=0;h<n.length;h++)n[h].setLocalTransform(l);if(n.length!==i)return console.error("Invalid morphing: unmatched splitted path"),sl()}n=ol(n),e=ol(e);for(var u=r.individualDelay,h=0;h<i;h++){var c=u?T({delay:(r.delay||0)+u(h,i,n[h],e[h])},r):r;nl(n[h],e[h],c)}return{fromIndividuals:n,toIndividuals:e,count:e.length}},defaultDividePath:Uh}),ul=(vt(cl,hl=va),cl.prototype._updatePathDirty=function(){for(var t=this.shape.paths,e=this.shapeChanged(),r=0;r<t.length;r++)e=e||t[r].shapeChanged();e&&this.dirtyShape()},cl.prototype.beforeBrush=function(){this._updatePathDirty();for(var t=this.shape.paths||[],e=this.getGlobalScale(),r=0;r<t.length;r++)t[r].path||t[r].createPathProxy(),t[r].path.setScale(e[0],e[1],t[r].segmentIgnoreThreshold)},cl.prototype.buildPath=function(t,e){for(var r=e.paths||[],i=0;i<r.length;i++)r[i].buildPath(t,r[i].shape,!0)},cl.prototype.afterBrush=function(){for(var t=this.shape.paths||[],e=0;e<t.length;e++)t[e].pathUpdated()},cl.prototype.getBoundingRect=function(){return this._updatePathDirty.call(this),va.prototype.getBoundingRect.call(this)},cl);function cl(){var t=null!==hl&&hl.apply(this,arguments)||this;return t.type="compound",t}var pl,fl=[],dl=(vt(yl,pl=fo),yl.prototype.traverse=function(t,e){t.call(e,this)},yl.prototype.useStyle=function(){this.style={}},yl.prototype.getCursor=function(){return this._cursor},yl.prototype.innerAfterBrush=function(){this._cursor=this._displayables.length},yl.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.markRedraw(),this.notClear=!1},yl.prototype.clearTemporalDisplayables=function(){this._temporaryDisplayables=[]},yl.prototype.addDisplayable=function(t,e){e?this._temporaryDisplayables.push(t):this._displayables.push(t),this.markRedraw()},yl.prototype.addDisplayables=function(t,e){e=e||!1;for(var r=0;r<t.length;r++)this.addDisplayable(t[r],e)},yl.prototype.getDisplayables=function(){return this._displayables},yl.prototype.getTemporalDisplayables=function(){return this._temporaryDisplayables},yl.prototype.eachPendingDisplayable=function(t){for(var e=this._cursor;e<this._displayables.length;e++)t&&t(this._displayables[e]);for(e=0;e<this._temporaryDisplayables.length;e++)t&&t(this._temporaryDisplayables[e])},yl.prototype.update=function(){this.updateTransform();for(var t=this._cursor;t<this._displayables.length;t++)(e=this._displayables[t]).parent=this,e.update(),e.parent=null;for(var e,t=0;t<this._temporaryDisplayables.length;t++)(e=this._temporaryDisplayables[t]).parent=this,e.update(),e.parent=null},yl.prototype.getBoundingRect=function(){if(!this._rect){for(var t=new Ae(1/0,1/0,-1/0,-1/0),e=0;e<this._displayables.length;e++){var r=this._displayables[e],i=r.getBoundingRect().clone();r.needLocalTransform()&&i.applyTransform(r.getLocalTransform(fl)),t.union(i)}this._rect=t}return this._rect},yl.prototype.contain=function(t,e){var r=this.transformCoordToLocal(t,e);if(this.getBoundingRect().contain(r[0],r[1]))for(var i=0;i<this._displayables.length;i++)if(this._displayables[i].contain(t,e))return!0;return!1},yl);function yl(){var t=null!==pl&&pl.apply(this,arguments)||this;return t.notClear=!0,t.incremental=!0,t._displayables=[],t._temporaryDisplayables=[],t._cursor=0,t}var vl,gl={fill:"#000"},_l={},ml={style:T({fill:!0,stroke:!0,fillOpacity:!0,strokeOpacity:!0,lineWidth:!0,fontSize:!0,lineHeight:!0,width:!0,height:!0,textShadowColor:!0,textShadowBlur:!0,textShadowOffsetX:!0,textShadowOffsetY:!0,backgroundColor:!0,padding:!0,borderColor:!0,borderWidth:!0,borderRadius:!0},ho.style)},xl=(vt(wl,vl=fo),wl.prototype.childrenRef=function(){return this._children},wl.prototype.update=function(){vl.prototype.update.call(this),this.styleChanged()&&this._updateSubTexts();for(var t=0;t<this._children.length;t++){var e=this._children[t];e.zlevel=this.zlevel,e.z=this.z,e.z2=this.z2,e.culling=this.culling,e.cursor=this.cursor,e.invisible=this.invisible}},wl.prototype.updateTransform=function(){var t=this.innerTransformable;t?(t.updateTransform(),t.transform&&(this.transform=t.transform)):vl.prototype.updateTransform.call(this)},wl.prototype.getLocalTransform=function(t){var e=this.innerTransformable;return e?e.getLocalTransform(t):vl.prototype.getLocalTransform.call(this,t)},wl.prototype.getComputedTransform=function(){return this.__hostTarget&&(this.__hostTarget.getComputedTransform(),this.__hostTarget.updateInnerText(!0)),vl.prototype.getComputedTransform.call(this)},wl.prototype._updateSubTexts=function(){var t;this._childCursor=0,Ml(t=this.style),R(t.rich,Ml),this.style.rich?this._updateRichTexts():this._updatePlainTexts(),this._children.length=this._childCursor,this.styleUpdated()},wl.prototype.addSelfToZr=function(t){vl.prototype.addSelfToZr.call(this,t);for(var e=0;e<this._children.length;e++)this._children[e].__zr=t},wl.prototype.removeSelfFromZr=function(t){vl.prototype.removeSelfFromZr.call(this,t);for(var e=0;e<this._children.length;e++)this._children[e].__zr=null},wl.prototype.getBoundingRect=function(){if(this.styleChanged()&&this._updateSubTexts(),!this._rect){for(var t=new Ae(0,0,0,0),e=this._children,r=[],i=null,n=0;n<e.length;n++){var o=e[n],a=o.getBoundingRect(),s=o.getLocalTransform(r);s?(t.copy(a),t.applyTransform(s),(i=i||t.clone()).union(t)):(i=i||a.clone()).union(a)}this._rect=i||t}return this._rect},wl.prototype.setDefaultTextStyle=function(t){this._defaultStyle=t||gl},wl.prototype.setTextContent=function(t){},wl.prototype._mergeStyle=function(t,e){if(!e)return t;var r=e.rich,i=t.rich||r&&{};return I(t,e),r&&i?(this._mergeRich(i,r),t.rich=i):i&&(t.rich=i),t},wl.prototype._mergeRich=function(t,e){for(var r=F(e),i=0;i<r.length;i++){var n=r[i];t[n]=t[n]||{},I(t[n],e[n])}},wl.prototype.getAnimationStyleProps=function(){return ml},wl.prototype._getOrCreateChild=function(t){var e=this._children[this._childCursor];return e&&e instanceof t||(e=new t),(this._children[this._childCursor++]=e).__zr=this.__zr,e.parent=this,e},wl.prototype._updatePlainTexts=function(){var t=this.style,e=t.font||W,r=t.padding,i=this._defaultStyle,n=t.x||0,o=t.y||0,a=t.align||i.align||"left",s=t.verticalAlign||i.verticalAlign||"top";Ys(_l,i.overflowRect,n,o,a,s),n=_l.baseX,o=_l.baseY;var h=function(t,e,r,i){var n=Vs(t),o=e.overflow,a=e.padding,s=a?a[1]+a[3]:0,h=a?a[0]+a[2]:0,l=e.font,u="truncate"===o,c=On(l),p=Z(e.lineHeight,c),f="truncate"===e.lineOverflow,d=!1,y=e.width;null==y&&null!=r&&(y=r-s);var v=e.height;null==v&&null!=i&&(v=i-h);var g,_,m=(_=null==y||"break"!==o&&"breakAll"!==o?n?n.split("\n"):[]:n?Xs(n,e.font,y,"breakAll"===o,0).lines:[]).length*p;if(null==v&&(v=m),v<m&&f&&(g=Math.floor(v/p),d=d||_.length>g,m=(_=_.slice(0,g)).length*p),n&&u&&null!=y)for(var x=Rs(y,l,e.ellipsis,{minChar:e.truncateMinChar,placeholder:e.placeholder}),w={},b=0;b<_.length;b++)Os(w,_[b],x),_[b]=w.textLine,d=d||w.isTruncated;for(var S=v,T=0,k=Mn(l),b=0;b<_.length;b++)T=Math.max(zn(k,_[b]),T);null==y&&(y=T);var C=y;return{lines:_,height:v,outerWidth:C+=s,outerHeight:S+=h,lineHeight:p,calculatedLineHeight:c,contentWidth:T,contentHeight:m,width:y,isTruncated:d}}(zl(t),t,_l.outerWidth,_l.outerHeight),l=Il(t),u=!!t.backgroundColor,c=h.outerHeight,p=h.outerWidth,f=h.lines,d=h.lineHeight;this.isTruncated=!!h.isTruncated;var y,v,g=n,_=Rn(o,h.contentHeight,s);(l||r)&&(y=In(n,p,a),v=Rn(o,c,s),l&&this._renderBackground(t,t,y,v,p,c)),_+=d/2,r&&(g=Ll(n,a,r),"top"===s?_+=r[0]:"bottom"===s&&(_-=r[2]));for(var m=0,x=!1,w=!1,b=(Dl("fill"in t?t.fill:(w=!0,i.fill))),S=(Al("stroke"in t?t.stroke:u||i.autoStroke&&!w?null:(m=2,x=!0,i.stroke))),T=0<t.textShadowBlur,k=0;k<f.length;k++){var C=this._getOrCreateChild($s),P=C.createStyle();C.useStyle(P),P.text=f[k],P.x=g,P.y=_,a&&(P.textAlign=a),P.textBaseline="middle",P.opacity=t.opacity,P.strokeFirst=!0,T&&(P.shadowBlur=t.textShadowBlur||0,P.shadowColor=t.textShadowColor||"transparent",P.shadowOffsetX=t.textShadowOffsetX||0,P.shadowOffsetY=t.textShadowOffsetY||0),P.stroke=S,P.fill=b,S&&(P.lineWidth=t.lineWidth||m,P.lineDash=t.lineDash,P.lineDashOffset=t.lineDashOffset||0),P.font=e,Cl(P,t),_+=d,C.setBoundingRect(Us(P,h.contentWidth,h.calculatedLineHeight,x?0:null))}},wl.prototype._updateRichTexts=function(){var t=this.style,e=this._defaultStyle,r=t.align||e.align,i=t.verticalAlign||e.verticalAlign,n=t.x||0,o=t.y||0;Ys(_l,e.overflowRect,n,o,r,i),n=_l.baseX,o=_l.baseY;var a=Hs(zl(t),t,_l.outerWidth,_l.outerHeight,r),s=a.width,h=a.outerWidth,l=a.outerHeight,u=t.padding;this.isTruncated=!!a.isTruncated;var c=In(n,h,r),p=Rn(o,l,i),f=c,d=p;u&&(f+=u[3],d+=u[0]);var y=f+s;Il(t)&&this._renderBackground(t,t,c,p,h,l);for(var v=!!t.backgroundColor,g=0;g<a.lines.length;g++){for(var _=a.lines[g],m=_.tokens,x=m.length,w=_.lineHeight,b=_.width,S=0,T=f,k=y,C=x-1,P=void 0;S<x&&(!(P=m[S]).align||"left"===P.align);)this._placeToken(P,t,w,d,T,"left",v),b-=P.width,T+=P.width,S++;for(;0<=C&&"right"===(P=m[C]).align;)this._placeToken(P,t,w,d,k,"right",v),b-=P.width,k-=P.width,C--;for(T+=(s-(T-f)-(y-k)-b)/2;S<=C;)P=m[S],this._placeToken(P,t,w,d,T+P.width/2,"center",v),T+=P.width,S++;d+=w}},wl.prototype._placeToken=function(t,e,r,i,n,o,a){var s=e.rich[t.styleName]||{};s.text=t.text;var h=t.verticalAlign,l=i+r/2;"top"===h?l=i+t.height/2:"bottom"===h&&(l=i+r-t.height/2),!t.isLineHolder&&Il(s)&&this._renderBackground(s,e,"right"===o?n-t.width:"center"===o?n-t.width/2:n,l-t.height/2,t.width,t.height);var u=!!s.backgroundColor,c=t.textPadding;c&&(n=Ll(n,o,c),l-=t.height/2-c[0]-t.innerHeight/2);var p=this._getOrCreateChild($s),f=p.createStyle();p.useStyle(f);var d=this._defaultStyle,y=!1,v=0,g=!1,_=Dl("fill"in s?s.fill:"fill"in e?e.fill:(y=!0,d.fill)),m=Al("stroke"in s?s.stroke:"stroke"in e?e.stroke:u||a||d.autoStroke&&!y?null:(v=2,g=!0,d.stroke)),x=0<s.textShadowBlur||0<e.textShadowBlur;f.text=t.text,f.x=n,f.y=l,x&&(f.shadowBlur=s.textShadowBlur||e.textShadowBlur||0,f.shadowColor=s.textShadowColor||e.textShadowColor||"transparent",f.shadowOffsetX=s.textShadowOffsetX||e.textShadowOffsetX||0,f.shadowOffsetY=s.textShadowOffsetY||e.textShadowOffsetY||0),f.textAlign=o,f.textBaseline="middle",f.font=t.font||W,f.opacity=K(s.opacity,e.opacity,1),Cl(f,s),m&&(f.lineWidth=K(s.lineWidth,e.lineWidth,v),f.lineDash=Z(s.lineDash,e.lineDash),f.lineDashOffset=e.lineDashOffset||0,f.stroke=m),_&&(f.fill=_),p.setBoundingRect(Us(f,t.contentWidth,t.contentHeight,g?0:null))},wl.prototype._renderBackground=function(t,e,r,i,n,o){var a,s,h,l,u,c=t.backgroundColor,p=t.borderWidth,f=t.borderColor,d=c&&c.image,y=c&&!d,v=t.borderRadius,g=this;(y||t.lineHeight||p&&f)&&((a=this._getOrCreateChild(es)).useStyle(a.createStyle()),a.style.fill=null,(h=a.shape).x=r,h.y=i,h.width=n,h.height=o,h.r=v,a.dirtyShape()),y?((u=a.style).fill=c||null,u.fillOpacity=Z(t.fillOpacity,1)):d&&((s=this._getOrCreateChild(qa)).onload=function(){g.dirtyStyle()},(l=s.style).image=c.image,l.x=r,l.y=i,l.width=n,l.height=o),p&&f&&((u=a.style).lineWidth=p,u.stroke=f,u.strokeOpacity=Z(t.strokeOpacity,1),u.lineDash=t.borderDash,u.lineDashOffset=t.borderDashOffset||0,a.strokeContainThreshold=0,a.hasFill()&&a.hasStroke()&&(u.strokeFirst=!0,u.lineWidth*=2));var _=(a||s).style;_.shadowBlur=t.shadowBlur||0,_.shadowColor=t.shadowColor||"transparent",_.shadowOffsetX=t.shadowOffsetX||0,_.shadowOffsetY=t.shadowOffsetY||0,_.opacity=K(t.opacity,e.opacity,1)},wl.makeFont=function(t){var e="";return Pl(t)&&(e=[t.fontStyle,t.fontWeight,kl(t.fontSize),t.fontFamily||"sans-serif"].join(" ")),e&&tt(e)||t.textFont||t.font},wl);function wl(t){var e=vl.call(this)||this;return e.type="text",e._children=[],e._defaultStyle=gl,e.attr(t),e}var bl={left:!0,right:1,center:1},Sl={top:1,bottom:1,middle:1},Tl=["fontStyle","fontWeight","fontSize","fontFamily"];function kl(t){return"string"!=typeof t||-1===t.indexOf("px")&&-1===t.indexOf("rem")&&-1===t.indexOf("em")?isNaN(+t)?h+"px":t+"px":t}function Cl(t,e){for(var r=0;r<Tl.length;r++){var i=Tl[r],n=e[i];null!=n&&(t[i]=n)}}function Pl(t){return null!=t.fontSize||t.fontFamily||t.fontWeight}function Ml(t){var e,r;t&&(t.font=xl.makeFont(t),"middle"===(e=t.align)&&(e="center"),t.align=null==e||bl[e]?e:"left","center"===(r=t.verticalAlign)&&(r="middle"),t.verticalAlign=null==r||Sl[r]?r:"top",t.padding&&(t.padding=$(t.padding)))}function Al(t,e){return null==t||e<=0||"transparent"===t||"none"===t?null:t.image||t.colorStops?"#000":t}function Dl(t){return null==t||"none"===t?null:t.image||t.colorStops?"#000":t}function Ll(t,e,r){return"right"===e?t-r[1]:"center"===e?t+r[3]/2-r[1]/2:t+r[3]}function zl(t){var e=t.text;return null!=e&&(e+=""),e}function Il(t){return!!(t.backgroundColor||t.lineHeight||t.borderWidth&&t.borderColor)}var Rl,Ol=function(){this.cx=0,this.cy=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0},Fl=(vt(Bl,Rl=va),Bl.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},Bl.prototype.getDefaultShape=function(){return new Ol},Bl.prototype.buildPath=function(t,e){var r=e.cx,i=e.cy,n=Math.max(e.r,0),o=e.startAngle,a=e.endAngle,s=e.clockwise,h=Math.cos(o),l=Math.sin(o);t.moveTo(h*n+r,l*n+i),t.arc(r,i,n,o,a,!s)},Bl);function Bl(t){return Rl.call(this,t)||this}Fl.prototype.type="arc";var Nl=[],Hl=function(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.percent=1};function Wl(t,e,r){var i=t.cpx2,n=t.cpy2;return null!=i||null!=n?[(r?Sr:br)(t.x1,t.cpx1,t.cpx2,t.x2,e),(r?Sr:br)(t.y1,t.cpy1,t.cpy2,t.y2,e)]:[(r?Mr:Pr)(t.x1,t.cpx1,t.x2,e),(r?Mr:Pr)(t.y1,t.cpy1,t.y2,e)]}var El,Xl=(vt(Yl,El=va),Yl.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},Yl.prototype.getDefaultShape=function(){return new Hl},Yl.prototype.buildPath=function(t,e){var r=e.x1,i=e.y1,n=e.x2,o=e.y2,a=e.cpx1,s=e.cpy1,h=e.cpx2,l=e.cpy2,u=e.percent;0!==u&&(t.moveTo(r,i),null==h||null==l?(u<1&&(Dr(r,a,n,u,Nl),a=Nl[1],n=Nl[2],Dr(i,s,o,u,Nl),s=Nl[1],o=Nl[2]),t.quadraticCurveTo(a,s,n,o)):(u<1&&(Cr(r,a,h,n,u,Nl),a=Nl[1],h=Nl[2],n=Nl[3],Cr(i,s,l,o,u,Nl),s=Nl[1],l=Nl[2],o=Nl[3]),t.bezierCurveTo(a,s,h,l,n,o)))},Yl.prototype.pointAt=function(t){return Wl(this.shape,t,!1)},Yl.prototype.tangentAt=function(t){var e=Wl(this.shape,t,!0);return Tt(e,e)},Yl);function Yl(t){return El.call(this,t)||this}Xl.prototype.type="bezier-curve";var ql,jl=function(){this.cx=0,this.cy=0,this.width=0,this.height=0},Vl=(vt(Ul,ql=va),Ul.prototype.getDefaultShape=function(){return new jl},Ul.prototype.buildPath=function(t,e){var r=e.cx,i=e.cy,n=e.width,o=e.height;t.moveTo(r,i+n),t.bezierCurveTo(r+n,i+n,r+3*n/2,i-n/3,r,i-o),t.bezierCurveTo(r-3*n/2,i-n/3,r-n,i+n,r,i+n),t.closePath()},Ul);function Ul(t){return ql.call(this,t)||this}Vl.prototype.type="droplet";var Gl,Zl=function(){this.cx=0,this.cy=0,this.width=0,this.height=0},Kl=(vt(Ql,Gl=va),Ql.prototype.getDefaultShape=function(){return new Zl},Ql.prototype.buildPath=function(t,e){var r=e.cx,i=e.cy,n=e.width,o=e.height;t.moveTo(r,i),t.bezierCurveTo(r+n/2,i-2*o/3,r+2*n,i+o/3,r,i+o),t.bezierCurveTo(r-2*n,i+o/3,r-n/2,i-2*o/3,r,i)},Ql);function Ql(t){return Gl.call(this,t)||this}Kl.prototype.type="heart";var $l,Jl=Math.PI,tu=Math.sin,eu=Math.cos,ru=function(){this.x=0,this.y=0,this.r=0,this.n=0},iu=(vt(nu,$l=va),nu.prototype.getDefaultShape=function(){return new ru},nu.prototype.buildPath=function(t,e){var r=e.n;if(r&&!(r<2)){var i=e.x,n=e.y,o=e.r,a=2*Jl/r,s=-Jl/2;t.moveTo(i+o*eu(s),n+o*tu(s));for(var h=0,l=r-1;h<l;h++)s+=a,t.lineTo(i+o*eu(s),n+o*tu(s));t.closePath()}},nu);function nu(t){return $l.call(this,t)||this}iu.prototype.type="isogon";var ou,au=function(){this.cx=0,this.cy=0,this.r=0,this.r0=0},su=(vt(hu,ou=va),hu.prototype.getDefaultShape=function(){return new au},hu.prototype.buildPath=function(t,e){var r=e.cx,i=e.cy,n=2*Math.PI;t.moveTo(r+e.r,i),t.arc(r,i,e.r,0,n,!1),t.moveTo(r+e.r0,i),t.arc(r,i,e.r0,0,n,!0)},hu);function hu(t){return ou.call(this,t)||this}su.prototype.type="ring";var lu,uu=Math.sin,cu=Math.cos,pu=Math.PI/180,fu=function(){this.cx=0,this.cy=0,this.r=[],this.k=0,this.n=1},du=(vt(yu,lu=va),yu.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},yu.prototype.getDefaultShape=function(){return new fu},yu.prototype.buildPath=function(t,e){var r,i,n,o=e.r,a=e.k,s=e.n,h=e.cx,l=e.cy;t.moveTo(h,l);for(var u=0,c=o.length;u<c;u++){n=o[u];for(var p=0;p<=360*s;p++)r=n*uu(a/s*p%360*pu)*cu(p*pu)+h,i=n*uu(a/s*p%360*pu)*uu(p*pu)+l,t.lineTo(r,i)}},yu);function yu(t){return lu.call(this,t)||this}du.prototype.type="rose";var vu,gu=Math.PI,_u=Math.cos,mu=Math.sin,xu=function(){this.cx=0,this.cy=0,this.n=3,this.r=0},wu=(vt(bu,vu=va),bu.prototype.getDefaultShape=function(){return new xu},bu.prototype.buildPath=function(t,e){var r=e.n;if(r&&!(r<2)){var i=e.cx,n=e.cy,o=e.r,a=e.r0;null==a&&(a=4<r?o*_u(2*gu/r)/_u(gu/r):o/3);var s=gu/r,h=-gu/2,l=i+o*_u(h),u=n+o*mu(h);h+=s,t.moveTo(l,u);for(var c,p=0,f=2*r-1;p<f;p++)c=p%2==0?a:o,t.lineTo(i+c*_u(h),n+c*mu(h)),h+=s;t.closePath()}},bu);function bu(t){return vu.call(this,t)||this}wu.prototype.type="star";var Su,Tu=Math.cos,ku=Math.sin,Cu=function(){this.cx=0,this.cy=0,this.r=0,this.r0=0,this.d=0,this.location="out"},Pu=(vt(Mu,Su=va),Mu.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},Mu.prototype.getDefaultShape=function(){return new Cu},Mu.prototype.buildPath=function(t,e){var r,i,n=e.r,o=e.r0,a=e.d,s=e.cx,h=e.cy,l="out"===e.location?1:-1;if(!(e.location&&n<=o)){var u,c=0,p=1,f=(n+l*o)*Tu(0)-l*a*Tu(0)+s,d=(n+l*o)*ku(0)-a*ku(0)+h;for(t.moveTo(f,d);o*++c%(n+l*o)!=0;);for(;u=Math.PI/180*p,r=(n+l*o)*Tu(u)-l*a*Tu((n/o+l)*u)+s,i=(n+l*o)*ku(u)-a*ku((n/o+l)*u)+h,t.lineTo(r,i),++p<=o*c/(n+l*o)*360;);}},Mu);function Mu(t){return Su.call(this,t)||this}Pu.prototype.type="trochoid";function Au(t,e){this.image=t,this.repeat=e,this.x=0,this.y=0,this.rotation=0,this.scaleX=1,this.scaleY=1}var Du=Math.min,Lu=Math.max,zu=Math.abs,Iu=[0,0],Ru=[0,0],Ou=Re(),Fu=Ou.minTv,Bu=Ou.maxTv,Nu=(Hu.prototype.fromBoundingRect=function(t,e){var r=this._corners,i=this._axes,n=t.x,o=t.y,a=n+t.width,s=o+t.height;if(r[0].set(n,o),r[1].set(a,o),r[2].set(a,s),r[3].set(n,s),e)for(var h=0;h<4;h++)r[h].transform(e);for(de.sub(i[0],r[1],r[0]),de.sub(i[1],r[3],r[0]),i[0].normalize(),i[1].normalize(),h=0;h<2;h++)this._origin[h]=i[h].dot(r[0])},Hu.prototype.intersect=function(t,e,r){var i=!0,n=!e;return e&&de.set(e,0,0),Ou.reset(r,!n),!this._intersectCheckOneSide(this,t,n,1)&&(i=!1,n)||!this._intersectCheckOneSide(t,this,n,-1)&&(i=!1,n)||n||Ou.negativeSize||de.copy(e,i?Ou.useDir?Ou.dirMinTv:Fu:Bu),i},Hu.prototype._intersectCheckOneSide=function(t,e,r,i){for(var n=!0,o=0;o<2;o++){var a=t._axes[o];if(t._getProjMinMaxOnAxis(o,t._corners,Iu),t._getProjMinMaxOnAxis(o,e._corners,Ru),Ou.negativeSize||Iu[1]<Ru[0]||Ru[1]<Iu[0]){if(n=!1,Ou.negativeSize||r)return n;var s=zu(Ru[0]-Iu[1]),h=zu(Iu[0]-Ru[1]);Du(s,h)>Bu.len()&&(s<h?de.scale(Bu,a,-s*i):de.scale(Bu,a,h*i))}else r||(s=zu(Ru[0]-Iu[1]),h=zu(Iu[0]-Ru[1]),(Ou.useDir||Du(s,h)<Fu.len())&&((s<h||!Ou.bidirectional)&&(de.scale(Fu,a,s*i),Ou.useDir&&Ou.calcDirMTV()),(h<=s||!Ou.bidirectional)&&(de.scale(Fu,a,-h*i),Ou.useDir&&Ou.calcDirMTV())))}return n},Hu.prototype._getProjMinMaxOnAxis=function(t,e,r){for(var i=this._axes[t],n=this._origin,o=e[0].dot(i)+n[t],a=o,s=o,h=1;h<e.length;h++)var l=e[h].dot(i)+n[t],a=Du(l,a),s=Lu(l,s);r[0]=a+Ou.touchThreshold,r[1]=s-Ou.touchThreshold,Ou.negativeSize=r[1]<r[0]},Hu);function Hu(t,e){this._corners=[],this._axes=[],this._origin=[0,0];for(var r=0;r<4;r++)this._corners[r]=new de;for(r=0;r<2;r++)this._axes[r]=new de;t&&this.fromBoundingRect(t,e)}var Wu=(Eu.prototype.update=function(t){var e=this.dom.style;e.width=t.width+"px",e.height=t.height+"px",e.left=t.x+"px",e.top=t.y+"px"},Eu.prototype.hide=function(){this.dom.style.opacity="0"},Eu.prototype.show=function(t){var e=this;clearTimeout(this._hideTimeout),this.dom.style.opacity="1",this._hideTimeout=setTimeout(function(){e.hide()},t||1e3)},Eu);function Eu(t){var e=this.dom=document.createElement("div");for(var r in e.className="ec-debug-dirty-rect",t=I({},t),I(t,{backgroundColor:"rgba(0, 0, 255, 0.2)",border:"1px solid #00f"}),e.style.cssText="\nposition: absolute;\nopacity: 0;\ntransition: opacity 0.5s linear;\npointer-events: none;\n",t)t.hasOwnProperty(r)&&(e.style[r]=t[r])}function Xu(t){return isFinite(t)}function Yu(t,e,r){for(var i,n,o,a,s,h,l,u,c,p,f,d,y,v,g,_,m="radial"===e.type?(u=t,c=e,f=(p=r).width,d=p.height,y=Math.min(f,d),v=null==c.x?.5:c.x,g=null==c.y?.5:c.y,_=null==c.r?.5:c.r,c.global||(v=v*f+p.x,g=g*d+p.y,_*=y),v=Xu(v)?v:.5,g=Xu(g)?g:.5,_=0<=_&&Xu(_)?_:.5,u.createRadialGradient(v,g,0,v,g,_)):(i=t,o=r,a=null==(n=e).x?0:n.x,s=null==n.x2?1:n.x2,h=null==n.y?0:n.y,l=null==n.y2?0:n.y2,n.global||(a=a*o.width+o.x,s=s*o.width+o.x,h=h*o.height+o.y,l=l*o.height+o.y),a=Xu(a)?a:0,s=Xu(s)?s:1,h=Xu(h)?h:0,l=Xu(l)?l:0,i.createLinearGradient(a,h,s,l)),x=e.colorStops,w=0;w<x.length;w++)m.addColorStop(x[w].offset,x[w].color);return m}function qu(t){return parseInt(t,10)}function ju(t,e,r){var i=["width","height"][e],n=["clientWidth","clientHeight"][e],o=["paddingLeft","paddingTop"][e],a=["paddingRight","paddingBottom"][e];if(null!=r[i]&&"auto"!==r[i])return parseFloat(r[i]);var s=document.defaultView.getComputedStyle(t);return(t[n]||qu(s[i])||qu(t.style[i]))-(qu(s[o])||0)-(qu(s[a])||0)|0}function Vu(t){var e,r,i,n=t.style,o=n.lineDash&&0<n.lineWidth&&(e=n.lineDash,r=n.lineWidth,e&&"solid"!==e&&0<r?"dashed"===e?[4*r,2*r]:"dotted"===e?[r]:H(e)?[e]:G(e)?e:null:null),a=n.lineDashOffset;return!o||(i=n.strokeNoScale&&t.getLineScale?t.getLineScale():1)&&1!==i&&(o=D(o,function(t){return t/i}),a/=i),[o,a]}var Uu=new Go(!0);function Gu(t){var e=t.stroke;return!(null==e||"none"===e||!(0<t.lineWidth))}function Zu(t){return"string"==typeof t&&"none"!==t}function Ku(t){var e=t.fill;return null!=e&&"none"!==e}function Qu(t,e){var r;null!=e.fillOpacity&&1!==e.fillOpacity?(r=t.globalAlpha,t.globalAlpha=e.fillOpacity*e.opacity,t.fill(),t.globalAlpha=r):t.fill()}function $u(t,e){var r;null!=e.strokeOpacity&&1!==e.strokeOpacity?(r=t.globalAlpha,t.globalAlpha=e.strokeOpacity*e.opacity,t.stroke(),t.globalAlpha=r):t.stroke()}function Ju(t,e,r){var i=Ds(e.image,e.__image,r);if(zs(i)){var n,o=t.createPattern(i,e.repeat||"repeat");return"function"==typeof DOMMatrix&&o&&o.setTransform&&((n=new DOMMatrix).translateSelf(e.x||0,e.y||0),n.rotateSelf(0,0,(e.rotation||0)*pt),n.scaleSelf(e.scaleX||1,e.scaleY||1),o.setTransform(n)),o}}var tc=["shadowBlur","shadowOffsetX","shadowOffsetY"],ec=[["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]];function rc(t,e,r,i,n){var o,a=!1;if(!i&&e===(r=r||{}))return!1;!i&&e.opacity===r.opacity||(lc(t,n),a=!0,o=Math.max(Math.min(e.opacity,1),0),t.globalAlpha=isNaN(o)?so.opacity:o),!i&&e.blend===r.blend||(a||(lc(t,n),a=!0),t.globalCompositeOperation=e.blend||so.blend);for(var s=0;s<tc.length;s++){var h=tc[s];!i&&e[h]===r[h]||(a||(lc(t,n),a=!0),t[h]=t.dpr*(e[h]||0))}return!i&&e.shadowColor===r.shadowColor||(a||(lc(t,n),a=!0),t.shadowColor=e.shadowColor||so.shadowColor),a}function ic(t,e,r,i,n){var o=uc(e,n.inHover),a=i?null:r&&uc(r,n.inHover)||{};if(o!==a){var s,h=rc(t,o,a,i,n);!i&&o.fill===a.fill||(h||(lc(t,n),h=!0),Zu(o.fill)&&(t.fillStyle=o.fill)),!i&&o.stroke===a.stroke||(h||(lc(t,n),h=!0),Zu(o.stroke)&&(t.strokeStyle=o.stroke)),!i&&o.opacity===a.opacity||(h||(lc(t,n),h=!0),t.globalAlpha=null==o.opacity?1:o.opacity),e.hasStroke()&&(s=o.lineWidth/(o.strokeNoScale&&e.getLineScale?e.getLineScale():1),t.lineWidth!==s&&(h||(lc(t,n),h=!0),t.lineWidth=s));for(var l=0;l<ec.length;l++){var u=ec[l],c=u[0];!i&&o[c]===a[c]||(h||(lc(t,n),h=!0),t[c]=o[c]||u[1])}return h}}function nc(t,e){var r=e.transform,i=t.dpr||1;r?t.setTransform(i*r[0],i*r[1],i*r[2],i*r[3],i*r[4],i*r[5]):t.setTransform(i,0,0,i,0,0)}var oc=1,ac=2,sc=3,hc=4;function lc(t,e){e.batchFill&&t.fill(),e.batchStroke&&t.stroke(),e.batchFill="",e.batchStroke=""}function uc(t,e){return e&&t.__hoverStyle||t.style}function cc(t,e,r,i){var n=e.transform;if(!e.shouldBePainted(r.viewWidth,r.viewHeight,!1,!1))return e.__dirty&=~rr,void(e.__isRendered=!1);var o,a,s,h,l,u,c,p,f,d,y,v,g,_,m,x,w,b,S,T,k,C,P,M,A,D,L,z,I,R,O,F=e.__clipPaths,B=r.prevElClipPaths,N=!1,H=!1;B&&!function(t,e){if(t!==e&&(t||e)){if(!t||!e||t.length!==e.length)return 1;for(var r=0;r<t.length;r++)if(t[r]!==e[r])return 1}}(F,B)||(B&&B.length&&(lc(t,r),t.restore(),H=N=!0,r.prevElClipPaths=null,r.allClipped=!1,r.prevEl=null),F&&F.length&&(lc(t,r),t.save(),function(t,e,r){for(var i=!1,n=0;n<t.length;n++){var o=t[n],i=i||o.isZeroArea();nc(e,o),e.beginPath(),o.buildPath(e,o.shape),e.clip()}r.allClipped=i}(F,t,r),N=!0),r.prevElClipPaths=F),r.allClipped?e.__isRendered=!1:(e.beforeBrush&&e.beforeBrush(),e.innerBeforeBrush(),(o=r.prevEl)||(H=N=!0),a=e instanceof va&&e.autoBatch&&(s=e.style,h=Ku(s),l=Gu(s),!(s.lineDash||!(+h^+l)||h&&"string"!=typeof s.fill||l&&"string"!=typeof s.stroke||s.strokePercent<1||s.strokeOpacity<1||s.fillOpacity<1)),N||(u=n,c=o.transform,u&&c?u[0]!==c[0]||u[1]!==c[1]||u[2]!==c[2]||u[3]!==c[3]||u[4]!==c[4]||u[5]!==c[5]:u||c)?(lc(t,r),nc(t,e)):a||lc(t,r),p=uc(e,r.inHover),e instanceof va?(r.lastDrawType!==oc&&(H=!0,r.lastDrawType=oc),ic(t,e,o,H,r),a&&(r.batchFill||r.batchStroke)||t.beginPath(),function(t,e,r,i){var n,o=Gu(r),a=Ku(r),s=r.strokePercent,h=s<1,l=!e.path;e.silent&&!h||!l||e.createPathProxy();var u,c,p,f,d,y,v,g,_,m,x,w=e.path||Uu,b=e.__dirty;i||(u=r.fill,c=r.stroke,p=a&&!!u.colorStops,f=o&&!!c.colorStops,d=a&&!!u.image,y=o&&!!c.image,x=m=_=g=v=void 0,(p||f)&&(x=e.getBoundingRect()),p&&(v=b?Yu(t,u,x):e.__canvasFillGradient,e.__canvasFillGradient=v),f&&(g=b?Yu(t,c,x):e.__canvasStrokeGradient,e.__canvasStrokeGradient=g),d&&(_=b||!e.__canvasFillPattern?Ju(t,u,e):e.__canvasFillPattern,e.__canvasFillPattern=_),y&&(m=b||!e.__canvasStrokePattern?Ju(t,c,e):e.__canvasStrokePattern,e.__canvasStrokePattern=m),p?t.fillStyle=v:d&&(_?t.fillStyle=_:a=!1),f?t.strokeStyle=g:y&&(m?t.strokeStyle=m:o=!1));var S,T,k=e.getGlobalScale();w.setScale(k[0],k[1],e.segmentIgnoreThreshold),t.setLineDash&&r.lineDash&&(S=(n=Vu(e))[0],T=n[1]);var C=!0;(l||b&ir)&&(w.setDPR(t.dpr),h?w.setContext(null):(w.setContext(t),C=!1),w.reset(),e.buildPath(w,e.shape,i),w.toStatic(),e.pathUpdated()),C&&w.rebuildPath(t,h?s:1),S&&(t.setLineDash(S),t.lineDashOffset=T),i||(r.strokeFirst?(o&&$u(t,r),a&&Qu(t,r)):(a&&Qu(t,r),o&&$u(t,r))),S&&t.setLineDash([])}(t,e,p,a),a&&(r.batchFill=p.fill||"",r.batchStroke=p.stroke||"")):e instanceof $s?(r.lastDrawType!==sc&&(H=!0,r.lastDrawType=sc),ic(t,e,o,H,r),A=t,D=e,null!=(O=(L=p).text)&&(O+=""),O&&(A.font=L.font||W,A.textAlign=L.textAlign,A.textBaseline=L.textBaseline,R=I=void 0,A.setLineDash&&L.lineDash&&(I=(z=Vu(D))[0],R=z[1]),I&&(A.setLineDash(I),A.lineDashOffset=R),L.strokeFirst?(Gu(L)&&A.strokeText(O,L.x,L.y),Ku(L)&&A.fillText(O,L.x,L.y)):(Ku(L)&&A.fillText(O,L.x,L.y),Gu(L)&&A.strokeText(O,L.x,L.y)),I&&A.setLineDash([]))):e instanceof qa?(r.lastDrawType!==ac&&(H=!0,r.lastDrawType=ac),C=o,P=H,rc(t,uc(e,(M=r).inHover),C&&uc(C,M.inHover),P,M),f=t,y=p,(k=(d=e).__image=Ds(y.image,d.__image,d,d.onload))&&zs(k)&&(v=y.x||0,g=y.y||0,_=d.getWidth(),m=d.getHeight(),x=k.width/k.height,null==_&&null!=m?_=m*x:null==m&&null!=_?m=_/x:null==_&&null==m&&(_=k.width,m=k.height),y.sWidth&&y.sHeight?(w=y.sx||0,b=y.sy||0,f.drawImage(k,w,b,y.sWidth,y.sHeight,v,g,_,m)):y.sx&&y.sy?(S=_-(w=y.sx),T=m-(b=y.sy),f.drawImage(k,w,b,S,T,v,g,_,m)):f.drawImage(k,v,g,_,m))):e.getTemporalDisplayables&&(r.lastDrawType!==hc&&(H=!0,r.lastDrawType=hc),function(t,e,r){var i=e.getDisplayables(),n=e.getTemporalDisplayables();t.save();var o,a,s={prevElClipPaths:null,prevEl:null,allClipped:!1,viewWidth:r.viewWidth,viewHeight:r.viewHeight,inHover:r.inHover};for(o=e.getCursor(),a=i.length;o<a;o++){(u=i[o]).beforeBrush&&u.beforeBrush(),u.innerBeforeBrush(),cc(t,u,s,o===a-1),u.innerAfterBrush(),u.afterBrush&&u.afterBrush(),s.prevEl=u}for(var h=0,l=n.length;h<l;h++){var u;(u=n[h]).beforeBrush&&u.beforeBrush(),u.innerBeforeBrush(),cc(t,u,s,h===l-1),u.innerAfterBrush(),u.afterBrush&&u.afterBrush(),s.prevEl=u}e.clearTemporalDisplayables(),e.notClear=!0,t.restore()}(t,e,r)),a&&i&&lc(t,r),e.innerAfterBrush(),e.afterBrush&&e.afterBrush(),(r.prevEl=e).__dirty=0,e.__isRendered=!0)}function pc(t,e,r){var i=f.createCanvas(),n=e.getWidth(),o=e.getHeight(),a=i.style;return a&&(a.position="absolute",a.left="0",a.top="0",a.width=n+"px",a.height=o+"px",i.setAttribute("data-zr-dom-id",t)),i.width=n*r,i.height=o*r,i}var fc,dc=(vt(yc,fc=Bt),yc.prototype.getElementCount=function(){return this.__endIndex-this.__startIndex},yc.prototype.afterBrush=function(){this.__prevStartIndex=this.__startIndex,this.__prevEndIndex=this.__endIndex},yc.prototype.initContext=function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},yc.prototype.setUnpainted=function(){this.__firstTimePaint=!0},yc.prototype.createBackBuffer=function(){var t=this.dpr;this.domBack=pc("back-"+this.id,this.painter,t),this.ctxBack=this.domBack.getContext("2d"),1!==t&&this.ctxBack.scale(t,t)},yc.prototype.createRepaintRects=function(t,e,r,i){if(this.__firstTimePaint)return this.__firstTimePaint=!1,null;var c=[],p=this.maxRepaintRectCount,f=!1,d=new Ae(0,0,0,0);function n(t){if(t.isFinite()&&!t.isZero())if(0===c.length)(e=new Ae(0,0,0,0)).copy(t),c.push(e);else{for(var e,r=!1,i=1/0,n=0,o=0;o<c.length;++o){var a,s,h,l=c[o];if(l.intersect(t)){var u=new Ae(0,0,0,0);u.copy(l),u.union(t),c[o]=u,r=!0;break}f&&(d.copy(t),d.union(l),a=t.width*t.height,s=l.width*l.height,(h=d.width*d.height-a-s)<i&&(i=h,n=o))}f&&(c[n].union(t),r=!0),r||((e=new Ae(0,0,0,0)).copy(t),c.push(e)),f=f||c.length>=p}}for(var o,a=this.__startIndex;a<this.__endIndex;++a)(h=t[a])&&(u=h.shouldBePainted(r,i,!0,!0),(l=h.__isRendered&&(h.__dirty&rr||!u)?h.getPrevPaintRect():null)&&n(l),(o=u&&(h.__dirty&rr||!h.__isRendered)?h.getPaintRect():null)&&n(o));for(var s,a=this.__prevStartIndex;a<this.__prevEndIndex;++a){var h,l,u=(h=e[a])&&h.shouldBePainted(r,i,!0,!0);!h||u&&h.__zr||!h.__isRendered||(l=h.getPrevPaintRect())&&n(l)}do{for(s=!1,a=0;a<c.length;)if(c[a].isZero())c.splice(a,1);else{for(var y=a+1;y<c.length;)c[a].intersect(c[y])?(s=!0,c[a].union(c[y]),c.splice(y,1)):y++;a++}}while(s);return this._paintRects=c},yc.prototype.debugGetPaintRects=function(){return(this._paintRects||[]).slice()},yc.prototype.resize=function(t,e){var r=this.dpr,i=this.dom,n=i.style,o=this.domBack;n&&(n.width=t+"px",n.height=e+"px"),i.width=t*r,i.height=e*r,o&&(o.width=t*r,o.height=e*r,1!==r&&this.ctxBack.scale(r,r))},yc.prototype.clear=function(t,o,e){var r=this.dom,a=this.ctx,i=r.width,n=r.height;o=o||this.clearColor;var s=this.motionBlur&&!t,h=this.lastFrameAlpha,l=this.dpr,u=this;s&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(r,0,0,i/l,n/l));var c=this.domBack;function p(t,e,r,i){var n;a.clearRect(t,e,r,i),o&&"transparent"!==o&&(n=void 0,j(o)?(n=(o.global||o.__width===r&&o.__height===i)&&o.__canvasGradient||Yu(a,o,{x:0,y:0,width:r,height:i}),o.__canvasGradient=n,o.__width=r,o.__height=i):V(o)&&(o.scaleX=o.scaleX||l,o.scaleY=o.scaleY||l,n=Ju(a,o,{dirty:function(){u.setUnpainted(),u.painter.refresh()}})),a.save(),a.fillStyle=n||o,a.fillRect(t,e,r,i),a.restore()),s&&(a.save(),a.globalAlpha=h,a.drawImage(c,t,e,r,i),a.restore())}!e||s?p(0,0,i,n):e.length&&R(e,function(t){p(t.x*l,t.y*l,t.width*l,t.height*l)})},yc);function yc(t,e,r){var i,n=fc.call(this)||this;n.motionBlur=!1,n.lastFrameAlpha=.7,n.dpr=1,n.virtual=!1,n.config={},n.incremental=!1,n.zlevel=0,n.maxRepaintRectCount=5,n.__dirty=!0,n.__firstTimePaint=!0,n.__used=!1,n.__drawIndex=0,n.__startIndex=0,n.__endIndex=0,n.__prevStartIndex=null,n.__prevEndIndex=null,r=r||fn,"string"==typeof t?i=pc(t,e,r):E(t)&&(t=(i=t).id),n.id=t;var o=(n.dom=i).style;return o&&(lt(i),i.onselectstart=function(){return!1},o.padding="0",o.margin="0",o.borderWidth="0"),n.painter=e,n.dpr=r,n}var vc=314159;var gc=(_c.prototype.getType=function(){return"canvas"},_c.prototype.isSingleCanvas=function(){return this._singleCanvas},_c.prototype.getViewportRoot=function(){return this._domRoot},_c.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},_c.prototype.refresh=function(t){var e=this.storage.getDisplayList(!0),r=this._prevDisplayList,i=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,r,t,this._redrawId);for(var n=0;n<i.length;n++){var o,a=i[n],s=this._layers[a];!s.__builtin__&&s.refresh&&(o=0===n?this._backgroundColor:null,s.refresh(o))}return this._opts.useDirtyRect&&(this._prevDisplayList=e.slice()),this},_c.prototype.refreshHover=function(){this._paintHoverList(this.storage.getDisplayList(!1))},_c.prototype._paintHoverList=function(t){var e=t.length,r=this._hoverlayer;if(r&&r.clear(),e){for(var i,n={inHover:!0,viewWidth:this._width,viewHeight:this._height},o=0;o<e;o++){var a=t[o];a.__inHover&&(r=r||(this._hoverlayer=this.getLayer(1e5)),i||(i=r.ctx).save(),cc(i,a,n,o===e-1))}i&&i.restore()}},_c.prototype.getHoverLayer=function(){return this.getLayer(1e5)},_c.prototype.paintOne=function(t,e){cc(t,e,{inHover:!1,viewWidth:0,viewHeight:0},!0)},_c.prototype._paintList=function(t,e,r,i){var n,o,a,s;this._redrawId===i&&(r=r||!1,this._updateLayerStatus(t),o=(n=this._doPaintList(t,e,r)).finished,a=n.needsRefreshHover,this._needsManuallyCompositing&&this._compositeManually(),a&&this._paintHoverList(t),o?this.eachLayer(function(t){t.afterBrush&&t.afterBrush()}):(s=this,lr(function(){s._paintList(t,e,r,i)})))},_c.prototype._compositeManually=function(){var e=this.getLayer(vc).ctx,r=this._domRoot.width,i=this._domRoot.height;e.clearRect(0,0,r,i),this.eachBuiltinLayer(function(t){t.virtual&&e.drawImage(t.dom,0,0,r,i)})},_c.prototype._doPaintList=function(d,y,v){for(var g=this,_=[],m=this._opts.useDirtyRect,t=0;t<this._zlevelList.length;t++){var e=this._zlevelList[t],r=this._layers[e];r.__builtin__&&r!==this._hoverlayer&&(r.__dirty||v)&&_.push(r)}for(var x=!0,w=!1,b=this,i=0;i<_.length;i++)!function(t){var e,i,n=_[t],o=n.ctx,r=m&&n.createRepaintRects(d,y,b._width,b._height),a=v?n.__startIndex:n.__drawIndex,s=!v&&n.incremental&&Date.now,h=s&&Date.now(),l=n.zlevel===b._zlevelList[0]?b._backgroundColor:null;function u(t){var e={inHover:!1,allClipped:!1,prevEl:null,viewWidth:g._width,viewHeight:g._height};for(i=a;i<n.__endIndex;i++){var r=d[i];if(r.__inHover&&(w=!0),g._doPaintEl(r,n,m,t,e,i===n.__endIndex-1),s&&15<Date.now()-h)break}e.prevElClipPaths&&o.restore()}if(n.__startIndex===n.__endIndex?n.clear(!1,l,r):a===n.__startIndex&&((e=d[a]).incremental&&e.notClear&&!v||n.clear(!1,l,r)),-1===a&&(console.error("For some unknown reason. drawIndex is -1"),a=n.__startIndex),r)if(0===r.length)i=n.__endIndex;else for(var c=b.dpr,p=0;p<r.length;++p){var f=r[p];o.save(),o.beginPath(),o.rect(f.x*c,f.y*c,f.width*c,f.height*c),o.clip(),u(f),o.restore()}else o.save(),u(),o.restore();n.__drawIndex=i,n.__drawIndex<n.__endIndex&&(x=!1)}(i);return c.wxa&&R(this._layers,function(t){t&&t.ctx&&t.ctx.draw&&t.ctx.draw()}),{finished:x,needsRefreshHover:w}},_c.prototype._doPaintEl=function(t,e,r,i,n,o){var a,s=e.ctx;r?(a=t.getPaintRect(),(!i||a&&a.intersect(i))&&(cc(s,t,n,o),t.setPrevPaintRect(a))):cc(s,t,n,o)},_c.prototype.getLayer=function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=vc);var r=this._layers[t];return r||((r=new dc("zr_"+t,this,this.dpr)).zlevel=t,r.__builtin__=!0,this._layerConfig[t]?S(r,this._layerConfig[t],!0):this._layerConfig[t-.01]&&S(r,this._layerConfig[t-.01],!0),e&&(r.virtual=e),this.insertLayer(t,r),r.initContext()),r},_c.prototype.insertLayer=function(t,e){var r,i=this._layers,n=this._zlevelList,o=n.length,a=this._domRoot,s=null,h=-1;if(!i[t]&&function(t){if(t){if(t.__builtin__)return 1;if("function"==typeof t.resize&&"function"==typeof t.refresh)return 1}}(e)){if(0<o&&t>n[0]){for(h=0;h<o-1&&!(n[h]<t&&n[h+1]>t);h++);s=i[n[h]]}n.splice(h+1,0,t),(i[t]=e).virtual||(s?(r=s.dom).nextSibling?a.insertBefore(e.dom,r.nextSibling):a.appendChild(e.dom):a.firstChild?a.insertBefore(e.dom,a.firstChild):a.appendChild(e.dom)),e.painter||(e.painter=this)}},_c.prototype.eachLayer=function(t,e){for(var r=this._zlevelList,i=0;i<r.length;i++){var n=r[i];t.call(e,this._layers[n],n)}},_c.prototype.eachBuiltinLayer=function(t,e){for(var r=this._zlevelList,i=0;i<r.length;i++){var n=r[i],o=this._layers[n];o.__builtin__&&t.call(e,o,n)}},_c.prototype.eachOtherLayer=function(t,e){for(var r=this._zlevelList,i=0;i<r.length;i++){var n=r[i],o=this._layers[n];o.__builtin__||t.call(e,o,n)}},_c.prototype.getLayers=function(){return this._layers},_c.prototype._updateLayerStatus=function(t){function e(t){n&&(n.__endIndex!==t&&(n.__dirty=!0),n.__endIndex=t)}if(this.eachBuiltinLayer(function(t,e){t.__dirty=t.__used=!1}),this._singleCanvas)for(var r=1;r<t.length;r++)if((s=t[r]).zlevel!==t[r-1].zlevel||s.incremental){this._needsManuallyCompositing=!0;break}for(var i,n=null,o=0,a=0;a<t.length;a++){var s,h=(s=t[a]).zlevel,l=void 0;i!==h&&(i=h,o=0),s.incremental?((l=this.getLayer(h+.001,this._needsManuallyCompositing)).incremental=!0,o=1):l=this.getLayer(h+(0<o?.01:0),this._needsManuallyCompositing),l.__builtin__||b("ZLevel "+h+" has been used by unkown layer "+l.id),l!==n&&(l.__used=!0,l.__startIndex!==a&&(l.__dirty=!0),l.__startIndex=a,l.incremental?l.__drawIndex=-1:l.__drawIndex=a,e(a),n=l),s.__dirty&rr&&!s.__inHover&&(l.__dirty=!0,l.incremental&&l.__drawIndex<0&&(l.__drawIndex=a))}e(a),this.eachBuiltinLayer(function(t,e){!t.__used&&0<t.getElementCount()&&(t.__dirty=!0,t.__startIndex=t.__endIndex=t.__drawIndex=0),t.__dirty&&t.__drawIndex<0&&(t.__drawIndex=t.__startIndex)})},_c.prototype.clear=function(){return this.eachBuiltinLayer(this._clearLayer),this},_c.prototype._clearLayer=function(t){t.clear()},_c.prototype.setBackgroundColor=function(t){this._backgroundColor=t,R(this._layers,function(t){t.setUnpainted()})},_c.prototype.configLayer=function(t,e){if(e){var r=this._layerConfig;r[t]?S(r[t],e,!0):r[t]=e;for(var i=0;i<this._zlevelList.length;i++){var n=this._zlevelList[i];n!==t&&n!==t+.01||S(this._layers[n],r[t],!0)}}},_c.prototype.delLayer=function(t){var e=this._layers,r=this._zlevelList,i=e[t];i&&(i.dom.parentNode.removeChild(i.dom),delete e[t],r.splice(P(r,t),1))},_c.prototype.resize=function(t,e){if(this._domRoot.style){var r=this._domRoot;r.style.display="none";var i=this._opts,n=this.root;if(null!=t&&(i.width=t),null!=e&&(i.height=e),t=ju(n,0,i),e=ju(n,1,i),r.style.display="",this._width!==t||e!==this._height){for(var o in r.style.width=t+"px",r.style.height=e+"px",this._layers)this._layers.hasOwnProperty(o)&&this._layers[o].resize(t,e);this.refresh(!0)}this._width=t,this._height=e}else{if(null==t||null==e)return;this._width=t,this._height=e,this.getLayer(vc).resize(t,e)}return this},_c.prototype.clearLayer=function(t){var e=this._layers[t];e&&e.clear()},_c.prototype.dispose=function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},_c.prototype.getRenderedCanvas=function(t){if(t=t||{},this._singleCanvas&&!this._compositeManually)return this._layers[vc].dom;var e=new dc("image",this,t.pixelRatio||this.dpr);e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor);var r=e.ctx;if(t.pixelRatio<=this.dpr){this.refresh();var i=e.dom.width,n=e.dom.height;this.eachLayer(function(t){t.__builtin__?r.drawImage(t.dom,0,0,i,n):t.renderToCanvas&&(r.save(),t.renderToCanvas(r),r.restore())})}else for(var o={inHover:!1,viewWidth:this._width,viewHeight:this._height},a=this.storage.getDisplayList(!0),s=0,h=a.length;s<h;s++){var l=a[s];cc(r,l,o,s===h-1)}return e.dom},_c.prototype.getWidth=function(){return this._width},_c.prototype.getHeight=function(){return this._height},_c);function _c(t,e,r,i){this.type="canvas",this._zlevelList=[],this._prevDisplayList=[],this._layers={},this._layerConfig={},this._needsManuallyCompositing=!1,this.type="canvas";var n=!t.nodeName||"CANVAS"===t.nodeName.toUpperCase();this._opts=r=I({},r||{}),this.dpr=r.devicePixelRatio||fn,this._singleCanvas=n,(this.root=t).style&&(lt(t),t.innerHTML=""),this.storage=e;var o=this._zlevelList;this._prevDisplayList=[];var a,s,h,l,u,c,p,f,d=this._layers;n?(s=(a=t).width,h=a.height,null!=r.width&&(s=r.width),null!=r.height&&(h=r.height),this.dpr=r.devicePixelRatio||1,a.width=s*this.dpr,a.height=h*this.dpr,this._width=s,this._height=h,(l=new dc(a,this,this.dpr)).__builtin__=!0,l.initContext(),(d[vc]=l).zlevel=vc,o.push(vc),this._domRoot=t):(this._width=ju(t,0,r),this._height=ju(t,1,r),u=this._domRoot=(c=this._width,p=this._height,(f=document.createElement("div")).style.cssText=["position:relative","width:"+c+"px","height:"+p+"px","padding:0","margin:0","border-width:0"].join(";")+";",f),t.appendChild(u))}var mc=Math.sin,xc=Math.cos,wc=Math.PI,bc=2*Math.PI,Sc=180/wc,Tc=(kc.prototype.reset=function(t){this._start=!0,this._d=[],this._str="",this._p=Math.pow(10,t||4)},kc.prototype.moveTo=function(t,e){this._add("M",t,e)},kc.prototype.lineTo=function(t,e){this._add("L",t,e)},kc.prototype.bezierCurveTo=function(t,e,r,i,n,o){this._add("C",t,e,r,i,n,o)},kc.prototype.quadraticCurveTo=function(t,e,r,i){this._add("Q",t,e,r,i)},kc.prototype.arc=function(t,e,r,i,n,o){this.ellipse(t,e,r,r,0,i,n,o)},kc.prototype.ellipse=function(t,e,r,i,n,o,a,s){var h=a-o,l=!s,u=Math.abs(h),c=fi(u-bc)||(l?bc<=h:bc<=-h),p=!1,p=!!c||!fi(u)&&wc<=(0<h?h%bc:h%bc+bc)==!!l,f=t+r*xc(o),d=e+i*mc(o);this._start&&this._add("M",f,d);var y,v,g,_,m=Math.round(n*Sc);c?(y=1/this._p,v=(l?1:-1)*(bc-y),this._add("A",r,i,m,1,+l,t+r*xc(o+v),e+i*mc(o+v)),.01<y&&this._add("A",r,i,m,0,+l,f,d)):(g=t+r*xc(a),_=e+i*mc(a),this._add("A",r,i,m,+p,+l,g,_))},kc.prototype.rect=function(t,e,r,i){this._add("M",t,e),this._add("l",r,0),this._add("l",0,i),this._add("l",-r,0),this._add("Z")},kc.prototype.closePath=function(){0<this._d.length&&this._add("Z")},kc.prototype._add=function(t,e,r,i,n,o,a,s,h){for(var l=[],u=this._p,c=1;c<arguments.length;c++){var p=arguments[c];if(isNaN(p))return void(this._invalid=!0);l.push(Math.round(p*u)/u)}this._d.push(t+l.join(" ")),this._start="Z"===t},kc.prototype.generateStr=function(){this._str=this._invalid?"":this._d.join(""),this._d=[]},kc.prototype.getStr=function(){return this._str},kc);function kc(){}var Cc="none",Pc=Math.round;var Mc=["lineCap","miterLimit","lineJoin"],Ac=D(Mc,function(t){return"stroke-"+t.toLowerCase()});function Dc(t,e,r,i){var n,o,a,s,h=null==e.opacity?1:e.opacity;if(r instanceof qa)t("opacity",h);else if(null!=(a=e.fill)&&a!==Cc?(t("fill",(n=ci(e.fill)).color),o=null!=e.fillOpacity?e.fillOpacity*n.opacity*h:n.opacity*h,(i||o<1)&&t("fill-opacity",o)):t("fill",Cc),null!=(s=e.stroke)&&s!==Cc){var l=ci(e.stroke);t("stroke",l.color);var u,c,p,f=e.strokeNoScale?r.getLineScale():1,d=f?(e.lineWidth||0)/f:0,y=null!=e.strokeOpacity?e.strokeOpacity*l.opacity*h:l.opacity*h,v=e.strokeFirst;!i&&1==d||t("stroke-width",d),(i||v)&&t("paint-order",v?"stroke":"fill"),(i||y<1)&&t("stroke-opacity",y),e.lineDash?(c=(u=Vu(r))[0],p=u[1],c&&(p=Pc(p||0),t("stroke-dasharray",c.join(",")),(p||i)&&t("stroke-dashoffset",p))):i&&t("stroke-dasharray",Cc);for(var g=0;g<Mc.length;g++){var _,m=Mc[g];!i&&e[m]===fa[m]||(_=e[m]||fa[m])&&t(Ac[g],_)}}else i&&t("stroke",Cc)}var Lc="http://www.w3.org/2000/svg",zc="http://www.w3.org/1999/xlink",Ic="http://www.w3.org/2000/xmlns/",Rc="http://www.w3.org/XML/1998/namespace",Oc="ecmeta_";function Fc(t){return document.createElementNS(Lc,t)}function Bc(t,e,r,i,n){return{tag:t,attrs:r||{},children:i,text:n,key:e}}function Nc(t,e){var s=(e=e||{}).newline?"\n":"";return function t(e){var r,i=e.children,n=e.tag,o=e.attrs,a=e.text;return function(t,e){var r=[];if(e)for(var i in e){var n=e[i],o=i;!1!==n&&(!0!==n&&null!=n&&(o+='="'+n+'"'),r.push(o))}return"<"+t+" "+r.join(" ")+">"}(n,o)+("style"!==n?null==(r=a)?"":(r+"").replace(jt,function(t,e){return Vt[e]}):a||"")+(i?""+s+D(i,t).join(s)+s:"")+"</"+n+">"}(t)}function Hc(t){return{zrId:t,shadowCache:{},patternCache:{},gradientCache:{},clipPathCache:{},defs:{},cssNodes:{},cssAnims:{},cssStyleCache:{},cssAnimIdx:0,shadowIdx:0,gradientIdx:0,patternIdx:0,clipPathIdx:0}}function Wc(t,e,r,i){return Bc("svg","root",{width:t,height:e,xmlns:Lc,"xmlns:xlink":zc,version:"1.1",baseProfile:"full",viewBox:!!i&&"0 0 "+t+" "+e},r)}var Ec=0;function Xc(){return Ec++}var Yc={cubicIn:"0.32,0,0.67,0",cubicOut:"0.33,1,0.68,1",cubicInOut:"0.65,0,0.35,1",quadraticIn:"0.11,0,0.5,0",quadraticOut:"0.5,1,0.89,1",quadraticInOut:"0.45,0,0.55,1",quarticIn:"0.5,0,0.75,0",quarticOut:"0.25,1,0.5,1",quarticInOut:"0.76,0,0.24,1",quinticIn:"0.64,0,0.78,0",quinticOut:"0.22,1,0.36,1",quinticInOut:"0.83,0,0.17,1",sinusoidalIn:"0.12,0,0.39,0",sinusoidalOut:"0.61,1,0.88,1",sinusoidalInOut:"0.37,0,0.63,1",exponentialIn:"0.7,0,0.84,0",exponentialOut:"0.16,1,0.3,1",exponentialInOut:"0.87,0,0.13,1",circularIn:"0.55,0,1,0.45",circularOut:"0,0.55,0.45,1",circularInOut:"0.85,0,0.15,1"},qc="transform-origin";var jc={fill:"fill",opacity:"opacity",lineWidth:"stroke-width",lineDashOffset:"stroke-dashoffset"};function Vc(t,e){var r=e.zrId+"-ani-"+e.cssAnimIdx++;return e.cssAnims[r]=t,r}function Uc(t){return N(t)?Yc[t]?"cubic-bezier("+Yc[t]+")":zr(t)?t:"":""}function Gc(A,D,L,z){var t=A.animators,e=t.length,r=[];if(A instanceof ul){if(i=function(t,e,c){var p,f,r=t.shape.paths,d={};if(R(r,function(t){var e=Hc(c.zrId);e.animation=!0,Gc(t,{},e,!0);var r=e.cssAnims,i=e.cssNodes,n=F(r),o=n.length;if(o){var a=r[f=n[o-1]];for(var s in a){var h=a[s];d[s]=d[s]||{d:""},d[s].d+=h.d||""}for(var l in i){var u=i[l].animation;0<=u.indexOf(f)&&(p=u)}}}),p){e.d=!1;var i=Vc(d,c);return p.replace(f,i)}}(A,D,L))r.push(i);else if(!e)return}else if(!e)return;for(var i,n,o={},a=0;a<e;a++){var s=t[a],h=[s.getMaxTime()/1e3+"s"],l=Uc(s.getClip().easing),u=s.getDelay();l?h.push(l):h.push("linear"),u&&h.push(u/1e3+"s"),s.getLoop()&&h.push("infinite");var c=h.join(" ");o[c]=o[c]||[c,[]],o[c][1].push(s)}function p(t){var e=t[1],r=e.length,i={},n={},o={},d="animation-timing-function";function a(t,e,r){for(var i=t.getTracks(),n=t.getMaxTime(),o=0;o<i.length;o++){var a=i[o];if(a.needsAnimate()){var s=a.keyframes,h=a.propName;if(r&&(h=r(h)),h)for(var l=0;l<s.length;l++){var u=s[l],c=Math.round(u.time/n*100)+"%",p=Uc(u.easing),f=u.rawValue;(N(f)||H(f))&&(e[c]=e[c]||{},e[c][h]=u.rawValue,p&&(e[c][d]=p))}}}}for(var s,h,l,u,c,p=0;p<r;p++){(S=(b=e[p]).targetName)?"shape"===S&&a(b,n):z||a(b,i)}for(var f in i){var y={};Pn(y,A),I(y,i[f]);var v=Ti(y),g=i[f][d];o[f]=v?{transform:v}:{},s=o[f],u=l=void 0,l=(h=y).originX,u=h.originY,(l||u)&&(s[qc]=l+"px "+u+"px"),g&&(o[f][d]=g)}var _=!0;for(var f in n){o[f]=o[f]||{};var m=!c,g=n[f][d];m&&(c=new Go);var x=c.len();c.reset(),o[f].d=function(t,e,r){var i=I({},t.shape);I(i,e),t.buildPath(r,i);var n=new Tc;return n.reset(Si(t)),r.rebuildPath(n,1),n.generateStr(),n.getStr()}(A,n[f],c);var w=c.len();if(!m&&x!==w){_=!1;break}g&&(o[f][d]=g)}if(!_)for(var f in o)delete o[f].d;if(!z)for(var b,S,p=0;p<r;p++){"style"===(S=(b=e[p]).targetName)&&a(b,o,function(t){return jc[t]})}for(var T,k=F(o),C=!0,p=1;p<k.length;p++){var P=k[p-1],M=k[p];if(o[P][qc]!==o[M][qc]){C=!1;break}T=o[P][qc]}if(C&&T){for(var f in o)o[f][qc]&&delete o[f][qc];D[qc]=T}if(O(k,function(t){return 0<F(o[t]).length}).length)return Vc(o,L)+" "+t[0]+" both"}for(var f in o){(i=p(o[f]))&&r.push(i)}r.length&&(n=L.zrId+"-cls-"+Xc(),L.cssNodes["."+n]={animation:r.join(",")},D.class=n)}function Zc(t,e,r,i){var n=JSON.stringify(t),o=r.cssStyleCache[n];o||(o=r.zrId+"-cls-"+Xc(),r.cssStyleCache[n]=o,r.cssNodes["."+o+(i?":hover":"")]=t),e.class=e.class?e.class+" "+o:o}var Kc=Math.round;function Qc(t){return t&&N(t.src)}function $c(t){return t&&B(t.toDataURL)}function Jc(i,n,o,a){Dc(function(t,e){var r="fill"===t||"stroke"===t;r&&wi(e)?lp(n,i,t,a):r&&_i(e)?up(o,i,t,a):i[t]=e,r&&a.ssr&&"none"===e&&(i["pointer-events"]="visible")},n,o,!1),function(t,e,r){var i=t.style;if(function(t){return t&&(t.shadowBlur||t.shadowOffsetX||t.shadowOffsetY)}(i)){var n=function(t){var e=t.style,r=t.getGlobalScale();return[e.shadowColor,(e.shadowBlur||0).toFixed(2),(e.shadowOffsetX||0).toFixed(2),(e.shadowOffsetY||0).toFixed(2),r[0],r[1]].join(",")}(t),o=r.shadowCache,a=o[n];if(!a){var s=t.getGlobalScale(),h=s[0],l=s[1];if(!h||!l)return;var u=i.shadowOffsetX||0,c=i.shadowOffsetY||0,p=i.shadowBlur,f=ci(i.shadowColor),d=f.opacity,y=f.color,v=p/2/h+" "+p/2/l;a=r.zrId+"-s"+r.shadowIdx++,r.defs[a]=Bc("filter",a,{id:a,x:"-100%",y:"-100%",width:"300%",height:"300%"},[Bc("feDropShadow","",{dx:u/h,dy:c/l,stdDeviation:v,"flood-color":y,"flood-opacity":d})]),o[n]=a}e.filter=bi(a)}}(o,i,a)}function tp(r,t){var e=oo(t);e&&(e.each(function(t,e){null!=t&&(r[(Oc+e).toLowerCase()]=t+"")}),t.isSilent()&&(r[Oc+"silent"]="true"))}function ep(t){return fi(t[0]-1)&&fi(t[1])&&fi(t[2])&&fi(t[3]-1)}function rp(t,e,r){var i,n,o;!e||fi((o=e)[4])&&fi(o[5])&&ep(e)||(i=r?10:1e4,t.transform=ep(e)?"translate("+Kc(e[4]*i)/i+" "+Kc(e[5]*i)/i+")":"matrix("+di((n=e)[0])+","+di(n[1])+","+di(n[2])+","+di(n[3])+","+yi(n[4])+","+yi(n[5])+")")}function ip(t,e,r){for(var i=t.points,n=[],o=0;o<i.length;o++)n.push(Kc(i[o][0]*r)/r),n.push(Kc(i[o][1]*r)/r);e.points=n.join(" ")}function np(t){return!t.smooth}var op,ap={circle:[(op=D(["cx","cy","r"],function(t){return"string"==typeof t?[t,t]:t}),function(t,e,r){for(var i=0;i<op.length;i++){var n=op[i],o=t[n[0]];null!=o&&(e[n[1]]=Kc(o*r)/r)}})],polyline:[ip,np],polygon:[ip,np]};function sp(t,e){var r,i,n,o,a,s,h,l,u,c,p,f,d,y,v,g,_=t.style,m=t.shape,x=ap[t.type],w={},b=e.animation,S="path",T=t.style.strokePercent,k=e.compress&&Si(t)||4;return!x||e.willUpdate||x[1]&&!x[1](m)||b&&function(t){for(var e=t.animators,r=0;r<e.length;r++)if("shape"===e[r].targetName)return 1}(t)||T<1?(r=!t.path||t.shapeChanged(),t.path||t.createPathProxy(),i=t.path,r&&(i.beginPath(),t.buildPath(i,t.shape),t.pathUpdated()),n=i.getVersion(),a=(o=t).__svgPathBuilder,o.__svgPathVersion===n&&a&&T===o.__svgPathStrokePercent||((a=a||(o.__svgPathBuilder=new Tc)).reset(k),i.rebuildPath(a,T),a.generateStr(),o.__svgPathVersion=n,o.__svgPathStrokePercent=T),w.d=a.getStr()):(S=t.type,s=Math.pow(10,k),x[0](m,w,s)),rp(w,t.transform),Jc(w,_,t,e),tp(w,t),e.animation&&Gc(t,w,e),e.emphasis&&(l=w,u=e,(h=t).ignore||(h.isSilent()?Zc(g={"pointer-events":"none"},l,u,!0):((p=(c=h.states.emphasis&&h.states.emphasis.style?h.states.emphasis.style:{}).fill)||(f=h.style&&h.style.fill,d=h.states.select&&h.states.select.style&&h.states.select.style.fill,(y=0<=h.currentStates.indexOf("select")&&d||f)&&(p=hi(y))),(v=c.lineWidth)&&(v/=!c.strokeNoScale&&h.transform?h.transform[0]:1),g={cursor:"pointer"},p&&(g.fill=p),c.stroke&&(g.stroke=c.stroke),v&&(g["stroke-width"]=v),Zc(g,l,u,!0)))),Bc(S,t.id+"",w)}function hp(t,e){return t instanceof va?sp(t,e):t instanceof qa?function(t,e){var r=t.style,i=r.image;if(i&&!N(i)&&(Qc(i)?i=i.src:$c(i)&&(i=i.toDataURL())),i){var n=r.x||0,o=r.y||0,a={href:i,width:r.width,height:r.height};return n&&(a.x=n),o&&(a.y=o),rp(a,t.transform),Jc(a,r,t,e),tp(a,t),e.animation&&Gc(t,a,e),Bc("image",t.id+"",a)}}(t,e):t instanceof $s?function(t,e){var r=t.style,i=r.text;if(null!=i&&(i+=""),i&&!isNaN(r.x)&&!isNaN(r.y)){var n,o,a,s=r.font||W,h=r.x||0,l=(n=r.y||0,o=On(s),"top"===(a=r.textBaseline)?n+=o/2:"bottom"===a&&(n-=o/2),n),u={"dominant-baseline":"central","text-anchor":vi[r.textAlign]||r.textAlign};if(Pl(r)){var c="",p=r.fontStyle,f=kl(r.fontSize);if(!parseFloat(f))return;var d=r.fontFamily||v,y=r.fontWeight;c+="font-size:"+f+";font-family:"+d+";",p&&"normal"!==p&&(c+="font-style:"+p+";"),y&&"normal"!==y&&(c+="font-weight:"+y+";"),u.style=c}else u.style="font: "+s;return i.match(/\s/)&&(u["xml:space"]="preserve"),h&&(u.x=h),l&&(u.y=l),rp(u,t.transform),Jc(u,r,t,e),tp(u,t),e.animation&&Gc(t,u,e),Bc("text",t.id+"",u,void 0,i)}}(t,e):void 0}function lp(t,e,r,i){var n,o=t[r],a={gradientUnits:o.global?"userSpaceOnUse":"objectBoundingBox"};if(mi(o))n="linearGradient",a.x1=o.x,a.y1=o.y,a.x2=o.x2,a.y2=o.y2;else{if(!xi(o))return;n="radialGradient",a.cx=Z(o.x,.5),a.cy=Z(o.y,.5),a.r=Z(o.r,.5)}for(var s=o.colorStops,h=[],l=0,u=s.length;l<u;++l){var c=100*yi(s[l].offset)+"%",p=ci(s[l].color),f=p.color,d=p.opacity,y={offset:c};y["stop-color"]=f,d<1&&(y["stop-opacity"]=d),h.push(Bc("stop",l+"",y))}var v=Nc(Bc(n,"",a,h)),g=i.gradientCache,_=g[v];_||(_=i.zrId+"-g"+i.gradientIdx++,g[v]=_,a.id=_,i.defs[_]=Bc(n,_,a,h)),e[r]=bi(_)}function up(t,e,r,i){var n,o,a,s,h,l,u,c,p,f,d,y,v,g,_,m=t.style[r],x=t.getBoundingRect(),w={},b=m.repeat,S="no-repeat"===b,T="repeat-x"===b,k="repeat-y"===b;gi(m)?(o=m.imageWidth,a=m.imageHeight,s=void 0,N(h=m.image)?s=h:Qc(h)?s=h.src:$c(h)&&(s=h.toDataURL()),"undefined"==typeof Image?(J(o,l="Image width/height must been given explictly in svg-ssr renderer."),J(a,l)):null!=o&&null!=a||(u=function(t,e){var r,i,n;t&&(r=t.elm,i=o||e.width,n=a||e.height,"pattern"===t.tag&&(T?(n=1,i/=x.width):k&&(i=1,n/=x.height)),t.attrs.width=i,t.attrs.height=n,r&&(r.setAttribute("width",i),r.setAttribute("height",n)))},(c=Ds(s,null,t,function(t){S||u(y,t),u(n,t)}))&&c.width&&c.height&&(o=o||c.width,a=a||c.height)),n=Bc("image","img",{href:s,width:o,height:a}),w.width=o,w.height=a):m.svgElement&&(n=C(m.svgElement),w.width=m.svgWidth,w.height=m.svgHeight),n&&(S?p=f=1:T?(f=1,p=w.width/x.width):k?(p=1,f=w.height/x.height):w.patternUnits="userSpaceOnUse",null==p||isNaN(p)||(w.width=p),null==f||isNaN(f)||(w.height=f),(d=Ti(m))&&(w.patternTransform=d),v=Nc(y=Bc("pattern","",w,[n])),(_=(g=i.patternCache)[v])||(_=i.zrId+"-p"+i.patternIdx++,g[v]=_,w.id=_,y=i.defs[_]=Bc("pattern",_,w,[n])),e[r]=bi(_))}function cp(t){return document.createTextNode(t)}function pp(t,e,r){t.insertBefore(e,r)}function fp(t,e){t.removeChild(e)}function dp(t,e){t.appendChild(e)}function yp(t){return t.parentNode}function vp(t){return t.nextSibling}function gp(t,e){t.textContent=e}var _p=58,mp=120,xp=Bc("","");function wp(t){return void 0===t}function bp(t){return void 0!==t}function Sp(t,e){var r=t.key===e.key;return t.tag===e.tag&&r}function Tp(t){var e,r=t.children,i=t.tag;if(bp(i)){var n=t.elm=Fc(i);if(Pp(xp,t),G(r))for(e=0;e<r.length;++e){var o=r[e];null!=o&&dp(n,Tp(o))}else bp(t.text)&&!E(t.text)&&dp(n,cp(t.text))}else t.elm=cp(t.text);return t.elm}function kp(t,e,r,i,n){for(;i<=n;++i){var o=r[i];null!=o&&pp(t,Tp(o),e)}}function Cp(t,e,r,i){for(;r<=i;++r){var n=e[r];null!=n&&(bp(n.tag)?fp(yp(n.elm),n.elm):fp(t,n.elm))}}function Pp(t,e){var r,i=e.elm,n=t&&t.attrs||{},o=e.attrs||{};if(n!==o){for(r in o){var a=o[r];n[r]!==a&&(!0===a?i.setAttribute(r,""):!1===a?i.removeAttribute(r):"style"===r?i.style.cssText=a:r.charCodeAt(0)!==mp?i.setAttribute(r,a):"xmlns:xlink"===r||"xmlns"===r?i.setAttributeNS(Ic,r,a):r.charCodeAt(3)===_p?i.setAttributeNS(Rc,r,a):r.charCodeAt(5)===_p?i.setAttributeNS(zc,r,a):i.setAttribute(r,a))}for(r in n)r in o||i.removeAttribute(r)}}function Mp(t,e,r){for(var i,n,o,a=0,s=0,h=e.length-1,l=e[0],u=e[h],c=r.length-1,p=r[0],f=r[c];a<=h&&s<=c;)null==l?l=e[++a]:null==u?u=e[--h]:null==p?p=r[++s]:null==f?f=r[--c]:Sp(l,p)?(Ap(l,p),l=e[++a],p=r[++s]):Sp(u,f)?(Ap(u,f),u=e[--h],f=r[--c]):Sp(l,f)?(Ap(l,f),pp(t,l.elm,vp(u.elm)),l=e[++a],f=r[--c]):p=(Sp(u,p)?(Ap(u,p),pp(t,u.elm,l.elm),u=e[--h]):(wp(i)&&(i=function(t,e,r){for(var i={},n=e;n<=r;++n){var o=t[n].key;void 0!==o&&(i[o]=n)}return i}(e,a,h)),wp(n=i[p.key])||(o=e[n]).tag!==p.tag?pp(t,Tp(p),l.elm):(Ap(o,p),e[n]=void 0,pp(t,o.elm,l.elm))),r[++s]);(a<=h||s<=c)&&(h<a?kp(t,null==r[c+1]?null:r[c+1].elm,r,s,c):Cp(t,e,a,h))}function Ap(t,e){var r=e.elm=t.elm,i=t.children,n=e.children;t!==e&&(Pp(t,e),wp(e.text)?bp(i)&&bp(n)?i!==n&&Mp(r,i,n):bp(n)?(bp(t.text)&&gp(r,""),kp(r,null,n,0,n.length-1)):bp(i)?Cp(r,i,0,i.length-1):bp(t.text)&&gp(r,""):t.text!==e.text&&(bp(i)&&Cp(r,i,0,i.length-1),gp(r,e.text)))}var Dp=0,Lp=(zp.prototype.getType=function(){return this.type},zp.prototype.getViewportRoot=function(){return this._viewport},zp.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},zp.prototype.getSvgDom=function(){return this._svgDom},zp.prototype.refresh=function(){var t,e,r,i,n;this.root&&((t=this.renderToVNode({willUpdate:!0})).attrs.style="position:absolute;left:0;top:0;user-select:none",Sp(e=this._oldVNode,r=t)?Ap(e,r):(n=yp(i=e.elm),Tp(r),null!==n&&(pp(n,r.elm,vp(i)),Cp(n,[e],0,0))),this._oldVNode=t)},zp.prototype.renderOneToVNode=function(t){return hp(t,Hc(this._id))},zp.prototype.renderToVNode=function(t){t=t||{};var e=this.storage.getDisplayList(!0),r=this._width,i=this._height,n=Hc(this._id);n.animation=t.animation,n.willUpdate=t.willUpdate,n.compress=t.compress,n.emphasis=t.emphasis,n.ssr=this._opts.ssr;var o=[],a=this._bgVNode=function(t,e,r,i){var n,o,a,s;return r&&"none"!==r&&(n=Bc("rect","bg",{width:t,height:e,x:"0",y:"0"}),wi(r)?lp({fill:r},n.attrs,"fill",i):_i(r)?up({style:{fill:r},dirty:ct,getBoundingRect:function(){return{width:t,height:e}}},n.attrs,"fill",i):(o=ci(r),a=o.color,s=o.opacity,n.attrs.fill=a,s<1&&(n.attrs["fill-opacity"]=s))),n}(r,i,this._backgroundColor,n);a&&o.push(a);var s=t.compress?null:this._mainVNode=Bc("g","main",{},[]);this._paintList(e,n,s?s.children:o),s&&o.push(s);var h,l,u,c,p,f,d,y,v,g=D(F(n.defs),function(t){return n.defs[t]});return g.length&&o.push(Bc("defs","defs",{},g)),t.animation&&(u=n.cssNodes,c=n.cssAnims,f=" {"+(p="\n"),d=p+"}",y=D(F(u),function(e){return e+f+D(F(u[e]),function(t){return t+":"+u[e][t]+";"}).join(p)+d}).join(p),v=D(F(c),function(i){return"@keyframes "+i+f+D(F(c[i]),function(r){return r+f+D(F(c[i][r]),function(t){var e=c[i][r][t];return"d"===t&&(e='path("'+e+'")'),t+":"+e+";"}).join(p)+d}).join(p)+d}).join(p),(h=y||v?["<![CDATA[",y,v,"]]>"].join(p):"")&&(l=Bc("style","stl",{},[],h),o.push(l))),Wc(r,i,o,t.useViewBox)},zp.prototype.renderToString=function(t){return t=t||{},Nc(this.renderToVNode({animation:Z(t.cssAnimation,!0),emphasis:Z(t.cssEmphasis,!0),willUpdate:!1,compress:!0,useViewBox:Z(t.useViewBox,!0)}),{newline:!0})},zp.prototype.setBackgroundColor=function(t){this._backgroundColor=t},zp.prototype.getSvgRoot=function(){return this._mainVNode&&this._mainVNode.elm},zp.prototype._paintList=function(t,e,r){for(var i,n,o,a,s,h,l,u,c,p=t.length,f=[],d=0,y=0,v=0;v<p;v++){var g=t[v];if(!g.invisible){for(var _=g.__clipPaths,m=_&&_.length||0,x=n&&n.length||0,w=void 0,w=Math.max(m-1,x-1);0<=w&&(!_||!n||_[w]!==n[w]);w--);for(var b=x-1;w<b;b--)i=f[--d-1];for(var S=w+1;S<m;S++){var T={};o=_[S],a=T,c=u=l=h=void 0,l=(s=e).clipPathCache,u=s.defs,(c=l[o.id])||(h={id:c=s.zrId+"-c"+s.clipPathIdx++},u[l[o.id]=c]=Bc("clipPath",c,h,[sp(o,s)])),a["clip-path"]=bi(c);var k=Bc("g","clip-g-"+y++,T,[]);(i?i.children:r).push(k),i=f[d++]=k}n=_;var C=hp(g,e);C&&(i?i.children:r).push(C)}}},zp.prototype.resize=function(t,e){var r,i,n,o=this._opts,a=this.root,s=this._viewport;null!=t&&(o.width=t),null!=e&&(o.height=e),a&&s&&(s.style.display="none",t=ju(a,0,o),e=ju(a,1,o),s.style.display=""),this._width===t&&this._height===e||(this._width=t,this._height=e,s&&((r=s.style).width=t+"px",r.height=e+"px"),_i(this._backgroundColor)?this.refresh():((i=this._svgDom)&&(i.setAttribute("width",t),i.setAttribute("height",e)),(n=this._bgVNode&&this._bgVNode.elm)&&(n.setAttribute("width",t),n.setAttribute("height",e))))},zp.prototype.getWidth=function(){return this._width},zp.prototype.getHeight=function(){return this._height},zp.prototype.dispose=function(){this.root&&(this.root.innerHTML=""),this._svgDom=this._viewport=this.storage=this._oldVNode=this._bgVNode=this._mainVNode=null},zp.prototype.clear=function(){this._svgDom&&(this._svgDom.innerHTML=null),this._oldVNode=null},zp.prototype.toDataURL=function(t){var e=this.renderToString(),r="data:image/svg+xml;";return t?(e=ki(e))&&r+"base64,"+e:r+"charset=UTF-8,"+encodeURIComponent(e)},zp);function zp(t,e,r){var i,n;this.type="svg",this.refreshHover=Ip(),this.configLayer=Ip(),this.storage=e,this._opts=r=I({},r),this.root=t,this._id="zr"+Dp++,this._oldVNode=Wc(r.width,r.height),t&&!r.ssr&&((i=this._viewport=document.createElement("div")).style.cssText="position:relative;overflow:hidden",n=this._svgDom=this._oldVNode.elm=Fc("svg"),Pp(null,this._oldVNode),i.appendChild(n),t.appendChild(i)),this.resize(r.width,r.height)}function Ip(){return function(){}}no("canvas",gc),no("svg",Lp),t.Arc=Fl,t.ArcShape=Ol,t.BezierCurve=Xl,t.BezierCurveShape=Hl,t.BoundingRect=Ae,t.Circle=Ga,t.CircleShape=Ua,t.CompoundPath=ul,t.Displayable=fo,t.Droplet=Vl,t.DropletShape=jl,t.Element=qn,t.Ellipse=os,t.EllipseShape=ns,t.Group=Qn,t.Heart=Kl,t.HeartShape=Zl,t.Image=qa,t.IncrementalDisplayable=dl,t.Isogon=iu,t.IsogonShape=ru,t.Line=us,t.LineShape=ls,t.LinearGradient=Ts,t.OrientedBoundingRect=Nu,t.Path=va,t.Pattern=Au,t.Point=de,t.Polygon=ys,t.PolygonShape=ds,t.Polyline=ms,t.PolylineShape=_s,t.RadialGradient=Ps,t.Rect=es,t.RectShape=Ja,t.Ring=su,t.RingShape=au,t.Rose=du,t.RoseShape=fu,t.Sector=Oh,t.SectorShape=Rh,t.Star=wu,t.StarShape=xu,t.TSpan=$s,t.Text=xl,t.Trochoid=Pu,t.TrochoidShape=Cu,t.color=li,t.dispose=function(t){t.dispose()},t.disposeAll=function(){for(var t in to)to.hasOwnProperty(t)&&to[t].dispose();to={}},t.getElementSSRData=oo,t.getInstance=function(t){return to[t]},t.init=function(t,e){var r=new ro(w(),t,e);return to[r.id]=r},t.matrix=fe,t.morph=ll,t.parseSVG=function(t,e){return(new nh).parse(t,e)},t.path=Wa,t.registerPainter=no,t.registerSSRDataGetter=function(t){eo=t},t.setPlatformAPI=function(t){for(var e in f)t[e]&&(f[e]=t[e])},t.showDebugDirtyRect=function(t,n){n=n||{};var e=t.painter;if(!e.getLayers)throw new Error("Debug dirty rect can only been used on canvas renderer.");if(e.isSingleCanvas())throw new Error("Debug dirty rect can only been used on zrender inited with container.");var o=document.createElement("div");o.style.cssText="\nposition:absolute;\nleft:0;\ntop:0;\nright:0;\nbottom:0;\npointer-events:none;\n",o.className="ec-debug-dirty-rect-container";var a=[],r=t.dom;r.appendChild(o),"static"===getComputedStyle(r).position&&(r.style.position="relative"),t.on("rendered",function(){if(e.getLayers){var i=0;e.eachBuiltinLayer(function(t){if(t.debugGetPaintRects)for(var e=t.debugGetPaintRects(),r=0;r<e.length;r++)e[r].width&&e[r].height&&(a[i]||(a[i]=new Wu(n.style),o.appendChild(a[i].dom)),a[i].show(n.autoHideDelay),a[i].update(e[r]),i++)});for(var t=i;t<a.length;t++)a[t].hide()}})},t.util=dt,t.vector=It,t.version="6.0.0",Object.defineProperty(t,"__esModule",{value:!0})});