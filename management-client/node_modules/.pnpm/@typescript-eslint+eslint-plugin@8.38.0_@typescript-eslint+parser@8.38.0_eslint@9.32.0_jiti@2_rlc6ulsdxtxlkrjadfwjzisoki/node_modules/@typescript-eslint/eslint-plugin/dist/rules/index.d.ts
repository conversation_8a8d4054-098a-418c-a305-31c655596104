declare const rules: {
    'adjacent-overload-signatures': import("@typescript-eslint/utils/ts-eslint").RuleModule<"adjacentSignature", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'array-type': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./array-type").MessageIds, import("./array-type").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'await-thenable': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./await-thenable").MessageId, [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'ban-ts-comment': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./ban-ts-comment").MessageIds, import("./ban-ts-comment").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'ban-tslint-comment': import("@typescript-eslint/utils/ts-eslint").RuleModule<"commentDetected", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'class-literal-property-style': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./class-literal-property-style").MessageIds, import("./class-literal-property-style").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'class-methods-use-this': import("@typescript-eslint/utils/ts-eslint").RuleModule<"missingThis", import("./class-methods-use-this").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'consistent-generic-constructors': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./consistent-generic-constructors").MessageIds, import("./consistent-generic-constructors").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'consistent-indexed-object-style': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./consistent-indexed-object-style").MessageIds, import("./consistent-indexed-object-style").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'consistent-return': import("@typescript-eslint/utils/ts-eslint").RuleModule<"missingReturn" | "missingReturnValue" | "unexpectedReturnValue", [({
        treatUndefinedAsUnspecified?: boolean;
    } | undefined)?], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'consistent-type-assertions': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./consistent-type-assertions").MessageIds, import("./consistent-type-assertions").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'consistent-type-definitions': import("@typescript-eslint/utils/ts-eslint").RuleModule<"interfaceOverType" | "typeOverInterface", [string], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'consistent-type-exports': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./consistent-type-exports").MessageIds, import("./consistent-type-exports").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'consistent-type-imports': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./consistent-type-imports").MessageIds, import("./consistent-type-imports").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'default-param-last': import("@typescript-eslint/utils/ts-eslint").RuleModule<"shouldBeLast", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'dot-notation': import("@typescript-eslint/utils/ts-eslint").RuleModule<"useBrackets" | "useDot", [{
        allowIndexSignaturePropertyAccess?: boolean;
        allowKeywords?: boolean;
        allowPattern?: string;
        allowPrivateClassPropertyAccess?: boolean;
        allowProtectedClassPropertyAccess?: boolean;
    }], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'explicit-function-return-type': import("@typescript-eslint/utils/ts-eslint").RuleModule<"missingReturnType", import("./explicit-function-return-type").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'explicit-member-accessibility': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./explicit-member-accessibility").MessageIds, import("./explicit-member-accessibility").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'explicit-module-boundary-types': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./explicit-module-boundary-types").MessageIds, import("./explicit-module-boundary-types").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'init-declarations': import("@typescript-eslint/utils/ts-eslint").RuleModule<"initialized" | "notInitialized", ["always" | "never", ({
        ignoreForLoopInit?: boolean;
    } | undefined)?], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'max-params': import("@typescript-eslint/utils/ts-eslint").RuleModule<"exceed", ({
        countVoidThis?: boolean;
        max: number;
    } | {
        countVoidThis?: boolean;
        maximum: number;
    })[], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'member-ordering': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./member-ordering").MessageIds, import("./member-ordering").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'method-signature-style': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./method-signature-style").MessageIds, import("./method-signature-style").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'naming-convention': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./naming-convention").MessageIds, import("./naming-convention").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-array-constructor': import("@typescript-eslint/utils/ts-eslint").RuleModule<"useLiteral", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-array-delete': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./no-array-delete").MessageId, [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-base-to-string': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./no-base-to-string").MessageIds, import("./no-base-to-string").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-confusing-non-null-assertion': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./no-confusing-non-null-assertion").MessageId, [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-confusing-void-expression': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./no-confusing-void-expression").MessageId, import("./no-confusing-void-expression").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-deprecated': import("@typescript-eslint/utils/ts-eslint").RuleModule<"deprecated" | "deprecatedWithReason", [{
        allow?: import("@typescript-eslint/type-utils").TypeOrValueSpecifier[];
    }], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-dupe-class-members': import("@typescript-eslint/utils/ts-eslint").RuleModule<"unexpected", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-duplicate-enum-values': import("@typescript-eslint/utils/ts-eslint").RuleModule<"duplicateValue", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-duplicate-type-constituents': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./no-duplicate-type-constituents").MessageIds, import("./no-duplicate-type-constituents").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-dynamic-delete': import("@typescript-eslint/utils/ts-eslint").RuleModule<"dynamicDelete", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-empty-function': import("@typescript-eslint/utils/ts-eslint").RuleModule<"unexpected", [{
        allow?: string[];
    }], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-empty-interface': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./no-empty-interface").MessageIds, import("./no-empty-interface").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-empty-object-type': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./no-empty-object-type").MessageIds, import("./no-empty-object-type").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-explicit-any': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./no-explicit-any").MessageIds, import("./no-explicit-any").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-extra-non-null-assertion': import("@typescript-eslint/utils/ts-eslint").RuleModule<"noExtraNonNullAssertion", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-extraneous-class': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./no-extraneous-class").MessageIds, import("./no-extraneous-class").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-floating-promises': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./no-floating-promises").MessageId, import("./no-floating-promises").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-for-in-array': import("@typescript-eslint/utils/ts-eslint").RuleModule<"forInViolation", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-implied-eval': import("@typescript-eslint/utils/ts-eslint").RuleModule<"noFunctionConstructor" | "noImpliedEvalError", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-import-type-side-effects': import("@typescript-eslint/utils/ts-eslint").RuleModule<"useTopLevelQualifier", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-inferrable-types': import("@typescript-eslint/utils/ts-eslint").RuleModule<"noInferrableType", import("./no-inferrable-types").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-invalid-this': import("@typescript-eslint/utils/ts-eslint").RuleModule<"unexpectedThis", [({
        capIsConstructor?: boolean;
    } | undefined)?], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-invalid-void-type': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./no-invalid-void-type").MessageIds, [import("./no-invalid-void-type").Options], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-loop-func': import("@typescript-eslint/utils/ts-eslint").RuleModule<"unsafeRefs", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-loss-of-precision': import("@typescript-eslint/utils/ts-eslint").RuleModule<"noLossOfPrecision", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-magic-numbers': import("@typescript-eslint/utils/ts-eslint").RuleModule<"noMagic", [{
        detectObjects?: boolean;
        enforceConst?: boolean;
        ignore?: (number | string)[];
        ignoreArrayIndexes?: boolean;
        ignoreEnums?: boolean;
        ignoreNumericLiteralTypes?: boolean;
        ignoreReadonlyClassProperties?: boolean;
        ignoreTypeIndexes?: boolean;
    }], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-meaningless-void-operator': import("@typescript-eslint/utils/ts-eslint").RuleModule<"meaninglessVoidOperator" | "removeVoid", import("./no-meaningless-void-operator").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-misused-new': import("@typescript-eslint/utils/ts-eslint").RuleModule<"errorMessageClass" | "errorMessageInterface", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-misused-promises': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./no-misused-promises").MessageId, import("./no-misused-promises").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-misused-spread': import("@typescript-eslint/utils/ts-eslint").RuleModule<"addAwait" | "noArraySpreadInObject" | "noClassDeclarationSpreadInObject" | "noClassInstanceSpreadInObject" | "noFunctionSpreadInObject" | "noIterableSpreadInObject" | "noMapSpreadInObject" | "noPromiseSpreadInObject" | "noStringSpread" | "replaceMapSpreadInObject", [{
        allow?: import("@typescript-eslint/type-utils").TypeOrValueSpecifier[];
    }], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-mixed-enums': import("@typescript-eslint/utils/ts-eslint").RuleModule<"mixed", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-namespace': import("@typescript-eslint/utils/ts-eslint").RuleModule<"moduleSyntaxIsPreferred", import("./no-namespace").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-non-null-asserted-nullish-coalescing': import("@typescript-eslint/utils/ts-eslint").RuleModule<"noNonNullAssertedNullishCoalescing" | "suggestRemovingNonNull", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-non-null-asserted-optional-chain': import("@typescript-eslint/utils/ts-eslint").RuleModule<"suggestRemovingNonNull" | "noNonNullOptionalChain", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-non-null-assertion': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./no-non-null-assertion").MessageIds, [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-redeclare': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./no-redeclare").MessageIds, import("./no-redeclare").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-redundant-type-constituents': import("@typescript-eslint/utils/ts-eslint").RuleModule<"overrides" | "errorTypeOverrides" | "literalOverridden" | "overridden" | "primitiveOverridden", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-require-imports': import("@typescript-eslint/utils/ts-eslint").RuleModule<"noRequireImports", import("./no-require-imports").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-restricted-imports': import("@typescript-eslint/utils/ts-eslint").RuleModule<"everything" | "everythingWithCustomMessage" | "importName" | "importNameWithCustomMessage" | "path" | "pathWithCustomMessage" | "patterns" | "patternWithCustomMessage", [import("eslint/lib/rules/no-restricted-imports").ObjectOfPathsAndPatterns] | import("eslint/lib/rules/no-restricted-imports").ArrayOfStringOrObject, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-restricted-types': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./no-restricted-types").MessageIds, import("./no-restricted-types").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-shadow': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./no-shadow").MessageIds, import("./no-shadow").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-this-alias': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./no-this-alias").MessageIds, import("./no-this-alias").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-type-alias': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./no-type-alias").MessageIds, import("./no-type-alias").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-unnecessary-boolean-literal-compare': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./no-unnecessary-boolean-literal-compare").MessageIds, import("./no-unnecessary-boolean-literal-compare").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-unnecessary-condition': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./no-unnecessary-condition").MessageId, import("./no-unnecessary-condition").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-unnecessary-parameter-property-assignment': import("@typescript-eslint/utils/ts-eslint").RuleModule<"unnecessaryAssign", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-unnecessary-qualifier': import("@typescript-eslint/utils/ts-eslint").RuleModule<"unnecessaryQualifier", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-unnecessary-template-expression': import("@typescript-eslint/utils/ts-eslint").RuleModule<"noUnnecessaryTemplateExpression", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-unnecessary-type-arguments': import("@typescript-eslint/utils/ts-eslint").RuleModule<"unnecessaryTypeParameter", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-unnecessary-type-assertion': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./no-unnecessary-type-assertion").MessageIds, import("./no-unnecessary-type-assertion").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-unnecessary-type-constraint': import("@typescript-eslint/utils/ts-eslint").RuleModule<"removeUnnecessaryConstraint" | "unnecessaryConstraint", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-unnecessary-type-conversion': import("@typescript-eslint/utils/ts-eslint").RuleModule<"suggestRemove" | "suggestSatisfies" | "unnecessaryTypeConversion", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-unnecessary-type-parameters': import("@typescript-eslint/utils/ts-eslint").RuleModule<"replaceUsagesWithConstraint" | "sole", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-unsafe-argument': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./no-unsafe-argument").MessageIds, [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-unsafe-assignment': import("@typescript-eslint/utils/ts-eslint").RuleModule<"unsafeArraySpread" | "anyAssignment" | "anyAssignmentThis" | "unsafeArrayPattern" | "unsafeArrayPatternFromTuple" | "unsafeAssignment", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-unsafe-call': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./no-unsafe-call").MessageIds, [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-unsafe-declaration-merging': import("@typescript-eslint/utils/ts-eslint").RuleModule<"unsafeMerging", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-unsafe-enum-comparison': import("@typescript-eslint/utils/ts-eslint").RuleModule<"mismatchedCase" | "mismatchedCondition" | "replaceValueWithEnum", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-unsafe-function-type': import("@typescript-eslint/utils/ts-eslint").RuleModule<"bannedFunctionType", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-unsafe-member-access': import("@typescript-eslint/utils/ts-eslint").RuleModule<"unsafeComputedMemberAccess" | "unsafeMemberExpression" | "unsafeThisMemberExpression", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-unsafe-return': import("@typescript-eslint/utils/ts-eslint").RuleModule<"unsafeReturn" | "unsafeReturnAssignment" | "unsafeReturnThis", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-unsafe-type-assertion': import("@typescript-eslint/utils/ts-eslint").RuleModule<"unsafeOfAnyTypeAssertion" | "unsafeToAnyTypeAssertion" | "unsafeToUnconstrainedTypeAssertion" | "unsafeTypeAssertion" | "unsafeTypeAssertionAssignableToConstraint", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-unsafe-unary-minus': import("@typescript-eslint/utils/ts-eslint").RuleModule<"unaryMinus", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-unused-expressions': import("@typescript-eslint/utils/ts-eslint").RuleModule<"unusedExpression", [{
        allowShortCircuit?: boolean;
        allowTaggedTemplates?: boolean;
        allowTernary?: boolean;
    }], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-unused-vars': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./no-unused-vars").MessageIds, import("./no-unused-vars").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-use-before-define': import("@typescript-eslint/utils/ts-eslint").RuleModule<"noUseBeforeDefine", import("./no-use-before-define").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-useless-constructor': import("@typescript-eslint/utils/ts-eslint").RuleModule<"noUselessConstructor" | "removeConstructor", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-useless-empty-export': import("@typescript-eslint/utils/ts-eslint").RuleModule<"uselessExport", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-var-requires': import("@typescript-eslint/utils/ts-eslint").RuleModule<"noVarReqs", import("./no-var-requires").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'no-wrapper-object-types': import("@typescript-eslint/utils/ts-eslint").RuleModule<"bannedClassType", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'non-nullable-type-assertion-style': import("@typescript-eslint/utils/ts-eslint").RuleModule<"preferNonNullAssertion", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'only-throw-error': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./only-throw-error").MessageIds, import("./only-throw-error").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'parameter-properties': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./parameter-properties").MessageIds, import("./parameter-properties").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'prefer-as-const': import("@typescript-eslint/utils/ts-eslint").RuleModule<"preferConstAssertion" | "variableConstAssertion" | "variableSuggest", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'prefer-destructuring': import("@typescript-eslint/utils/ts-eslint").RuleModule<"preferDestructuring", import("./prefer-destructuring").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'prefer-enum-initializers': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./prefer-enum-initializers").MessageIds, [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'prefer-find': import("@typescript-eslint/utils/ts-eslint").RuleModule<"preferFind" | "preferFindSuggestion", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'prefer-for-of': import("@typescript-eslint/utils/ts-eslint").RuleModule<"preferForOf", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'prefer-function-type': import("@typescript-eslint/utils/ts-eslint").RuleModule<"functionTypeOverCallableType" | "unexpectedThisOnFunctionOnlyInterface", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'prefer-includes': import("@typescript-eslint/utils/ts-eslint").RuleModule<"preferIncludes" | "preferStringIncludes", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'prefer-literal-enum-member': import("@typescript-eslint/utils/ts-eslint").RuleModule<"notLiteral" | "notLiteralOrBitwiseExpression", [{
        allowBitwiseExpressions: boolean;
    }], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'prefer-namespace-keyword': import("@typescript-eslint/utils/ts-eslint").RuleModule<"useNamespace", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'prefer-nullish-coalescing': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./prefer-nullish-coalescing").MessageIds, import("./prefer-nullish-coalescing").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'prefer-optional-chain': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./prefer-optional-chain-utils/PreferOptionalChainOptions").PreferOptionalChainMessageIds, [import("./prefer-optional-chain-utils/PreferOptionalChainOptions").PreferOptionalChainOptions], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'prefer-promise-reject-errors': import("@typescript-eslint/utils/ts-eslint").RuleModule<"rejectAnError", import("./prefer-promise-reject-errors").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'prefer-readonly': import("@typescript-eslint/utils/ts-eslint").RuleModule<"preferReadonly", import("./prefer-readonly").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'prefer-readonly-parameter-types': import("@typescript-eslint/utils/ts-eslint").RuleModule<"shouldBeReadonly", import("./prefer-readonly-parameter-types").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'prefer-reduce-type-parameter': import("@typescript-eslint/utils/ts-eslint").RuleModule<"preferTypeParameter", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'prefer-regexp-exec': import("@typescript-eslint/utils/ts-eslint").RuleModule<"regExpExecOverStringMatch", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'prefer-return-this-type': import("@typescript-eslint/utils/ts-eslint").RuleModule<"useThisType", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'prefer-string-starts-ends-with': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./prefer-string-starts-ends-with").MessageIds, import("./prefer-string-starts-ends-with").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'prefer-ts-expect-error': import("@typescript-eslint/utils/ts-eslint").RuleModule<"preferExpectErrorComment", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'promise-function-async': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./promise-function-async").MessageIds, import("./promise-function-async").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'related-getter-setter-pairs': import("@typescript-eslint/utils/ts-eslint").RuleModule<"mismatch", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'require-array-sort-compare': import("@typescript-eslint/utils/ts-eslint").RuleModule<"requireCompare", import("./require-array-sort-compare").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'require-await': import("@typescript-eslint/utils/ts-eslint").RuleModule<"missingAwait" | "removeAsync", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'restrict-plus-operands': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./restrict-plus-operands").MessageIds, import("./restrict-plus-operands").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'restrict-template-expressions': import("@typescript-eslint/utils/ts-eslint").RuleModule<"invalidType", import("./restrict-template-expressions").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'return-await': import("@typescript-eslint/utils/ts-eslint").RuleModule<"disallowedPromiseAwait" | "disallowedPromiseAwaitSuggestion" | "nonPromiseAwait" | "requiredPromiseAwait" | "requiredPromiseAwaitSuggestion", [string], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'sort-type-constituents': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./sort-type-constituents").MessageIds, import("./sort-type-constituents").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'strict-boolean-expressions': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./strict-boolean-expressions").MessageId, import("./strict-boolean-expressions").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'switch-exhaustiveness-check': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./switch-exhaustiveness-check").MessageIds, import("./switch-exhaustiveness-check").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'triple-slash-reference': import("@typescript-eslint/utils/ts-eslint").RuleModule<"tripleSlashReference", import("./triple-slash-reference").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    typedef: import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./typedef").MessageIds, import("./typedef").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'unbound-method': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./unbound-method").MessageIds, import("./unbound-method").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'unified-signatures': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./unified-signatures").MessageIds, import("./unified-signatures").Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
    'use-unknown-in-catch-callback-variable': import("@typescript-eslint/utils/ts-eslint").RuleModule<import("./use-unknown-in-catch-callback-variable").MessageIds, [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
};
export = rules;
//# sourceMappingURL=index.d.ts.map