import type { TSESTree } from '@typescript-eslint/utils';
/**
 * Tests if a node appears at the beginning of an ancestor ExpressionStatement node.
 * @param node The node to check.
 * @returns Whether the node appears at the beginning of an ancestor ExpressionStatement node.
 */
export declare function isStartOfExpressionStatement(node: TSESTree.Node): boolean;
//# sourceMappingURL=isStartOfExpressionStatement.d.ts.map