export type Options = [
    {
        allowDeclarations?: boolean;
        allowDefinitionFiles?: boolean;
    }
];
export type MessageIds = 'moduleSyntaxIsPreferred';
declare const _default: import("@typescript-eslint/utils/ts-eslint").RuleModule<"moduleSyntaxIsPreferred", Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
export default _default;
//# sourceMappingURL=no-namespace.d.ts.map