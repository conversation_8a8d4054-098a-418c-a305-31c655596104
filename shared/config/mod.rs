// 配置管理系统模块
// Configuration Management System Module

use crate::database::DatabaseConfig;
use crate::logging::LoggingConfig;
use crate::messaging::MessageQueueConfig;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::env;
use std::fs;
use std::path::{Path, PathBuf};
use anyhow::{Context, Result};

// 应用程序配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppConfig {
    pub app: ApplicationConfig,
    pub database: DatabaseConfig,
    pub messaging: MessageQueueConfig,
    pub logging: LoggingConfig,
    pub crawler: CrawlerConfig,
    pub analysis: AnalysisConfig,
    pub server: ServerConfig,
    pub security: SecurityConfig,
}

// 应用程序基础配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApplicationConfig {
    pub name: String,
    pub version: String,
    pub environment: Environment,
    pub debug: bool,
    pub data_dir: PathBuf,
    pub temp_dir: PathBuf,
}

// 环境类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum Environment {
    Development,
    Testing,
    Staging,
    Production,
}

impl std::fmt::Display for Environment {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Environment::Development => write!(f, "development"),
            Environment::Testing => write!(f, "testing"),
            Environment::Staging => write!(f, "staging"),
            Environment::Production => write!(f, "production"),
        }
    }
}

// 爬虫配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CrawlerConfig {
    pub max_concurrent_tasks: usize,
    pub request_timeout: u64,
    pub retry_attempts: u32,
    pub retry_delay: u64,
    pub rate_limit: RateLimitConfig,
    pub user_agent: String,
    pub proxy: Option<ProxyConfig>,
    pub cookies_file: Option<PathBuf>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RateLimitConfig {
    pub requests_per_minute: u32,
    pub requests_per_hour: u32,
    pub burst_size: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProxyConfig {
    pub enabled: bool,
    pub url: String,
    pub username: Option<String>,
    pub password: Option<String>,
}

// 分析配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnalysisConfig {
    pub sentiment_model: SentimentModelConfig,
    pub trend_analysis: TrendAnalysisConfig,
    pub batch_size: usize,
    pub max_parallel_jobs: usize,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SentimentModelConfig {
    pub model_path: PathBuf,
    pub model_type: String,
    pub confidence_threshold: f32,
    pub batch_size: usize,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrendAnalysisConfig {
    pub window_size: u32,
    pub min_mentions: u32,
    pub update_interval: u64,
}

// 服务器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerConfig {
    pub host: String,
    pub port: u16,
    pub workers: Option<usize>,
    pub keep_alive: u64,
    pub client_timeout: u64,
    pub client_shutdown: u64,
    pub tls: Option<TlsConfig>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TlsConfig {
    pub cert_file: PathBuf,
    pub key_file: PathBuf,
}

// 安全配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityConfig {
    pub secret_key: String,
    pub jwt_expiration: u64,
    pub password_min_length: usize,
    pub max_login_attempts: u32,
    pub lockout_duration: u64,
    pub cors: CorsConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CorsConfig {
    pub allowed_origins: Vec<String>,
    pub allowed_methods: Vec<String>,
    pub allowed_headers: Vec<String>,
    pub max_age: u32,
}

impl Default for AppConfig {
    fn default() -> Self {
        Self {
            app: ApplicationConfig {
                name: "WeiboAnalytics".to_string(),
                version: env!("CARGO_PKG_VERSION").to_string(),
                environment: Environment::Development,
                debug: true,
                data_dir: PathBuf::from("data"),
                temp_dir: PathBuf::from("tmp"),
            },
            database: DatabaseConfig::default(),
            messaging: MessageQueueConfig::default(),
            logging: LoggingConfig::default(),
            crawler: CrawlerConfig {
                max_concurrent_tasks: 10,
                request_timeout: 30,
                retry_attempts: 3,
                retry_delay: 5,
                rate_limit: RateLimitConfig {
                    requests_per_minute: 60,
                    requests_per_hour: 1000,
                    burst_size: 10,
                },
                user_agent: "WeiboAnalytics/1.0".to_string(),
                proxy: None,
                cookies_file: None,
            },
            analysis: AnalysisConfig {
                sentiment_model: SentimentModelConfig {
                    model_path: PathBuf::from("models/sentiment"),
                    model_type: "bert".to_string(),
                    confidence_threshold: 0.7,
                    batch_size: 32,
                },
                trend_analysis: TrendAnalysisConfig {
                    window_size: 24,
                    min_mentions: 10,
                    update_interval: 3600,
                },
                batch_size: 100,
                max_parallel_jobs: 4,
            },
            server: ServerConfig {
                host: "127.0.0.1".to_string(),
                port: 8080,
                workers: None,
                keep_alive: 75,
                client_timeout: 5000,
                client_shutdown: 5000,
                tls: None,
            },
            security: SecurityConfig {
                secret_key: "your-secret-key-change-in-production".to_string(),
                jwt_expiration: 86400,
                password_min_length: 8,
                max_login_attempts: 5,
                lockout_duration: 900,
                cors: CorsConfig {
                    allowed_origins: vec!["http://localhost:3000".to_string()],
                    allowed_methods: vec!["GET".to_string(), "POST".to_string(), "PUT".to_string(), "DELETE".to_string()],
                    allowed_headers: vec!["Content-Type".to_string(), "Authorization".to_string()],
                    max_age: 3600,
                },
            },
        }
    }
}

// 配置管理器
pub struct ConfigManager {
    config: AppConfig,
    config_path: PathBuf,
    environment_overrides: HashMap<String, String>,
}

impl ConfigManager {
    // 从文件加载配置
    pub fn from_file<P: AsRef<Path>>(path: P) -> Result<Self> {
        let config_path = path.as_ref().to_path_buf();
        let config_content = fs::read_to_string(&config_path)
            .with_context(|| format!("无法读取配置文件: {:?}", config_path))?;

        let mut config: AppConfig = match config_path.extension().and_then(|s| s.to_str()) {
            Some("toml") => toml::from_str(&config_content)?,
            Some("yaml") | Some("yml") => serde_yaml::from_str(&config_content)?,
            Some("json") => serde_json::from_str(&config_content)?,
            _ => return Err(anyhow::anyhow!("不支持的配置文件格式")),
        };

        // 应用环境变量覆盖
        let environment_overrides = Self::load_environment_overrides();
        Self::apply_environment_overrides(&mut config, &environment_overrides)?;

        Ok(Self {
            config,
            config_path,
            environment_overrides,
        })
    }

    // 从默认位置加载配置
    pub fn load() -> Result<Self> {
        let env = env::var("WEIBO_ENV").unwrap_or_else(|_| "development".to_string());
        let config_file = format!("config/{}.toml", env);
        
        if Path::new(&config_file).exists() {
            Self::from_file(config_file)
        } else {
            // 如果配置文件不存在，使用默认配置
            Ok(Self {
                config: AppConfig::default(),
                config_path: PathBuf::from(config_file),
                environment_overrides: Self::load_environment_overrides(),
            })
        }
    }

    // 加载环境变量覆盖
    fn load_environment_overrides() -> HashMap<String, String> {
        env::vars()
            .filter(|(key, _)| key.starts_with("WEIBO_"))
            .collect()
    }

    // 应用环境变量覆盖
    fn apply_environment_overrides(
        config: &mut AppConfig,
        overrides: &HashMap<String, String>,
    ) -> Result<()> {
        for (key, value) in overrides {
            match key.as_str() {
                "WEIBO_DATABASE_URL" => config.database.postgres_url = value.clone(),
                "WEIBO_REDIS_URL" => config.database.redis_url = value.clone(),
                "WEIBO_RABBITMQ_URL" => config.messaging.url = value.clone(),
                "WEIBO_LOG_LEVEL" => config.logging.level = value.clone(),
                "WEIBO_SERVER_HOST" => config.server.host = value.clone(),
                "WEIBO_SERVER_PORT" => {
                    config.server.port = value.parse()
                        .with_context(|| format!("无效的端口号: {}", value))?;
                }
                "WEIBO_SECRET_KEY" => config.security.secret_key = value.clone(),
                "WEIBO_DEBUG" => {
                    config.app.debug = value.parse()
                        .with_context(|| format!("无效的布尔值: {}", value))?;
                }
                _ => {} // 忽略未知的环境变量
            }
        }
        Ok(())
    }

    // 获取配置
    pub fn config(&self) -> &AppConfig {
        &self.config
    }

    // 获取可变配置
    pub fn config_mut(&mut self) -> &mut AppConfig {
        &mut self.config
    }

    // 保存配置到文件
    pub fn save(&self) -> Result<()> {
        let config_content = match self.config_path.extension().and_then(|s| s.to_str()) {
            Some("toml") => toml::to_string_pretty(&self.config)?,
            Some("yaml") | Some("yml") => serde_yaml::to_string(&self.config)?,
            Some("json") => serde_json::to_string_pretty(&self.config)?,
            _ => return Err(anyhow::anyhow!("不支持的配置文件格式")),
        };

        // 确保配置目录存在
        if let Some(parent) = self.config_path.parent() {
            fs::create_dir_all(parent)?;
        }

        fs::write(&self.config_path, config_content)
            .with_context(|| format!("无法写入配置文件: {:?}", self.config_path))?;

        Ok(())
    }

    // 验证配置
    pub fn validate(&self) -> Result<()> {
        // 验证数据库配置
        if self.config.database.postgres_url.is_empty() {
            return Err(anyhow::anyhow!("数据库URL不能为空"));
        }

        // 验证服务器配置
        if self.config.server.port == 0 {
            return Err(anyhow::anyhow!("服务器端口不能为0"));
        }

        // 验证安全配置
        if self.config.security.secret_key.len() < 32 {
            return Err(anyhow::anyhow!("密钥长度至少需要32个字符"));
        }

        // 验证目录存在性
        for dir in [&self.config.app.data_dir, &self.config.app.temp_dir] {
            if !dir.exists() {
                fs::create_dir_all(dir)
                    .with_context(|| format!("无法创建目录: {:?}", dir))?;
            }
        }

        Ok(())
    }

    // 获取环境类型
    pub fn environment(&self) -> &Environment {
        &self.config.app.environment
    }

    // 是否为开发环境
    pub fn is_development(&self) -> bool {
        self.config.app.environment == Environment::Development
    }

    // 是否为生产环境
    pub fn is_production(&self) -> bool {
        self.config.app.environment == Environment::Production
    }

    // 重新加载配置
    pub fn reload(&mut self) -> Result<()> {
        let new_manager = Self::from_file(&self.config_path)?;
        self.config = new_manager.config;
        self.environment_overrides = new_manager.environment_overrides;
        Ok(())
    }
}
