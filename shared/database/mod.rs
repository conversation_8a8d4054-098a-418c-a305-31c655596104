// 数据库连接和ORM配置
// Database Connection and ORM Configuration

use sqlx::{PgPool, Pool, Postgres, Row};
use redis::Client as RedisClient;
use serde::{Deserialize, Serialize};
use std::env;
use anyhow::Result;

// 数据库配置结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseConfig {
    pub postgres_url: String,
    pub redis_url: String,
    pub max_connections: u32,
    pub min_connections: u32,
    pub connection_timeout: u64,
    pub idle_timeout: u64,
}

impl Default for DatabaseConfig {
    fn default() -> Self {
        Self {
            postgres_url: env::var("DATABASE_URL")
                .unwrap_or_else(|_| "postgresql://weibo_app:weibo_app_password@localhost:5432/weibo_analytics".to_string()),
            redis_url: env::var("REDIS_URL")
                .unwrap_or_else(|_| "redis://localhost:6379".to_string()),
            max_connections: 20,
            min_connections: 5,
            connection_timeout: 30,
            idle_timeout: 600,
        }
    }
}

// 数据库连接管理器
#[derive(Clone)]
pub struct DatabaseManager {
    pub pg_pool: PgPool,
    pub redis_client: RedisClient,
    pub config: DatabaseConfig,
}

impl DatabaseManager {
    // 创建新的数据库管理器
    pub async fn new(config: DatabaseConfig) -> Result<Self> {
        // 创建PostgreSQL连接池
        let pg_pool = sqlx::postgres::PgPoolOptions::new()
            .max_connections(config.max_connections)
            .min_connections(config.min_connections)
            .connect_timeout(std::time::Duration::from_secs(config.connection_timeout))
            .idle_timeout(std::time::Duration::from_secs(config.idle_timeout))
            .connect(&config.postgres_url)
            .await?;

        // 创建Redis客户端
        let redis_client = RedisClient::open(config.redis_url.as_str())?;

        // 测试连接
        let mut redis_conn = redis_client.get_connection()?;
        redis::cmd("PING").execute(&mut redis_conn);

        Ok(Self {
            pg_pool,
            redis_client,
            config,
        })
    }

    // 获取PostgreSQL连接池
    pub fn get_pg_pool(&self) -> &PgPool {
        &self.pg_pool
    }

    // 获取Redis连接
    pub fn get_redis_connection(&self) -> Result<redis::Connection> {
        Ok(self.redis_client.get_connection()?)
    }

    // 健康检查
    pub async fn health_check(&self) -> Result<bool> {
        // 检查PostgreSQL
        let pg_result = sqlx::query("SELECT 1")
            .fetch_one(&self.pg_pool)
            .await;

        if pg_result.is_err() {
            return Ok(false);
        }

        // 检查Redis
        let mut redis_conn = self.get_redis_connection()?;
        let redis_result: Result<String, redis::RedisError> = redis::cmd("PING").query(&mut redis_conn);

        Ok(redis_result.is_ok())
    }

    // 执行数据库迁移
    pub async fn run_migrations(&self) -> Result<()> {
        sqlx::migrate!("./database/migrations")
            .run(&self.pg_pool)
            .await?;
        Ok(())
    }
}

// Redis键命名空间
pub struct RedisKeys;

impl RedisKeys {
    pub fn user_session(user_id: &str) -> String {
        format!("weibo:session:{}", user_id)
    }

    pub fn user_cache(user_id: &str) -> String {
        format!("weibo:user:{}", user_id)
    }

    pub fn post_cache(post_id: &str) -> String {
        format!("weibo:post:{}", post_id)
    }

    pub fn task_status(task_id: &str) -> String {
        format!("weibo:task:status:{}", task_id)
    }

    pub fn rate_limit(identifier: &str) -> String {
        format!("weibo:rate_limit:{}", identifier)
    }

    pub fn realtime_stats() -> String {
        "weibo:stats:realtime".to_string()
    }

    pub fn trending_topics() -> String {
        "weibo:trending:topics".to_string()
    }
}

// 数据库错误类型
#[derive(Debug, thiserror::Error)]
pub enum DatabaseError {
    #[error("Connection error: {0}")]
    Connection(#[from] sqlx::Error),
    
    #[error("Redis error: {0}")]
    Redis(#[from] redis::RedisError),
    
    #[error("Migration error: {0}")]
    Migration(String),
    
    #[error("Serialization error: {0}")]
    Serialization(#[from] serde_json::Error),
    
    #[error("Not found")]
    NotFound,
    
    #[error("Duplicate entry")]
    Duplicate,
}

// 分页参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaginationParams {
    pub page: i64,
    pub per_page: i64,
    pub offset: i64,
}

impl PaginationParams {
    pub fn new(page: Option<i64>, per_page: Option<i64>) -> Self {
        let page = page.unwrap_or(1).max(1);
        let per_page = per_page.unwrap_or(20).clamp(1, 100);
        let offset = (page - 1) * per_page;

        Self {
            page,
            per_page,
            offset,
        }
    }
}

// 分页结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaginatedResult<T> {
    pub items: Vec<T>,
    pub total: i64,
    pub page: i64,
    pub per_page: i64,
    pub total_pages: i64,
}

impl<T> PaginatedResult<T> {
    pub fn new(items: Vec<T>, total: i64, pagination: &PaginationParams) -> Self {
        let total_pages = (total + pagination.per_page - 1) / pagination.per_page;

        Self {
            items,
            total,
            page: pagination.page,
            per_page: pagination.per_page,
            total_pages,
        }
    }
}

// 数据库事务宏
#[macro_export]
macro_rules! with_transaction {
    ($pool:expr, $tx_var:ident, $body:block) => {{
        let mut $tx_var = $pool.begin().await?;
        let result = async move $body.await;
        match result {
            Ok(value) => {
                $tx_var.commit().await?;
                Ok(value)
            }
            Err(e) => {
                $tx_var.rollback().await?;
                Err(e)
            }
        }
    }};
}
