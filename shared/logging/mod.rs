// 日志系统模块
// Logging System Module

use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use tracing::{Level, Subscriber};
use tracing_appender::{non_blocking, rolling};
use tracing_subscriber::{
    fmt::{self, format::FmtSpan},
    layer::SubscriberExt,
    util::SubscriberInitExt,
    EnvFilter, Layer, Registry,
};
use anyhow::Result;

// 日志配置结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoggingConfig {
    pub level: String,
    pub format: LogFormat,
    pub output: LogOutput,
    pub file: Option<FileLogConfig>,
    pub console: ConsoleLogConfig,
    pub structured: bool,
    pub include_location: bool,
    pub include_thread_id: bool,
    pub include_thread_name: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum LogFormat {
    <PERSON>,
    Compact,
    <PERSON><PERSON>,
    <PERSON>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum LogOutput {
    Console,
    File,
    Both,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileLogConfig {
    pub directory: PathBuf,
    pub filename_prefix: String,
    pub rotation: FileRotation,
    pub max_files: Option<usize>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FileRotation {
    Never,
    Minutely,
    Hourly,
    Daily,
    Weekly,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConsoleLogConfig {
    pub colored: bool,
    pub show_target: bool,
    pub show_level: bool,
    pub show_time: bool,
}

impl Default for LoggingConfig {
    fn default() -> Self {
        Self {
            level: "info".to_string(),
            format: LogFormat::Pretty,
            output: LogOutput::Both,
            file: Some(FileLogConfig {
                directory: PathBuf::from("logs"),
                filename_prefix: "weibo_analytics".to_string(),
                rotation: FileRotation::Daily,
                max_files: Some(30),
            }),
            console: ConsoleLogConfig {
                colored: true,
                show_target: true,
                show_level: true,
                show_time: true,
            },
            structured: false,
            include_location: true,
            include_thread_id: false,
            include_thread_name: true,
        }
    }
}

// 日志系统初始化器
pub struct LoggingSystem;

impl LoggingSystem {
    // 初始化日志系统
    pub fn init(config: &LoggingConfig) -> Result<()> {
        // 解析日志级别
        let level = match config.level.to_lowercase().as_str() {
            "trace" => Level::TRACE,
            "debug" => Level::DEBUG,
            "info" => Level::INFO,
            "warn" => Level::WARN,
            "error" => Level::ERROR,
            _ => Level::INFO,
        };

        // 创建环境过滤器
        let env_filter = EnvFilter::try_from_default_env()
            .unwrap_or_else(|_| EnvFilter::new(format!("weibo_analytics={}", level)));

        // 创建注册表
        let registry = Registry::default().with(env_filter);

        match config.output {
            LogOutput::Console => {
                let console_layer = Self::create_console_layer(config)?;
                registry.with(console_layer).init();
            }
            LogOutput::File => {
                if let Some(file_config) = &config.file {
                    let file_layer = Self::create_file_layer(config, file_config)?;
                    registry.with(file_layer).init();
                } else {
                    return Err(anyhow::anyhow!("File logging enabled but no file config provided"));
                }
            }
            LogOutput::Both => {
                let console_layer = Self::create_console_layer(config)?;
                
                if let Some(file_config) = &config.file {
                    let file_layer = Self::create_file_layer(config, file_config)?;
                    registry.with(console_layer).with(file_layer).init();
                } else {
                    registry.with(console_layer).init();
                }
            }
        }

        tracing::info!("日志系统初始化完成");
        tracing::info!("日志级别: {}", config.level);
        tracing::info!("日志格式: {:?}", config.format);
        tracing::info!("输出方式: {:?}", config.output);

        Ok(())
    }

    // 创建控制台日志层
    fn create_console_layer(
        config: &LoggingConfig,
    ) -> Result<impl Layer<Registry> + Send + Sync + 'static> {
        let fmt_layer = fmt::layer()
            .with_target(config.console.show_target)
            .with_level(config.console.show_level)
            .with_thread_ids(config.include_thread_id)
            .with_thread_names(config.include_thread_name)
            .with_file(config.include_location)
            .with_line_number(config.include_location)
            .with_span_events(FmtSpan::CLOSE);

        let layer = match config.format {
            LogFormat::Pretty => fmt_layer.pretty().boxed(),
            LogFormat::Compact => fmt_layer.compact().boxed(),
            LogFormat::Json => fmt_layer.json().boxed(),
            LogFormat::Full => fmt_layer.boxed(),
        };

        Ok(layer)
    }

    // 创建文件日志层
    fn create_file_layer(
        config: &LoggingConfig,
        file_config: &FileLogConfig,
    ) -> Result<impl Layer<Registry> + Send + Sync + 'static> {
        // 确保日志目录存在
        std::fs::create_dir_all(&file_config.directory)?;

        // 创建文件追加器
        let file_appender = match file_config.rotation {
            FileRotation::Never => {
                let log_file = file_config.directory.join(format!("{}.log", file_config.filename_prefix));
                rolling::never(&file_config.directory, log_file.file_name().unwrap())
            }
            FileRotation::Minutely => {
                rolling::minutely(&file_config.directory, &file_config.filename_prefix)
            }
            FileRotation::Hourly => {
                rolling::hourly(&file_config.directory, &file_config.filename_prefix)
            }
            FileRotation::Daily => {
                rolling::daily(&file_config.directory, &file_config.filename_prefix)
            }
            FileRotation::Weekly => {
                // tracing_appender 不直接支持周轮转，使用日轮转
                rolling::daily(&file_config.directory, &file_config.filename_prefix)
            }
        };

        let (non_blocking, _guard) = non_blocking(file_appender);

        let fmt_layer = fmt::layer()
            .with_writer(non_blocking)
            .with_target(true)
            .with_level(true)
            .with_thread_ids(config.include_thread_id)
            .with_thread_names(config.include_thread_name)
            .with_file(config.include_location)
            .with_line_number(config.include_location)
            .with_span_events(FmtSpan::CLOSE);

        let layer = if config.structured {
            fmt_layer.json().boxed()
        } else {
            fmt_layer.boxed()
        };

        Ok(layer)
    }

    // 创建结构化日志记录器
    pub fn create_structured_logger() -> StructuredLogger {
        StructuredLogger::new()
    }
}

// 结构化日志记录器
pub struct StructuredLogger {
    service_name: String,
    version: String,
}

impl StructuredLogger {
    pub fn new() -> Self {
        Self {
            service_name: "weibo_analytics".to_string(),
            version: env!("CARGO_PKG_VERSION").to_string(),
        }
    }

    pub fn with_service_name(mut self, name: String) -> Self {
        self.service_name = name;
        self
    }

    // 记录请求日志
    pub fn log_request(&self, method: &str, path: &str, status: u16, duration_ms: u64) {
        tracing::info!(
            service = %self.service_name,
            version = %self.version,
            event_type = "http_request",
            method = %method,
            path = %path,
            status = %status,
            duration_ms = %duration_ms,
            "HTTP请求处理完成"
        );
    }

    // 记录任务日志
    pub fn log_task(&self, task_id: &str, task_type: &str, status: &str, duration_ms: Option<u64>) {
        tracing::info!(
            service = %self.service_name,
            version = %self.version,
            event_type = "task_execution",
            task_id = %task_id,
            task_type = %task_type,
            status = %status,
            duration_ms = ?duration_ms,
            "任务执行状态更新"
        );
    }

    // 记录错误日志
    pub fn log_error(&self, error: &anyhow::Error, context: Option<&str>) {
        tracing::error!(
            service = %self.service_name,
            version = %self.version,
            event_type = "error",
            error = %error,
            context = ?context,
            "系统错误发生"
        );
    }

    // 记录性能指标
    pub fn log_metrics(&self, metric_name: &str, value: f64, unit: &str, tags: Option<&std::collections::HashMap<String, String>>) {
        tracing::info!(
            service = %self.service_name,
            version = %self.version,
            event_type = "metrics",
            metric_name = %metric_name,
            value = %value,
            unit = %unit,
            tags = ?tags,
            "性能指标记录"
        );
    }
}

// 日志宏定义
#[macro_export]
macro_rules! log_info {
    ($($arg:tt)*) => {
        tracing::info!($($arg)*)
    };
}

#[macro_export]
macro_rules! log_warn {
    ($($arg:tt)*) => {
        tracing::warn!($($arg)*)
    };
}

#[macro_export]
macro_rules! log_error {
    ($($arg:tt)*) => {
        tracing::error!($($arg)*)
    };
}

#[macro_export]
macro_rules! log_debug {
    ($($arg:tt)*) => {
        tracing::debug!($($arg)*)
    };
}

// 性能监控宏
#[macro_export]
macro_rules! measure_time {
    ($name:expr, $code:block) => {{
        let start = std::time::Instant::now();
        let result = $code;
        let duration = start.elapsed();
        tracing::info!(
            event_type = "performance",
            operation = $name,
            duration_ms = duration.as_millis(),
            "操作执行时间"
        );
        result
    }};
}
