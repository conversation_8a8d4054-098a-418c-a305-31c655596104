// 通用工具函数库 - Rust版本
// Common Utility Functions Library - Rust Version

use chrono::{DateTime, Local, NaiveDateTime, TimeZone, Utc};
use regex::Regex;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use uuid::Uuid;
use anyhow::Result;

// 时间处理工具
pub mod time {
    use super::*;

    // 格式化时间戳
    pub fn format_timestamp(timestamp: i64, format: &str) -> String {
        let dt = DateTime::<Utc>::from_utc(
            NaiveDateTime::from_timestamp(timestamp, 0).unwrap_or_default(),
            Utc,
        );
        dt.format(format).to_string()
    }

    // 获取当前时间戳
    pub fn current_timestamp() -> i64 {
        Utc::now().timestamp()
    }

    // 获取当前时间戳（毫秒）
    pub fn current_timestamp_millis() -> i64 {
        Utc::now().timestamp_millis()
    }

    // 相对时间格式化
    pub fn format_relative_time(timestamp: i64) -> String {
        let now = Utc::now().timestamp();
        let diff = now - timestamp;

        match diff {
            0..=59 => "刚刚".to_string(),
            60..=3599 => format!("{}分钟前", diff / 60),
            3600..=86399 => format!("{}小时前", diff / 3600),
            86400..=2591999 => format!("{}天前", diff / 86400),
            2592000..=31535999 => format!("{}个月前", diff / 2592000),
            _ => format!("{}年前", diff / 31536000),
        }
    }

    // 解析时间字符串
    pub fn parse_time_string(time_str: &str) -> Result<DateTime<Utc>> {
        let formats = vec![
            "%Y-%m-%d %H:%M:%S",
            "%Y-%m-%dT%H:%M:%S",
            "%Y-%m-%dT%H:%M:%SZ",
            "%Y-%m-%d",
        ];

        for format in formats {
            if let Ok(dt) = NaiveDateTime::parse_from_str(time_str, format) {
                return Ok(Utc.from_utc_datetime(&dt));
            }
        }

        Err(anyhow::anyhow!("无法解析时间字符串: {}", time_str))
    }

    // 时间范围检查
    pub fn is_within_range(timestamp: i64, start: i64, end: i64) -> bool {
        timestamp >= start && timestamp <= end
    }
}

// 字符串处理工具
pub mod string {
    use super::*;

    // 截断文本
    pub fn truncate(text: &str, max_length: usize) -> String {
        if text.len() <= max_length {
            text.to_string()
        } else {
            format!("{}...", &text[..max_length.saturating_sub(3)])
        }
    }

    // 提取话题标签
    pub fn extract_hashtags(text: &str) -> Vec<String> {
        let re = Regex::new(r"#([^#\s]+)#").unwrap();
        re.captures_iter(text)
            .map(|cap| cap[1].to_string())
            .collect()
    }

    // 提取@用户
    pub fn extract_mentions(text: &str) -> Vec<String> {
        let re = Regex::new(r"@([^\s@]+)").unwrap();
        re.captures_iter(text)
            .map(|cap| cap[1].to_string())
            .collect()
    }

    // 清理HTML标签
    pub fn strip_html_tags(html: &str) -> String {
        let re = Regex::new(r"<[^>]*>").unwrap();
        re.replace_all(html, "").to_string()
    }

    // 验证邮箱格式
    pub fn is_valid_email(email: &str) -> bool {
        let re = Regex::new(r"^[^\s@]+@[^\s@]+\.[^\s@]+$").unwrap();
        re.is_match(email)
    }

    // 验证URL格式
    pub fn is_valid_url(url: &str) -> bool {
        url::Url::parse(url).is_ok()
    }

    // 生成随机字符串
    pub fn generate_random_string(length: usize) -> String {
        use rand::Rng;
        const CHARSET: &[u8] = b"ABCDEFGHIJKLMNOPQRSTUVWXYZ\
                                abcdefghijklmnopqrstuvwxyz\
                                0123456789";
        let mut rng = rand::thread_rng();
        (0..length)
            .map(|_| {
                let idx = rng.gen_range(0..CHARSET.len());
                CHARSET[idx] as char
            })
            .collect()
    }

    // 计算字符串相似度
    pub fn similarity(s1: &str, s2: &str) -> f64 {
        let len1 = s1.len();
        let len2 = s2.len();
        
        if len1 == 0 && len2 == 0 {
            return 1.0;
        }
        
        if len1 == 0 || len2 == 0 {
            return 0.0;
        }

        let mut matrix = vec![vec![0; len2 + 1]; len1 + 1];
        
        for i in 0..=len1 {
            matrix[i][0] = i;
        }
        
        for j in 0..=len2 {
            matrix[0][j] = j;
        }
        
        for (i, c1) in s1.chars().enumerate() {
            for (j, c2) in s2.chars().enumerate() {
                let cost = if c1 == c2 { 0 } else { 1 };
                matrix[i + 1][j + 1] = std::cmp::min(
                    std::cmp::min(
                        matrix[i][j + 1] + 1,
                        matrix[i + 1][j] + 1
                    ),
                    matrix[i][j] + cost
                );
            }
        }
        
        let distance = matrix[len1][len2];
        1.0 - (distance as f64 / std::cmp::max(len1, len2) as f64)
    }
}

// 数字处理工具
pub mod number {
    use super::*;

    // 格式化数字
    pub fn format_number(num: i64) -> String {
        match num {
            0..=999 => num.to_string(),
            1000..=9999 => format!("{:.1}K", num as f64 / 1000.0),
            10000..=99999999 => format!("{:.1}万", num as f64 / 10000.0),
            _ => format!("{:.1}亿", num as f64 / 100000000.0),
        }
    }

    // 计算百分比
    pub fn calculate_percentage(value: f64, total: f64) -> f64 {
        if total == 0.0 {
            0.0
        } else {
            (value / total) * 100.0
        }
    }

    // 格式化百分比
    pub fn format_percentage(value: f64, total: f64, decimal_places: usize) -> String {
        let percentage = calculate_percentage(value, total);
        format!("{:.1$}%", percentage, decimal_places)
    }

    // 安全除法
    pub fn safe_divide(dividend: f64, divisor: f64) -> Option<f64> {
        if divisor == 0.0 {
            None
        } else {
            Some(dividend / divisor)
        }
    }

    // 数字范围检查
    pub fn is_in_range<T: PartialOrd>(value: T, min: T, max: T) -> bool {
        value >= min && value <= max
    }
}

// 加密和哈希工具
pub mod crypto {
    use super::*;
    use sha2::{Digest, Sha256};
    use base64::{Engine as _, engine::general_purpose};

    // 生成UUID
    pub fn generate_uuid() -> String {
        Uuid::new_v4().to_string()
    }

    // SHA256哈希
    pub fn sha256_hash(input: &str) -> String {
        let mut hasher = Sha256::new();
        hasher.update(input.as_bytes());
        format!("{:x}", hasher.finalize())
    }

    // Base64编码
    pub fn base64_encode(input: &[u8]) -> String {
        general_purpose::STANDARD.encode(input)
    }

    // Base64解码
    pub fn base64_decode(input: &str) -> Result<Vec<u8>> {
        general_purpose::STANDARD.decode(input)
            .map_err(|e| anyhow::anyhow!("Base64解码失败: {}", e))
    }

    // 简单哈希函数
    pub fn simple_hash(input: &str) -> u64 {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};
        
        let mut hasher = DefaultHasher::new();
        input.hash(&mut hasher);
        hasher.finish()
    }
}

// 文件处理工具
pub mod file {
    use super::*;
    use std::path::Path;

    // 格式化文件大小
    pub fn format_file_size(bytes: u64) -> String {
        const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
        let mut size = bytes as f64;
        let mut unit_index = 0;

        while size >= 1024.0 && unit_index < UNITS.len() - 1 {
            size /= 1024.0;
            unit_index += 1;
        }

        format!("{:.2} {}", size, UNITS[unit_index])
    }

    // 获取文件扩展名
    pub fn get_file_extension(filename: &str) -> Option<String> {
        Path::new(filename)
            .extension()
            .and_then(|ext| ext.to_str())
            .map(|ext| ext.to_lowercase())
    }

    // 检查是否为图片文件
    pub fn is_image_file(filename: &str) -> bool {
        const IMAGE_EXTENSIONS: &[&str] = &["jpg", "jpeg", "png", "gif", "bmp", "webp", "svg"];
        get_file_extension(filename)
            .map(|ext| IMAGE_EXTENSIONS.contains(&ext.as_str()))
            .unwrap_or(false)
    }

    // 检查是否为视频文件
    pub fn is_video_file(filename: &str) -> bool {
        const VIDEO_EXTENSIONS: &[&str] = &["mp4", "avi", "mov", "wmv", "flv", "webm", "mkv"];
        get_file_extension(filename)
            .map(|ext| VIDEO_EXTENSIONS.contains(&ext.as_str()))
            .unwrap_or(false)
    }

    // 生成安全的文件名
    pub fn sanitize_filename(filename: &str) -> String {
        let re = Regex::new(r"[<>:\"/\\|?*]").unwrap();
        re.replace_all(filename, "_").to_string()
    }
}

// 网络工具
pub mod network {
    use super::*;

    // 验证IP地址
    pub fn is_valid_ip(ip: &str) -> bool {
        ip.parse::<std::net::IpAddr>().is_ok()
    }

    // 提取域名
    pub fn extract_domain(url: &str) -> Option<String> {
        url::Url::parse(url)
            .ok()
            .and_then(|u| u.host_str().map(|h| h.to_string()))
    }

    // 构建查询字符串
    pub fn build_query_string(params: &HashMap<String, String>) -> String {
        params
            .iter()
            .map(|(k, v)| format!("{}={}", urlencoding::encode(k), urlencoding::encode(v)))
            .collect::<Vec<_>>()
            .join("&")
    }
}

// 性能监控工具
pub mod performance {
    use super::*;

    // 性能计时器
    pub struct Timer {
        start: SystemTime,
        name: String,
    }

    impl Timer {
        pub fn new(name: &str) -> Self {
            Self {
                start: SystemTime::now(),
                name: name.to_string(),
            }
        }

        pub fn elapsed(&self) -> Duration {
            self.start.elapsed().unwrap_or_default()
        }

        pub fn elapsed_millis(&self) -> u128 {
            self.elapsed().as_millis()
        }
    }

    impl Drop for Timer {
        fn drop(&mut self) {
            let elapsed = self.elapsed_millis();
            tracing::debug!("操作 '{}' 耗时: {}ms", self.name, elapsed);
        }
    }

    // 内存使用情况
    pub fn get_memory_usage() -> Option<usize> {
        // 这里可以实现具体的内存使用情况获取逻辑
        // 为了演示，返回None
        None
    }
}

// 重试机制
pub mod retry {
    use super::*;
    use tokio::time::{sleep, Duration};

    // 重试配置
    #[derive(Debug, Clone)]
    pub struct RetryConfig {
        pub max_attempts: u32,
        pub initial_delay: Duration,
        pub max_delay: Duration,
        pub backoff_factor: f64,
    }

    impl Default for RetryConfig {
        fn default() -> Self {
            Self {
                max_attempts: 3,
                initial_delay: Duration::from_millis(100),
                max_delay: Duration::from_secs(30),
                backoff_factor: 2.0,
            }
        }
    }

    // 重试执行
    pub async fn retry_async<F, Fut, T, E>(
        config: &RetryConfig,
        mut operation: F,
    ) -> Result<T, E>
    where
        F: FnMut() -> Fut,
        Fut: std::future::Future<Output = Result<T, E>>,
        E: std::fmt::Debug,
    {
        let mut delay = config.initial_delay;
        
        for attempt in 1..=config.max_attempts {
            match operation().await {
                Ok(result) => return Ok(result),
                Err(error) => {
                    if attempt == config.max_attempts {
                        return Err(error);
                    }
                    
                    tracing::warn!("操作失败，第{}次重试，错误: {:?}", attempt, error);
                    sleep(delay).await;
                    
                    delay = std::cmp::min(
                        Duration::from_millis((delay.as_millis() as f64 * config.backoff_factor) as u64),
                        config.max_delay,
                    );
                }
            }
        }
        
        unreachable!()
    }
}
