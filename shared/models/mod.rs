// 数据模型定义
// Data Models Definition

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;

// 用户模型
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct User {
    pub id: Uuid,
    pub username: String,
    pub nickname: String,
    pub avatar: Option<String>,
    pub verified: bool,
    pub followers_count: i32,
    pub following_count: i32,
    pub posts_count: i32,
    pub description: Option<String>,
    pub location: Option<String>,
    pub gender: Option<String>,
    pub birthday: Option<chrono::NaiveDate>,
    pub registration_date: Option<chrono::NaiveDate>,
    pub verification_type: Option<String>,
    pub verification_reason: Option<String>,
    pub is_active: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

// 用户创建请求
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct CreateUserRequest {
    pub username: String,
    pub nickname: String,
    pub avatar: Option<String>,
    pub verified: Option<bool>,
    pub description: Option<String>,
    pub location: Option<String>,
    pub gender: Option<String>,
}

// 微博内容模型
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct Post {
    pub id: Uuid,
    pub weibo_id: String,
    pub user_id: Uuid,
    pub content: String,
    pub images: Option<serde_json::Value>, // JSONB
    pub video: Option<serde_json::Value>,  // JSONB
    pub repost_count: i32,
    pub comment_count: i32,
    pub like_count: i32,
    pub topic_tags: Vec<String>,
    pub mentions: Vec<String>,
    pub is_repost: bool,
    pub original_post_id: Option<Uuid>,
    pub source: Option<String>,
    pub location: Option<String>,
    pub ip_location: Option<String>,
    pub is_deleted: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

// 微博创建请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreatePostRequest {
    pub weibo_id: String,
    pub user_id: Uuid,
    pub content: String,
    pub images: Option<serde_json::Value>,
    pub video: Option<serde_json::Value>,
    pub topic_tags: Option<Vec<String>>,
    pub mentions: Option<Vec<String>>,
    pub is_repost: Option<bool>,
    pub original_post_id: Option<Uuid>,
    pub source: Option<String>,
    pub location: Option<String>,
}

// 评论模型
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct Comment {
    pub id: Uuid,
    pub comment_id: String,
    pub post_id: Uuid,
    pub user_id: Uuid,
    pub content: String,
    pub like_count: i32,
    pub reply_to: Option<Uuid>,
    pub images: Option<serde_json::Value>,
    pub ip_location: Option<String>,
    pub is_deleted: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

// 评论创建请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateCommentRequest {
    pub comment_id: String,
    pub post_id: Uuid,
    pub user_id: Uuid,
    pub content: String,
    pub reply_to: Option<Uuid>,
    pub images: Option<serde_json::Value>,
}

// 爬取任务模型
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct CrawlTask {
    pub id: Uuid,
    pub task_type: String, // 'user', 'keyword', 'topic', 'comment'
    pub target: String,
    pub status: String, // 'pending', 'running', 'completed', 'failed', 'paused'
    pub progress: i32,
    pub config: serde_json::Value,
    pub result: Option<serde_json::Value>,
    pub error_message: Option<String>,
    pub created_by: Option<String>,
    pub started_at: Option<DateTime<Utc>>,
    pub completed_at: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

// 任务创建请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateTaskRequest {
    pub task_type: String,
    pub target: String,
    pub config: serde_json::Value,
    pub created_by: Option<String>,
}

// 任务配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskConfig {
    pub max_posts: Option<i32>,
    pub max_comments: Option<i32>,
    pub date_range: Option<DateRange>,
    pub include_reposts: Option<bool>,
    pub include_comments: Option<bool>,
    pub rate_limit: Option<RateLimit>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DateRange {
    pub start: DateTime<Utc>,
    pub end: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RateLimit {
    pub requests_per_minute: i32,
    pub requests_per_hour: i32,
}

// 情感分析结果模型
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct SentimentAnalysis {
    pub id: Uuid,
    pub post_id: Uuid,
    pub sentiment: String, // 'positive', 'negative', 'neutral'
    pub score: f32,
    pub confidence: f32,
    pub keywords: Vec<String>,
    pub model_version: Option<String>,
    pub analyzed_at: DateTime<Utc>,
}

// 情感分析创建请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateSentimentAnalysisRequest {
    pub post_id: Uuid,
    pub sentiment: String,
    pub score: f32,
    pub confidence: f32,
    pub keywords: Vec<String>,
    pub model_version: Option<String>,
}

// 系统配置模型
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct SystemConfig {
    pub id: Uuid,
    pub key: String,
    pub value: serde_json::Value,
    pub description: Option<String>,
    pub is_active: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

// 统计数据模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Statistics {
    pub total_users: i64,
    pub total_posts: i64,
    pub total_comments: i64,
    pub active_tasks: i64,
    pub completed_tasks: i64,
    pub failed_tasks: i64,
    pub last_updated: DateTime<Utc>,
}

// 趋势分析模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrendAnalysis {
    pub keyword: String,
    pub date: chrono::NaiveDate,
    pub mention_count: i32,
    pub sentiment_score: f32,
    pub influence_score: f32,
}

// 查询过滤器
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PostFilter {
    pub user_id: Option<Uuid>,
    pub keyword: Option<String>,
    pub topic_tags: Option<Vec<String>>,
    pub date_from: Option<DateTime<Utc>>,
    pub date_to: Option<DateTime<Utc>>,
    pub min_likes: Option<i32>,
    pub verified_only: Option<bool>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserFilter {
    pub verified: Option<bool>,
    pub min_followers: Option<i32>,
    pub location: Option<String>,
    pub registration_after: Option<chrono::NaiveDate>,
}

// API响应包装器
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub error: Option<String>,
    pub message: Option<String>,
    pub timestamp: DateTime<Utc>,
}

impl<T> ApiResponse<T> {
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            error: None,
            message: None,
            timestamp: Utc::now(),
        }
    }

    pub fn error(error: String) -> Self {
        Self {
            success: false,
            data: None,
            error: Some(error),
            message: None,
            timestamp: Utc::now(),
        }
    }

    pub fn message(message: String) -> Self {
        Self {
            success: true,
            data: None,
            error: None,
            message: Some(message),
            timestamp: Utc::now(),
        }
    }
}
