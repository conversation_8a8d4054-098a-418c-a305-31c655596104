// 消息队列系统模块
// Message Queue System Module

use lapin::{
    options::*, types::FieldTable, BasicProperties, Channel, Connection, ConnectionProperties,
    Consumer, Queue,
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tokio_amqp::*;
use anyhow::Result;
use tracing::{error, info, warn};

// 消息队列配置
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct MessageQueueConfig {
    pub url: String,
    pub vhost: String,
    pub username: String,
    pub password: String,
    pub connection_timeout: u64,
    pub heartbeat: u16,
    pub max_retries: u32,
    pub retry_delay: u64,
}

impl Default for MessageQueueConfig {
    fn default() -> Self {
        Self {
            url: "amqp://localhost:5672".to_string(),
            vhost: "/weibo".to_string(),
            username: "weibo_app".to_string(),
            password: "weibo_app_password".to_string(),
            connection_timeout: 30,
            heartbeat: 60,
            max_retries: 3,
            retry_delay: 5,
        }
    }
}

// 消息类型定义
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
#[serde(tag = "type", content = "data")]
pub enum MessageType {
    CrawlTask(CrawlTaskMessage),
    CrawlResult(CrawlResultMessage),
    AnalysisTask(AnalysisTaskMessage),
    Notification(NotificationMessage),
    HealthCheck(HealthCheckMessage),
}

// 爬取任务消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CrawlTaskMessage {
    pub task_id: String,
    pub task_type: String,
    pub target: String,
    pub config: serde_json::Value,
    pub priority: u8,
    pub created_at: chrono::DateTime<chrono::Utc>,
}

// 爬取结果消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CrawlResultMessage {
    pub task_id: String,
    pub status: String,
    pub progress: u8,
    pub data: Option<serde_json::Value>,
    pub error: Option<String>,
    pub completed_at: chrono::DateTime<chrono::Utc>,
}

// 分析任务消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnalysisTaskMessage {
    pub task_id: String,
    pub analysis_type: String,
    pub data_id: String,
    pub config: serde_json::Value,
    pub created_at: chrono::DateTime<chrono::Utc>,
}

// 通知消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NotificationMessage {
    pub id: String,
    pub title: String,
    pub content: String,
    pub level: String, // info, warning, error
    pub target: Option<String>,
    pub created_at: chrono::DateTime<chrono::Utc>,
}

// 健康检查消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthCheckMessage {
    pub service_name: String,
    pub status: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

// 消息队列管理器
#[derive(Clone)]
pub struct MessageQueueManager {
    connection: Connection,
    channel: Channel,
    config: MessageQueueConfig,
    queues: HashMap<String, Queue>,
}

impl MessageQueueManager {
    // 创建新的消息队列管理器
    pub async fn new(config: MessageQueueConfig) -> Result<Self> {
        let connection_properties = ConnectionProperties::default()
            .with_connection_name("weibo_analytics".into())
            .with_executor(tokio_executor_trait::Tokio::current())
            .with_reactor(tokio_reactor_trait::Tokio);

        let connection = Connection::connect(&config.url, connection_properties).await?;
        let channel = connection.create_channel().await?;

        let mut manager = Self {
            connection,
            channel,
            config,
            queues: HashMap::new(),
        };

        // 初始化队列
        manager.initialize_queues().await?;

        Ok(manager)
    }

    // 初始化队列
    async fn initialize_queues(&mut self) -> Result<()> {
        let queue_configs = vec![
            ("weibo.crawl.tasks", true, false),
            ("weibo.crawl.results", true, false),
            ("weibo.analysis.tasks", true, false),
            ("weibo.notifications", true, false),
            ("weibo.dead_letter", true, false),
        ];

        for (queue_name, durable, auto_delete) in queue_configs {
            let queue = self
                .channel
                .queue_declare(
                    queue_name,
                    QueueDeclareOptions {
                        durable,
                        auto_delete,
                        ..Default::default()
                    },
                    FieldTable::default(),
                )
                .await?;

            self.queues.insert(queue_name.to_string(), queue);
            info!("Queue '{}' initialized", queue_name);
        }

        Ok(())
    }

    // 发布消息
    pub async fn publish_message(
        &self,
        exchange: &str,
        routing_key: &str,
        message: &MessageType,
        priority: Option<u8>,
    ) -> Result<()> {
        let payload = serde_json::to_vec(message)?;
        
        let properties = BasicProperties::default()
            .with_priority(priority.unwrap_or(0))
            .with_timestamp(chrono::Utc::now().timestamp() as u64)
            .with_content_type("application/json".into())
            .with_delivery_mode(2); // 持久化消息

        self.channel
            .basic_publish(
                exchange,
                routing_key,
                BasicPublishOptions::default(),
                &payload,
                properties,
            )
            .await?;

        info!("Message published to {}:{}", exchange, routing_key);
        Ok(())
    }

    // 消费消息
    pub async fn consume_messages<F>(
        &self,
        queue_name: &str,
        consumer_tag: &str,
        handler: F,
    ) -> Result<Consumer>
    where
        F: Fn(MessageType) -> Result<()> + Send + Sync + 'static,
    {
        let consumer = self
            .channel
            .basic_consume(
                queue_name,
                consumer_tag,
                BasicConsumeOptions::default(),
                FieldTable::default(),
            )
            .await?;

        info!("Consumer '{}' started for queue '{}'", consumer_tag, queue_name);
        Ok(consumer)
    }

    // 发布爬取任务
    pub async fn publish_crawl_task(&self, task: CrawlTaskMessage) -> Result<()> {
        let message = MessageType::CrawlTask(task);
        self.publish_message("weibo.direct", "crawl.task", &message, Some(5))
            .await
    }

    // 发布爬取结果
    pub async fn publish_crawl_result(&self, result: CrawlResultMessage) -> Result<()> {
        let message = MessageType::CrawlResult(result);
        self.publish_message("weibo.direct", "crawl.result", &message, Some(3))
            .await
    }

    // 发布分析任务
    pub async fn publish_analysis_task(&self, task: AnalysisTaskMessage) -> Result<()> {
        let message = MessageType::AnalysisTask(task);
        self.publish_message("weibo.direct", "analysis.task", &message, Some(4))
            .await
    }

    // 发布通知
    pub async fn publish_notification(&self, notification: NotificationMessage) -> Result<()> {
        let routing_key = format!("notification.{}", notification.level);
        let message = MessageType::Notification(notification);
        self.publish_message("weibo.topic", &routing_key, &message, Some(2))
            .await
    }

    // 健康检查
    pub async fn health_check(&self) -> Result<bool> {
        // 检查连接状态
        if self.connection.status().connected() {
            // 发送健康检查消息
            let health_msg = HealthCheckMessage {
                service_name: "message_queue".to_string(),
                status: "healthy".to_string(),
                timestamp: chrono::Utc::now(),
            };
            
            let message = MessageType::HealthCheck(health_msg);
            self.publish_message("weibo.direct", "health.check", &message, Some(1))
                .await?;
            
            Ok(true)
        } else {
            Ok(false)
        }
    }

    // 获取队列信息
    pub async fn get_queue_info(&self, queue_name: &str) -> Result<Option<(u32, u32)>> {
        if let Some(_queue) = self.queues.get(queue_name) {
            // 这里应该调用RabbitMQ管理API获取队列统计信息
            // 为了演示，返回模拟数据
            Ok(Some((0, 0))) // (message_count, consumer_count)
        } else {
            Ok(None)
        }
    }

    // 关闭连接
    pub async fn close(&self) -> Result<()> {
        self.channel.close(200, "Normal shutdown").await?;
        self.connection.close(200, "Normal shutdown").await?;
        info!("Message queue connection closed");
        Ok(())
    }
}

// 消息处理器特征
#[async_trait::async_trait]
pub trait MessageHandler: Send + Sync {
    async fn handle_crawl_task(&self, task: CrawlTaskMessage) -> Result<()>;
    async fn handle_crawl_result(&self, result: CrawlResultMessage) -> Result<()>;
    async fn handle_analysis_task(&self, task: AnalysisTaskMessage) -> Result<()>;
    async fn handle_notification(&self, notification: NotificationMessage) -> Result<()>;
    async fn handle_health_check(&self, health: HealthCheckMessage) -> Result<()>;
}

// 消息路由器
pub struct MessageRouter<H: MessageHandler> {
    handler: H,
}

impl<H: MessageHandler> MessageRouter<H> {
    pub fn new(handler: H) -> Self {
        Self { handler }
    }

    pub async fn route_message(&self, message: MessageType) -> Result<()> {
        match message {
            MessageType::CrawlTask(task) => self.handler.handle_crawl_task(task).await,
            MessageType::CrawlResult(result) => self.handler.handle_crawl_result(result).await,
            MessageType::AnalysisTask(task) => self.handler.handle_analysis_task(task).await,
            MessageType::Notification(notification) => {
                self.handler.handle_notification(notification).await
            }
            MessageType::HealthCheck(health) => self.handler.handle_health_check(health).await,
        }
    }
}
