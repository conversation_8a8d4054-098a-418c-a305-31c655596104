// 共享类型定义
// 微博数据采集分析系统

// 用户数据模型
export interface WeiboUser {
  id: string;
  username: string;
  nickname: string;
  avatar: string;
  verified: boolean;
  followers_count: number;
  following_count: number;
  posts_count: number;
  description?: string;
  location?: string;
  created_at: string;
  updated_at: string;
}

// 微博内容模型
export interface WeiboPost {
  id: string;
  user_id: string;
  content: string;
  images?: string[];
  video?: string;
  repost_count: number;
  comment_count: number;
  like_count: number;
  topic_tags: string[];
  mentions: string[];
  is_repost: boolean;
  original_post_id?: string;
  created_at: string;
  updated_at: string;
}

// 评论数据模型
export interface WeiboComment {
  id: string;
  post_id: string;
  user_id: string;
  content: string;
  like_count: number;
  reply_to?: string;
  created_at: string;
}

// 任务相关类型
export interface CrawlTask {
  id: string;
  type: 'user' | 'keyword' | 'topic' | 'comment';
  target: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  error_message?: string;
  config: CrawlConfig;
}

export interface CrawlConfig {
  max_posts?: number;
  max_comments?: number;
  date_range?: {
    start: string;
    end: string;
  };
  include_reposts?: boolean;
  include_comments?: boolean;
}

// 分析结果类型
export interface SentimentAnalysis {
  post_id: string;
  sentiment: 'positive' | 'negative' | 'neutral';
  score: number;
  confidence: number;
  keywords: string[];
  analyzed_at: string;
}

export interface TrendAnalysis {
  keyword: string;
  date: string;
  mention_count: number;
  sentiment_score: number;
  influence_score: number;
}

// API响应类型
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// 分页类型
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  per_page: number;
  total_pages: number;
}

// 统计数据类型
export interface Statistics {
  total_users: number;
  total_posts: number;
  total_comments: number;
  active_tasks: number;
  completed_tasks: number;
  failed_tasks: number;
  last_updated: string;
}
