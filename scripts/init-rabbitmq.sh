#!/bin/bash

# 微博数据采集分析系统 - RabbitMQ初始化脚本
# WeiboAnalytics RabbitMQ Initialization Script

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
RABBITMQ_HOST=${RABBITMQ_HOST:-localhost}
RABBITMQ_PORT=${RABBITMQ_PORT:-5672}
RABBITMQ_MGMT_PORT=${RABBITMQ_MGMT_PORT:-15672}
RABBITMQ_USER=${RABBITMQ_USER:-guest}
RABBITMQ_PASS=${RABBITMQ_PASS:-guest}

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}微博数据采集分析系统 - RabbitMQ初始化${NC}"
echo -e "${BLUE}WeiboAnalytics RabbitMQ Initialization${NC}"
echo -e "${BLUE}========================================${NC}"

# 检查RabbitMQ服务状态
check_rabbitmq_status() {
    echo -e "${YELLOW}检查RabbitMQ服务状态...${NC}"
    
    if systemctl is-active --quiet rabbitmq-server; then
        echo -e "${GREEN}✓ RabbitMQ服务正在运行${NC}"
    else
        echo -e "${RED}✗ RabbitMQ服务未运行，正在启动...${NC}"
        sudo systemctl start rabbitmq-server
        sleep 5
        
        if systemctl is-active --quiet rabbitmq-server; then
            echo -e "${GREEN}✓ RabbitMQ服务启动成功${NC}"
        else
            echo -e "${RED}✗ RabbitMQ服务启动失败${NC}"
            exit 1
        fi
    fi
}

# 启用管理插件
enable_management_plugin() {
    echo -e "${YELLOW}启用RabbitMQ管理插件...${NC}"
    
    if sudo rabbitmq-plugins list | grep -q "rabbitmq_management.*E"; then
        echo -e "${GREEN}✓ 管理插件已启用${NC}"
    else
        sudo rabbitmq-plugins enable rabbitmq_management
        echo -e "${GREEN}✓ 管理插件启用成功${NC}"
    fi
}

# 创建用户和虚拟主机
setup_users_and_vhosts() {
    echo -e "${YELLOW}设置用户和虚拟主机...${NC}"
    
    # 创建虚拟主机
    sudo rabbitmqctl add_vhost /weibo || echo "虚拟主机 /weibo 可能已存在"
    
    # 创建管理员用户
    sudo rabbitmqctl add_user weibo_admin weibo_admin_password || echo "用户 weibo_admin 可能已存在"
    sudo rabbitmqctl set_user_tags weibo_admin administrator
    sudo rabbitmqctl set_permissions -p / weibo_admin ".*" ".*" ".*"
    sudo rabbitmqctl set_permissions -p /weibo weibo_admin ".*" ".*" ".*"
    
    # 创建应用用户
    sudo rabbitmqctl add_user weibo_app weibo_app_password || echo "用户 weibo_app 可能已存在"
    sudo rabbitmqctl set_permissions -p /weibo weibo_app "weibo\..*" "weibo\..*" "weibo\..*"
    
    echo -e "${GREEN}✓ 用户和虚拟主机设置完成${NC}"
}

# 创建交换机
create_exchanges() {
    echo -e "${YELLOW}创建交换机...${NC}"
    
    # 使用rabbitmqadmin创建交换机
    if command -v rabbitmqadmin &> /dev/null; then
        rabbitmqadmin -H $RABBITMQ_HOST -P $RABBITMQ_MGMT_PORT -u weibo_admin -p weibo_admin_password \
            declare exchange --vhost=/weibo name=weibo.direct type=direct durable=true
        
        rabbitmqadmin -H $RABBITMQ_HOST -P $RABBITMQ_MGMT_PORT -u weibo_admin -p weibo_admin_password \
            declare exchange --vhost=/weibo name=weibo.topic type=topic durable=true
        
        rabbitmqadmin -H $RABBITMQ_HOST -P $RABBITMQ_MGMT_PORT -u weibo_admin -p weibo_admin_password \
            declare exchange --vhost=/weibo name=weibo.fanout type=fanout durable=true
        
        rabbitmqadmin -H $RABBITMQ_HOST -P $RABBITMQ_MGMT_PORT -u weibo_admin -p weibo_admin_password \
            declare exchange --vhost=/weibo name=weibo.dead_letter_exchange type=direct durable=true
        
        echo -e "${GREEN}✓ 交换机创建完成${NC}"
    else
        echo -e "${YELLOW}⚠ rabbitmqadmin未安装，跳过交换机创建${NC}"
    fi
}

# 创建队列
create_queues() {
    echo -e "${YELLOW}创建队列...${NC}"
    
    if command -v rabbitmqadmin &> /dev/null; then
        # 爬取任务队列
        rabbitmqadmin -H $RABBITMQ_HOST -P $RABBITMQ_MGMT_PORT -u weibo_admin -p weibo_admin_password \
            declare queue --vhost=/weibo name=weibo.crawl.tasks durable=true \
            arguments='{"x-message-ttl":3600000,"x-max-length":10000,"x-overflow":"reject-publish"}'
        
        # 爬取结果队列
        rabbitmqadmin -H $RABBITMQ_HOST -P $RABBITMQ_MGMT_PORT -u weibo_admin -p weibo_admin_password \
            declare queue --vhost=/weibo name=weibo.crawl.results durable=true \
            arguments='{"x-message-ttl":1800000,"x-max-length":5000}'
        
        # 分析任务队列
        rabbitmqadmin -H $RABBITMQ_HOST -P $RABBITMQ_MGMT_PORT -u weibo_admin -p weibo_admin_password \
            declare queue --vhost=/weibo name=weibo.analysis.tasks durable=true \
            arguments='{"x-message-ttl":3600000,"x-max-length":5000}'
        
        # 通知队列
        rabbitmqadmin -H $RABBITMQ_HOST -P $RABBITMQ_MGMT_PORT -u weibo_admin -p weibo_admin_password \
            declare queue --vhost=/weibo name=weibo.notifications durable=true \
            arguments='{"x-message-ttl":600000,"x-max-length":1000}'
        
        # 死信队列
        rabbitmqadmin -H $RABBITMQ_HOST -P $RABBITMQ_MGMT_PORT -u weibo_admin -p weibo_admin_password \
            declare queue --vhost=/weibo name=weibo.dead_letter durable=true
        
        echo -e "${GREEN}✓ 队列创建完成${NC}"
    else
        echo -e "${YELLOW}⚠ rabbitmqadmin未安装，跳过队列创建${NC}"
    fi
}

# 创建绑定
create_bindings() {
    echo -e "${YELLOW}创建队列绑定...${NC}"
    
    if command -v rabbitmqadmin &> /dev/null; then
        # 绑定爬取任务队列
        rabbitmqadmin -H $RABBITMQ_HOST -P $RABBITMQ_MGMT_PORT -u weibo_admin -p weibo_admin_password \
            declare binding --vhost=/weibo source=weibo.direct destination=weibo.crawl.tasks routing_key=crawl.task
        
        # 绑定爬取结果队列
        rabbitmqadmin -H $RABBITMQ_HOST -P $RABBITMQ_MGMT_PORT -u weibo_admin -p weibo_admin_password \
            declare binding --vhost=/weibo source=weibo.direct destination=weibo.crawl.results routing_key=crawl.result
        
        # 绑定分析任务队列
        rabbitmqadmin -H $RABBITMQ_HOST -P $RABBITMQ_MGMT_PORT -u weibo_admin -p weibo_admin_password \
            declare binding --vhost=/weibo source=weibo.direct destination=weibo.analysis.tasks routing_key=analysis.task
        
        # 绑定通知队列
        rabbitmqadmin -H $RABBITMQ_HOST -P $RABBITMQ_MGMT_PORT -u weibo_admin -p weibo_admin_password \
            declare binding --vhost=/weibo source=weibo.topic destination=weibo.notifications routing_key="notification.*"
        
        # 绑定死信队列
        rabbitmqadmin -H $RABBITMQ_HOST -P $RABBITMQ_MGMT_PORT -u weibo_admin -p weibo_admin_password \
            declare binding --vhost=/weibo source=weibo.dead_letter_exchange destination=weibo.dead_letter routing_key=dead_letter
        
        echo -e "${GREEN}✓ 队列绑定创建完成${NC}"
    else
        echo -e "${YELLOW}⚠ rabbitmqadmin未安装，跳过绑定创建${NC}"
    fi
}

# 设置策略
set_policies() {
    echo -e "${YELLOW}设置队列策略...${NC}"
    
    # 设置高可用策略
    sudo rabbitmqctl set_policy -p /weibo ha-all ".*" '{"ha-mode":"all","ha-sync-mode":"automatic"}' --priority 0
    
    echo -e "${GREEN}✓ 队列策略设置完成${NC}"
}

# 验证配置
verify_setup() {
    echo -e "${YELLOW}验证RabbitMQ配置...${NC}"
    
    # 检查虚拟主机
    if sudo rabbitmqctl list_vhosts | grep -q "/weibo"; then
        echo -e "${GREEN}✓ 虚拟主机 /weibo 存在${NC}"
    else
        echo -e "${RED}✗ 虚拟主机 /weibo 不存在${NC}"
    fi
    
    # 检查用户
    if sudo rabbitmqctl list_users | grep -q "weibo_admin"; then
        echo -e "${GREEN}✓ 用户 weibo_admin 存在${NC}"
    else
        echo -e "${RED}✗ 用户 weibo_admin 不存在${NC}"
    fi
    
    if sudo rabbitmqctl list_users | grep -q "weibo_app"; then
        echo -e "${GREEN}✓ 用户 weibo_app 存在${NC}"
    else
        echo -e "${RED}✗ 用户 weibo_app 不存在${NC}"
    fi
    
    # 检查队列数量
    queue_count=$(sudo rabbitmqctl list_queues -p /weibo | wc -l)
    echo -e "${GREEN}✓ 虚拟主机 /weibo 中有 $((queue_count-1)) 个队列${NC}"
}

# 显示连接信息
show_connection_info() {
    echo -e "${GREEN}========================================${NC}"
    echo -e "${GREEN}RabbitMQ初始化完成！${NC}"
    echo -e "${GREEN}========================================${NC}"
    echo -e "${BLUE}连接信息:${NC}"
    echo -e "${BLUE}AMQP URL: amqp://weibo_app:weibo_app_password@${RABBITMQ_HOST}:${RABBITMQ_PORT}/weibo${NC}"
    echo -e "${BLUE}管理界面: http://${RABBITMQ_HOST}:${RABBITMQ_MGMT_PORT}${NC}"
    echo -e "${BLUE}管理员账号: weibo_admin / weibo_admin_password${NC}"
    echo -e "${BLUE}应用账号: weibo_app / weibo_app_password${NC}"
    echo -e "${GREEN}========================================${NC}"
}

# 主执行流程
main() {
    check_rabbitmq_status
    enable_management_plugin
    setup_users_and_vhosts
    create_exchanges
    create_queues
    create_bindings
    set_policies
    verify_setup
    show_connection_info
}

# 执行主函数
main "$@"
