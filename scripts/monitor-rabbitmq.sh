#!/bin/bash

# 微博数据采集分析系统 - RabbitMQ监控脚本
# WeiboAnalytics RabbitMQ Monitoring Script

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
RABBITMQ_HOST=${RABBITMQ_HOST:-localhost}
RABBITMQ_MGMT_PORT=${RABBITMQ_MGMT_PORT:-15672}
RABBITMQ_USER=${RABBITMQ_USER:-weibo_admin}
RABBITMQ_PASS=${RABBITMQ_PASS:-weibo_admin_password}
VHOST="/weibo"

# 显示标题
show_header() {
    clear
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}微博数据采集分析系统 - RabbitMQ监控${NC}"
    echo -e "${BLUE}WeiboAnalytics RabbitMQ Monitor${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo -e "${YELLOW}更新时间: $(date '+%Y-%m-%d %H:%M:%S')${NC}"
    echo ""
}

# 检查RabbitMQ状态
check_rabbitmq_status() {
    echo -e "${YELLOW}RabbitMQ服务状态:${NC}"
    
    if systemctl is-active --quiet rabbitmq-server; then
        echo -e "${GREEN}✓ RabbitMQ服务: 运行中${NC}"
    else
        echo -e "${RED}✗ RabbitMQ服务: 未运行${NC}"
        return 1
    fi
    
    # 检查管理界面
    if curl -s -u $RABBITMQ_USER:$RABBITMQ_PASS \
        http://$RABBITMQ_HOST:$RABBITMQ_MGMT_PORT/api/overview > /dev/null; then
        echo -e "${GREEN}✓ 管理界面: 可访问${NC}"
    else
        echo -e "${RED}✗ 管理界面: 不可访问${NC}"
    fi
    
    echo ""
}

# 显示节点信息
show_node_info() {
    echo -e "${YELLOW}节点信息:${NC}"
    
    if command -v rabbitmqadmin &> /dev/null; then
        rabbitmqadmin -H $RABBITMQ_HOST -P $RABBITMQ_MGMT_PORT \
            -u $RABBITMQ_USER -p $RABBITMQ_PASS \
            list nodes --format=table
    else
        echo -e "${RED}rabbitmqadmin未安装，无法显示详细信息${NC}"
    fi
    
    echo ""
}

# 显示虚拟主机信息
show_vhost_info() {
    echo -e "${YELLOW}虚拟主机信息:${NC}"
    
    if command -v rabbitmqadmin &> /dev/null; then
        rabbitmqadmin -H $RABBITMQ_HOST -P $RABBITMQ_MGMT_PORT \
            -u $RABBITMQ_USER -p $RABBITMQ_PASS \
            list vhosts --format=table
    else
        sudo rabbitmqctl list_vhosts
    fi
    
    echo ""
}

# 显示队列状态
show_queue_status() {
    echo -e "${YELLOW}队列状态 (虚拟主机: $VHOST):${NC}"
    
    if command -v rabbitmqadmin &> /dev/null; then
        rabbitmqadmin -H $RABBITMQ_HOST -P $RABBITMQ_MGMT_PORT \
            -u $RABBITMQ_USER -p $RABBITMQ_PASS \
            list queues --vhost="$VHOST" \
            --format=table \
            name messages consumers message_stats.publish_details.rate
    else
        sudo rabbitmqctl list_queues -p "$VHOST" name messages consumers
    fi
    
    echo ""
}

# 显示交换机状态
show_exchange_status() {
    echo -e "${YELLOW}交换机状态 (虚拟主机: $VHOST):${NC}"
    
    if command -v rabbitmqadmin &> /dev/null; then
        rabbitmqadmin -H $RABBITMQ_HOST -P $RABBITMQ_MGMT_PORT \
            -u $RABBITMQ_USER -p $RABBITMQ_PASS \
            list exchanges --vhost="$VHOST" \
            --format=table \
            name type message_stats.publish_in_details.rate
    else
        sudo rabbitmqctl list_exchanges -p "$VHOST" name type
    fi
    
    echo ""
}

# 显示连接信息
show_connections() {
    echo -e "${YELLOW}活跃连接:${NC}"
    
    if command -v rabbitmqadmin &> /dev/null; then
        rabbitmqadmin -H $RABBITMQ_HOST -P $RABBITMQ_MGMT_PORT \
            -u $RABBITMQ_USER -p $RABBITMQ_PASS \
            list connections --format=table \
            name user vhost state channels
    else
        sudo rabbitmqctl list_connections name user vhost state
    fi
    
    echo ""
}

# 显示消费者信息
show_consumers() {
    echo -e "${YELLOW}消费者信息 (虚拟主机: $VHOST):${NC}"
    
    if command -v rabbitmqadmin &> /dev/null; then
        rabbitmqadmin -H $RABBITMQ_HOST -P $RABBITMQ_MGMT_PORT \
            -u $RABBITMQ_USER -p $RABBITMQ_PASS \
            list consumers --vhost="$VHOST" \
            --format=table \
            queue_name consumer_tag channel_details.name
    else
        sudo rabbitmqctl list_consumers -p "$VHOST"
    fi
    
    echo ""
}

# 显示内存使用情况
show_memory_usage() {
    echo -e "${YELLOW}内存使用情况:${NC}"
    
    if curl -s -u $RABBITMQ_USER:$RABBITMQ_PASS \
        http://$RABBITMQ_HOST:$RABBITMQ_MGMT_PORT/api/nodes > /dev/null; then
        
        memory_info=$(curl -s -u $RABBITMQ_USER:$RABBITMQ_PASS \
            http://$RABBITMQ_HOST:$RABBITMQ_MGMT_PORT/api/nodes | \
            jq -r '.[0] | "内存使用: \(.mem_used / 1024 / 1024 | floor)MB / \(.mem_limit / 1024 / 1024 | floor)MB"')
        
        echo -e "${GREEN}$memory_info${NC}"
    else
        echo -e "${RED}无法获取内存信息${NC}"
    fi
    
    echo ""
}

# 显示警告和错误
show_alerts() {
    echo -e "${YELLOW}系统警告:${NC}"
    
    # 检查队列积压
    if command -v rabbitmqadmin &> /dev/null; then
        high_queue=$(rabbitmqadmin -H $RABBITMQ_HOST -P $RABBITMQ_MGMT_PORT \
            -u $RABBITMQ_USER -p $RABBITMQ_PASS \
            list queues --vhost="$VHOST" --format=bash | \
            awk -F'=' '$2 > 1000 {print $1}')
        
        if [ -n "$high_queue" ]; then
            echo -e "${RED}⚠ 队列积压警告: $high_queue${NC}"
        else
            echo -e "${GREEN}✓ 所有队列正常${NC}"
        fi
    fi
    
    # 检查无消费者的队列
    no_consumer_queue=$(sudo rabbitmqctl list_queues -p "$VHOST" name consumers 2>/dev/null | \
        awk '$2 == 0 && $1 != "Listing" {print $1}')
    
    if [ -n "$no_consumer_queue" ]; then
        echo -e "${YELLOW}⚠ 无消费者队列: $no_consumer_queue${NC}"
    fi
    
    echo ""
}

# 实时监控模式
monitor_mode() {
    while true; do
        show_header
        check_rabbitmq_status
        show_queue_status
        show_memory_usage
        show_alerts
        
        echo -e "${BLUE}按 Ctrl+C 退出监控模式${NC}"
        sleep 5
    done
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -m, --monitor     实时监控模式"
    echo "  -q, --queues      显示队列状态"
    echo "  -e, --exchanges   显示交换机状态"
    echo "  -c, --connections 显示连接信息"
    echo "  -n, --nodes       显示节点信息"
    echo "  -a, --all         显示所有信息"
    echo "  -h, --help        显示帮助信息"
    echo ""
}

# 主函数
main() {
    case "${1:-all}" in
        -m|--monitor)
            monitor_mode
            ;;
        -q|--queues)
            show_header
            check_rabbitmq_status
            show_queue_status
            ;;
        -e|--exchanges)
            show_header
            check_rabbitmq_status
            show_exchange_status
            ;;
        -c|--connections)
            show_header
            check_rabbitmq_status
            show_connections
            show_consumers
            ;;
        -n|--nodes)
            show_header
            check_rabbitmq_status
            show_node_info
            show_memory_usage
            ;;
        -a|--all|all)
            show_header
            check_rabbitmq_status
            show_node_info
            show_vhost_info
            show_queue_status
            show_exchange_status
            show_connections
            show_consumers
            show_memory_usage
            show_alerts
            ;;
        -h|--help)
            show_help
            ;;
        *)
            echo "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
