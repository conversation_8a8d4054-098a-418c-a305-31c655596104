#!/bin/bash

# 微博数据采集分析系统 - 系统启动脚本
# WeiboAnalytics System Startup Script

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
ENVIRONMENT=${WEIBO_ENV:-development}
PROJECT_ROOT=$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)
LOG_DIR="$PROJECT_ROOT/logs"
PID_DIR="$PROJECT_ROOT/pids"

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}微博数据采集分析系统 - 系统启动${NC}"
echo -e "${BLUE}WeiboAnalytics System Startup${NC}"
echo -e "${BLUE}========================================${NC}"
echo -e "${YELLOW}环境: $ENVIRONMENT${NC}"
echo -e "${YELLOW}项目根目录: $PROJECT_ROOT${NC}"

# 创建必要的目录
create_directories() {
    echo -e "${YELLOW}创建必要的目录...${NC}"
    
    mkdir -p "$LOG_DIR"
    mkdir -p "$PID_DIR"
    mkdir -p "$PROJECT_ROOT/data"
    mkdir -p "$PROJECT_ROOT/tmp"
    
    echo -e "${GREEN}✓ 目录创建完成${NC}"
}

# 检查依赖服务
check_dependencies() {
    echo -e "${YELLOW}检查依赖服务...${NC}"
    
    # 检查PostgreSQL
    if systemctl is-active --quiet postgresql; then
        echo -e "${GREEN}✓ PostgreSQL: 运行中${NC}"
    else
        echo -e "${RED}✗ PostgreSQL: 未运行${NC}"
        echo -e "${YELLOW}正在启动PostgreSQL...${NC}"
        sudo systemctl start postgresql
    fi
    
    # 检查Redis
    if systemctl is-active --quiet redis-server; then
        echo -e "${GREEN}✓ Redis: 运行中${NC}"
    else
        echo -e "${RED}✗ Redis: 未运行${NC}"
        echo -e "${YELLOW}正在启动Redis...${NC}"
        sudo systemctl start redis-server
    fi
    
    # 检查RabbitMQ
    if systemctl is-active --quiet rabbitmq-server; then
        echo -e "${GREEN}✓ RabbitMQ: 运行中${NC}"
    else
        echo -e "${RED}✗ RabbitMQ: 未运行${NC}"
        echo -e "${YELLOW}正在启动RabbitMQ...${NC}"
        sudo systemctl start rabbitmq-server
    fi
}

# 初始化数据库
init_database() {
    echo -e "${YELLOW}初始化数据库...${NC}"
    
    if [ -f "$PROJECT_ROOT/database/init_database.sh" ]; then
        cd "$PROJECT_ROOT"
        ./database/init_database.sh
        echo -e "${GREEN}✓ 数据库初始化完成${NC}"
    else
        echo -e "${YELLOW}⚠ 数据库初始化脚本不存在，跳过${NC}"
    fi
}

# 初始化消息队列
init_messaging() {
    echo -e "${YELLOW}初始化消息队列...${NC}"
    
    if [ -f "$PROJECT_ROOT/scripts/init-rabbitmq.sh" ]; then
        cd "$PROJECT_ROOT"
        ./scripts/init-rabbitmq.sh
        echo -e "${GREEN}✓ 消息队列初始化完成${NC}"
    else
        echo -e "${YELLOW}⚠ 消息队列初始化脚本不存在，跳过${NC}"
    fi
}

# 构建项目
build_project() {
    echo -e "${YELLOW}构建项目...${NC}"
    
    # 构建爬虫客户端
    if [ -d "$PROJECT_ROOT/crawler-client" ]; then
        echo -e "${YELLOW}构建爬虫客户端...${NC}"
        cd "$PROJECT_ROOT/crawler-client"
        pnpm build
        echo -e "${GREEN}✓ 爬虫客户端构建完成${NC}"
    fi
    
    # 构建管理客户端
    if [ -d "$PROJECT_ROOT/management-client" ]; then
        echo -e "${YELLOW}构建管理客户端...${NC}"
        cd "$PROJECT_ROOT/management-client"
        pnpm build
        echo -e "${GREEN}✓ 管理客户端构建完成${NC}"
    fi
}

# 启动爬虫客户端
start_crawler_client() {
    echo -e "${YELLOW}启动爬虫客户端...${NC}"
    
    if [ -d "$PROJECT_ROOT/crawler-client" ]; then
        cd "$PROJECT_ROOT/crawler-client"
        
        if [ "$ENVIRONMENT" = "development" ]; then
            # 开发模式：使用Tauri开发服务器
            nohup cargo tauri dev > "$LOG_DIR/crawler-client.log" 2>&1 &
            echo $! > "$PID_DIR/crawler-client.pid"
        else
            # 生产模式：构建并运行
            cargo tauri build
            # 这里需要根据实际的构建输出路径调整
            nohup ./src-tauri/target/release/weibo-crawler-client > "$LOG_DIR/crawler-client.log" 2>&1 &
            echo $! > "$PID_DIR/crawler-client.pid"
        fi
        
        echo -e "${GREEN}✓ 爬虫客户端启动完成 (PID: $(cat $PID_DIR/crawler-client.pid))${NC}"
    else
        echo -e "${YELLOW}⚠ 爬虫客户端目录不存在，跳过${NC}"
    fi
}

# 启动管理客户端
start_management_client() {
    echo -e "${YELLOW}启动管理客户端...${NC}"
    
    if [ -d "$PROJECT_ROOT/management-client" ]; then
        cd "$PROJECT_ROOT/management-client"
        
        if [ "$ENVIRONMENT" = "development" ]; then
            # 开发模式：使用Tauri开发服务器
            nohup cargo tauri dev > "$LOG_DIR/management-client.log" 2>&1 &
            echo $! > "$PID_DIR/management-client.pid"
        else
            # 生产模式：构建并运行
            cargo tauri build
            # 这里需要根据实际的构建输出路径调整
            nohup ./src-tauri/target/release/weibo-management-client > "$LOG_DIR/management-client.log" 2>&1 &
            echo $! > "$PID_DIR/management-client.pid"
        fi
        
        echo -e "${GREEN}✓ 管理客户端启动完成 (PID: $(cat $PID_DIR/management-client.pid))${NC}"
    else
        echo -e "${YELLOW}⚠ 管理客户端目录不存在，跳过${NC}"
    fi
}

# 显示状态信息
show_status() {
    echo -e "${GREEN}========================================${NC}"
    echo -e "${GREEN}系统启动完成！${NC}"
    echo -e "${GREEN}========================================${NC}"
    
    echo -e "${BLUE}服务状态:${NC}"
    
    # 检查PID文件并显示状态
    if [ -f "$PID_DIR/crawler-client.pid" ]; then
        local pid=$(cat "$PID_DIR/crawler-client.pid")
        if kill -0 "$pid" 2>/dev/null; then
            echo -e "${GREEN}✓ 爬虫客户端: 运行中 (PID: $pid)${NC}"
        else
            echo -e "${RED}✗ 爬虫客户端: 未运行${NC}"
        fi
    fi
    
    if [ -f "$PID_DIR/management-client.pid" ]; then
        local pid=$(cat "$PID_DIR/management-client.pid")
        if kill -0 "$pid" 2>/dev/null; then
            echo -e "${GREEN}✓ 管理客户端: 运行中 (PID: $pid)${NC}"
        else
            echo -e "${RED}✗ 管理客户端: 未运行${NC}"
        fi
    fi
    
    echo ""
    echo -e "${BLUE}访问地址:${NC}"
    echo -e "${BLUE}爬虫客户端: http://localhost:1420${NC}"
    echo -e "${BLUE}管理客户端: http://localhost:1421${NC}"
    echo -e "${BLUE}RabbitMQ管理界面: http://localhost:15672${NC}"
    
    echo ""
    echo -e "${BLUE}日志文件:${NC}"
    echo -e "${BLUE}爬虫客户端: $LOG_DIR/crawler-client.log${NC}"
    echo -e "${BLUE}管理客户端: $LOG_DIR/management-client.log${NC}"
    
    echo ""
    echo -e "${YELLOW}使用 './scripts/stop-system.sh' 停止系统${NC}"
}

# 主执行流程
main() {
    case "${1:-start}" in
        start)
            create_directories
            check_dependencies
            init_database
            init_messaging
            build_project
            start_crawler_client
            start_management_client
            show_status
            ;;
        build)
            build_project
            ;;
        status)
            show_status
            ;;
        *)
            echo "用法: $0 [start|build|status]"
            echo ""
            echo "命令:"
            echo "  start   启动完整系统 (默认)"
            echo "  build   仅构建项目"
            echo "  status  显示系统状态"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
