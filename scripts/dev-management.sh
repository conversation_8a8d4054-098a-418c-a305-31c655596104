#!/bin/bash

# 微博管理客户端开发启动脚本
# WeiboAnalytics Management Client Development Script

echo "=========================================="
echo "启动微博管理客户端开发环境"
echo "Starting Weibo Management Client Development"
echo "=========================================="

# 检查是否在正确的目录
if [ ! -d "management-client" ]; then
    echo "错误: 请在项目根目录运行此脚本"
    echo "Error: Please run this script from project root directory"
    exit 1
fi

# 进入管理客户端目录
cd management-client

# 检查依赖是否安装
if [ ! -d "node_modules" ]; then
    echo "正在安装依赖..."
    echo "Installing dependencies..."
    pnpm install
fi

# 启动开发服务器
echo "启动管理客户端开发服务器 (端口: 1421)"
echo "Starting management client development server (port: 1421)"
echo "=========================================="

# 启动Tauri开发模式
cargo tauri dev
