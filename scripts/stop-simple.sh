#!/bin/sh

# 微博数据采集分析系统 - 简单停止脚本 (POSIX兼容)
# WeiboAnalytics Simple Stop Script (POSIX Compatible)

# 项目根目录
PROJECT_ROOT=$(cd "$(dirname "$0")/.." && pwd)
PID_DIR="$PROJECT_ROOT/pids"

printf "========================================\n"
printf "微博数据采集分析系统 - 停止服务\n"
printf "WeiboAnalytics System Stop\n"
printf "========================================\n"

# 停止PID文件记录的进程
stop_pid_processes() {
    printf "停止PID文件记录的进程...\n"
    
    if [ -d "$PID_DIR" ]; then
        for pid_file in "$PID_DIR"/*.pid; do
            if [ -f "$pid_file" ]; then
                pid=$(cat "$pid_file")
                service_name=$(basename "$pid_file" .pid)
                
                if kill -0 "$pid" 2>/dev/null; then
                    printf "  停止 %s (PID: %s)...\n" "$service_name" "$pid"
                    kill -TERM "$pid" 2>/dev/null || kill -KILL "$pid" 2>/dev/null
                    sleep 1
                    
                    if kill -0 "$pid" 2>/dev/null; then
                        printf "  强制停止 %s (PID: %s)...\n" "$service_name" "$pid"
                        kill -KILL "$pid" 2>/dev/null || true
                    fi
                    
                    printf "✓ %s 已停止\n" "$service_name"
                else
                    printf "○ %s 进程不存在\n" "$service_name"
                fi
                
                rm -f "$pid_file"
            fi
        done
    fi
}

# 停止端口占用的进程
stop_port_processes() {
    printf "检查并停止端口占用的进程...\n"
    
    ports="1420 1421 3000 3001"
    
    for port in $ports; do
        pids=$(lsof -ti:$port 2>/dev/null || true)
        
        if [ -n "$pids" ]; then
            printf "停止占用端口 %s 的进程...\n" "$port"
            
            for pid in $pids; do
                cmd=$(ps -p $pid -o comm= 2>/dev/null || echo "unknown")
                printf "  停止进程 %s (%s)...\n" "$pid" "$cmd"
                
                # 先尝试优雅停止
                kill -TERM "$pid" 2>/dev/null || true
                sleep 2
                
                # 检查是否还在运行
                if kill -0 "$pid" 2>/dev/null; then
                    printf "  强制停止进程 %s...\n" "$pid"
                    kill -KILL "$pid" 2>/dev/null || true
                fi
            done
            
            printf "✓ 端口 %s 已释放\n" "$port"
        else
            printf "○ 端口 %s 空闲\n" "$port"
        fi
    done
}

# 停止Vite开发服务器
stop_vite_processes() {
    printf "停止Vite开发服务器...\n"
    
    vite_pids=$(pgrep -f "vite" 2>/dev/null || true)
    
    if [ -n "$vite_pids" ]; then
        for pid in $vite_pids; do
            cmd=$(ps -p $pid -o args= 2>/dev/null | cut -c1-50 || echo "unknown")
            printf "  停止Vite进程 %s (%s...)...\n" "$pid" "$cmd"
            
            kill -TERM "$pid" 2>/dev/null || true
            sleep 1
            
            if kill -0 "$pid" 2>/dev/null; then
                kill -KILL "$pid" 2>/dev/null || true
            fi
        done
        printf "✓ Vite进程已停止\n"
    else
        printf "○ 没有运行的Vite进程\n"
    fi
}

# 停止Node.js进程
stop_node_processes() {
    printf "停止相关Node.js进程...\n"
    
    # 查找可能的开发服务器进程
    node_pids=$(pgrep -f "node.*vite\|node.*dev\|node.*1420\|node.*1421" 2>/dev/null || true)
    
    if [ -n "$node_pids" ]; then
        for pid in $node_pids; do
            cmd=$(ps -p $pid -o args= 2>/dev/null | cut -c1-50 || echo "unknown")
            printf "  停止Node.js进程 %s (%s...)...\n" "$pid" "$cmd"
            
            kill -TERM "$pid" 2>/dev/null || true
            sleep 1
            
            if kill -0 "$pid" 2>/dev/null; then
                kill -KILL "$pid" 2>/dev/null || true
            fi
        done
        printf "✓ Node.js进程已停止\n"
    else
        printf "○ 没有相关的Node.js进程\n"
    fi
}

# 清理临时文件
cleanup_temp_files() {
    printf "清理临时文件...\n"
    
    # 清理PID目录
    rm -f "$PID_DIR"/*.pid 2>/dev/null || true
    
    # 清理可能的锁文件
    rm -f "$PROJECT_ROOT"/.vite-* 2>/dev/null || true
    rm -f "$PROJECT_ROOT"/crawler-client/.vite-* 2>/dev/null || true
    rm -f "$PROJECT_ROOT"/management-client/.vite-* 2>/dev/null || true
    
    printf "✓ 临时文件已清理\n"
}

# 验证停止结果
verify_stop() {
    printf "验证停止结果...\n"
    
    ports="1420 1421"
    all_stopped=true
    
    for port in $ports; do
        if lsof -i:$port >/dev/null 2>&1; then
            printf "✗ 端口 %s 仍被占用\n" "$port"
            lsof -i:$port
            all_stopped=false
        else
            printf "✓ 端口 %s 已释放\n" "$port"
        fi
    done
    
    if [ "$all_stopped" = "true" ]; then
        printf "✓ 所有服务已成功停止\n"
    else
        printf "⚠ 部分服务可能仍在运行\n"
    fi
}

# 主执行流程
main() {
    case "${1:-normal}" in
        force|--force|-f)
            printf "强制停止模式\n"
            stop_pid_processes
            stop_port_processes
            stop_vite_processes
            stop_node_processes
            cleanup_temp_files
            verify_stop
            ;;
        ports|--ports|-p)
            printf "仅停止端口占用进程\n"
            stop_port_processes
            verify_stop
            ;;
        clean|--clean|-c)
            printf "停止并清理\n"
            stop_pid_processes
            stop_port_processes
            stop_vite_processes
            stop_node_processes
            cleanup_temp_files
            verify_stop
            ;;
        help|--help|-h)
            printf "用法: %s [选项]\n" "$0"
            printf "\n"
            printf "选项:\n"
            printf "  force, -f     强制停止所有相关进程\n"
            printf "  ports, -p     仅停止端口占用的进程\n"
            printf "  clean, -c     停止后清理临时文件\n"
            printf "  help, -h      显示帮助信息\n"
            printf "\n"
            ;;
        normal|*)
            stop_pid_processes
            stop_port_processes
            stop_vite_processes
            stop_node_processes
            verify_stop
            ;;
    esac
    
    printf "\n"
    printf "========================================\n"
    printf "系统停止完成！\n"
    printf "========================================\n"
}

# 执行主函数
main "$@"
