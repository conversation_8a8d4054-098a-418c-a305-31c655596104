#!/bin/bash

# 微博数据采集分析系统 - 开发环境启动脚本
# WeiboAnalytics Development Start Script

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT=$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)
LOG_DIR="$PROJECT_ROOT/logs"
PID_DIR="$PROJECT_ROOT/pids"

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}微博数据采集分析系统 - 开发环境启动${NC}"
echo -e "${BLUE}WeiboAnalytics Development Start${NC}"
echo -e "${BLUE}========================================${NC}"

# 创建必要目录
mkdir -p "$LOG_DIR" "$PID_DIR"

# 清理现有进程
cleanup_existing() {
    echo -e "${YELLOW}清理现有进程...${NC}"
    "$PROJECT_ROOT/scripts/stop-system.sh" --ports >/dev/null 2>&1 || true
    sleep 1
}

# 启动爬虫客户端
start_crawler() {
    echo -e "${BLUE}启动爬虫客户端开发服务器...${NC}"
    
    cd "$PROJECT_ROOT/crawler-client"
    
    # 使用setsid创建新的会话，避免信号传播问题
    setsid pnpm dev > "$LOG_DIR/crawler-dev.log" 2>&1 &
    local pid=$!
    echo $pid > "$PID_DIR/crawler-dev.pid"
    
    echo -e "${GREEN}✓ 爬虫客户端进程启动 (PID: $pid)${NC}"
    
    # 等待端口可用
    local count=0
    while [ $count -lt 30 ]; do
        if lsof -i:1420 >/dev/null 2>&1; then
            echo -e "${GREEN}✓ 爬虫客户端服务器就绪: http://localhost:1420${NC}"
            return 0
        fi
        sleep 1
        count=$((count + 1))
    done
    
    echo -e "${RED}✗ 爬虫客户端启动超时${NC}"
    return 1
}

# 启动管理客户端
start_management() {
    echo -e "${BLUE}启动管理客户端开发服务器...${NC}"
    
    cd "$PROJECT_ROOT/management-client"
    
    # 使用setsid创建新的会话，避免信号传播问题
    setsid pnpm dev > "$LOG_DIR/management-dev.log" 2>&1 &
    local pid=$!
    echo $pid > "$PID_DIR/management-dev.pid"
    
    echo -e "${GREEN}✓ 管理客户端进程启动 (PID: $pid)${NC}"
    
    # 等待端口可用
    local count=0
    while [ $count -lt 30 ]; do
        if lsof -i:1421 >/dev/null 2>&1; then
            echo -e "${GREEN}✓ 管理客户端服务器就绪: http://localhost:1421${NC}"
            return 0
        fi
        sleep 1
        count=$((count + 1))
    done
    
    echo -e "${RED}✗ 管理客户端启动超时${NC}"
    return 1
}

# 显示状态
show_status() {
    echo -e "${GREEN}========================================${NC}"
    echo -e "${GREEN}开发环境启动完成！${NC}"
    echo -e "${GREEN}========================================${NC}"
    
    echo -e "${BLUE}访问地址:${NC}"
    if lsof -i:1420 >/dev/null 2>&1; then
        echo -e "${GREEN}  爬虫客户端: http://localhost:1420${NC}"
    else
        echo -e "${RED}  爬虫客户端: 启动失败${NC}"
    fi
    
    if lsof -i:1421 >/dev/null 2>&1; then
        echo -e "${GREEN}  管理客户端: http://localhost:1421${NC}"
    else
        echo -e "${RED}  管理客户端: 启动失败${NC}"
    fi
    
    echo ""
    echo -e "${BLUE}日志文件:${NC}"
    echo -e "${BLUE}  爬虫客户端: $LOG_DIR/crawler-dev.log${NC}"
    echo -e "${BLUE}  管理客户端: $LOG_DIR/management-dev.log${NC}"
    
    echo ""
    echo -e "${BLUE}管理命令:${NC}"
    echo -e "${BLUE}  查看状态: make status${NC}"
    echo -e "${BLUE}  查看日志: make logs${NC}"
    echo -e "${BLUE}  停止服务: make stop${NC}"
    echo -e "${BLUE}  强制停止: make stop-force${NC}"
    
    echo ""
    echo -e "${YELLOW}提示: 开发服务器在后台运行，使用 Ctrl+C 无法直接停止${NC}"
    echo -e "${YELLOW}请使用上述管理命令来控制服务器${NC}"
}

# 错误处理
handle_error() {
    echo -e "${RED}启动过程中发生错误，正在清理...${NC}"
    "$PROJECT_ROOT/scripts/stop-system.sh" --force >/dev/null 2>&1 || true
    exit 1
}

# 设置错误处理
trap handle_error ERR

# 主执行流程
main() {
    case "${1:-both}" in
        crawler)
            cleanup_existing
            start_crawler
            show_status
            ;;
        management)
            cleanup_existing
            start_management
            show_status
            ;;
        both)
            cleanup_existing
            start_crawler
            start_management
            show_status
            ;;
        *)
            echo "用法: $0 [crawler|management|both]"
            echo ""
            echo "选项:"
            echo "  crawler     仅启动爬虫客户端"
            echo "  management  仅启动管理客户端"
            echo "  both        启动两个客户端 (默认)"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
