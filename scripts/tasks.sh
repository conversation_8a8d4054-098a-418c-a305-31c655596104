#!/bin/bash

# 微博数据采集分析系统 - 快速任务脚本
# WeiboAnalytics Quick Tasks Script

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT=$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)

# 显示帮助
show_help() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}微博数据采集分析系统 - 快速任务${NC}"
    echo -e "${BLUE}WeiboAnalytics Quick Tasks${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
    echo -e "${YELLOW}用法: $0 <任务名称>${NC}"
    echo ""
    echo -e "${YELLOW}可用任务:${NC}"
    echo -e "${GREEN}  setup${NC}          - 快速设置项目"
    echo -e "${GREEN}  dev${NC}            - 启动开发环境"
    echo -e "${GREEN}  build${NC}          - 构建项目"
    echo -e "${GREEN}  clean${NC}          - 清理项目"
    echo -e "${GREEN}  test${NC}           - 运行测试"
    echo -e "${GREEN}  lint${NC}           - 代码检查"
    echo -e "${GREEN}  format${NC}         - 格式化代码"
    echo -e "${GREEN}  db${NC}             - 数据库操作"
    echo -e "${GREEN}  mq${NC}             - 消息队列操作"
    echo -e "${GREEN}  logs${NC}           - 查看日志"
    echo -e "${GREEN}  status${NC}         - 系统状态"
    echo -e "${GREEN}  update${NC}         - 更新依赖"
    echo -e "${GREEN}  backup${NC}         - 备份数据"
    echo ""
}

# 快速设置
task_setup() {
    echo -e "${YELLOW}快速设置项目...${NC}"
    
    # 创建目录
    mkdir -p logs pids data tmp backups
    
    # 安装依赖
    echo -e "${BLUE}安装依赖...${NC}"
    cd "$PROJECT_ROOT/crawler-client" && pnpm install
    cd "$PROJECT_ROOT/management-client" && pnpm install
    
    # 初始化数据库（如果可能）
    if command -v psql &> /dev/null; then
        echo -e "${BLUE}初始化数据库...${NC}"
        "$PROJECT_ROOT/database/init_database.sh" || echo -e "${YELLOW}⚠ 数据库初始化跳过${NC}"
    fi
    
    # 初始化消息队列（如果可能）
    if command -v rabbitmqctl &> /dev/null; then
        echo -e "${BLUE}初始化消息队列...${NC}"
        "$PROJECT_ROOT/scripts/init-rabbitmq.sh" || echo -e "${YELLOW}⚠ 消息队列初始化跳过${NC}"
    fi
    
    echo -e "${GREEN}✓ 项目设置完成${NC}"
}

# 开发环境
task_dev() {
    echo -e "${YELLOW}启动开发环境...${NC}"
    
    # 检查端口是否被占用
    if lsof -i:1420 &> /dev/null; then
        echo -e "${YELLOW}⚠ 端口1420已被占用${NC}"
    fi
    
    if lsof -i:1421 &> /dev/null; then
        echo -e "${YELLOW}⚠ 端口1421已被占用${NC}"
    fi
    
    # 启动开发服务器
    echo -e "${BLUE}启动爬虫客户端开发服务器...${NC}"
    cd "$PROJECT_ROOT/crawler-client"
    pnpm dev &
    
    echo -e "${BLUE}启动管理客户端开发服务器...${NC}"
    cd "$PROJECT_ROOT/management-client"
    pnpm dev &
    
    echo -e "${GREEN}✓ 开发环境启动完成${NC}"
    echo -e "${BLUE}爬虫客户端: http://localhost:1420${NC}"
    echo -e "${BLUE}管理客户端: http://localhost:1421${NC}"
    
    # 等待用户输入退出
    echo -e "${YELLOW}按 Enter 键停止开发服务器...${NC}"
    read
    
    # 停止服务器
    pkill -f "vite" 2>/dev/null || true
    echo -e "${GREEN}✓ 开发服务器已停止${NC}"
}

# 构建项目
task_build() {
    echo -e "${YELLOW}构建项目...${NC}"
    
    echo -e "${BLUE}构建爬虫客户端...${NC}"
    cd "$PROJECT_ROOT/crawler-client" && pnpm build
    
    echo -e "${BLUE}构建管理客户端...${NC}"
    cd "$PROJECT_ROOT/management-client" && pnpm build
    
    echo -e "${GREEN}✓ 构建完成${NC}"
}

# 清理项目
task_clean() {
    echo -e "${YELLOW}清理项目...${NC}"
    
    # 清理构建文件
    rm -rf "$PROJECT_ROOT/crawler-client/dist"
    rm -rf "$PROJECT_ROOT/crawler-client/src-tauri/target"
    rm -rf "$PROJECT_ROOT/management-client/dist"
    rm -rf "$PROJECT_ROOT/management-client/src-tauri/target"
    
    # 清理日志和临时文件
    rm -rf "$PROJECT_ROOT/logs"/*
    rm -rf "$PROJECT_ROOT/pids"/*
    rm -rf "$PROJECT_ROOT/tmp"/*
    
    echo -e "${GREEN}✓ 清理完成${NC}"
}

# 运行测试
task_test() {
    echo -e "${YELLOW}运行测试...${NC}"
    
    echo -e "${BLUE}测试爬虫客户端...${NC}"
    cd "$PROJECT_ROOT/crawler-client" && pnpm test || true
    
    echo -e "${BLUE}测试管理客户端...${NC}"
    cd "$PROJECT_ROOT/management-client" && pnpm test || true
    
    echo -e "${GREEN}✓ 测试完成${NC}"
}

# 代码检查
task_lint() {
    echo -e "${YELLOW}代码检查...${NC}"
    
    echo -e "${BLUE}检查爬虫客户端...${NC}"
    cd "$PROJECT_ROOT/crawler-client" && pnpm lint || true
    
    echo -e "${BLUE}检查管理客户端...${NC}"
    cd "$PROJECT_ROOT/management-client" && pnpm lint || true
    
    echo -e "${GREEN}✓ 代码检查完成${NC}"
}

# 格式化代码
task_format() {
    echo -e "${YELLOW}格式化代码...${NC}"
    
    echo -e "${BLUE}格式化爬虫客户端...${NC}"
    cd "$PROJECT_ROOT/crawler-client" && pnpm format || true
    
    echo -e "${BLUE}格式化管理客户端...${NC}"
    cd "$PROJECT_ROOT/management-client" && pnpm format || true
    
    echo -e "${GREEN}✓ 代码格式化完成${NC}"
}

# 数据库操作
task_db() {
    echo -e "${YELLOW}数据库操作:${NC}"
    echo -e "${GREEN}1.${NC} 初始化数据库"
    echo -e "${GREEN}2.${NC} 重置数据库"
    echo -e "${GREEN}3.${NC} 备份数据库"
    echo -e "${GREEN}4.${NC} 查看数据库状态"
    
    read -p "请选择操作 (1-4): " choice
    
    case $choice in
        1)
            "$PROJECT_ROOT/database/init_database.sh"
            ;;
        2)
            echo -e "${RED}警告: 这将删除所有数据！${NC}"
            read -p "确认继续? (y/N): " confirm
            if [ "$confirm" = "y" ]; then
                "$PROJECT_ROOT/database/init_database.sh"
            fi
            ;;
        3)
            mkdir -p "$PROJECT_ROOT/backups"
            pg_dump weibo_analytics > "$PROJECT_ROOT/backups/weibo_analytics_$(date +%Y%m%d_%H%M%S).sql"
            echo -e "${GREEN}✓ 数据库备份完成${NC}"
            ;;
        4)
            systemctl is-active --quiet postgresql && echo -e "${GREEN}✓ PostgreSQL: 运行中${NC}" || echo -e "${RED}✗ PostgreSQL: 未运行${NC}"
            ;;
        *)
            echo -e "${RED}无效选择${NC}"
            ;;
    esac
}

# 消息队列操作
task_mq() {
    echo -e "${YELLOW}消息队列操作:${NC}"
    echo -e "${GREEN}1.${NC} 初始化消息队列"
    echo -e "${GREEN}2.${NC} 查看队列状态"
    echo -e "${GREEN}3.${NC} 监控消息队列"
    
    read -p "请选择操作 (1-3): " choice
    
    case $choice in
        1)
            "$PROJECT_ROOT/scripts/init-rabbitmq.sh"
            ;;
        2)
            "$PROJECT_ROOT/scripts/monitor-rabbitmq.sh"
            ;;
        3)
            "$PROJECT_ROOT/scripts/monitor-rabbitmq.sh" --monitor
            ;;
        *)
            echo -e "${RED}无效选择${NC}"
            ;;
    esac
}

# 查看日志
task_logs() {
    echo -e "${YELLOW}查看日志:${NC}"
    echo -e "${GREEN}1.${NC} 爬虫客户端日志"
    echo -e "${GREEN}2.${NC} 管理客户端日志"
    echo -e "${GREEN}3.${NC} 所有日志"
    echo -e "${GREEN}4.${NC} 实时日志"
    
    read -p "请选择 (1-4): " choice
    
    case $choice in
        1)
            if [ -f "$PROJECT_ROOT/logs/crawler-client.log" ]; then
                tail -50 "$PROJECT_ROOT/logs/crawler-client.log"
            else
                echo -e "${YELLOW}日志文件不存在${NC}"
            fi
            ;;
        2)
            if [ -f "$PROJECT_ROOT/logs/management-client.log" ]; then
                tail -50 "$PROJECT_ROOT/logs/management-client.log"
            else
                echo -e "${YELLOW}日志文件不存在${NC}"
            fi
            ;;
        3)
            for log in "$PROJECT_ROOT/logs"/*.log; do
                if [ -f "$log" ]; then
                    echo -e "${BLUE}=== $(basename "$log") ===${NC}"
                    tail -20 "$log"
                    echo ""
                fi
            done
            ;;
        4)
            tail -f "$PROJECT_ROOT/logs"/*.log 2>/dev/null || echo -e "${YELLOW}没有找到日志文件${NC}"
            ;;
        *)
            echo -e "${RED}无效选择${NC}"
            ;;
    esac
}

# 系统状态
task_status() {
    echo -e "${YELLOW}系统状态:${NC}"
    echo ""
    
    # 服务状态
    echo -e "${BLUE}基础服务:${NC}"
    systemctl is-active --quiet postgresql && echo -e "${GREEN}✓ PostgreSQL: 运行中${NC}" || echo -e "${RED}✗ PostgreSQL: 未运行${NC}"
    systemctl is-active --quiet redis-server && echo -e "${GREEN}✓ Redis: 运行中${NC}" || echo -e "${RED}✗ Redis: 未运行${NC}"
    systemctl is-active --quiet rabbitmq-server && echo -e "${GREEN}✓ RabbitMQ: 运行中${NC}" || echo -e "${RED}✗ RabbitMQ: 未运行${NC}"
    
    echo ""
    
    # 端口状态
    echo -e "${BLUE}端口状态:${NC}"
    lsof -i:1420 &> /dev/null && echo -e "${GREEN}✓ 端口1420: 使用中${NC}" || echo -e "${YELLOW}○ 端口1420: 空闲${NC}"
    lsof -i:1421 &> /dev/null && echo -e "${GREEN}✓ 端口1421: 使用中${NC}" || echo -e "${YELLOW}○ 端口1421: 空闲${NC}"
    lsof -i:5672 &> /dev/null && echo -e "${GREEN}✓ 端口5672: 使用中${NC}" || echo -e "${YELLOW}○ 端口5672: 空闲${NC}"
    lsof -i:15672 &> /dev/null && echo -e "${GREEN}✓ 端口15672: 使用中${NC}" || echo -e "${YELLOW}○ 端口15672: 空闲${NC}"
}

# 更新依赖
task_update() {
    echo -e "${YELLOW}更新依赖...${NC}"
    
    echo -e "${BLUE}更新爬虫客户端依赖...${NC}"
    cd "$PROJECT_ROOT/crawler-client" && pnpm update
    
    echo -e "${BLUE}更新管理客户端依赖...${NC}"
    cd "$PROJECT_ROOT/management-client" && pnpm update
    
    echo -e "${GREEN}✓ 依赖更新完成${NC}"
}

# 备份数据
task_backup() {
    echo -e "${YELLOW}备份数据...${NC}"
    
    mkdir -p "$PROJECT_ROOT/backups"
    
    # 备份数据库
    if command -v pg_dump &> /dev/null; then
        pg_dump weibo_analytics > "$PROJECT_ROOT/backups/weibo_analytics_$(date +%Y%m%d_%H%M%S).sql"
        echo -e "${GREEN}✓ 数据库备份完成${NC}"
    fi
    
    # 备份配置文件
    tar -czf "$PROJECT_ROOT/backups/config_$(date +%Y%m%d_%H%M%S).tar.gz" -C "$PROJECT_ROOT" config/
    echo -e "${GREEN}✓ 配置文件备份完成${NC}"
    
    # 备份数据目录
    if [ -d "$PROJECT_ROOT/data" ]; then
        tar -czf "$PROJECT_ROOT/backups/data_$(date +%Y%m%d_%H%M%S).tar.gz" -C "$PROJECT_ROOT" data/
        echo -e "${GREEN}✓ 数据目录备份完成${NC}"
    fi
}

# 主函数
main() {
    cd "$PROJECT_ROOT"
    
    case "${1:-help}" in
        setup)
            task_setup
            ;;
        dev)
            task_dev
            ;;
        build)
            task_build
            ;;
        clean)
            task_clean
            ;;
        test)
            task_test
            ;;
        lint)
            task_lint
            ;;
        format)
            task_format
            ;;
        db)
            task_db
            ;;
        mq)
            task_mq
            ;;
        logs)
            task_logs
            ;;
        status)
            task_status
            ;;
        update)
            task_update
            ;;
        backup)
            task_backup
            ;;
        help|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
