#!/bin/bash

# 微博数据采集分析系统 - 系统停止脚本
# WeiboAnalytics System Stop Script

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT=$(cd "$(dirname "$0")/.." && pwd)
PID_DIR="$PROJECT_ROOT/pids"

printf "${BLUE}========================================${NC}\n"
printf "${BLUE}微博数据采集分析系统 - 停止服务${NC}\n"
printf "${BLUE}WeiboAnalytics System Stop${NC}\n"
printf "${BLUE}========================================${NC}\n"

# 停止PID文件记录的进程
stop_pid_processes() {
    echo -e "${YELLOW}停止PID文件记录的进程...${NC}"
    
    for pid_file in "$PID_DIR"/*.pid; do
        if [ -f "$pid_file" ]; then
            local pid=$(cat "$pid_file")
            local service_name=$(basename "$pid_file" .pid)
            
            if kill -0 "$pid" 2>/dev/null; then
                echo -e "${YELLOW}停止 $service_name (PID: $pid)...${NC}"
                kill -TERM "$pid" 2>/dev/null || kill -KILL "$pid" 2>/dev/null
                sleep 1
                
                if kill -0 "$pid" 2>/dev/null; then
                    echo -e "${RED}强制停止 $service_name (PID: $pid)...${NC}"
                    kill -KILL "$pid" 2>/dev/null || true
                fi
                
                echo -e "${GREEN}✓ $service_name 已停止${NC}"
            else
                echo -e "${YELLOW}○ $service_name 进程不存在${NC}"
            fi
            
            rm -f "$pid_file"
        fi
    done
}

# 停止端口占用的进程
stop_port_processes() {
    echo -e "${YELLOW}检查并停止端口占用的进程...${NC}"
    
    local ports="1420 1421 3000 3001"

    for port in $ports; do
        local pids=$(lsof -ti:$port 2>/dev/null || true)
        
        if [ -n "$pids" ]; then
            echo -e "${YELLOW}停止占用端口 $port 的进程...${NC}"
            
            for pid in $pids; do
                local cmd=$(ps -p $pid -o comm= 2>/dev/null || echo "unknown")
                echo -e "${YELLOW}  停止进程 $pid ($cmd)...${NC}"
                
                # 先尝试优雅停止
                kill -TERM "$pid" 2>/dev/null || true
                sleep 2
                
                # 检查是否还在运行
                if kill -0 "$pid" 2>/dev/null; then
                    echo -e "${RED}  强制停止进程 $pid...${NC}"
                    kill -KILL "$pid" 2>/dev/null || true
                fi
            done
            
            echo -e "${GREEN}✓ 端口 $port 已释放${NC}"
        else
            echo -e "${GREEN}○ 端口 $port 空闲${NC}"
        fi
    done
}

# 停止Vite开发服务器
stop_vite_processes() {
    echo -e "${YELLOW}停止Vite开发服务器...${NC}"
    
    local vite_pids=$(pgrep -f "vite" 2>/dev/null || true)
    
    if [ -n "$vite_pids" ]; then
        for pid in $vite_pids; do
            local cmd=$(ps -p $pid -o args= 2>/dev/null | head -c 50 || echo "unknown")
            echo -e "${YELLOW}  停止Vite进程 $pid ($cmd...)${NC}"
            
            kill -TERM "$pid" 2>/dev/null || true
            sleep 1
            
            if kill -0 "$pid" 2>/dev/null; then
                kill -KILL "$pid" 2>/dev/null || true
            fi
        done
        echo -e "${GREEN}✓ Vite进程已停止${NC}"
    else
        echo -e "${GREEN}○ 没有运行的Vite进程${NC}"
    fi
}

# 停止Node.js进程
stop_node_processes() {
    echo -e "${YELLOW}停止相关Node.js进程...${NC}"
    
    # 查找可能的开发服务器进程
    local node_pids=$(pgrep -f "node.*vite\|node.*dev\|node.*1420\|node.*1421" 2>/dev/null || true)
    
    if [ -n "$node_pids" ]; then
        for pid in $node_pids; do
            local cmd=$(ps -p $pid -o args= 2>/dev/null | head -c 50 || echo "unknown")
            echo -e "${YELLOW}  停止Node.js进程 $pid ($cmd...)${NC}"
            
            kill -TERM "$pid" 2>/dev/null || true
            sleep 1
            
            if kill -0 "$pid" 2>/dev/null; then
                kill -KILL "$pid" 2>/dev/null || true
            fi
        done
        echo -e "${GREEN}✓ Node.js进程已停止${NC}"
    else
        echo -e "${GREEN}○ 没有相关的Node.js进程${NC}"
    fi
}

# 停止Tauri进程
stop_tauri_processes() {
    echo -e "${YELLOW}停止Tauri进程...${NC}"
    
    local tauri_pids=$(pgrep -f "tauri\|cargo.*tauri" 2>/dev/null || true)
    
    if [ -n "$tauri_pids" ]; then
        for pid in $tauri_pids; do
            local cmd=$(ps -p $pid -o comm= 2>/dev/null || echo "unknown")
            echo -e "${YELLOW}  停止Tauri进程 $pid ($cmd)${NC}"
            
            kill -TERM "$pid" 2>/dev/null || true
            sleep 2
            
            if kill -0 "$pid" 2>/dev/null; then
                kill -KILL "$pid" 2>/dev/null || true
            fi
        done
        echo -e "${GREEN}✓ Tauri进程已停止${NC}"
    else
        echo -e "${GREEN}○ 没有运行的Tauri进程${NC}"
    fi
}

# 清理临时文件
cleanup_temp_files() {
    echo -e "${YELLOW}清理临时文件...${NC}"
    
    # 清理PID目录
    rm -f "$PID_DIR"/*.pid 2>/dev/null || true
    
    # 清理可能的锁文件
    rm -f "$PROJECT_ROOT"/.vite-* 2>/dev/null || true
    rm -f "$PROJECT_ROOT"/crawler-client/.vite-* 2>/dev/null || true
    rm -f "$PROJECT_ROOT"/management-client/.vite-* 2>/dev/null || true
    
    echo -e "${GREEN}✓ 临时文件已清理${NC}"
}

# 验证停止结果
verify_stop() {
    echo -e "${YELLOW}验证停止结果...${NC}"
    
    local ports="1420 1421"
    local all_stopped=true

    for port in $ports; do
        if lsof -i:$port >/dev/null 2>&1; then
            echo -e "${RED}✗ 端口 $port 仍被占用${NC}"
            lsof -i:$port
            all_stopped=false
        else
            echo -e "${GREEN}✓ 端口 $port 已释放${NC}"
        fi
    done
    
    if [ "$all_stopped" = true ]; then
        echo -e "${GREEN}✓ 所有服务已成功停止${NC}"
    else
        echo -e "${YELLOW}⚠ 部分服务可能仍在运行${NC}"
    fi
}

# 显示使用帮助
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -f, --force     强制停止所有相关进程"
    echo "  -p, --ports     仅停止端口占用的进程"
    echo "  -c, --clean     停止后清理临时文件"
    echo "  -h, --help      显示帮助信息"
    echo ""
}

# 主执行流程
main() {
    case "${1:-normal}" in
        -f|--force)
            echo -e "${RED}强制停止模式${NC}"
            stop_pid_processes
            stop_port_processes
            stop_vite_processes
            stop_node_processes
            stop_tauri_processes
            cleanup_temp_files
            verify_stop
            ;;
        -p|--ports)
            echo -e "${YELLOW}仅停止端口占用进程${NC}"
            stop_port_processes
            verify_stop
            ;;
        -c|--clean)
            echo -e "${YELLOW}停止并清理${NC}"
            stop_pid_processes
            stop_port_processes
            stop_vite_processes
            stop_node_processes
            cleanup_temp_files
            verify_stop
            ;;
        -h|--help)
            show_help
            ;;
        normal)
            stop_pid_processes
            stop_port_processes
            stop_vite_processes
            stop_node_processes
            verify_stop
            ;;
        *)
            echo "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
    
    echo ""
    echo -e "${GREEN}========================================${NC}"
    echo -e "${GREEN}系统停止完成！${NC}"
    echo -e "${GREEN}========================================${NC}"
}

# 执行主函数
main "$@"
