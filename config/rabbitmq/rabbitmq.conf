# 微博数据采集分析系统 - RabbitMQ配置
# WeiboAnalytics RabbitMQ Configuration

# 基础配置
listeners.tcp.default = 5672
management.tcp.port = 15672

# 内存和磁盘限制
vm_memory_high_watermark.relative = 0.6
disk_free_limit.relative = 2.0

# 日志配置
log.console = true
log.console.level = info
log.file = /var/log/rabbitmq/rabbit.log
log.file.level = info

# 集群配置
cluster_formation.peer_discovery_backend = rabbit_peer_discovery_classic_config
cluster_formation.classic_config.nodes.1 = rabbit@localhost

# 队列配置
queue_master_locator = min-masters

# 消息持久化
default_vhost = /
default_user = weibo_admin
default_pass = weibo_admin_password
default_user_tags.administrator = true
default_permissions.configure = .*
default_permissions.write = .*
default_permissions.read = .*

# 性能优化
channel_max = 2047
frame_max = 131072
heartbeat = 60

# 插件启用
management.load_definitions = /etc/rabbitmq/definitions.json
