{"rabbit_version": "3.9.27", "rabbitmq_version": "3.9.27", "product_name": "RabbitMQ", "product_version": "3.9.27", "users": [{"name": "weibo_admin", "password_hash": "weibo_admin_password", "hashing_algorithm": "rabbit_password_hashing_sha256", "tags": "administrator"}, {"name": "weibo_app", "password_hash": "weibo_app_password", "hashing_algorithm": "rabbit_password_hashing_sha256", "tags": ""}], "vhosts": [{"name": "/"}, {"name": "/weibo"}], "permissions": [{"user": "weibo_admin", "vhost": "/", "configure": ".*", "write": ".*", "read": ".*"}, {"user": "weibo_admin", "vhost": "/weibo", "configure": ".*", "write": ".*", "read": ".*"}, {"user": "weibo_app", "vhost": "/weibo", "configure": "weibo\\..*", "write": "weibo\\..*", "read": "weibo\\..*"}], "topic_permissions": [], "parameters": [], "global_parameters": [{"name": "cluster_name", "value": "weibo_analytics_cluster"}], "policies": [{"vhost": "/weibo", "name": "ha-all", "pattern": ".*", "apply-to": "all", "definition": {"ha-mode": "all", "ha-sync-mode": "automatic"}, "priority": 0}], "queues": [{"name": "weibo.crawl.tasks", "vhost": "/weibo", "durable": true, "auto_delete": false, "arguments": {"x-message-ttl": 3600000, "x-max-length": 10000, "x-overflow": "reject-publish"}}, {"name": "weibo.crawl.results", "vhost": "/weibo", "durable": true, "auto_delete": false, "arguments": {"x-message-ttl": 1800000, "x-max-length": 5000}}, {"name": "weibo.analysis.tasks", "vhost": "/weibo", "durable": true, "auto_delete": false, "arguments": {"x-message-ttl": 3600000, "x-max-length": 5000}}, {"name": "weibo.notifications", "vhost": "/weibo", "durable": true, "auto_delete": false, "arguments": {"x-message-ttl": 600000, "x-max-length": 1000}}, {"name": "weibo.dead_letter", "vhost": "/weibo", "durable": true, "auto_delete": false, "arguments": {}}], "exchanges": [{"name": "weibo.direct", "vhost": "/weibo", "type": "direct", "durable": true, "auto_delete": false, "internal": false, "arguments": {}}, {"name": "weibo.topic", "vhost": "/weibo", "type": "topic", "durable": true, "auto_delete": false, "internal": false, "arguments": {}}, {"name": "weibo.fanout", "vhost": "/weibo", "type": "fanout", "durable": true, "auto_delete": false, "internal": false, "arguments": {}}, {"name": "weibo.dead_letter_exchange", "vhost": "/weibo", "type": "direct", "durable": true, "auto_delete": false, "internal": false, "arguments": {}}], "bindings": [{"source": "weibo.direct", "vhost": "/weibo", "destination": "weibo.crawl.tasks", "destination_type": "queue", "routing_key": "crawl.task", "arguments": {}}, {"source": "weibo.direct", "vhost": "/weibo", "destination": "weibo.crawl.results", "destination_type": "queue", "routing_key": "crawl.result", "arguments": {}}, {"source": "weibo.direct", "vhost": "/weibo", "destination": "weibo.analysis.tasks", "destination_type": "queue", "routing_key": "analysis.task", "arguments": {}}, {"source": "weibo.topic", "vhost": "/weibo", "destination": "weibo.notifications", "destination_type": "queue", "routing_key": "notification.*", "arguments": {}}, {"source": "weibo.dead_letter_exchange", "vhost": "/weibo", "destination": "weibo.dead_letter", "destination_type": "queue", "routing_key": "dead_letter", "arguments": {}}]}