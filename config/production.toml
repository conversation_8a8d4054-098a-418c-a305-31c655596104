# 微博数据采集分析系统 - 生产环境配置
# WeiboAnalytics Production Configuration

[app]
name = "WeiboAnalytics"
version = "1.0.0"
environment = "Production"
debug = false
data_dir = "/var/lib/weibo-analytics"
temp_dir = "/tmp/weibo-analytics"

[database]
postgres_url = "postgresql://weibo_app:${WEIBO_DB_PASSWORD}@${WEIBO_DB_HOST}:5432/weibo_analytics"
redis_url = "redis://:${WEIBO_REDIS_PASSWORD}@${WEIBO_REDIS_HOST}:6379"
max_connections = 50
min_connections = 10
connection_timeout = 30
idle_timeout = 600

[messaging]
url = "amqp://weibo_app:${WEIBO_RABBITMQ_PASSWORD}@${WEIBO_RABBITMQ_HOST}:5672/weibo"
vhost = "/weibo"
username = "weibo_app"
password = "${WEIBO_RABBITMQ_PASSWORD}"
connection_timeout = 30
heartbeat = 60
max_retries = 5
retry_delay = 10

[logging]
level = "info"
format = "Json"
output = "Both"
structured = true
include_location = false
include_thread_id = true
include_thread_name = true

[logging.file]
directory = "/var/log/weibo-analytics"
filename_prefix = "weibo_analytics"
rotation = "Daily"
max_files = 30

[logging.console]
colored = false
show_target = false
show_level = true
show_time = true

[crawler]
max_concurrent_tasks = 20
request_timeout = 60
retry_attempts = 5
retry_delay = 10
user_agent = "WeiboAnalytics/1.0"
cookies_file = "/var/lib/weibo-analytics/cookies.json"

[crawler.rate_limit]
requests_per_minute = 60
requests_per_hour = 1000
burst_size = 10

[analysis]
batch_size = 200
max_parallel_jobs = 8

[analysis.sentiment_model]
model_path = "/opt/weibo-analytics/models/sentiment"
model_type = "bert"
confidence_threshold = 0.8
batch_size = 64

[analysis.trend_analysis]
window_size = 24
min_mentions = 20
update_interval = 1800

[server]
host = "0.0.0.0"
port = 8080
keep_alive = 75
client_timeout = 10000
client_shutdown = 10000

[security]
secret_key = "${WEIBO_SECRET_KEY}"
jwt_expiration = 43200
password_min_length = 8
max_login_attempts = 5
lockout_duration = 900

[security.cors]
allowed_origins = ["https://weibo-analytics.example.com"]
allowed_methods = ["GET", "POST", "PUT", "DELETE"]
allowed_headers = ["Content-Type", "Authorization"]
max_age = 86400
