# 微博数据采集分析系统 - 开发环境配置
# WeiboAnalytics Development Configuration

[app]
name = "WeiboAnalytics"
version = "1.0.0"
environment = "Development"
debug = true
data_dir = "data"
temp_dir = "tmp"

[database]
postgres_url = "postgresql://weibo_app:weibo_app_password@localhost:5432/weibo_analytics"
redis_url = "redis://localhost:6379"
max_connections = 10
min_connections = 2
connection_timeout = 30
idle_timeout = 600

[messaging]
url = "amqp://weibo_app:weibo_app_password@localhost:5672/weibo"
vhost = "/weibo"
username = "weibo_app"
password = "weibo_app_password"
connection_timeout = 30
heartbeat = 60
max_retries = 3
retry_delay = 5

[logging]
level = "debug"
format = "Pretty"
output = "Both"
structured = false
include_location = true
include_thread_id = false
include_thread_name = true

[logging.file]
directory = "logs"
filename_prefix = "weibo_analytics"
rotation = "Daily"
max_files = 7

[logging.console]
colored = true
show_target = true
show_level = true
show_time = true

[crawler]
max_concurrent_tasks = 5
request_timeout = 30
retry_attempts = 3
retry_delay = 5
user_agent = "WeiboAnalytics/1.0 (Development)"
cookies_file = "data/cookies.json"

[crawler.rate_limit]
requests_per_minute = 30
requests_per_hour = 500
burst_size = 5

[analysis]
batch_size = 50
max_parallel_jobs = 2

[analysis.sentiment_model]
model_path = "models/sentiment"
model_type = "bert"
confidence_threshold = 0.7
batch_size = 16

[analysis.trend_analysis]
window_size = 24
min_mentions = 5
update_interval = 3600

[server]
host = "127.0.0.1"
port = 8080
workers = 2
keep_alive = 75
client_timeout = 5000
client_shutdown = 5000

[security]
secret_key = "development-secret-key-change-in-production-32-chars"
jwt_expiration = 86400
password_min_length = 6
max_login_attempts = 10
lockout_duration = 300

[security.cors]
allowed_origins = ["http://localhost:3000", "http://localhost:1420", "http://localhost:1421"]
allowed_methods = ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
allowed_headers = ["Content-Type", "Authorization", "X-Requested-With"]
max_age = 3600
