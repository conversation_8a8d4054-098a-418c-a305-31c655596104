import React from 'react';
import { QrC<PERSON>, RefreshCw, CheckCircle, XCircle } from 'lucide-react';
import { useAuthStore } from '../store/authStore';
import { <PERSON><PERSON>, Card, CardHeader, CardContent, Badge } from '../components/ui';

const LoginPage: React.FC = () => {
  const { 
    qrCode, 
    loginStatus, 
    userInfo, 
    isLoggedIn,
    setQrCode,
    setLoginStatus,
    clearQrCode 
  } = useAuthStore();

  const handleGenerateQrCode = () => {
    setLoginStatus('generating');
    // 模拟生成二维码
    setTimeout(() => {
      const mockQrCode = `data:image/svg+xml;base64,${btoa('<svg>Mock QR Code</svg>')}`;
      setQrCode(mockQrCode);
    }, 1000);
  };

  const handleRefreshQrCode = () => {
    clearQrCode();
    handleGenerateQrCode();
  };

  const getStatusBadge = () => {
    switch (loginStatus) {
      case 'generating':
        return <Badge variant="info">生成中</Badge>;
      case 'waiting':
        return <Badge variant="warning">等待扫码</Badge>;
      case 'success':
        return <Badge variant="success">登录成功</Badge>;
      case 'failed':
        return <Badge variant="danger">登录失败</Badge>;
      default:
        return <Badge variant="default">未开始</Badge>;
    }
  };

  if (isLoggedIn && userInfo) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader 
            title="登录成功"
            subtitle="欢迎使用微博爬虫客户端"
          />
          <CardContent>
            <div className="flex items-center space-x-4">
              <img 
                src={userInfo.avatar || '/default-avatar.png'} 
                alt="用户头像"
                className="w-12 h-12 rounded-full"
              />
              <div>
                <h3 className="font-medium text-gray-900 dark:text-white">
                  {userInfo.nickname}
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  @{userInfo.username}
                </p>
              </div>
              {userInfo.verified && (
                <CheckCircle className="w-5 h-5 text-blue-500" />
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader 
          title="微博登录"
          subtitle="扫描二维码登录微博账号"
          action={getStatusBadge()}
        />
        <CardContent>
          <div className="text-center">
            {!qrCode && loginStatus === 'idle' && (
              <div className="py-12">
                <QrCode className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 dark:text-gray-400 mb-6">
                  点击下方按钮生成登录二维码
                </p>
                <Button 
                  onClick={handleGenerateQrCode}
                  icon={<QrCode className="w-4 h-4" />}
                >
                  生成二维码
                </Button>
              </div>
            )}

            {loginStatus === 'generating' && (
              <div className="py-12">
                <RefreshCw className="w-16 h-16 text-blue-500 mx-auto mb-4 animate-spin" />
                <p className="text-gray-500 dark:text-gray-400">
                  正在生成二维码...
                </p>
              </div>
            )}

            {qrCode && loginStatus === 'waiting' && (
              <div className="py-4">
                <div className="w-48 h-48 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                  <QrCode className="w-32 h-32 text-gray-400" />
                </div>
                <p className="text-gray-500 dark:text-gray-400 mb-4">
                  请使用微博APP扫描二维码
                </p>
                <div className="flex justify-center space-x-2">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={handleRefreshQrCode}
                    icon={<RefreshCw className="w-4 h-4" />}
                  >
                    刷新
                  </Button>
                </div>
              </div>
            )}

            {loginStatus === 'failed' && (
              <div className="py-12">
                <XCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
                <p className="text-red-500 mb-6">
                  登录失败，请重试
                </p>
                <Button 
                  onClick={handleGenerateQrCode}
                  icon={<QrCode className="w-4 h-4" />}
                >
                  重新生成
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default LoginPage;
