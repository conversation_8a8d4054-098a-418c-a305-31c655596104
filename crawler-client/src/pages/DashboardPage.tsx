import React from 'react';
import { Activity, Play, Pause, Plus, Settings } from 'lucide-react';
import { useTaskStore } from '../store/taskStore';
import { useAuthStore } from '../store/authStore';
import { But<PERSON>, Card, CardHeader, CardContent, Badge } from '../components/ui';

const DashboardPage: React.FC = () => {
  const { userInfo } = useAuthStore();
  const { 
    tasks, 
    currentTask, 
    isRunning,
    getPendingTasks,
    getRunningTasks,
    getCompletedTasks,
    getFailedTasks 
  } = useTaskStore();

  const pendingTasks = getPendingTasks();
  const runningTasks = getRunningTasks();
  const completedTasks = getCompletedTasks();
  const failedTasks = getFailedTasks();

  const stats = [
    { label: '总任务', value: tasks.length, color: 'text-gray-600' },
    { label: '等待中', value: pendingTasks.length, color: 'text-yellow-600' },
    { label: '运行中', value: runningTasks.length, color: 'text-blue-600' },
    { label: '已完成', value: completedTasks.length, color: 'text-green-600' },
    { label: '失败', value: failedTasks.length, color: 'text-red-600' },
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Activity className="h-8 w-8 text-primary-600" />
              <h1 className="ml-3 text-xl font-semibold text-gray-900 dark:text-white">
                微博爬虫客户端
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              {userInfo && (
                <div className="flex items-center space-x-2">
                  <img 
                    src={userInfo.avatar || '/default-avatar.png'} 
                    alt="用户头像"
                    className="w-8 h-8 rounded-full"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">
                    {userInfo.nickname}
                  </span>
                </div>
              )}
              <Button variant="ghost" size="sm">
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
            {stats.map((stat, index) => (
              <Card key={index} padding="sm">
                <div className="text-center">
                  <div className={`text-2xl font-bold ${stat.color}`}>
                    {stat.value}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    {stat.label}
                  </div>
                </div>
              </Card>
            ))}
          </div>

          {/* Control Panel */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
            {/* Current Task */}
            <Card>
              <CardHeader 
                title="当前任务"
                subtitle={currentTask ? `正在执行: ${currentTask.type}` : '无任务运行'}
              />
              <CardContent>
                {currentTask ? (
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">目标:</span>
                      <span className="text-sm font-medium">{currentTask.target}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">进度:</span>
                      <span className="text-sm font-medium">{currentTask.progress}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${currentTask.progress}%` }}
                      />
                    </div>
                    <div className="flex justify-center">
                      <Button 
                        variant={isRunning ? "danger" : "primary"}
                        size="sm"
                        icon={isRunning ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                      >
                        {isRunning ? '暂停' : '继续'}
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Activity className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                    <p className="text-gray-500 dark:text-gray-400">
                      暂无运行任务
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader title="快速操作" />
              <CardContent>
                <div className="space-y-3">
                  <Button 
                    className="w-full justify-start"
                    variant="outline"
                    icon={<Plus className="w-4 h-4" />}
                  >
                    新建用户爬取任务
                  </Button>
                  <Button 
                    className="w-full justify-start"
                    variant="outline"
                    icon={<Plus className="w-4 h-4" />}
                  >
                    新建关键词任务
                  </Button>
                  <Button 
                    className="w-full justify-start"
                    variant="outline"
                    icon={<Plus className="w-4 h-4" />}
                  >
                    新建话题任务
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* System Status */}
            <Card>
              <CardHeader title="系统状态" />
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">登录状态:</span>
                    <Badge variant="success">已登录</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">运行状态:</span>
                    <Badge variant={isRunning ? "info" : "default"}>
                      {isRunning ? '运行中' : '空闲'}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">队列状态:</span>
                    <Badge variant={pendingTasks.length > 0 ? "warning" : "default"}>
                      {pendingTasks.length > 0 ? `${pendingTasks.length}个等待` : '空闲'}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Recent Tasks */}
          <Card>
            <CardHeader 
              title="最近任务"
              subtitle={`共 ${tasks.length} 个任务`}
              action={
                <Button size="sm" variant="outline">
                  查看全部
                </Button>
              }
            />
            <CardContent>
              {tasks.length > 0 ? (
                <div className="space-y-3">
                  {tasks.slice(0, 5).map((task) => (
                    <div 
                      key={task.id}
                      className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
                    >
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <span className="font-medium text-sm">{task.target}</span>
                          <Badge 
                            variant={
                              task.status === 'completed' ? 'success' :
                              task.status === 'running' ? 'info' :
                              task.status === 'failed' ? 'danger' : 'warning'
                            }
                            size="sm"
                          >
                            {task.status === 'completed' ? '完成' :
                             task.status === 'running' ? '运行中' :
                             task.status === 'failed' ? '失败' : '等待'}
                          </Badge>
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                          {task.type} • {new Date(task.created_at).toLocaleString()}
                        </p>
                      </div>
                      <div className="text-sm text-gray-500">
                        {task.progress}%
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Activity className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                  <p className="text-gray-500 dark:text-gray-400">
                    暂无任务记录
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
};

export default DashboardPage;
