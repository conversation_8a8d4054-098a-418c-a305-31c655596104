import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// 登录状态类型
export interface LoginState {
  isLoggedIn: boolean;
  userInfo: {
    username: string;
    nickname: string;
    avatar: string;
    verified: boolean;
  } | null;
  cookies: string | null;
  qrCode: string | null;
  loginStatus: 'idle' | 'generating' | 'waiting' | 'success' | 'failed';
}

// 认证状态管理
interface AuthStore extends LoginState {
  // Actions
  setQrCode: (qrCode: string) => void;
  setLoginStatus: (status: LoginState['loginStatus']) => void;
  setUserInfo: (userInfo: LoginState['userInfo']) => void;
  setCookies: (cookies: string) => void;
  login: (userInfo: LoginState['userInfo'], cookies: string) => void;
  logout: () => void;
  clearQrCode: () => void;
}

const initialState: LoginState = {
  isLoggedIn: false,
  userInfo: null,
  cookies: null,
  qrCode: null,
  loginStatus: 'idle',
};

export const useAuthStore = create<AuthStore>()(
  persist(
    (set) => ({
      ...initialState,
      
      setQrCode: (qrCode: string) => {
        set({ qrCode, loginStatus: 'waiting' });
      },
      
      setLoginStatus: (loginStatus: LoginState['loginStatus']) => {
        set({ loginStatus });
      },
      
      setUserInfo: (userInfo: LoginState['userInfo']) => {
        set({ userInfo });
      },
      
      setCookies: (cookies: string) => {
        set({ cookies });
      },
      
      login: (userInfo: LoginState['userInfo'], cookies: string) => {
        set({
          isLoggedIn: true,
          userInfo,
          cookies,
          loginStatus: 'success',
          qrCode: null,
        });
      },
      
      logout: () => {
        set({
          ...initialState,
          loginStatus: 'idle',
        });
      },
      
      clearQrCode: () => {
        set({ qrCode: null, loginStatus: 'idle' });
      },
    }),
    {
      name: 'weibo-auth-storage',
      partialize: (state) => ({
        isLoggedIn: state.isLoggedIn,
        userInfo: state.userInfo,
        cookies: state.cookies,
      }),
    }
  )
);
