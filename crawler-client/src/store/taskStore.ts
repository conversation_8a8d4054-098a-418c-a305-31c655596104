import { create } from 'zustand';
import { CrawlTask } from '../../../shared/types';

// 任务状态管理
interface TaskStore {
  // State
  tasks: CrawlTask[];
  currentTask: CrawlTask | null;
  isRunning: boolean;
  
  // Actions
  addTask: (task: Omit<CrawlTask, 'id' | 'created_at'>) => void;
  updateTask: (id: string, updates: Partial<CrawlTask>) => void;
  removeTask: (id: string) => void;
  setCurrentTask: (task: CrawlTask | null) => void;
  setIsRunning: (isRunning: boolean) => void;
  clearTasks: () => void;
  
  // Getters
  getTaskById: (id: string) => CrawlTask | undefined;
  getPendingTasks: () => CrawlTask[];
  getRunningTasks: () => CrawlTask[];
  getCompletedTasks: () => CrawlTask[];
  getFailedTasks: () => CrawlTask[];
}

export const useTaskStore = create<TaskStore>((set, get) => ({
  // Initial state
  tasks: [],
  currentTask: null,
  isRunning: false,
  
  // Actions
  addTask: (taskData) => {
    const newTask: CrawlTask = {
      ...taskData,
      id: crypto.randomUUID(),
      created_at: new Date().toISOString(),
    };
    
    set((state) => ({
      tasks: [...state.tasks, newTask],
    }));
  },
  
  updateTask: (id, updates) => {
    set((state) => ({
      tasks: state.tasks.map((task) =>
        task.id === id ? { ...task, ...updates } : task
      ),
    }));
  },
  
  removeTask: (id) => {
    set((state) => ({
      tasks: state.tasks.filter((task) => task.id !== id),
      currentTask: state.currentTask?.id === id ? null : state.currentTask,
    }));
  },
  
  setCurrentTask: (task) => {
    set({ currentTask: task });
  },
  
  setIsRunning: (isRunning) => {
    set({ isRunning });
  },
  
  clearTasks: () => {
    set({ tasks: [], currentTask: null, isRunning: false });
  },
  
  // Getters
  getTaskById: (id) => {
    return get().tasks.find((task) => task.id === id);
  },
  
  getPendingTasks: () => {
    return get().tasks.filter((task) => task.status === 'pending');
  },
  
  getRunningTasks: () => {
    return get().tasks.filter((task) => task.status === 'running');
  },
  
  getCompletedTasks: () => {
    return get().tasks.filter((task) => task.status === 'completed');
  },
  
  getFailedTasks: () => {
    return get().tasks.filter((task) => task.status === 'failed');
  },
}));
