/**
 * @license lucide-react v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M5 7v11a1 1 0 0 0 1 1h11", key: "13dt1j" }],
  ["path", { d: "M5.293 18.707 11 13", key: "ezgbsx" }],
  ["circle", { cx: "19", cy: "19", r: "2", key: "17f5cg" }],
  ["circle", { cx: "5", cy: "5", r: "2", key: "1gwv83" }]
];
const Scale3d = createLucideIcon("scale-3d", __iconNode);

export { __iconNode, Scale3d as default };
//# sourceMappingURL=scale-3d.js.map
