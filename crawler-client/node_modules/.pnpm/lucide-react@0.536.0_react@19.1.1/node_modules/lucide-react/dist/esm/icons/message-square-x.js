/**
 * @license lucide-react v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  [
    "path",
    {
      d: "M22 17a2 2 0 0 1-2 2H6.828a2 2 0 0 0-1.414.586l-2.202 2.202A.71.71 0 0 1 2 21.286V5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2z",
      key: "18887p"
    }
  ],
  ["path", { d: "m14.5 8.5-5 5", key: "19tnj2" }],
  ["path", { d: "m9.5 8.5 5 5", key: "1oa8ql" }]
];
const MessageSquareX = createLucideIcon("message-square-x", __iconNode);

export { __iconNode, MessageSquareX as default };
//# sourceMappingURL=message-square-x.js.map
