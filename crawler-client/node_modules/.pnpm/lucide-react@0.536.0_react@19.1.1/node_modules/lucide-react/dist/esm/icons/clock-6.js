/**
 * @license lucide-react v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M12 6v10", key: "wf7rdh" }],
  ["circle", { cx: "12", cy: "12", r: "10", key: "1mglay" }]
];
const Clock6 = createLucideIcon("clock-6", __iconNode);

export { __iconNode, Clock6 as default };
//# sourceMappingURL=clock-6.js.map
