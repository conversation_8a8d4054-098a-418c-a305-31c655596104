/**
 * @license lucide-react v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  [
    "path",
    {
      d: "M12 3H4a2 2 0 0 0-2 2v16.286a.71.71 0 0 0 1.212.502l2.202-2.202A2 2 0 0 1 6.828 19H20a2 2 0 0 0 2-2v-4",
      key: "11da1y"
    }
  ],
  ["path", { d: "M16 3h6v6", key: "1bx56c" }],
  ["path", { d: "m16 9 6-6", key: "m4dnic" }]
];
const MessageSquareShare = createLucideIcon("message-square-share", __iconNode);

export { __iconNode, MessageSquareShare as default };
//# sourceMappingURL=message-square-share.js.map
