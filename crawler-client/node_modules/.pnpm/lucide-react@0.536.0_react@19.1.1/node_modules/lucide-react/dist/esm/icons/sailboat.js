/**
 * @license lucide-react v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M10 2v15", key: "1qf71f" }],
  [
    "path",
    { d: "M7 22a4 4 0 0 1-4-4 1 1 0 0 1 1-1h16a1 1 0 0 1 1 1 4 4 0 0 1-4 4z", key: "1pxcvx" }
  ],
  [
    "path",
    {
      d: "M9.159 2.46a1 1 0 0 1 1.521-.193l9.977 8.98A1 1 0 0 1 20 13H4a1 1 0 0 1-.824-1.567z",
      key: "5oog16"
    }
  ]
];
const Sailboat = createLucideIcon("sailboat", __iconNode);

export { __iconNode, Sailboat as default };
//# sourceMappingURL=sailboat.js.map
