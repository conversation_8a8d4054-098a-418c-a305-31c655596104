/**
 * @license lucide-react v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["rect", { width: "6", height: "14", x: "6", y: "5", rx: "2", key: "hsirpf" }],
  ["rect", { width: "6", height: "10", x: "16", y: "7", rx: "2", key: "13zkjt" }],
  ["path", { d: "M2 2v20", key: "1ivd8o" }]
];
const AlignHorizontalJustifyStart = createLucideIcon("align-horizontal-justify-start", __iconNode);

export { __iconNode, AlignHorizontalJustifyStart as default };
//# sourceMappingURL=align-horizontal-justify-start.js.map
