/**
 * @license lucide-react v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M13.5 8h-3", key: "xvov4w" }],
  [
    "path",
    {
      d: "m15 2-1 2h3a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h3",
      key: "16uttc"
    }
  ],
  ["path", { d: "M16.899 22A5 5 0 0 0 7.1 22", key: "1d0ppr" }],
  ["path", { d: "m9 2 3 6", key: "1o7bd9" }],
  ["circle", { cx: "12", cy: "15", r: "3", key: "g36mzq" }]
];
const IdCardLanyard = createLucideIcon("id-card-lanyard", __iconNode);

export { __iconNode, IdCardLanyard as default };
//# sourceMappingURL=id-card-lanyard.js.map
