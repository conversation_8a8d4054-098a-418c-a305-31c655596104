/**
 * @license lucide-react v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  [
    "path",
    {
      d: "M13.971 4.285A2 2 0 0 1 17 6v12a2 2 0 0 1-3.029 1.715l-9.997-5.998a2 2 0 0 1-.003-3.432z",
      key: "19qhus"
    }
  ],
  ["path", { d: "M21 20V4", key: "cb8qj8" }]
];
const StepBack = createLucideIcon("step-back", __iconNode);

export { __iconNode, StepBack as default };
//# sourceMappingURL=step-back.js.map
