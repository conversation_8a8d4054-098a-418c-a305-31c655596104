/**
 * @license lucide-react v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M10 5V3", key: "1y54qe" }],
  ["path", { d: "M14 5V3", key: "m6isi" }],
  ["path", { d: "M15 21v-3a3 3 0 0 0-6 0v3", key: "lbp5hj" }],
  ["path", { d: "M18 3v8", key: "2ollhf" }],
  ["path", { d: "M18 5H6", key: "98imr9" }],
  ["path", { d: "M22 11H2", key: "1lmjae" }],
  ["path", { d: "M22 9v10a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V9", key: "1rly83" }],
  ["path", { d: "M6 3v8", key: "csox7g" }]
];
const Castle = createLucideIcon("castle", __iconNode);

export { __iconNode, Castle as default };
//# sourceMappingURL=castle.js.map
