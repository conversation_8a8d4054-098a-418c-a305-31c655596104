/**
 * @license lucide-react v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M10 6.5v11a5.5 5.5 0 0 0 5.5-5.5", key: "nw10mp" }],
  ["path", { d: "m14 8-6 3", key: "2tb98i" }],
  [
    "path",
    { d: "M4 2v20l2-1 2 1 2-1 2 1 2-1 2 1 2-1 2 1V2l-2 1-2-1-2 1-2-1-2 1-2-1-2 1z", key: "io9ry0" }
  ]
];
const ReceiptTurkishLira = createLucideIcon("receipt-turkish-lira", __iconNode);

export { __iconNode, ReceiptTurkishLira as default };
//# sourceMappingURL=receipt-turkish-lira.js.map
