/**
 * @license lucide-react v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  [
    "path",
    {
      d: "M7 3.5c5-2 7 2.5 3 4C1.5 10 2 15 5 16c5 2 9-10 14-7s.5 13.5-4 12c-5-2.5.5-11 6-2",
      key: "1lrphd"
    }
  ]
];
const LineSquiggle = createLucideIcon("line-squiggle", __iconNode);

export { __iconNode, LineSquiggle as default };
//# sourceMappingURL=line-squiggle.js.map
