/**
 * @license lucide-react v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M11 7v10a5 5 0 0 0 5-5", key: "1ja3ih" }],
  ["path", { d: "m15 8-6 3", key: "4x0uwz" }],
  [
    "path",
    {
      d: "M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76",
      key: "18242g"
    }
  ]
];
const BadgeTurkishLira = createLucideIcon("badge-turkish-lira", __iconNode);

export { __iconNode, BadgeTurkishLira as default };
//# sourceMappingURL=badge-turkish-lira.js.map
