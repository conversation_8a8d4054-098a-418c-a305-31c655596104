/**
 * @license lucide-react v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "m10.852 14.772-.383.923", key: "11vil6" }],
  ["path", { d: "m10.852 9.228-.383-.923", key: "1fjppe" }],
  ["path", { d: "m13.148 14.772.382.924", key: "je3va1" }],
  ["path", { d: "m13.531 8.305-.383.923", key: "18epck" }],
  ["path", { d: "m14.772 10.852.923-.383", key: "k9m8cz" }],
  ["path", { d: "m14.772 13.148.923.383", key: "1xvhww" }],
  [
    "path",
    {
      d: "M17.598 6.5A3 3 0 1 0 12 5a3 3 0 0 0-5.63-1.446 3 3 0 0 0-.368 1.571 4 4 0 0 0-2.525 5.771",
      key: "jcbbz1"
    }
  ],
  ["path", { d: "M17.998 5.125a4 4 0 0 1 2.525 5.771", key: "1kkn7e" }],
  ["path", { d: "M19.505 10.294a4 4 0 0 1-1.5 7.706", key: "18bmuc" }],
  [
    "path",
    {
      d: "M4.032 17.483A4 4 0 0 0 11.464 20c.18-.311.892-.311 1.072 0a4 4 0 0 0 7.432-2.516",
      key: "uozx0d"
    }
  ],
  ["path", { d: "M4.5 10.291A4 4 0 0 0 6 18", key: "whdemb" }],
  ["path", { d: "M6.002 5.125a3 3 0 0 0 .4 1.375", key: "1kqy2g" }],
  ["path", { d: "m9.228 10.852-.923-.383", key: "1wtb30" }],
  ["path", { d: "m9.228 13.148-.923.383", key: "1a830x" }],
  ["circle", { cx: "12", cy: "12", r: "3", key: "1v7zrd" }]
];
const BrainCog = createLucideIcon("brain-cog", __iconNode);

export { __iconNode, BrainCog as default };
//# sourceMappingURL=brain-cog.js.map
