/**
 * @license lucide-react v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M15 3v18", key: "14nvp0" }],
  ["path", { d: "M3 12h18", key: "1i2n21" }],
  ["path", { d: "M9 3v18", key: "fh3hqa" }],
  ["rect", { x: "3", y: "3", width: "18", height: "18", rx: "2", key: "h1oib" }]
];
const Grid3x2 = createLucideIcon("grid-3x2", __iconNode);

export { __iconNode, Grid3x2 as default };
//# sourceMappingURL=grid-3x2.js.map
