{"name": "eslint-visitor-keys", "version": "3.4.3", "description": "Constants and utilities about visitor keys to traverse AST.", "type": "module", "main": "dist/eslint-visitor-keys.cjs", "types": "./dist/index.d.ts", "exports": {".": [{"import": "./lib/index.js", "require": "./dist/eslint-visitor-keys.cjs"}, "./dist/eslint-visitor-keys.cjs"], "./package.json": "./package.json"}, "files": ["dist/index.d.ts", "dist/visitor-keys.d.ts", "dist/eslint-visitor-keys.cjs", "dist/eslint-visitor-keys.d.cts", "lib"], "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "devDependencies": {"@types/estree": "^0.0.51", "@types/estree-jsx": "^0.0.1", "@typescript-eslint/parser": "^5.14.0", "c8": "^7.11.0", "chai": "^4.3.6", "eslint": "^7.29.0", "eslint-config-eslint": "^7.0.0", "eslint-plugin-jsdoc": "^35.4.0", "eslint-plugin-node": "^11.1.0", "eslint-release": "^3.2.0", "esquery": "^1.4.0", "json-diff": "^0.7.3", "mocha": "^9.2.1", "opener": "^1.5.2", "rollup": "^2.70.0", "rollup-plugin-dts": "^4.2.3", "tsd": "^0.19.1", "typescript": "^4.6.2"}, "scripts": {"build": "npm run build:cjs && npm run build:types", "build:cjs": "rollup -c", "build:debug": "npm run build:cjs -- -m && npm run build:types", "build:keys": "node tools/build-keys-from-ts", "build:types": "tsc", "lint": "eslint .", "prepare": "npm run build", "release:generate:latest": "eslint-generate-release", "release:generate:alpha": "eslint-generate-prerelease alpha", "release:generate:beta": "eslint-generate-prerelease beta", "release:generate:rc": "eslint-generate-prerelease rc", "release:publish": "eslint-publish-release", "test": "mocha tests/lib/**/*.cjs && c8 mocha tests/lib/**/*.js && npm run test:types", "test:open-coverage": "c8 report --reporter lcov && opener coverage/lcov-report/index.html", "test:types": "tsd"}, "repository": "eslint/eslint-visitor-keys", "funding": "https://opencollective.com/eslint", "keywords": [], "author": "<PERSON><PERSON> (https://github.com/mysticatea)", "license": "Apache-2.0", "bugs": {"url": "https://github.com/eslint/eslint-visitor-keys/issues"}, "homepage": "https://github.com/eslint/eslint-visitor-keys#readme"}