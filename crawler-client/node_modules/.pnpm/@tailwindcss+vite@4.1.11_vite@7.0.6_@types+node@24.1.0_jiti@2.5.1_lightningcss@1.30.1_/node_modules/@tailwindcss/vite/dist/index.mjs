var D=(r,e)=>(e=Symbol[r])?e:Symbol.for("Symbol."+r),P=r=>{throw TypeError(r)};var b=(r,e,s)=>{if(e!=null){typeof e!="object"&&typeof e!="function"&&P("Object expected");var t,u;s&&(t=e[D("asyncDispose")]),t===void 0&&(t=e[D("dispose")],s&&(u=t)),typeof t!="function"&&P("Object not disposable"),u&&(t=function(){try{u.call(this)}catch(n){return Promise.reject(n)}}),r.push([s,t,e])}else s&&r.push([s]);return e},w=(r,e,s)=>{var t=typeof SuppressedError=="function"?SuppressedError:function(a,p,d,l){return l=Error(d),l.name="SuppressedError",l.error=a,l.suppressed=p,l},u=a=>e=s?new t(a,e,"An error was suppressed during disposal"):(s=!0,a),n=a=>{for(;a=r.pop();)try{var p=a[1]&&a[1].call(a[2]);if(a[0])return Promise.resolve(p).then(n,d=>(u(d),n()))}catch(d){u(d)}if(s)throw e};return n()};import{compile as F,env as _,Features as h,Instrumentation as M,normalizePath as B,optimize as U,toSourceMap as V}from"@tailwindcss/node";import{clearRequireCache as G}from"@tailwindcss/node/require-cache";import{Scanner as J}from"@tailwindcss/oxide";import y from"fs/promises";import m from"path";var c=_.DEBUG,A=/[?&](?:worker|sharedworker|raw|url)\b/,K=/\?commonjs-proxy/,O=/[?&]index\=\d+\.css$/;function T(){let r=[],e=null,s=!1,t=!1,u=new R(n=>{let a=e.createResolver({...e.resolve,extensions:[".css"],mainFields:["style"],conditions:["style","development|production"],tryIndex:!1,preferRelative:!0});function p(i,o){return a(i,o,!0,s)}let d=e.createResolver(e.resolve);function l(i,o){return d(i,o,!0,s)}return new C(n,e.root,e?.css.devSourcemap??!1,p,l)});return[{name:"@tailwindcss/vite:scan",enforce:"pre",configureServer(n){r.push(n)},async configResolved(n){e=n,t=e.build.cssMinify!==!1,s=e.build.ssr!==!1&&e.build.ssr!==void 0}},{name:"@tailwindcss/vite:generate:serve",apply:"serve",enforce:"pre",async transform(n,a,p){var o=[];try{if(!x(a))return;let d=b(o,new M);c&&d.start("[@tailwindcss/vite] Generate CSS (serve)");let l=u.get(a);let i=await l.generate(n,S=>this.addWatchFile(S),d);if(!i)return u.delete(a),n;c&&d.end("[@tailwindcss/vite] Generate CSS (serve)");return i}catch(f){var v=f,g=!0}finally{w(o,v,g)}}},{name:"@tailwindcss/vite:generate:build",apply:"build",enforce:"pre",async transform(n,a){var i=[];try{if(!x(a))return;let p=b(i,new M);c&&p.start("[@tailwindcss/vite] Generate CSS (build)");let d=u.get(a);let l=await d.generate(n,g=>this.addWatchFile(g),p);if(!l)return u.delete(a),n;c&&p.end("[@tailwindcss/vite] Generate CSS (build)");c&&p.start("[@tailwindcss/vite] Optimize CSS");l=U(l.code,{minify:t,map:l.map});c&&p.end("[@tailwindcss/vite] Optimize CSS");return l}catch(o){var f=o,v=!0}finally{w(i,f,v)}}}]}function q(r){let[e]=r.split("?",2);return m.extname(e).slice(1)}function x(r){return r.includes("/.vite/")?void 0:(q(r)==="css"||r.includes("&lang.css")||r.match(O))&&!A.test(r)&&!K.test(r)}function E(r){return m.resolve(r.replace(/\?.*$/,""))}var R=class extends Map{constructor(s){super();this.factory=s}get(s){let t=super.get(s);return t===void 0&&(t=this.factory(s,this),this.set(s,t)),t}},C=class{constructor(e,s,t,u,n){this.id=e;this.base=s;this.enableSourceMaps=t;this.customCssResolver=u;this.customJsResolver=n}compiler;scanner;candidates=new Set;buildDependencies=new Map;async generate(e,s,t){let u=E(this.id);function n(i){i!==u&&(/[\#\?].*\.svg$/.test(i)||s(i))}let a=this.requiresBuild(),p=m.dirname(m.resolve(u));if(!this.compiler||!this.scanner||await a){G(Array.from(this.buildDependencies.keys())),this.buildDependencies.clear(),this.addBuildDependency(E(u)),c&&t.start("Setup compiler");let i=[];this.compiler=await F(e,{from:this.enableSourceMaps?this.id:void 0,base:p,shouldRewriteUrls:!0,onDependency:f=>{n(f),i.push(this.addBuildDependency(f))},customCssResolver:this.customCssResolver,customJsResolver:this.customJsResolver}),await Promise.all(i),c&&t.end("Setup compiler"),c&&t.start("Setup scanner");let o=(this.compiler.root==="none"?[]:this.compiler.root===null?[{base:this.base,pattern:"**/*",negated:!1}]:[{...this.compiler.root,negated:!1}]).concat(this.compiler.sources);this.scanner=new J({sources:o}),c&&t.end("Setup scanner")}else for(let i of this.buildDependencies.keys())n(i);if(!(this.compiler.features&(h.AtApply|h.JsPluginCompat|h.ThemeFunction|h.Utilities)))return!1;if(this.compiler.features&h.Utilities){c&&t.start("Scan for candidates");for(let i of this.scanner.scan())this.candidates.add(i);c&&t.end("Scan for candidates")}if(this.compiler.features&h.Utilities){for(let i of this.scanner.files)n(i);for(let i of this.scanner.globs){if(i.pattern[0]==="!")continue;let o=m.relative(this.base,i.base);o[0]!=="."&&(o="./"+o),o=B(o),n(m.posix.join(o,i.pattern));let f=this.compiler.root;if(f!=="none"&&f!==null){let v=B(m.resolve(f.base,f.pattern));if(!await y.stat(v).then(S=>S.isDirectory(),()=>!1))throw new Error(`The path given to \`source(\u2026)\` must be a directory but got \`source(${v})\` instead.`)}}}c&&t.start("Build CSS");let d=this.compiler.build([...this.candidates]);c&&t.end("Build CSS"),c&&t.start("Build Source Map");let l=this.enableSourceMaps?V(this.compiler.buildSourceMap()).raw:void 0;return c&&t.end("Build Source Map"),{code:d,map:l}}async addBuildDependency(e){let s=null;try{s=(await y.stat(e)).mtimeMs}catch{}this.buildDependencies.set(e,s)}async requiresBuild(){for(let[e,s]of this.buildDependencies){if(s===null)return!0;try{if((await y.stat(e)).mtimeMs>s)return!0}catch{return!0}}return!1}};export{T as default};
