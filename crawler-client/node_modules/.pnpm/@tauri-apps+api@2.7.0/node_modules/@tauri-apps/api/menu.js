export { Submenu, itemFromKind } from './menu/submenu.js';
export { MenuItem } from './menu/menuItem.js';
export { Menu } from './menu/menu.js';
export { CheckMenuItem } from './menu/checkMenuItem.js';
export { IconMenuItem, NativeIcon } from './menu/iconMenuItem.js';
export { PredefinedMenuItem } from './menu/predefinedMenuItem.js';

// Copyright 2019-2024 Tauri Programme within The Commons Conservancy
// SPDX-License-Identifier: Apache-2.0
// SPDX-License-Identifier: MIT
/**
 * Menu types and utilities.
 *
 * This package is also accessible with `window.__TAURI__.menu` when [`app.withGlobalTauri`](https://v2.tauri.app/reference/config/#withglobaltauri) in `tauri.conf.json` is set to `true`.
 * @module
 */
