{"version": 3, "file": "unpack.d.ts", "sourceRoot": "", "sources": ["../../src/unpack.ts"], "names": [], "mappings": ";AASA,OAAW,EAAE,KAAK,KAAK,EAAE,MAAM,SAAS,CAAA;AAGxC,OAAO,EAAS,UAAU,EAAa,MAAM,YAAY,CAAA;AAGzD,OAAO,EAAE,MAAM,EAAE,MAAM,YAAY,CAAA;AAKnC,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAA;AACzC,OAAO,EAAE,gBAAgB,EAAE,MAAM,wBAAwB,CAAA;AACzD,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAA;AAC3C,OAAO,EAAE,QAAQ,EAAE,MAAM,kBAAkB,CAAA;AAE3C,QAAA,MAAM,OAAO,eAAoB,CAAA;AACjC,QAAA,MAAM,OAAO,eAAoB,CAAA;AACjC,QAAA,MAAM,QAAQ,eAAqB,CAAA;AACnC,QAAA,MAAM,UAAU,eAAuB,CAAA;AACvC,QAAA,MAAM,UAAU,eAAuB,CAAA;AACvC,QAAA,MAAM,MAAM,eAAmB,CAAA;AAC/B,QAAA,MAAM,IAAI,eAAiB,CAAA;AAC3B,QAAA,MAAM,SAAS,eAAsB,CAAA;AACrC,QAAA,MAAM,IAAI,eAAiB,CAAA;AAC3B,QAAA,MAAM,OAAO,eAAoB,CAAA;AACjC,QAAA,MAAM,QAAQ,eAAqB,CAAA;AACnC,QAAA,MAAM,WAAW,eAAwB,CAAA;AACzC,QAAA,MAAM,SAAS,eAAsB,CAAA;AACrC,QAAA,MAAM,KAAK,eAAkB,CAAA;AAC7B,QAAA,MAAM,OAAO,eAAoB,CAAA;AACjC,QAAA,MAAM,OAAO,eAAoB,CAAA;AACjC,QAAA,MAAM,IAAI,eAAiB,CAAA;AAC3B,QAAA,MAAM,MAAM,eAAmB,CAAA;AAC/B,QAAA,MAAM,KAAK,eAAkB,CAAA;AAC7B,QAAA,MAAM,UAAU,eAAuB,CAAA;AACvC,QAAA,MAAM,IAAI,eAAiB,CAAA;AAC3B,QAAA,MAAM,OAAO,eAAoB,CAAA;AACjC,QAAA,MAAM,GAAG,eAAgB,CAAA;AACzB,QAAA,MAAM,GAAG,eAAgB,CAAA;AACzB,QAAA,MAAM,WAAW,eAAuB,CAAA;AA6FxC,qBAAa,MAAO,SAAQ,MAAM;IAChC,CAAC,KAAK,CAAC,EAAE,OAAO,CAAS;IACzB,CAAC,WAAW,CAAC,EAAE,OAAO,CAAS;IAC/B,CAAC,OAAO,CAAC,EAAE,MAAM,CAAI;IAErB,YAAY,EAAE,gBAAgB,CAAyB;IACvD,SAAS,CAAC,EAAE,UAAU,CAAC,WAAW,CAAC,CAAA;IACnC,QAAQ,EAAE,IAAI,CAAO;IACrB,QAAQ,EAAE,KAAK,CAAQ;IACvB,QAAQ,EAAE,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,SAAS,CAAC,CAAA;IACpD,GAAG,CAAC,EAAE,MAAM,CAAA;IACZ,GAAG,CAAC,EAAE,MAAM,CAAA;IACZ,QAAQ,EAAE,OAAO,CAAA;IACjB,aAAa,EAAE,OAAO,CAAA;IACtB,UAAU,CAAC,EAAE,MAAM,CAAA;IACnB,UAAU,CAAC,EAAE,MAAM,CAAA;IACnB,QAAQ,EAAE,MAAM,CAAA;IAChB,UAAU,EAAE,OAAO,CAAA;IACnB,KAAK,EAAE,OAAO,CAAA;IACd,KAAK,EAAE,OAAO,CAAA;IACd,IAAI,EAAE,OAAO,CAAA;IACb,OAAO,EAAE,OAAO,CAAA;IAChB,aAAa,EAAE,OAAO,CAAA;IACtB,MAAM,EAAE,OAAO,CAAA;IACf,GAAG,EAAE,MAAM,CAAA;IACX,KAAK,EAAE,MAAM,CAAA;IACb,YAAY,EAAE,MAAM,CAAA;IACpB,KAAK,EAAE,MAAM,CAAA;IACb,KAAK,EAAE,MAAM,CAAA;IACb,KAAK,EAAE,MAAM,CAAA;IACb,KAAK,EAAE,OAAO,CAAA;gBAEF,GAAG,GAAE,UAAe;IAgHhC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,GAAG,KAAK,EAAE,IAAI,GAAE,QAAa;IAO3D,CAAC,UAAU,CAAC;IAQZ,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,SAAS;IA8G5B,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,SAAS;IA8B1B,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS;IAarC,CAAC,KAAK,CAAC,CACL,GAAG,EAAE,MAAM,EACX,IAAI,EAAE,MAAM,EACZ,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,GAAG,UAAU,EAAE,IAAI,CAAC,EAAE,MAAM,KAAK,IAAI;IAoBrD,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,SAAS;IAgB1B,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,SAAS;IAItB,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,SAAS;IAItB,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,IAAI;IAiG9C,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,IAAI;IA6CnD,CAAC,WAAW,CAAC,CAAC,KAAK,EAAE,SAAS;IAU9B,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,IAAI;IAI5C,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,IAAI;IAO7C,CAAC,IAAI,CAAC;IAIN,CAAC,MAAM,CAAC;IAKR,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,SAAS;IAQvB,CAAC,UAAU,CAAC,CAAC,KAAK,EAAE,SAAS,EAAE,EAAE,EAAE,KAAK;IAWxC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,SAAS;IAW1B,CAAC,UAAU,CAAC,CAAC,KAAK,EAAE,SAAS;IAkB7B,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,KAAK,IAAI;IA2G5D,CAAC,MAAM,CAAC,CACN,EAAE,EAAE,IAAI,GAAG,SAAS,GAAG,KAAK,EAC5B,KAAK,EAAE,SAAS,EAChB,IAAI,EAAE,MAAM,IAAI;IA0BlB,CAAC,IAAI,CAAC,CACJ,KAAK,EAAE,SAAS,EAChB,QAAQ,EAAE,MAAM,EAChB,IAAI,EAAE,MAAM,GAAG,SAAS,EACxB,IAAI,EAAE,MAAM,IAAI;CAanB;AAUD,qBAAa,UAAW,SAAQ,MAAM;IACpC,IAAI,EAAE,IAAI,CAAQ;IAElB,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,IAAI,GAAG,KAAK,GAAG,SAAS,EAAE,KAAK,EAAE,SAAS;IAIvD,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,SAAS;IAuE1B,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,IAAI;IAoFzC,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,IAAI;IAkC9C,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM;IAmBjC,CAAC,IAAI,CAAC,CACJ,KAAK,EAAE,SAAS,EAChB,QAAQ,EAAE,MAAM,EAChB,IAAI,EAAE,MAAM,GAAG,SAAS,EACxB,IAAI,EAAE,MAAM,IAAI;CAWnB"}