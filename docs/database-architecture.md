# 微博数据采集分析系统 - 数据库架构设计

**项目代号**: WeiboAnalytics  
**文档版本**: v1.0  
**创建日期**: 2025-08-04  
**状态**: 设计中  

---

## 📋 架构概述

本系统采用三层存储架构，根据数据访问频率和用途进行分层存储：

- **热数据层 (Redis)**: 缓存频繁访问的数据，提供毫秒级响应
- **温数据层 (PostgreSQL)**: 存储结构化业务数据，支持复杂查询
- **冷数据层 (ClickHouse)**: 存储历史分析数据，支持大数据分析

## 🏗️ 三层存储架构

### 1. 热数据层 - Redis

**用途**: 缓存、会话存储、实时计数器
**数据类型**: 
- 用户会话信息
- 登录状态缓存
- 实时统计计数
- 任务队列状态
- API限流计数器

**数据结构**:
```
weibo:session:{user_id}     # 用户会话信息
weibo:stats:realtime        # 实时统计数据
weibo:rate_limit:{ip}       # API限流
weibo:task:status:{task_id} # 任务状态缓存
```

### 2. 温数据层 - PostgreSQL

**用途**: 主要业务数据存储
**数据类型**:
- 用户基础信息
- 微博内容数据
- 评论数据
- 任务配置和状态
- 系统配置

### 3. 冷数据层 - ClickHouse

**用途**: 大数据分析和历史数据存储
**数据类型**:
- 历史微博数据
- 用户行为分析数据
- 情感分析结果
- 趋势分析数据
- 统计报表数据

---

## 📊 数据模型设计

### 核心实体关系

```
用户 (Users) 1:N 微博 (Posts)
微博 (Posts) 1:N 评论 (Comments)
用户 (Users) 1:N 任务 (Tasks)
微博 (Posts) 1:N 分析结果 (Analysis)
```

### 数据生命周期

1. **实时数据** → Redis (1-7天)
2. **活跃数据** → PostgreSQL (1-12个月)
3. **历史数据** → ClickHouse (长期存储)

---

## 🔄 数据迁移策略

### 自动迁移规则

1. **Redis → PostgreSQL**: 
   - 触发条件: 数据超过7天或Redis内存压力
   - 迁移频率: 每日凌晨2点

2. **PostgreSQL → ClickHouse**:
   - 触发条件: 数据超过3个月
   - 迁移频率: 每周日凌晨3点

### 数据保留策略

- **Redis**: 7天自动过期
- **PostgreSQL**: 12个月后迁移到ClickHouse
- **ClickHouse**: 永久保存（可配置压缩）

---

## 🚀 性能优化

### 索引策略
- 主键索引: 所有表
- 时间索引: created_at, updated_at
- 业务索引: user_id, post_id等外键
- 复合索引: 常用查询组合

### 分区策略
- 按时间分区: 微博数据按月分区
- 按用户分区: 大用户数据独立分区

### 缓存策略
- 查询结果缓存: 30分钟
- 用户信息缓存: 1小时
- 统计数据缓存: 5分钟

---

**下一步**: 创建具体的表结构和迁移脚本
