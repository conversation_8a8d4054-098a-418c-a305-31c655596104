# Ctrl+C 无法停止开发服务器问题修复

## 问题描述

在开发环境中，使用 `Ctrl+C` 无法正常停止Vite开发服务器，导致端口被占用：

```bash
$ lsof -i :1421
COMMAND    PID    USER   FD   TYPE  DEVICE SIZE/OFF NODE NAME
node    282709 imeepos   34u  IPv4 5229591      0t0  TCP localhost:1421 (LISTEN)

$ lsof -i :1420  
COMMAND    PID    USER   FD   TYPE  DEVICE SIZE/OFF NODE NAME
node    282710 imeepos   34u  IPv4 5227567      0t0  TCP localhost:1420 (LISTEN)
```

按 `Ctrl+C` 后进程仍在后台运行，无法正常终止。

## 根本原因

1. **进程分离问题**: 开发服务器进程没有正确绑定到终端会话
2. **信号传播问题**: `SIGINT` 信号没有正确传播到子进程
3. **后台进程管理**: 使用 `&` 启动的后台进程不受终端控制
4. **进程组问题**: 子进程没有在正确的进程组中

## 解决方案

### 1. 创建专用的停止脚本

创建 `scripts/stop-system.sh` 脚本，提供多种停止方式：

```bash
#!/bin/bash
# 停止PID文件记录的进程
# 停止端口占用的进程  
# 停止Vite和Node.js进程
# 停止Tauri进程
# 清理临时文件
```

**主要功能**:
- 优雅停止 (`SIGTERM`) + 强制停止 (`SIGKILL`)
- 端口检查和进程清理
- PID文件管理
- 临时文件清理

### 2. 改进开发启动脚本

创建 `scripts/dev-start.sh` 脚本，使用 `setsid` 创建新会话：

```bash
# 使用setsid避免信号传播问题
setsid pnpm dev > "$LOG_DIR/crawler-dev.log" 2>&1 &
local pid=$!
echo $pid > "$PID_DIR/crawler-dev.pid"
```

**关键改进**:
- 使用 `setsid` 创建独立会话
- 重定向输出到日志文件
- 记录PID到文件便于管理
- 端口状态检查和超时处理

### 3. 更新Makefile命令

```makefile
# 开发模式
dev: ## 启动开发模式
	@./scripts/dev-start.sh both

# 停止系统
stop: ## 停止所有服务
	@./scripts/stop-system.sh

# 强制停止系统
stop-force: ## 强制停止所有相关进程
	@./scripts/stop-system.sh --force
```

### 4. 进程管理最佳实践

**启动进程**:
```bash
# 创建独立会话，避免信号传播
setsid command > logfile 2>&1 &
PID=$!
echo $PID > pidfile
```

**停止进程**:
```bash
# 优雅停止
kill -TERM $PID
sleep 2

# 检查是否还在运行
if kill -0 $PID 2>/dev/null; then
    # 强制停止
    kill -KILL $PID
fi
```

## 验证修复

### 启动测试
```bash
# 启动开发环境
make dev

# 检查进程状态
make status
lsof -i :1420
lsof -i :1421
```

### 停止测试
```bash
# 正常停止
make stop

# 强制停止
make stop-force

# 验证端口释放
lsof -i :1420 || echo "端口1420已释放"
lsof -i :1421 || echo "端口1421已释放"
```

## 新增的管理命令

### Make命令
```bash
make dev              # 启动开发环境
make dev-crawler      # 仅启动爬虫客户端
make dev-management   # 仅启动管理客户端
make stop             # 停止所有服务
make stop-force       # 强制停止
make stop-ports       # 停止端口占用进程
make status           # 查看系统状态
make logs             # 查看日志
```

### 脚本命令
```bash
./scripts/dev-start.sh both        # 启动两个客户端
./scripts/dev-start.sh crawler     # 仅启动爬虫客户端
./scripts/dev-start.sh management  # 仅启动管理客户端

./scripts/stop-system.sh           # 正常停止
./scripts/stop-system.sh --force   # 强制停止
./scripts/stop-system.sh --ports   # 停止端口占用进程
./scripts/stop-system.sh --clean   # 停止并清理
```

## 影响的文件

### 新增文件
- `scripts/stop-system.sh` - 系统停止脚本
- `scripts/dev-start.sh` - 开发启动脚本
- `docs/bug-fixes/ctrl-c-process-management.md` - 本文档

### 修改文件
- `Makefile` - 更新dev和stop命令
- `scripts/tasks.sh` - 改进开发环境任务

### 目录结构
```
scripts/
├── dev-start.sh      # 开发启动脚本
├── stop-system.sh    # 系统停止脚本
├── tasks.sh          # 任务管理脚本
└── ...

logs/
├── crawler-dev.log   # 爬虫客户端日志
├── management-dev.log # 管理客户端日志
└── ...

pids/
├── crawler-dev.pid   # 爬虫客户端PID
├── management-dev.pid # 管理客户端PID
└── ...
```

## 注意事项

1. **后台运行**: 开发服务器现在在后台运行，不会阻塞终端
2. **日志查看**: 使用 `make logs` 查看实时日志
3. **进程管理**: 使用专用命令管理进程，不要依赖 `Ctrl+C`
4. **端口检查**: 启动前会自动清理占用的端口
5. **错误处理**: 启动失败时会自动清理资源

## 相关资源

- [Linux进程管理](https://man7.org/linux/man-pages/man1/setsid.1.html)
- [信号处理](https://man7.org/linux/man-pages/man7/signal.7.html)
- [Vite开发服务器](https://vitejs.dev/guide/cli.html)

## 修复日期

2025-01-08

## 修复状态

✅ 已修复并验证
