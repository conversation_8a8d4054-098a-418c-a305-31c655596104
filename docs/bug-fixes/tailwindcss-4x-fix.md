# TailwindCSS 4.x 配置修复

## 问题描述

在使用TailwindCSS 4.1.11版本时遇到以下错误：

```
Error: Cannot apply unknown utility class `px-4`. Are you using CSS modules or similar and missing `@reference`?
```

后续还出现了自定义颜色无法识别的问题：

```
error during build:
[@tailwindcss/vite:generate:build] Cannot apply unknown utility class `bg-primary-600`
```

## 根本原因

TailwindCSS 4.x 引入了重大变更：

1. **CSS导入语法变更**：从 `@tailwind` 指令改为 `@import "tailwindcss"`
2. **主题配置方式变更**：从JavaScript配置文件改为CSS中的 `@theme` 块
3. **Vite插件要求**：需要使用 `@tailwindcss/vite` 插件
4. **@apply指令限制**：在某些情况下不再支持复杂的 `@apply` 用法

## 解决方案

### 1. 安装必要的依赖

```bash
pnpm add -D @tailwindcss/vite
```

### 2. 更新CSS文件

**之前 (src/index.css):**
```css
@tailwind base;
@tailwind components;
@tailwind utilities;
```

**之后 (src/index.css):**
```css
@import "tailwindcss";

@theme {
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;
  
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;
  
  --font-family-sans: Inter, ui-sans-serif, system-ui;
  --font-family-mono: JetBrains Mono, ui-monospace, SFMono-Regular;
}

@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes slideUp {
  0% { transform: translateY(10px); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}
```

### 3. 更新Vite配置

**之前 (vite.config.js):**
```javascript
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";

export default defineConfig({
  plugins: [react()],
  // ...
});
```

**之后 (vite.config.js):**
```javascript
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import tailwindcss from "@tailwindcss/vite";

export default defineConfig({
  plugins: [react(), tailwindcss()],
  // ...
});
```

### 4. 简化TailwindCSS配置

**之前 (tailwind.config.js):**
```javascript
/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          // 颜色配置...
        },
      },
    },
  },
  plugins: [],
  darkMode: 'class',
}
```

**之后 (tailwind.config.js):**
```javascript
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
}
```

### 5. 替换@apply指令

**之前:**
```css
.btn-primary {
  @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500;
}
```

**之后:**
```css
.btn-primary {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  border: 1px solid transparent;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  color: white;
  background-color: var(--color-primary-600);
}

.btn-primary:hover {
  background-color: var(--color-primary-700);
}

.btn-primary:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--color-primary-500);
}
```

## 验证修复

### 构建测试
```bash
# 爬虫客户端
cd crawler-client && pnpm build

# 管理客户端  
cd management-client && pnpm build
```

### 开发模式测试
```bash
# 爬虫客户端
cd crawler-client && pnpm dev

# 管理客户端
cd management-client && pnpm dev
```

## 影响的文件

### 爬虫客户端
- `crawler-client/src/index.css`
- `crawler-client/vite.config.js`
- `crawler-client/tailwind.config.js`
- `crawler-client/package.json` (添加 @tailwindcss/vite)

### 管理客户端
- `management-client/src/index.css`
- `management-client/vite.config.js`
- `management-client/tailwind.config.js`
- `management-client/package.json` (添加 @tailwindcss/vite)

## 注意事项

1. **主题配置迁移**：所有主题配置都需要从JavaScript配置文件迁移到CSS的 `@theme` 块中
2. **CSS变量命名**：颜色需要使用 `--color-` 前缀，字体使用 `--font-family-` 前缀
3. **@apply限制**：复杂的 `@apply` 用法可能需要改写为标准CSS
4. **PostCSS配置**：保持现有的PostCSS配置不变
5. **兼容性**：确保所有工具类在新版本中仍然可用

## 相关资源

- [TailwindCSS 4.0 升级指南](https://tailwindcss.com/docs/upgrade-guide)
- [TailwindCSS 4.0 主题配置](https://tailwindcss.com/docs/theme)
- [@tailwindcss/vite 插件文档](https://github.com/tailwindlabs/tailwindcss/tree/next/packages/%40tailwindcss-vite)

## 修复日期

2025-01-08

## 修复状态

✅ 已修复并验证
