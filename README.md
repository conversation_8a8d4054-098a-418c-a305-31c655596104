# 微博数据采集分析系统 (WeiboAnalytics)

一个基于 Tauri + React + Rust 的现代化微博数据采集和分析系统。

## 🚀 快速开始

### 方式一：使用 Make 命令（推荐）

```bash
# 查看所有可用命令
make help

# 快速初始化项目
make init

# 启动开发环境
make dev

# 构建项目
make build

# 查看系统状态
make status
```

### 方式二：使用任务脚本

```bash
# 查看所有可用任务
./scripts/tasks.sh help

# 快速设置项目
./scripts/tasks.sh setup

# 启动开发环境
./scripts/tasks.sh dev

# 构建项目
./scripts/tasks.sh build
```

### 方式三：使用 npm/pnpm 脚本

```bash
# 查看帮助
pnpm run help

# 设置项目
pnpm run setup

# 启动开发环境
pnpm run dev

# 构建项目
pnpm run build
```

## 📋 管理命令详解

### 🔧 项目管理

| 命令 | Make | 任务脚本 | npm脚本 | 说明 |
|------|------|----------|---------|------|
| 帮助 | `make help` | `./scripts/tasks.sh help` | `pnpm run help` | 显示所有可用命令 |
| 初始化 | `make init` | `./scripts/tasks.sh setup` | `pnpm run setup` | 安装依赖并初始化项目 |
| 环境检查 | `make check-env` | - | `pnpm run env:check` | 检查开发环境 |
| 清理 | `make clean` | `./scripts/tasks.sh clean` | `pnpm run clean` | 清理构建文件 |
| 深度清理 | `make clean-all` | - | `pnpm run clean:all` | 清理所有文件包括依赖 |

### 🛠️ 开发相关

| 命令 | Make | 任务脚本 | npm脚本 | 说明 |
|------|------|----------|---------|------|
| 开发模式 | `make dev` | `./scripts/tasks.sh dev` | `pnpm run dev` | 启动前端开发服务器 |
| Tauri开发 | `make dev-tauri` | - | - | 启动Tauri开发模式 |
| 构建 | `make build` | `./scripts/tasks.sh build` | `pnpm run build` | 构建所有客户端 |
| 测试 | `make test` | `./scripts/tasks.sh test` | `pnpm run test` | 运行所有测试 |
| 代码检查 | `make lint` | `./scripts/tasks.sh lint` | `pnpm run lint` | 运行代码检查 |
| 格式化 | `make format` | `./scripts/tasks.sh format` | `pnpm run format` | 格式化代码 |

### 🗄️ 数据库管理

| 命令 | Make | 任务脚本 | npm脚本 | 说明 |
|------|------|----------|---------|------|
| 初始化数据库 | `make db-init` | `./scripts/tasks.sh db` | `pnpm run db:init` | 初始化数据库 |
| 重置数据库 | `make db-reset` | `./scripts/tasks.sh db` | - | 重置数据库（危险操作） |
| 备份数据 | `make backup` | `./scripts/tasks.sh backup` | `pnpm run backup` | 备份数据库 |

### 📨 消息队列管理

| 命令 | Make | 任务脚本 | npm脚本 | 说明 |
|------|------|----------|---------|------|
| 初始化MQ | `make mq-init` | `./scripts/tasks.sh mq` | `pnpm run mq:init` | 初始化RabbitMQ |
| MQ状态 | `make mq-status` | `./scripts/tasks.sh mq` | `pnpm run mq:monitor` | 查看消息队列状态 |
| MQ监控 | `make monitor` | `./scripts/tasks.sh mq` | - | 实时监控消息队列 |

### 📊 系统监控

| 命令 | Make | 任务脚本 | npm脚本 | 说明 |
|------|------|----------|---------|------|
| 系统状态 | `make status` | `./scripts/tasks.sh status` | `pnpm run status` | 查看系统状态 |
| 查看日志 | `make logs` | `./scripts/tasks.sh logs` | `pnpm run logs` | 查看应用日志 |
| 实时日志 | `make logs-follow` | `./scripts/tasks.sh logs` | - | 实时查看日志 |
| 启动系统 | `make start` | - | `pnpm run start` | 启动完整系统 |
| 停止系统 | `make stop` | - | `pnpm run stop` | 停止所有服务 |

## 🏗️ 项目结构

```
weibo/
├── crawler-client/          # 爬虫客户端 (Tauri + React)
├── management-client/       # 管理客户端 (Tauri + React)
├── shared/                  # 共享代码库
│   ├── types/              # TypeScript类型定义
│   ├── utils/              # 工具函数
│   ├── database/           # 数据库配置
│   ├── messaging/          # 消息队列
│   ├── logging/            # 日志系统
│   └── config/             # 配置管理
├── database/               # 数据库脚本
│   ├── schemas/            # 数据库架构
│   ├── migrations/         # 迁移脚本
│   └── seeds/              # 种子数据
├── config/                 # 配置文件
│   ├── development.toml    # 开发环境配置
│   ├── production.toml     # 生产环境配置
│   └── rabbitmq/           # RabbitMQ配置
├── scripts/                # 管理脚本
│   ├── tasks.sh            # 快速任务脚本
│   ├── start-system.sh     # 系统启动脚本
│   ├── init-rabbitmq.sh    # RabbitMQ初始化
│   └── monitor-rabbitmq.sh # RabbitMQ监控
├── logs/                   # 日志文件
├── pids/                   # 进程ID文件
├── data/                   # 数据文件
├── tmp/                    # 临时文件
├── backups/                # 备份文件
├── Makefile                # Make构建脚本
├── package.json            # 项目配置
└── README.md               # 项目说明
```

## 🔧 开发环境要求

- **Node.js**: >= 18.0.0
- **pnpm**: >= 8.0.0
- **Rust**: >= 1.70.0
- **PostgreSQL**: >= 13.0
- **Redis**: >= 6.0
- **RabbitMQ**: >= 3.9

## 🚀 快速开发流程

1. **初始化项目**
   ```bash
   make init
   ```

2. **启动开发环境**
   ```bash
   make dev
   ```

3. **在另一个终端查看状态**
   ```bash
   make status
   ```

4. **查看日志**
   ```bash
   make logs
   ```

5. **停止开发环境**
   ```bash
   make stop
   ```

## 📝 常用开发命令

```bash
# 只启动爬虫客户端
pnpm run crawler:dev

# 只启动管理客户端
pnpm run management:dev

# 启动Tauri开发模式
pnpm run crawler:tauri
pnpm run management:tauri

# 更新所有依赖
make update

# 运行代码检查和格式化
make lint
make format

# 备份数据
make backup
```

## 🔍 故障排除

### 端口冲突
```bash
# 检查端口占用
make status

# 停止所有服务
make stop
```

### 数据库连接问题
```bash
# 检查数据库状态
make status

# 重新初始化数据库
make db-init
```

### 消息队列问题
```bash
# 检查RabbitMQ状态
make mq-status

# 重新初始化消息队列
make mq-init
```

### 清理和重建
```bash
# 清理所有文件
make clean-all

# 重新初始化
make init
```

## 📚 更多信息

- [数据库架构文档](docs/database-architecture.md)
- [API文档](docs/api.md)
- [部署指南](docs/deployment.md)
- [开发指南](docs/development.md)

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
