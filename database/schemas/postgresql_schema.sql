-- 微博数据采集分析系统 - PostgreSQL数据库架构
-- WeiboAnalytics Database Schema
-- 创建日期: 2025-08-04

-- 创建数据库
CREATE DATABASE IF NOT EXISTS weibo_analytics;
USE weibo_analytics;

-- 启用必要的扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";

-- ================================
-- 1. 用户表 (Users)
-- ================================
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) UNIQUE NOT NULL,
    nickname VARCHAR(100) NOT NULL,
    avatar TEXT,
    verified BOOL<PERSON><PERSON> DEFAULT FALSE,
    followers_count INTEGER DEFAULT 0,
    following_count INTEGER DEFAULT 0,
    posts_count INTEGER DEFAULT 0,
    description TEXT,
    location VARCHAR(100),
    gender VARCHAR(10),
    birthday DATE,
    registration_date DATE,
    verification_type VARCHAR(20),
    verification_reason TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 用户表索引
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_nickname ON users USING gin(nickname gin_trgm_ops);
CREATE INDEX idx_users_verified ON users(verified);
CREATE INDEX idx_users_created_at ON users(created_at);
CREATE INDEX idx_users_followers_count ON users(followers_count DESC);

-- ================================
-- 2. 微博内容表 (Posts)
-- ================================
CREATE TABLE posts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    weibo_id VARCHAR(50) UNIQUE NOT NULL, -- 微博原始ID
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    images JSONB, -- 图片URL数组
    video JSONB, -- 视频信息
    repost_count INTEGER DEFAULT 0,
    comment_count INTEGER DEFAULT 0,
    like_count INTEGER DEFAULT 0,
    topic_tags TEXT[], -- 话题标签数组
    mentions TEXT[], -- @用户数组
    is_repost BOOLEAN DEFAULT FALSE,
    original_post_id UUID REFERENCES posts(id),
    source VARCHAR(100), -- 发布来源
    location VARCHAR(200), -- 发布位置
    ip_location VARCHAR(100), -- IP归属地
    is_deleted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 微博表索引
CREATE INDEX idx_posts_weibo_id ON posts(weibo_id);
CREATE INDEX idx_posts_user_id ON posts(user_id);
CREATE INDEX idx_posts_created_at ON posts(created_at DESC);
CREATE INDEX idx_posts_content ON posts USING gin(content gin_trgm_ops);
CREATE INDEX idx_posts_topic_tags ON posts USING gin(topic_tags);
CREATE INDEX idx_posts_mentions ON posts USING gin(mentions);
CREATE INDEX idx_posts_is_repost ON posts(is_repost);
CREATE INDEX idx_posts_like_count ON posts(like_count DESC);

-- ================================
-- 3. 评论表 (Comments)
-- ================================
CREATE TABLE comments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    comment_id VARCHAR(50) UNIQUE NOT NULL, -- 评论原始ID
    post_id UUID NOT NULL REFERENCES posts(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    like_count INTEGER DEFAULT 0,
    reply_to UUID REFERENCES comments(id), -- 回复的评论ID
    images JSONB, -- 评论图片
    ip_location VARCHAR(100), -- IP归属地
    is_deleted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 评论表索引
CREATE INDEX idx_comments_comment_id ON comments(comment_id);
CREATE INDEX idx_comments_post_id ON comments(post_id);
CREATE INDEX idx_comments_user_id ON comments(user_id);
CREATE INDEX idx_comments_created_at ON comments(created_at DESC);
CREATE INDEX idx_comments_reply_to ON comments(reply_to);
CREATE INDEX idx_comments_content ON comments USING gin(content gin_trgm_ops);

-- ================================
-- 4. 爬取任务表 (Crawl_Tasks)
-- ================================
CREATE TABLE crawl_tasks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    type VARCHAR(20) NOT NULL CHECK (type IN ('user', 'keyword', 'topic', 'comment')),
    target TEXT NOT NULL, -- 爬取目标（用户名、关键词等）
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'failed', 'paused')),
    progress INTEGER DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
    config JSONB NOT NULL, -- 任务配置
    result JSONB, -- 执行结果统计
    error_message TEXT,
    created_by VARCHAR(100),
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 任务表索引
CREATE INDEX idx_crawl_tasks_type ON crawl_tasks(type);
CREATE INDEX idx_crawl_tasks_status ON crawl_tasks(status);
CREATE INDEX idx_crawl_tasks_created_at ON crawl_tasks(created_at DESC);
CREATE INDEX idx_crawl_tasks_target ON crawl_tasks USING gin(target gin_trgm_ops);

-- ================================
-- 5. 情感分析结果表 (Sentiment_Analysis)
-- ================================
CREATE TABLE sentiment_analysis (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    post_id UUID NOT NULL REFERENCES posts(id) ON DELETE CASCADE,
    sentiment VARCHAR(10) NOT NULL CHECK (sentiment IN ('positive', 'negative', 'neutral')),
    score DECIMAL(3,2) NOT NULL CHECK (score >= -1 AND score <= 1),
    confidence DECIMAL(3,2) NOT NULL CHECK (confidence >= 0 AND confidence <= 1),
    keywords TEXT[],
    model_version VARCHAR(20),
    analyzed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 情感分析表索引
CREATE INDEX idx_sentiment_post_id ON sentiment_analysis(post_id);
CREATE INDEX idx_sentiment_sentiment ON sentiment_analysis(sentiment);
CREATE INDEX idx_sentiment_score ON sentiment_analysis(score DESC);
CREATE INDEX idx_sentiment_analyzed_at ON sentiment_analysis(analyzed_at DESC);

-- ================================
-- 6. 系统配置表 (System_Config)
-- ================================
CREATE TABLE system_config (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    key VARCHAR(100) UNIQUE NOT NULL,
    value JSONB NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 系统配置表索引
CREATE INDEX idx_system_config_key ON system_config(key);
CREATE INDEX idx_system_config_is_active ON system_config(is_active);

-- ================================
-- 触发器：自动更新 updated_at
-- ================================
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为所有表添加更新时间触发器
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_posts_updated_at BEFORE UPDATE ON posts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_comments_updated_at BEFORE UPDATE ON comments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_crawl_tasks_updated_at BEFORE UPDATE ON crawl_tasks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_system_config_updated_at BEFORE UPDATE ON system_config FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
