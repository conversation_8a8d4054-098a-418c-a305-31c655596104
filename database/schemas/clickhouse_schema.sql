-- 微博数据采集分析系统 - ClickHouse分析数据库架构
-- WeiboAnalytics Analytics Database Schema
-- 创建日期: 2025-08-04

-- 创建数据库
CREATE DATABASE IF NOT EXISTS weibo_analytics_dw;

-- ================================
-- 1. 用户维度表 (dim_users)
-- ================================
CREATE TABLE weibo_analytics_dw.dim_users (
    user_id String,
    username String,
    nickname String,
    verified UInt8,
    followers_count UInt32,
    following_count UInt32,
    posts_count UInt32,
    location String,
    gender String,
    registration_date Date,
    verification_type String,
    created_at DateTime,
    updated_at DateTime
) ENGINE = ReplacingMergeTree(updated_at)
ORDER BY user_id
SETTINGS index_granularity = 8192;

-- ================================
-- 2. 微博事实表 (fact_posts)
-- ================================
CREATE TABLE weibo_analytics_dw.fact_posts (
    post_id String,
    weibo_id String,
    user_id String,
    content String,
    repost_count UInt32,
    comment_count UInt32,
    like_count UInt32,
    topic_tags Array(String),
    mentions Array(String),
    is_repost UInt8,
    source String,
    location String,
    ip_location String,
    post_date Date,
    post_hour UInt8,
    post_weekday UInt8,
    created_at DateTime
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(post_date)
ORDER BY (post_date, user_id, post_id)
SETTINGS index_granularity = 8192;

-- ================================
-- 3. 评论事实表 (fact_comments)
-- ================================
CREATE TABLE weibo_analytics_dw.fact_comments (
    comment_id String,
    post_id String,
    user_id String,
    content String,
    like_count UInt32,
    reply_to String,
    ip_location String,
    comment_date Date,
    comment_hour UInt8,
    comment_weekday UInt8,
    created_at DateTime
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(comment_date)
ORDER BY (comment_date, post_id, comment_id)
SETTINGS index_granularity = 8192;

-- ================================
-- 4. 情感分析事实表 (fact_sentiment)
-- ================================
CREATE TABLE weibo_analytics_dw.fact_sentiment (
    post_id String,
    user_id String,
    sentiment Enum8('positive' = 1, 'negative' = -1, 'neutral' = 0),
    score Float32,
    confidence Float32,
    keywords Array(String),
    model_version String,
    analysis_date Date,
    analyzed_at DateTime
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(analysis_date)
ORDER BY (analysis_date, sentiment, post_id)
SETTINGS index_granularity = 8192;

-- ================================
-- 5. 话题趋势分析表 (agg_topic_trends)
-- ================================
CREATE TABLE weibo_analytics_dw.agg_topic_trends (
    topic String,
    date Date,
    hour UInt8,
    mention_count UInt32,
    unique_users UInt32,
    total_likes UInt32,
    total_reposts UInt32,
    total_comments UInt32,
    avg_sentiment Float32,
    sentiment_positive UInt32,
    sentiment_negative UInt32,
    sentiment_neutral UInt32,
    created_at DateTime
) ENGINE = SummingMergeTree()
PARTITION BY toYYYYMM(date)
ORDER BY (date, topic, hour)
SETTINGS index_granularity = 8192;

-- ================================
-- 6. 用户行为分析表 (agg_user_behavior)
-- ================================
CREATE TABLE weibo_analytics_dw.agg_user_behavior (
    user_id String,
    date Date,
    posts_count UInt32,
    comments_count UInt32,
    likes_received UInt32,
    reposts_received UInt32,
    comments_received UInt32,
    active_hours Array(UInt8),
    top_topics Array(String),
    avg_sentiment Float32,
    influence_score Float32,
    created_at DateTime
) ENGINE = SummingMergeTree()
PARTITION BY toYYYYMM(date)
ORDER BY (date, user_id)
SETTINGS index_granularity = 8192;

-- ================================
-- 7. 实时统计表 (realtime_stats)
-- ================================
CREATE TABLE weibo_analytics_dw.realtime_stats (
    metric_name String,
    metric_value UInt64,
    dimensions Map(String, String),
    timestamp DateTime
) ENGINE = MergeTree()
PARTITION BY toYYYYMMDD(timestamp)
ORDER BY (metric_name, timestamp)
TTL timestamp + INTERVAL 7 DAY
SETTINGS index_granularity = 8192;

-- ================================
-- 8. 关键词热度表 (keyword_trends)
-- ================================
CREATE TABLE weibo_analytics_dw.keyword_trends (
    keyword String,
    date Date,
    hour UInt8,
    mention_count UInt32,
    unique_posts UInt32,
    unique_users UInt32,
    avg_sentiment Float32,
    heat_score Float32,
    created_at DateTime
) ENGINE = SummingMergeTree()
PARTITION BY toYYYYMM(date)
ORDER BY (date, keyword, hour)
SETTINGS index_granularity = 8192;

-- ================================
-- 9. 地域分析表 (location_analysis)
-- ================================
CREATE TABLE weibo_analytics_dw.location_analysis (
    location String,
    date Date,
    posts_count UInt32,
    users_count UInt32,
    avg_sentiment Float32,
    top_keywords Array(String),
    created_at DateTime
) ENGINE = SummingMergeTree()
PARTITION BY toYYYYMM(date)
ORDER BY (date, location)
SETTINGS index_granularity = 8192;

-- ================================
-- 10. 数据质量监控表 (data_quality_metrics)
-- ================================
CREATE TABLE weibo_analytics_dw.data_quality_metrics (
    table_name String,
    metric_type String,
    metric_value Float64,
    check_date Date,
    created_at DateTime
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(check_date)
ORDER BY (check_date, table_name, metric_type)
TTL check_date + INTERVAL 90 DAY
SETTINGS index_granularity = 8192;

-- ================================
-- 物化视图：实时话题热度
-- ================================
CREATE MATERIALIZED VIEW weibo_analytics_dw.mv_topic_realtime_trends
TO weibo_analytics_dw.agg_topic_trends
AS SELECT
    arrayJoin(topic_tags) as topic,
    toDate(created_at) as date,
    toHour(created_at) as hour,
    count() as mention_count,
    uniq(user_id) as unique_users,
    sum(like_count) as total_likes,
    sum(repost_count) as total_reposts,
    sum(comment_count) as total_comments,
    0 as avg_sentiment,
    0 as sentiment_positive,
    0 as sentiment_negative,
    0 as sentiment_neutral,
    now() as created_at
FROM weibo_analytics_dw.fact_posts
WHERE length(topic_tags) > 0
GROUP BY topic, date, hour;

-- ================================
-- 物化视图：用户日活跃度
-- ================================
CREATE MATERIALIZED VIEW weibo_analytics_dw.mv_user_daily_activity
TO weibo_analytics_dw.agg_user_behavior
AS SELECT
    user_id,
    toDate(created_at) as date,
    count() as posts_count,
    0 as comments_count,
    sum(like_count) as likes_received,
    sum(repost_count) as reposts_received,
    sum(comment_count) as comments_received,
    groupArray(toHour(created_at)) as active_hours,
    arrayDistinct(arrayFlatten(groupArray(topic_tags))) as top_topics,
    0 as avg_sentiment,
    0 as influence_score,
    now() as created_at
FROM weibo_analytics_dw.fact_posts
GROUP BY user_id, date;
