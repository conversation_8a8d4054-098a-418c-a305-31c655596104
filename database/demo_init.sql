-- 微博数据采集分析系统 - 演示数据库初始化
-- WeiboAnalytics Demo Database Initialization

-- 创建数据库（如果不存在）
SELECT 'CREATE DATABASE weibo_analytics' 
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'weibo_analytics')\gexec

-- 连接到数据库
\c weibo_analytics

-- 启用必要的扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) UNIQUE NOT NULL,
    nickname VARCHAR(100) NOT NULL,
    avatar TEXT,
    verified BOOLEAN DEFAULT FALSE,
    followers_count INTEGER DEFAULT 0,
    following_count INTEGER DEFAULT 0,
    posts_count INTEGER DEFAULT 0,
    description TEXT,
    location VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建微博内容表
CREATE TABLE IF NOT EXISTS posts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    weibo_id VARCHAR(50) UNIQUE NOT NULL,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    repost_count INTEGER DEFAULT 0,
    comment_count INTEGER DEFAULT 0,
    like_count INTEGER DEFAULT 0,
    topic_tags TEXT[] DEFAULT '{}',
    mentions TEXT[] DEFAULT '{}',
    is_repost BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建爬取任务表
CREATE TABLE IF NOT EXISTS crawl_tasks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    type VARCHAR(20) NOT NULL CHECK (type IN ('user', 'keyword', 'topic', 'comment')),
    target TEXT NOT NULL,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'failed', 'paused')),
    progress INTEGER DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
    config JSONB NOT NULL,
    result JSONB,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建系统配置表
CREATE TABLE IF NOT EXISTS system_config (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    key VARCHAR(100) UNIQUE NOT NULL,
    value JSONB NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建基础索引
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_posts_user_id ON posts(user_id);
CREATE INDEX IF NOT EXISTS idx_posts_created_at ON posts(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_crawl_tasks_status ON crawl_tasks(status);
CREATE INDEX IF NOT EXISTS idx_system_config_key ON system_config(key);

-- 插入初始配置数据
INSERT INTO system_config (key, value, description) VALUES
('crawler.rate_limit', '{"requests_per_minute": 60, "requests_per_hour": 1000}', '爬虫速率限制配置'),
('crawler.retry_config', '{"max_retries": 3, "retry_delay": 5, "backoff_factor": 2}', '爬虫重试配置'),
('system.data_retention', '{"redis_ttl": 604800, "postgres_months": 12}', '数据保留策略')
ON CONFLICT (key) DO NOTHING;

-- 插入演示用户数据
INSERT INTO users (username, nickname, verified, followers_count, description) VALUES
('demo_user_1', '演示用户1', true, 10000, '这是一个演示用户账号'),
('demo_user_2', '演示用户2', false, 5000, '另一个演示用户账号'),
('demo_user_3', '演示用户3', true, 50000, '知名演示用户账号')
ON CONFLICT (username) DO NOTHING;

-- 插入演示微博数据
WITH demo_users AS (
    SELECT id, username FROM users WHERE username LIKE 'demo_user_%'
)
INSERT INTO posts (weibo_id, user_id, content, like_count, repost_count, comment_count, topic_tags)
SELECT 
    'demo_post_' || generate_series(1, 10),
    (SELECT id FROM demo_users ORDER BY random() LIMIT 1),
    '这是演示微博内容 #演示话题# @演示用户 ' || generate_series(1, 10),
    (random() * 1000)::integer,
    (random() * 100)::integer,
    (random() * 200)::integer,
    ARRAY['演示话题', '测试标签']
ON CONFLICT (weibo_id) DO NOTHING;

-- 插入演示任务数据
INSERT INTO crawl_tasks (type, target, status, progress, config) VALUES
('user', 'demo_user_1', 'completed', 100, '{"max_posts": 100, "include_comments": true}'),
('keyword', '人工智能', 'running', 45, '{"max_posts": 500, "date_range": {"days": 7}}'),
('topic', '新能源汽车', 'pending', 0, '{"max_posts": 200, "include_reposts": false}')
ON CONFLICT DO NOTHING;

-- 显示初始化结果
SELECT 'Database initialized successfully!' as status;
SELECT COUNT(*) as user_count FROM users;
SELECT COUNT(*) as post_count FROM posts;
SELECT COUNT(*) as task_count FROM crawl_tasks;
SELECT COUNT(*) as config_count FROM system_config;
