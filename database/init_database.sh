#!/bin/bash

# 微博数据采集分析系统 - 数据库初始化脚本
# WeiboAnalytics Database Initialization Script

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
POSTGRES_HOST=${POSTGRES_HOST:-localhost}
POSTGRES_PORT=${POSTGRES_PORT:-5432}
POSTGRES_USER=${POSTGRES_USER:-postgres}
POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-postgres}
POSTGRES_DB=${POSTGRES_DB:-weibo_analytics}

REDIS_HOST=${REDIS_HOST:-localhost}
REDIS_PORT=${REDIS_PORT:-6379}
REDIS_PASSWORD=${REDIS_PASSWORD:-}

CLICKHOUSE_HOST=${CLICKHOUSE_HOST:-localhost}
CLICKHOUSE_PORT=${CLICKHOUSE_PORT:-8123}
CLICKHOUSE_USER=${CLICKHOUSE_USER:-default}
CLICKHOUSE_PASSWORD=${CLICKHOUSE_PASSWORD:-}

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}微博数据采集分析系统 - 数据库初始化${NC}"
echo -e "${BLUE}WeiboAnalytics Database Initialization${NC}"
echo -e "${BLUE}========================================${NC}"

# 检查依赖
check_dependencies() {
    echo -e "${YELLOW}检查依赖工具...${NC}"
    
    if ! command -v psql &> /dev/null; then
        echo -e "${RED}错误: psql 未安装${NC}"
        exit 1
    fi
    
    if ! command -v redis-cli &> /dev/null; then
        echo -e "${RED}错误: redis-cli 未安装${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✓ 依赖检查完成${NC}"
}

# 测试数据库连接
test_connections() {
    echo -e "${YELLOW}测试数据库连接...${NC}"
    
    # 测试PostgreSQL连接
    if PGPASSWORD=$POSTGRES_PASSWORD psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER -d postgres -c "SELECT 1;" &> /dev/null; then
        echo -e "${GREEN}✓ PostgreSQL 连接成功${NC}"
    else
        echo -e "${RED}✗ PostgreSQL 连接失败${NC}"
        exit 1
    fi
    
    # 测试Redis连接
    if redis-cli -h $REDIS_HOST -p $REDIS_PORT ping &> /dev/null; then
        echo -e "${GREEN}✓ Redis 连接成功${NC}"
    else
        echo -e "${RED}✗ Redis 连接失败${NC}"
        exit 1
    fi
}

# 初始化PostgreSQL数据库
init_postgresql() {
    echo -e "${YELLOW}初始化 PostgreSQL 数据库...${NC}"
    
    # 创建数据库
    PGPASSWORD=$POSTGRES_PASSWORD psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER -d postgres -c "
        SELECT 'CREATE DATABASE $POSTGRES_DB' 
        WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = '$POSTGRES_DB')\\gexec
    "
    
    # 执行架构脚本
    echo -e "${YELLOW}执行 PostgreSQL 架构脚本...${NC}"
    PGPASSWORD=$POSTGRES_PASSWORD psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER -d $POSTGRES_DB -f schemas/postgresql_schema.sql
    
    echo -e "${GREEN}✓ PostgreSQL 初始化完成${NC}"
}

# 初始化Redis配置
init_redis() {
    echo -e "${YELLOW}初始化 Redis 配置...${NC}"
    
    # 设置基础配置
    redis-cli -h $REDIS_HOST -p $REDIS_PORT << EOF
CONFIG SET save "900 1 300 10 60 10000"
CONFIG SET maxmemory-policy allkeys-lru
CONFIG SET timeout 300
CONFIG REWRITE
EOF
    
    # 创建基础键空间
    redis-cli -h $REDIS_HOST -p $REDIS_PORT << EOF
SET weibo:system:initialized "$(date -Iseconds)"
HSET weibo:config database_version "1.0"
HSET weibo:config redis_initialized "true"
EXPIRE weibo:system:initialized 86400
EOF
    
    echo -e "${GREEN}✓ Redis 初始化完成${NC}"
}

# 初始化ClickHouse数据库
init_clickhouse() {
    echo -e "${YELLOW}初始化 ClickHouse 数据库...${NC}"
    
    # 检查ClickHouse是否可用
    if command -v clickhouse-client &> /dev/null; then
        echo -e "${YELLOW}执行 ClickHouse 架构脚本...${NC}"
        clickhouse-client --host $CLICKHOUSE_HOST --port $CLICKHOUSE_PORT --user $CLICKHOUSE_USER --multiquery < schemas/clickhouse_schema.sql
        echo -e "${GREEN}✓ ClickHouse 初始化完成${NC}"
    else
        echo -e "${YELLOW}⚠ ClickHouse 客户端未安装，跳过初始化${NC}"
    fi
}

# 插入初始数据
insert_seed_data() {
    echo -e "${YELLOW}插入初始数据...${NC}"
    
    # 插入系统配置
    PGPASSWORD=$POSTGRES_PASSWORD psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER -d $POSTGRES_DB << EOF
INSERT INTO system_config (key, value, description) VALUES
('crawler.rate_limit', '{"requests_per_minute": 60, "requests_per_hour": 1000}', '爬虫速率限制配置'),
('crawler.retry_config', '{"max_retries": 3, "retry_delay": 5, "backoff_factor": 2}', '爬虫重试配置'),
('analysis.sentiment_model', '{"model_name": "bert-base-chinese", "version": "1.0", "threshold": 0.5}', '情感分析模型配置'),
('system.data_retention', '{"redis_ttl": 604800, "postgres_months": 12, "clickhouse_years": 5}', '数据保留策略'),
('notification.webhook', '{"enabled": false, "url": "", "events": ["task_completed", "task_failed"]}', '通知配置')
ON CONFLICT (key) DO NOTHING;
EOF
    
    echo -e "${GREEN}✓ 初始数据插入完成${NC}"
}

# 创建数据库用户和权限
setup_users_and_permissions() {
    echo -e "${YELLOW}设置数据库用户和权限...${NC}"
    
    PGPASSWORD=$POSTGRES_PASSWORD psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER -d $POSTGRES_DB << EOF
-- 创建应用用户
DO \$\$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_user WHERE usename = 'weibo_app') THEN
        CREATE USER weibo_app WITH PASSWORD 'weibo_app_password';
    END IF;
END
\$\$;

-- 授予权限
GRANT CONNECT ON DATABASE $POSTGRES_DB TO weibo_app;
GRANT USAGE ON SCHEMA public TO weibo_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO weibo_app;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO weibo_app;

-- 创建只读用户
DO \$\$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_user WHERE usename = 'weibo_readonly') THEN
        CREATE USER weibo_readonly WITH PASSWORD 'weibo_readonly_password';
    END IF;
END
\$\$;

-- 授予只读权限
GRANT CONNECT ON DATABASE $POSTGRES_DB TO weibo_readonly;
GRANT USAGE ON SCHEMA public TO weibo_readonly;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO weibo_readonly;
EOF
    
    echo -e "${GREEN}✓ 用户和权限设置完成${NC}"
}

# 验证初始化结果
verify_initialization() {
    echo -e "${YELLOW}验证初始化结果...${NC}"
    
    # 检查PostgreSQL表
    table_count=$(PGPASSWORD=$POSTGRES_PASSWORD psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER -d $POSTGRES_DB -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';")
    echo -e "${GREEN}✓ PostgreSQL 表数量: $table_count${NC}"
    
    # 检查Redis配置
    redis_status=$(redis-cli -h $REDIS_HOST -p $REDIS_PORT HGET weibo:config redis_initialized)
    echo -e "${GREEN}✓ Redis 初始化状态: $redis_status${NC}"
    
    # 检查系统配置
    config_count=$(PGPASSWORD=$POSTGRES_PASSWORD psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER -d $POSTGRES_DB -t -c "SELECT COUNT(*) FROM system_config;")
    echo -e "${GREEN}✓ 系统配置数量: $config_count${NC}"
}

# 主执行流程
main() {
    check_dependencies
    test_connections
    init_postgresql
    init_redis
    init_clickhouse
    insert_seed_data
    setup_users_and_permissions
    verify_initialization
    
    echo -e "${GREEN}========================================${NC}"
    echo -e "${GREEN}数据库初始化完成！${NC}"
    echo -e "${GREEN}========================================${NC}"
    echo -e "${BLUE}连接信息:${NC}"
    echo -e "${BLUE}PostgreSQL: ${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}${NC}"
    echo -e "${BLUE}Redis: ${REDIS_HOST}:${REDIS_PORT}${NC}"
    echo -e "${BLUE}ClickHouse: ${CLICKHOUSE_HOST}:${CLICKHOUSE_PORT}${NC}"
}

# 执行主函数
main "$@"
