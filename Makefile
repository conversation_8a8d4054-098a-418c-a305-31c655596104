# 微博数据采集分析系统 - Makefile
# WeiboAnalytics Project Management

.PHONY: help install build dev start stop clean test lint format check status logs

# 默认目标
.DEFAULT_GOAL := help

# 颜色定义
BLUE := \033[0;34m
GREEN := \033[0;32m
YELLOW := \033[1;33m
RED := \033[0;31m
NC := \033[0m # No Color

# 项目配置
PROJECT_NAME := WeiboAnalytics
ENVIRONMENT := development
LOG_DIR := logs
PID_DIR := pids

# 帮助信息
help: ## 显示帮助信息
	@echo "$(BLUE)========================================$(NC)"
	@echo "$(BLUE)微博数据采集分析系统 - 项目管理$(NC)"
	@echo "$(BLUE)WeiboAnalytics Project Management$(NC)"
	@echo "$(BLUE)========================================$(NC)"
	@echo ""
	@echo "$(YELLOW)可用命令:$(NC)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(GREEN)%-15s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""
	@echo "$(YELLOW)环境变量:$(NC)"
	@echo "  $(GREEN)WEIBO_ENV$(NC)        设置环境 (development/production)"
	@echo "  $(GREEN)WEIBO_LOG_LEVEL$(NC)  设置日志级别 (debug/info/warn/error)"

# 安装依赖
install: ## 安装所有依赖
	@echo "$(YELLOW)安装项目依赖...$(NC)"
	@echo "$(BLUE)安装爬虫客户端依赖...$(NC)"
	@cd crawler-client && pnpm install
	@echo "$(BLUE)安装管理客户端依赖...$(NC)"
	@cd management-client && pnpm install
	@echo "$(GREEN)✓ 依赖安装完成$(NC)"

# 环境检查
check-env: ## 检查开发环境
	@echo "$(YELLOW)检查开发环境...$(NC)"
	@./scripts/check-environment.sh

# 初始化项目
init: check-env install ## 初始化项目（检查环境 + 安装依赖）
	@echo "$(YELLOW)初始化数据库...$(NC)"
	@./database/init_database.sh || echo "$(YELLOW)⚠ 数据库初始化跳过（可能需要手动配置）$(NC)"
	@echo "$(YELLOW)初始化消息队列...$(NC)"
	@./scripts/init-rabbitmq.sh || echo "$(YELLOW)⚠ 消息队列初始化跳过（可能需要手动配置）$(NC)"
	@echo "$(GREEN)✓ 项目初始化完成$(NC)"

# 构建项目
build: ## 构建所有客户端
	@echo "$(YELLOW)构建项目...$(NC)"
	@echo "$(BLUE)构建爬虫客户端...$(NC)"
	@cd crawler-client && pnpm build
	@echo "$(BLUE)构建管理客户端...$(NC)"
	@cd management-client && pnpm build
	@echo "$(GREEN)✓ 构建完成$(NC)"

# 开发模式
dev: ## 启动开发模式
	@./scripts/dev-start.sh both

# 启动爬虫客户端开发模式
dev-crawler: ## 仅启动爬虫客户端开发模式
	@./scripts/dev-start.sh crawler

# 启动管理客户端开发模式
dev-management: ## 仅启动管理客户端开发模式
	@./scripts/dev-start.sh management

# 启动Tauri开发模式
dev-tauri: ## 启动Tauri开发模式
	@echo "$(YELLOW)启动Tauri开发模式...$(NC)"
	@echo "$(BLUE)启动爬虫客户端Tauri...$(NC)"
	@cd crawler-client && cargo tauri dev &
	@echo "$(BLUE)启动管理客户端Tauri...$(NC)"
	@cd management-client && cargo tauri dev &
	@echo "$(GREEN)✓ Tauri开发模式启动完成$(NC)"

# 启动系统
start: ## 启动完整系统
	@echo "$(YELLOW)启动完整系统...$(NC)"
	@./scripts/start-system.sh

# 停止系统
stop: ## 停止所有服务
	@echo "$(YELLOW)停止系统服务...$(NC)"
	@./scripts/stop-system.sh

# 强制停止系统
stop-force: ## 强制停止所有相关进程
	@echo "$(YELLOW)强制停止系统服务...$(NC)"
	@./scripts/stop-system.sh --force

# 停止端口占用进程
stop-ports: ## 停止端口占用的进程
	@echo "$(YELLOW)停止端口占用进程...$(NC)"
	@./scripts/stop-system.sh --ports

# 重启系统
restart: stop start ## 重启系统

# 查看状态
status: ## 查看系统状态
	@echo "$(YELLOW)系统状态:$(NC)"
	@echo ""
	@echo "$(BLUE)服务状态:$(NC)"
	@systemctl is-active --quiet postgresql && echo "$(GREEN)✓ PostgreSQL: 运行中$(NC)" || echo "$(RED)✗ PostgreSQL: 未运行$(NC)"
	@systemctl is-active --quiet redis-server && echo "$(GREEN)✓ Redis: 运行中$(NC)" || echo "$(RED)✗ Redis: 未运行$(NC)"
	@systemctl is-active --quiet rabbitmq-server && echo "$(GREEN)✓ RabbitMQ: 运行中$(NC)" || echo "$(RED)✗ RabbitMQ: 未运行$(NC)"
	@echo ""
	@echo "$(BLUE)应用状态:$(NC)"
	@if [ -f $(PID_DIR)/crawler-client.pid ] && kill -0 $$(cat $(PID_DIR)/crawler-client.pid) 2>/dev/null; then \
		echo "$(GREEN)✓ 爬虫客户端: 运行中 (PID: $$(cat $(PID_DIR)/crawler-client.pid))$(NC)"; \
	else \
		echo "$(RED)✗ 爬虫客户端: 未运行$(NC)"; \
	fi
	@if [ -f $(PID_DIR)/management-client.pid ] && kill -0 $$(cat $(PID_DIR)/management-client.pid) 2>/dev/null; then \
		echo "$(GREEN)✓ 管理客户端: 运行中 (PID: $$(cat $(PID_DIR)/management-client.pid))$(NC)"; \
	else \
		echo "$(RED)✗ 管理客户端: 未运行$(NC)"; \
	fi

# 查看日志
logs: ## 查看应用日志
	@echo "$(YELLOW)应用日志:$(NC)"
	@if [ -f $(LOG_DIR)/crawler-client.log ]; then \
		echo "$(BLUE)=== 爬虫客户端日志 (最后20行) ===$(NC)"; \
		tail -20 $(LOG_DIR)/crawler-client.log; \
		echo ""; \
	fi
	@if [ -f $(LOG_DIR)/management-client.log ]; then \
		echo "$(BLUE)=== 管理客户端日志 (最后20行) ===$(NC)"; \
		tail -20 $(LOG_DIR)/management-client.log; \
	fi

# 实时日志
logs-follow: ## 实时查看日志
	@echo "$(YELLOW)实时日志 (Ctrl+C 退出):$(NC)"
	@tail -f $(LOG_DIR)/*.log 2>/dev/null || echo "$(RED)没有找到日志文件$(NC)"

# 清理项目
clean: ## 清理构建文件和日志
	@echo "$(YELLOW)清理项目文件...$(NC)"
	@rm -rf crawler-client/dist
	@rm -rf crawler-client/src-tauri/target
	@rm -rf management-client/dist
	@rm -rf management-client/src-tauri/target
	@rm -rf $(LOG_DIR)/*
	@rm -rf $(PID_DIR)/*
	@rm -rf tmp/*
	@echo "$(GREEN)✓ 清理完成$(NC)"

# 深度清理
clean-all: clean ## 深度清理（包括node_modules）
	@echo "$(YELLOW)深度清理...$(NC)"
	@rm -rf crawler-client/node_modules
	@rm -rf management-client/node_modules
	@echo "$(GREEN)✓ 深度清理完成$(NC)"

# 代码检查
lint: ## 运行代码检查
	@echo "$(YELLOW)运行代码检查...$(NC)"
	@echo "$(BLUE)检查爬虫客户端...$(NC)"
	@cd crawler-client && pnpm lint || true
	@echo "$(BLUE)检查管理客户端...$(NC)"
	@cd management-client && pnpm lint || true
	@echo "$(BLUE)检查Rust代码...$(NC)"
	@cd crawler-client/src-tauri && cargo clippy -- -D warnings || true
	@cd management-client/src-tauri && cargo clippy -- -D warnings || true

# 代码格式化
format: ## 格式化代码
	@echo "$(YELLOW)格式化代码...$(NC)"
	@echo "$(BLUE)格式化前端代码...$(NC)"
	@cd crawler-client && pnpm format || true
	@cd management-client && pnpm format || true
	@echo "$(BLUE)格式化Rust代码...$(NC)"
	@cd crawler-client/src-tauri && cargo fmt || true
	@cd management-client/src-tauri && cargo fmt || true
	@echo "$(GREEN)✓ 代码格式化完成$(NC)"

# 运行测试
test: ## 运行所有测试
	@echo "$(YELLOW)运行测试...$(NC)"
	@echo "$(BLUE)运行前端测试...$(NC)"
	@cd crawler-client && pnpm test || true
	@cd management-client && pnpm test || true
	@echo "$(BLUE)运行Rust测试...$(NC)"
	@cd crawler-client/src-tauri && cargo test || true
	@cd management-client/src-tauri && cargo test || true

# 数据库管理
db-init: ## 初始化数据库
	@echo "$(YELLOW)初始化数据库...$(NC)"
	@./database/init_database.sh

db-reset: ## 重置数据库
	@echo "$(YELLOW)重置数据库...$(NC)"
	@echo "$(RED)警告: 这将删除所有数据！$(NC)"
	@read -p "确认继续? (y/N): " confirm && [ "$$confirm" = "y" ]
	@./database/init_database.sh

# 消息队列管理
mq-init: ## 初始化消息队列
	@echo "$(YELLOW)初始化消息队列...$(NC)"
	@./scripts/init-rabbitmq.sh

mq-status: ## 查看消息队列状态
	@echo "$(YELLOW)消息队列状态:$(NC)"
	@./scripts/monitor-rabbitmq.sh

# 系统监控
monitor: ## 启动系统监控
	@echo "$(YELLOW)启动系统监控...$(NC)"
	@./scripts/monitor-rabbitmq.sh --monitor

# 备份数据
backup: ## 备份数据
	@echo "$(YELLOW)备份数据...$(NC)"
	@mkdir -p backups
	@pg_dump weibo_analytics > backups/weibo_analytics_$$(date +%Y%m%d_%H%M%S).sql
	@echo "$(GREEN)✓ 数据库备份完成$(NC)"

# 生产部署
deploy: ## 部署到生产环境
	@echo "$(YELLOW)部署到生产环境...$(NC)"
	@echo "$(RED)请确保已经配置好生产环境变量$(NC)"
	@WEIBO_ENV=production $(MAKE) build
	@echo "$(GREEN)✓ 生产构建完成$(NC)"

# 版本信息
version: ## 显示版本信息
	@echo "$(BLUE)版本信息:$(NC)"
	@echo "项目: $(PROJECT_NAME)"
	@echo "Node.js: $$(node --version)"
	@echo "pnpm: $$(pnpm --version)"
	@echo "Rust: $$(rustc --version)"
	@echo "Cargo: $$(cargo --version)"
	@echo "Tauri CLI: $$(cargo tauri --version 2>/dev/null || echo '未安装')"

# 创建必要目录
setup-dirs: ## 创建必要的目录
	@mkdir -p $(LOG_DIR) $(PID_DIR) data tmp backups
	@echo "$(GREEN)✓ 目录创建完成$(NC)"
