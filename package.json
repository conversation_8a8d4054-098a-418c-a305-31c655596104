{"name": "weibo-analytics", "version": "1.0.0", "description": "微博数据采集分析系统 - WeiboAnalytics", "private": true, "type": "module", "scripts": {"setup": "scripts/tasks.sh setup", "dev": "scripts/tasks.sh dev", "build": "scripts/tasks.sh build", "start": "scripts/start-system.sh", "stop": "make stop", "clean": "scripts/tasks.sh clean", "test": "scripts/tasks.sh test", "lint": "scripts/tasks.sh lint", "format": "scripts/tasks.sh format", "status": "scripts/tasks.sh status", "logs": "scripts/tasks.sh logs", "backup": "scripts/tasks.sh backup", "update": "scripts/tasks.sh update", "db:init": "database/init_database.sh", "mq:init": "scripts/init-rabbitmq.sh", "mq:monitor": "scripts/monitor-rabbitmq.sh", "env:check": "scripts/check-environment.sh", "crawler:dev": "cd crawler-client && pnpm dev", "crawler:build": "cd crawler-client && pnpm build", "crawler:tauri": "cd crawler-client && cargo tauri dev", "management:dev": "cd management-client && pnpm dev", "management:build": "cd management-client && pnpm build", "management:tauri": "cd management-client && cargo tauri dev", "install:all": "pnpm install && cd crawler-client && pnpm install && cd ../management-client && pnpm install", "build:all": "cd crawler-client && pnpm build && cd ../management-client && pnpm build", "clean:all": "rm -rf crawler-client/dist crawler-client/node_modules management-client/dist management-client/node_modules logs/* pids/* tmp/*", "help": "scripts/tasks.sh help"}, "keywords": ["weibo", "crawler", "analytics", "tauri", "rust", "react", "typescript"], "author": "WeiboAnalytics Team", "license": "MIT", "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "workspaces": ["crawler-client", "management-client"], "devDependencies": {"@types/node": "^20.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/weibo-analytics.git"}, "bugs": {"url": "https://github.com/your-org/weibo-analytics/issues"}, "homepage": "https://github.com/your-org/weibo-analytics#readme"}